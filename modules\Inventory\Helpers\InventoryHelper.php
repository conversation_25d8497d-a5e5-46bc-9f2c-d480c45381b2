<?php

namespace Modules\Inventory\Helpers;

use App\Models\BranchInventory;
use App\Models\Product;
use App\Models\InventoryMovement;
use App\Models\InventoryLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InventoryHelper
{
    /**
     * Calculate inventory turnover rate
     */
    public static function calculateTurnoverRate(string $inventoryId, int $days = 90): float
    {
        $inventory = BranchInventory::findOrFail($inventoryId);
        
        $totalUsed = InventoryMovement::where('branch_inventory_id', $inventoryId)
            ->where('type', 'subtract')
            ->whereDate('created_at', '>=', now()->subDays($days))
            ->sum('quantity');
            
        $averageStock = ($inventory->current_stock + $inventory->minimum_stock_level) / 2;
        
        return $averageStock > 0 ? $totalUsed / $averageStock : 0;
    }

    /**
     * Calculate days of supply remaining
     */
    public static function calculateDaysOfSupply(string $inventoryId, int $lookbackDays = 30): int
    {
        $inventory = BranchInventory::findOrFail($inventoryId);
        
        $dailyUsage = InventoryMovement::where('branch_inventory_id', $inventoryId)
            ->where('type', 'subtract')
            ->whereDate('created_at', '>=', now()->subDays($lookbackDays))
            ->sum('quantity') / $lookbackDays;
            
        return $dailyUsage > 0 ? (int) ceil($inventory->current_stock / $dailyUsage) : 999;
    }

    /**
     * Generate suggested reorder quantity
     */
    public static function calculateReorderQuantity(string $inventoryId, int $leadTimeDays = 7, int $safetyStockDays = 3): float
    {
        $inventory = BranchInventory::findOrFail($inventoryId);
        
        // Calculate average daily usage
        $dailyUsage = InventoryMovement::where('branch_inventory_id', $inventoryId)
            ->where('type', 'subtract')
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->sum('quantity') / 30;
            
        // Calculate reorder quantity: (Lead time + Safety stock) * Daily usage - Current stock
        $reorderQuantity = (($leadTimeDays + $safetyStockDays) * $dailyUsage) - $inventory->current_stock;
        
        return max(0, $reorderQuantity);
    }

    /**
     * Check if item is low stock
     */
    public static function isLowStock(BranchInventory $inventory): bool
    {
        return $inventory->current_stock <= $inventory->minimum_stock_level;
    }

    /**
     * Check if item is out of stock
     */
    public static function isOutOfStock(BranchInventory $inventory): bool
    {
        return $inventory->current_stock <= 0;
    }

    /**
     * Check if item is overstocked
     */
    public static function isOverstocked(BranchInventory $inventory): bool
    {
        return $inventory->current_stock > $inventory->maximum_stock_level;
    }

    /**
     * Check if item is expiring soon
     */
    public static function isExpiringSoon(BranchInventory $inventory, int $days = 7): bool
    {
        return $inventory->expiry_date && 
               $inventory->expiry_date <= now()->addDays($days);
    }

    /**
     * Calculate ABC classification based on value
     */
    public static function calculateABCClassification(string $branchId): array
    {
        $inventories = BranchInventory::where('branch_id', $branchId)
            ->with('product')
            ->get()
            ->map(function ($inventory) {
                $inventory->total_value = $inventory->current_stock * $inventory->unit_cost;
                return $inventory;
            })
            ->sortByDesc('total_value');
            
        $totalValue = $inventories->sum('total_value');
        $runningValue = 0;
        $classifications = [];
        
        foreach ($inventories as $inventory) {
            $runningValue += $inventory->total_value;
            $percentage = ($runningValue / $totalValue) * 100;
            
            if ($percentage <= 80) {
                $class = 'A';
            } elseif ($percentage <= 95) {
                $class = 'B';
            } else {
                $class = 'C';
            }
            
            $classifications[$inventory->id] = $class;
        }
        
        return $classifications;
    }

    /**
     * Generate inventory valuation report
     */
    public static function generateValuationReport(string $branchId): array
    {
        $inventories = BranchInventory::where('branch_id', $branchId)
            ->with('product')
            ->get();
            
        $totalValue = 0;
        $categoryValues = [];
        $lowStockValue = 0;
        $expiredValue = 0;
        
        foreach ($inventories as $inventory) {
            $itemValue = $inventory->current_stock * $inventory->unit_cost;
            $totalValue += $itemValue;
            
            // Group by category
            $category = $inventory->product->category ?? 'Uncategorized';
            $categoryValues[$category] = ($categoryValues[$category] ?? 0) + $itemValue;
            
            // Low stock value
            if (self::isLowStock($inventory)) {
                $lowStockValue += $itemValue;
            }
            
            // Expired value
            if ($inventory->expiry_date && $inventory->expiry_date < now()) {
                $expiredValue += $itemValue;
            }
        }
        
        return [
            'total_value' => $totalValue,
            'total_items' => $inventories->count(),
            'category_values' => $categoryValues,
            'low_stock_value' => $lowStockValue,
            'expired_value' => $expiredValue,
            'average_item_value' => $inventories->count() > 0 ? $totalValue / $inventories->count() : 0,
        ];
    }

    /**
     * Log inventory action
     */
    public static function logAction(
        string $branchInventoryId,
        string $action,
        float $quantityBefore,
        float $quantityAfter,
        string $reason = null,
        string $notes = null
    ): InventoryLog {
        return InventoryLog::create([
            'branch_inventory_id' => $branchInventoryId,
            'action' => $action,
            'quantity_before' => $quantityBefore,
            'quantity_after' => $quantityAfter,
            'quantity_changed' => $quantityAfter - $quantityBefore,
            'reason' => $reason,
            'notes' => $notes,
            'user_id' => Auth::id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'resolved' => false,
        ]);
    }

    /**
     * Record inventory movement
     */
    public static function recordMovement(
        string $branchInventoryId,
        string $type,
        float $quantity,
        float $unitCost = null,
        string $reason = null,
        string $notes = null,
        array $additionalData = []
    ): InventoryMovement {
        $inventory = BranchInventory::findOrFail($branchInventoryId);
        $unitCost = $unitCost ?? $inventory->unit_cost;
        
        return InventoryMovement::create(array_merge([
            'branch_inventory_id' => $branchInventoryId,
            'type' => $type,
            'quantity' => $quantity,
            'unit_cost' => $unitCost,
            'total_cost' => $quantity * $unitCost,
            'reason' => $reason,
            'notes' => $notes,
            'user_id' => Auth::id(),
            'performed_at' => now(),
        ], $additionalData));
    }

    /**
     * Update stock level with logging
     */
    public static function updateStock(
        string $branchInventoryId,
        float $newQuantity,
        string $reason = null,
        string $notes = null
    ): BranchInventory {
        $inventory = BranchInventory::findOrFail($branchInventoryId);
        $oldQuantity = $inventory->current_stock;
        
        DB::transaction(function () use ($inventory, $newQuantity, $oldQuantity, $reason, $notes) {
            // Update inventory
            $inventory->update([
                'current_stock' => $newQuantity,
                'available_stock' => $newQuantity - $inventory->reserved_stock,
                'total_value' => $newQuantity * $inventory->unit_cost,
                'last_counted_at' => now(),
            ]);
            
            // Log the action
            self::logAction(
                $inventory->id,
                'stock_update',
                $oldQuantity,
                $newQuantity,
                $reason,
                $notes
            );
            
            // Record movement
            $movementType = $newQuantity > $oldQuantity ? 'add' : 'subtract';
            $quantityChanged = abs($newQuantity - $oldQuantity);
            
            if ($quantityChanged > 0) {
                self::recordMovement(
                    $inventory->id,
                    $movementType,
                    $quantityChanged,
                    $inventory->unit_cost,
                    $reason,
                    $notes
                );
            }
        });
        
        return $inventory->fresh();
    }

    /**
     * Format currency value
     */
    public static function formatCurrency(float $amount, string $currency = 'USD'): string
    {
        return number_format($amount, 2) . ' ' . $currency;
    }

    /**
     * Format quantity with unit
     */
    public static function formatQuantity(float $quantity, string $unit): string
    {
        return number_format($quantity, 2) . ' ' . $unit;
    }

    /**
     * Get stock status color
     */
    public static function getStockStatusColor(BranchInventory $inventory): string
    {
        if (self::isOutOfStock($inventory)) {
            return 'red';
        } elseif (self::isLowStock($inventory)) {
            return 'orange';
        } elseif (self::isOverstocked($inventory)) {
            return 'blue';
        } else {
            return 'green';
        }
    }

    /**
     * Get stock status text
     */
    public static function getStockStatusText(BranchInventory $inventory): string
    {
        if (self::isOutOfStock($inventory)) {
            return 'Out of Stock';
        } elseif (self::isLowStock($inventory)) {
            return 'Low Stock';
        } elseif (self::isOverstocked($inventory)) {
            return 'Overstocked';
        } else {
            return 'In Stock';
        }
    }

    /**
     * Generate barcode for product
     */
    public static function generateBarcode(string $sku): string
    {
        // Simple barcode generation - in production, use a proper barcode library
        return 'BC' . str_pad(crc32($sku), 10, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate optimal reorder point
     */
    public static function calculateOptimalReorderPoint(
        string $branchInventoryId,
        int $leadTimeDays = 7,
        float $serviceLevel = 0.95
    ): float {
        $inventory = BranchInventory::findOrFail($branchInventoryId);
        
        // Get usage data for the last 90 days
        $usageData = InventoryMovement::where('branch_inventory_id', $branchInventoryId)
            ->where('type', 'subtract')
            ->whereDate('created_at', '>=', now()->subDays(90))
            ->selectRaw('DATE(created_at) as date, SUM(quantity) as daily_usage')
            ->groupBy('date')
            ->pluck('daily_usage')
            ->toArray();
            
        if (empty($usageData)) {
            return $inventory->minimum_stock_level;
        }
        
        $averageDailyUsage = array_sum($usageData) / count($usageData);
        $variance = array_sum(array_map(function($x) use ($averageDailyUsage) {
            return pow($x - $averageDailyUsage, 2);
        }, $usageData)) / count($usageData);
        
        $standardDeviation = sqrt($variance);
        
        // Z-score for service level (95% = 1.645)
        $zScore = $serviceLevel >= 0.95 ? 1.645 : 1.28;
        
        $safetyStock = $zScore * $standardDeviation * sqrt($leadTimeDays);
        $reorderPoint = ($averageDailyUsage * $leadTimeDays) + $safetyStock;
        
        return max(0, $reorderPoint);
    }
}
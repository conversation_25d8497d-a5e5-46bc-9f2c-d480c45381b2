<?php

namespace Modules\Payment\Services;

use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\Transaction;
use Modules\Payment\Services\TransactionService;
use Modules\Payment\Helpers\PaymentHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Exception;

class PaymentService
{
    protected TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    /**
     * Process a payment
     */
    public function processPayment(array $data): Payment
    {
        return DB::transaction(function () use ($data) {
            // Validate payment method
            $paymentMethod = PaymentMethod::where('id', $data['payment_method_id'])
                ->where('is_active', true)
                ->firstOrFail();

            // Create payment record
            $payment = Payment::create([
                'id' => Str::uuid(),
                'payment_number' => $this->generatePaymentNumber(),
                'transaction_id' => $data['transaction_id'] ?? null,
                'payment_method_uuid' => $paymentMethod->id,
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? 'USD',
                'status' => 'pending',
                'payment_date' => now(),
                'reference_number' => $data['reference_number'] ?? null,
                'notes' => $data['notes'] ?? null,
                'processed_by' => Auth::id(),
                'metadata' => $data['metadata'] ?? null
            ]);

            // Process payment based on method type
            $this->processPaymentByMethod($payment, $paymentMethod, $data);

            // Update transaction if provided
            if (isset($data['transaction_id'])) {
                $this->updateTransactionPaymentStatus($data['transaction_id'], $payment);
            }

            // Log payment activity
            $this->logPaymentActivity($payment, 'payment_processed');

            return $payment->fresh();
        });
    }

    /**
     * Process payment based on payment method type
     */
    protected function processPaymentByMethod(Payment $payment, PaymentMethod $paymentMethod, array $data): void
    {
        switch ($paymentMethod->type) {
            case 'cash':
                $this->processCashPayment($payment, $data);
                break;
            case 'card':
                $this->processCardPayment($payment, $paymentMethod, $data);
                break;
            case 'digital_wallet':
                $this->processDigitalWalletPayment($payment, $paymentMethod, $data);
                break;
            case 'bank_transfer':
                $this->processBankTransferPayment($payment, $data);
                break;
            case 'crypto':
                $this->processCryptoPayment($payment, $data);
                break;
            default:
                throw new Exception('Unsupported payment method type: ' . $paymentMethod->type);
        }
    }

    /**
     * Process cash payment
     */
    protected function processCashPayment(Payment $payment, array $data): void
    {
        $payment->update([
            'status' => 'completed',
            'processed_at' => now(),
            'gateway_response' => [
                'type' => 'cash',
                'received_amount' => $data['received_amount'] ?? $payment->amount,
                'change_amount' => ($data['received_amount'] ?? $payment->amount) - $payment->amount
            ]
        ]);
    }

    /**
     * Process card payment
     */
    protected function processCardPayment(Payment $payment, PaymentMethod $paymentMethod, array $data): void
    {
        // Simulate card processing (integrate with actual payment gateway)
        $gatewayResponse = $this->processWithGateway($paymentMethod, $payment, $data);
        
        $payment->update([
            'status' => $gatewayResponse['status'],
            'processed_at' => $gatewayResponse['status'] === 'completed' ? now() : null,
            'gateway_transaction_id' => $gatewayResponse['transaction_id'] ?? null,
            'gateway_response' => $gatewayResponse
        ]);
    }

    /**
     * Process digital wallet payment
     */
    protected function processDigitalWalletPayment(Payment $payment, PaymentMethod $paymentMethod, array $data): void
    {
        // Simulate digital wallet processing
        $gatewayResponse = $this->processWithGateway($paymentMethod, $payment, $data);
        
        $payment->update([
            'status' => $gatewayResponse['status'],
            'processed_at' => $gatewayResponse['status'] === 'completed' ? now() : null,
            'gateway_transaction_id' => $gatewayResponse['transaction_id'] ?? null,
            'gateway_response' => $gatewayResponse
        ]);
    }

    /**
     * Process bank transfer payment
     */
    protected function processBankTransferPayment(Payment $payment, array $data): void
    {
        $payment->update([
            'status' => 'pending',
            'gateway_response' => [
                'type' => 'bank_transfer',
                'account_number' => $data['account_number'] ?? null,
                'routing_number' => $data['routing_number'] ?? null
            ]
        ]);
    }

    /**
     * Process cryptocurrency payment
     */
    protected function processCryptoPayment(Payment $payment, array $data): void
    {
        $payment->update([
            'status' => 'pending',
            'gateway_response' => [
                'type' => 'crypto',
                'wallet_address' => $data['wallet_address'] ?? null,
                'crypto_currency' => $data['crypto_currency'] ?? 'BTC'
            ]
        ]);
    }

    /**
     * Simulate gateway processing
     */
    protected function processWithGateway(PaymentMethod $paymentMethod, Payment $payment, array $data): array
    {
        // This is a simulation - replace with actual gateway integration
        $success = rand(1, 100) > 5; // 95% success rate for simulation
        
        return [
            'status' => $success ? 'completed' : 'failed',
            'transaction_id' => $success ? 'gw_' . Str::random(16) : null,
            'gateway' => $paymentMethod->provider,
            'response_code' => $success ? '00' : '05',
            'response_message' => $success ? 'Transaction approved' : 'Transaction declined',
            'processed_at' => now()->toISOString()
        ];
    }

    /**
     * Cancel a payment
     */
    public function cancelPayment(Payment $payment): Payment
    {
        if (!in_array($payment->status, ['pending', 'processing'])) {
            throw new Exception('Payment cannot be cancelled in current status: ' . $payment->status);
        }

        $payment->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancelled_by' => Auth::id()
        ]);

        $this->logPaymentActivity($payment, 'payment_cancelled');

        return $payment;
    }

    /**
     * Update transaction payment status
     */
    protected function updateTransactionPaymentStatus(string $transactionId, Payment $payment): void
    {
        $transaction = Transaction::findOrFail($transactionId);
        
        // Calculate total paid amount
        $totalPaid = $transaction->payments()->where('status', 'completed')->sum('amount');
        
        // Update transaction status based on payment status
        if ($payment->status === 'completed') {
            if ($totalPaid >= $transaction->amount) {
                $transaction->update(['status' => 'completed']);
            } else {
                $transaction->update(['status' => 'partially_paid']);
            }
        }
    }

    /**
     * Generate unique payment number
     */
    protected function generatePaymentNumber(): string
    {
        $prefix = 'PAY';
        $date = now()->format('Ymd');
        $sequence = Payment::whereDate('created_at', today())->count() + 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get payment analytics
     */
    public function getPaymentAnalytics(array $filters = []): array
    {
        $query = Payment::query();
        
        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        // Get basic statistics
        $totalPayments = $query->count();
        $totalAmount = $query->where('status', 'completed')->sum('amount');
        $averageAmount = $totalPayments > 0 ? $totalAmount / $totalPayments : 0;
        
        // Get payment status breakdown
        $statusBreakdown = $query->groupBy('status')
            ->selectRaw('status, count(*) as count, sum(amount) as total_amount')
            ->get()
            ->keyBy('status');
        
        // Get payment method breakdown
        $methodBreakdown = $query->join('payment_methods', 'payments.payment_method_uuid', '=', 'payment_methods.id')
            ->groupBy('payment_methods.type')
            ->selectRaw('payment_methods.type, count(*) as count, sum(payments.amount) as total_amount')
            ->get()
            ->keyBy('type');
        
        // Get daily trends
        $dailyTrends = $query->where('status', 'completed')
            ->selectRaw('DATE(created_at) as date, count(*) as count, sum(amount) as total_amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
        
        return [
            'summary' => [
                'total_payments' => $totalPayments,
                'total_amount' => $totalAmount,
                'average_amount' => $averageAmount,
                'success_rate' => $totalPayments > 0 ? ($statusBreakdown['completed']->count ?? 0) / $totalPayments * 100 : 0
            ],
            'status_breakdown' => $statusBreakdown,
            'method_breakdown' => $methodBreakdown,
            'daily_trends' => $dailyTrends
        ];
    }

    /**
     * Reconcile payments
     */
    public function reconcilePayments(array $filters = []): array
    {
        $query = Payment::query();
        
        // Apply filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        $payments = $query->with(['paymentMethod', 'transaction'])->get();
        
        $reconciliation = [
            'total_payments' => $payments->count(),
            'completed_payments' => $payments->where('status', 'completed')->count(),
            'failed_payments' => $payments->where('status', 'failed')->count(),
            'pending_payments' => $payments->where('status', 'pending')->count(),
            'total_amount' => $payments->where('status', 'completed')->sum('amount'),
            'discrepancies' => []
        ];
        
        // Check for discrepancies
        foreach ($payments as $payment) {
            if ($payment->transaction && $payment->status === 'completed') {
                $transactionTotal = $payment->transaction->payments()->where('status', 'completed')->sum('amount');
                if ($transactionTotal != $payment->transaction->amount) {
                    $reconciliation['discrepancies'][] = [
                        'transaction_id' => $payment->transaction->id,
                        'expected_amount' => $payment->transaction->amount,
                        'actual_amount' => $transactionTotal,
                        'difference' => $payment->transaction->amount - $transactionTotal
                    ];
                }
            }
        }
        
        return $reconciliation;
    }

    /**
     * Log payment activity
     */
    protected function logPaymentActivity(Payment $payment, string $action): void
    {
        // Log to audit trail or activity log
        // This can be implemented based on your logging requirements
    }
}
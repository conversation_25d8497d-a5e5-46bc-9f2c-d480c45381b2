<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAddonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'code' => 'sometimes|required|string|max:100',
            'price' => 'sometimes|required|numeric|min:0',
            'cost' => 'nullable|numeric|min:0',
            'addon_group_name' => 'nullable|string|max:100',
            'is_required' => 'boolean',
            'max_quantity' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Addon name is required.',
            'name.max' => 'Addon name cannot exceed 255 characters.',
            'code.required' => 'Addon code is required.',
            'code.max' => 'Addon code cannot exceed 100 characters.',
            'price.required' => 'Price is required.',
            'price.numeric' => 'Price must be a valid number.',
            'price.min' => 'Price must be greater than or equal to 0.',
            'cost.numeric' => 'Cost must be a valid number.',
            'cost.min' => 'Cost must be greater than or equal to 0.',
            'addon_group_name.max' => 'Addon group name cannot exceed 100 characters.',
            'max_quantity.integer' => 'Max quantity must be a valid number.',
            'max_quantity.min' => 'Max quantity must be at least 1.',
            'sort_order.integer' => 'Sort order must be a valid number.',
            'sort_order.min' => 'Sort order must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'addon_group_name' => 'addon group name',
            'is_required' => 'required status',
            'max_quantity' => 'maximum quantity',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_required')) {
            $this->merge([
                'is_required' => filter_var($this->is_required, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
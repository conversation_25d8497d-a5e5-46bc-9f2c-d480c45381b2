<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuAvailability extends Model
{
    protected $table = 'menu_availability';
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'menu_item_id',
        'day_of_week',
        'start_time',
        'end_time',
        'is_available',
        'date_specific',
        'start_date',
        'end_date',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_time' => 'datetime:H:i:s',
            'end_time' => 'datetime:H:i:s',
            'is_available' => 'boolean',
            'date_specific' => 'date',
            'start_date' => 'date',
            'end_date' => 'date',
        ];
    }

    // Relationships
    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    // Scopes
    public function scopeForDay($query, $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('date_specific', $date)
                    ->orWhere(function($q) use ($date) {
                        $q->whereNull('date_specific')
                          ->where('day_of_week', date('w', strtotime($date)));
                    });
    }
}
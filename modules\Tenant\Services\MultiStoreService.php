<?php

namespace Modules\Tenant\Services;

use App\Models\Tenant;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class MultiStoreService
{
    /**
     * Create a new branch for tenant
     */
    public function createBranch(Tenant $tenant, array $branchData): Branch
    {
        // Validate tenant can create more branches
        if (!$this->canCreateBranch($tenant)) {
            throw new \Exception('Tenant has reached maximum branch limit');
        }

        // Generate unique branch code if not provided
        if (!isset($branchData['code'])) {
            $branchData['code'] = $this->generateBranchCode($tenant, $branchData['name']);
        }

        $branchData['tenant_id'] = $tenant->id;
        $branchData['status'] = $branchData['status'] ?? 'active';

        return Branch::create($branchData);
    }

    /**
     * Update branch information
     */
    public function updateBranch(Branch $branch, array $data): Branch
    {
        $branch->update($data);
        return $branch->fresh();
    }

    /**
     * Delete branch (soft delete)
     */
    public function deleteBranch(Branch $branch): bool
    {
        // Prevent deletion of main branch
        if ($branch->is_main_branch) {
            throw new \Exception('Cannot delete main branch');
        }

        // Check if branch has active orders
        if ($this->hasActiveOrders($branch)) {
            throw new \Exception('Cannot delete branch with active orders');
        }

        return $branch->delete();
    }

    /**
     * Get all branches for tenant with statistics
     */
    public function getTenantBranches(Tenant $tenant, bool $includeStats = false): Collection
    {
        $query = $tenant->branches();
        
        if ($includeStats) {
            $query->withCount([
                'orders',
                'users',
                'tables'
            ]);
        }
        
        return $query->get();
    }

    /**
     * Get branch performance statistics
     */
    public function getBranchStatistics(Branch $branch, ?string $period = null): array
    {
        $period = $period ?? 'current_month';
        [$startDate, $endDate] = $this->getPeriodDates($period);

        $stats = [
            'basic_info' => [
                'total_orders' => $branch->orders()->count(),
                'total_customers' => $branch->customers()->count(),
                'total_staff' => $branch->users()->count(),
                'total_tables' => $branch->tables()->count(),
            ],
            'period_stats' => [
                'period' => $period,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'orders_count' => $branch->orders()
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                'revenue' => $branch->orders()
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->where('status', 'completed')
                    ->sum('total_amount'),
                'average_order_value' => 0,
            ],
            'top_items' => $this->getTopMenuItems($branch, $startDate, $endDate),
            'performance_metrics' => $this->getPerformanceMetrics($branch, $startDate, $endDate),
        ];

        // Calculate average order value
        if ($stats['period_stats']['orders_count'] > 0) {
            $stats['period_stats']['average_order_value'] = round(
                $stats['period_stats']['revenue'] / $stats['period_stats']['orders_count'],
                2
            );
        }

        return $stats;
    }

    /**
     * Transfer data between branches
     */
    public function transferData(Branch $fromBranch, Branch $toBranch, array $dataTypes): array
    {
        $results = [];
        
        DB::beginTransaction();
        
        try {
            foreach ($dataTypes as $dataType) {
                switch ($dataType) {
                    case 'menu_items':
                        $results['menu_items'] = $this->transferMenuItems($fromBranch, $toBranch);
                        break;
                    case 'customers':
                        $results['customers'] = $this->transferCustomers($fromBranch, $toBranch);
                        break;
                    case 'inventory':
                        $results['inventory'] = $this->transferInventory($fromBranch, $toBranch);
                        break;
                    case 'settings':
                        $results['settings'] = $this->transferSettings($fromBranch, $toBranch);
                        break;
                }
            }
            
            DB::commit();
            
            return [
                'success' => true,
                'results' => $results,
                'message' => 'Data transfer completed successfully'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Data transfer failed'
            ];
        }
    }

    /**
     * Sync menu items across branches
     */
    public function syncMenuAcrossBranches(Tenant $tenant, array $branchIds = null): array
    {
        $branches = $branchIds 
            ? $tenant->branches()->whereIn('id', $branchIds)->get()
            : $tenant->branches()->get();

        $mainBranch = $branches->where('is_main_branch', true)->first();
        
        if (!$mainBranch) {
            throw new \Exception('No main branch found for menu sync');
        }

        $syncResults = [];
        
        DB::beginTransaction();
        
        try {
            foreach ($branches as $branch) {
                if ($branch->id === $mainBranch->id) {
                    continue;
                }
                
                $syncResults[$branch->id] = $this->syncBranchMenu($mainBranch, $branch);
            }
            
            DB::commit();
            
            return [
                'success' => true,
                'synced_branches' => count($syncResults),
                'results' => $syncResults,
                'message' => 'Menu sync completed successfully'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Menu sync failed'
            ];
        }
    }

    /**
     * Get consolidated report across all branches
     */
    public function getConsolidatedReport(Tenant $tenant, string $reportType, ?string $period = null): array
    {
        $period = $period ?? 'current_month';
        [$startDate, $endDate] = $this->getPeriodDates($period);
        
        $branches = $tenant->branches()->get();
        
        switch ($reportType) {
            case 'sales':
                return $this->getConsolidatedSalesReport($branches, $startDate, $endDate);
            case 'inventory':
                return $this->getConsolidatedInventoryReport($branches, $startDate, $endDate);
            case 'staff':
                return $this->getConsolidatedStaffReport($branches, $startDate, $endDate);
            default:
                throw new \Exception('Invalid report type');
        }
    }

    /**
     * Check if tenant can create more branches
     */
    protected function canCreateBranch(Tenant $tenant): bool
    {
        $subscription = $tenant->subscriptions()
            ->where('status', 'active')
            ->with('plan')
            ->first();

        if (!$subscription) {
            return false;
        }

        $currentBranchCount = $tenant->branches()->count();
        return $currentBranchCount < $subscription->plan->max_branches;
    }

    /**
     * Generate unique branch code
     */
    protected function generateBranchCode(Tenant $tenant, string $branchName): string
    {
        $baseCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $branchName), 0, 4));
        $code = $baseCode;
        $counter = 1;

        while ($tenant->branches()->where('code', $code)->exists()) {
            $code = $baseCode . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $counter++;
        }

        return $code;
    }

    /**
     * Check if branch has active orders
     */
    protected function hasActiveOrders(Branch $branch): bool
    {
        return $branch->orders()
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->exists();
    }

    /**
     * Get period dates
     */
    protected function getPeriodDates(string $period): array
    {
        switch ($period) {
            case 'today':
                return [Carbon::today(), Carbon::today()->endOfDay()];
            case 'yesterday':
                return [Carbon::yesterday(), Carbon::yesterday()->endOfDay()];
            case 'current_week':
                return [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
            case 'last_week':
                return [Carbon::now()->subWeek()->startOfWeek(), Carbon::now()->subWeek()->endOfWeek()];
            case 'current_month':
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
            case 'last_month':
                return [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()];
            case 'current_year':
                return [Carbon::now()->startOfYear(), Carbon::now()->endOfYear()];
            default:
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
        }
    }

    /**
     * Get top menu items for branch
     */
    protected function getTopMenuItems(Branch $branch, Carbon $startDate, Carbon $endDate): array
    {
        // This would typically query order_items joined with menu_items
        // For now, return mock data
        return [
            ['name' => 'Burger Deluxe', 'quantity' => 150, 'revenue' => 1500.00],
            ['name' => 'Pizza Margherita', 'quantity' => 120, 'revenue' => 1440.00],
            ['name' => 'Caesar Salad', 'quantity' => 80, 'revenue' => 640.00],
        ];
    }

    /**
     * Get performance metrics for branch
     */
    protected function getPerformanceMetrics(Branch $branch, Carbon $startDate, Carbon $endDate): array
    {
        return [
            'average_preparation_time' => 15.5, // minutes
            'customer_satisfaction' => 4.2, // out of 5
            'order_accuracy' => 98.5, // percentage
            'table_turnover_rate' => 2.3, // times per day
        ];
    }

    /**
     * Transfer menu items between branches
     */
    protected function transferMenuItems(Branch $fromBranch, Branch $toBranch): array
    {
        // Implementation would copy menu items and their configurations
        return ['transferred' => 0, 'skipped' => 0];
    }

    /**
     * Transfer customers between branches
     */
    protected function transferCustomers(Branch $fromBranch, Branch $toBranch): array
    {
        // Implementation would transfer customer data
        return ['transferred' => 0, 'skipped' => 0];
    }

    /**
     * Transfer inventory between branches
     */
    protected function transferInventory(Branch $fromBranch, Branch $toBranch): array
    {
        // Implementation would transfer inventory data
        return ['transferred' => 0, 'skipped' => 0];
    }

    /**
     * Transfer settings between branches
     */
    protected function transferSettings(Branch $fromBranch, Branch $toBranch): array
    {
        // Implementation would copy branch-specific settings
        return ['transferred' => 0, 'skipped' => 0];
    }

    /**
     * Sync menu from main branch to target branch
     */
    protected function syncBranchMenu(Branch $mainBranch, Branch $targetBranch): array
    {
        // Implementation would sync menu items, categories, and pricing
        return ['synced_items' => 0, 'updated_items' => 0, 'new_items' => 0];
    }

    /**
     * Get consolidated sales report
     */
    protected function getConsolidatedSalesReport(Collection $branches, Carbon $startDate, Carbon $endDate): array
    {
        $totalRevenue = 0;
        $totalOrders = 0;
        $branchData = [];

        foreach ($branches as $branch) {
            $branchRevenue = $branch->orders()
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('total_amount');
            
            $branchOrders = $branch->orders()
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $totalRevenue += $branchRevenue;
            $totalOrders += $branchOrders;

            $branchData[] = [
                'branch_id' => $branch->id,
                'branch_name' => $branch->name,
                'revenue' => $branchRevenue,
                'orders' => $branchOrders,
                'average_order_value' => $branchOrders > 0 ? round($branchRevenue / $branchOrders, 2) : 0
            ];
        }

        return [
            'period' => [$startDate, $endDate],
            'total_revenue' => $totalRevenue,
            'total_orders' => $totalOrders,
            'average_order_value' => $totalOrders > 0 ? round($totalRevenue / $totalOrders, 2) : 0,
            'branch_breakdown' => $branchData
        ];
    }

    /**
     * Get consolidated inventory report
     */
    protected function getConsolidatedInventoryReport(Collection $branches, Carbon $startDate, Carbon $endDate): array
    {
        // Implementation would aggregate inventory data across branches
        return [
            'total_products' => 0,
            'low_stock_items' => 0,
            'out_of_stock_items' => 0,
            'branch_breakdown' => []
        ];
    }

    /**
     * Get consolidated staff report
     */
    protected function getConsolidatedStaffReport(Collection $branches, Carbon $startDate, Carbon $endDate): array
    {
        $totalStaff = 0;
        $branchData = [];

        foreach ($branches as $branch) {
            $branchStaff = $branch->users()->count();
            $totalStaff += $branchStaff;

            $branchData[] = [
                'branch_id' => $branch->id,
                'branch_name' => $branch->name,
                'total_staff' => $branchStaff,
                'active_staff' => $branch->users()->where('status', 'active')->count()
            ];
        }

        return [
            'period' => [$startDate, $endDate],
            'total_staff' => $totalStaff,
            'branch_breakdown' => $branchData
        ];
    }
}
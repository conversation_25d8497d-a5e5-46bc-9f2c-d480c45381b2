<?php

namespace Modules\HR\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Shift;
use App\Models\ShiftType;
use App\Models\ShiftAssignment;
use App\Models\StaffAttendance;
use App\Models\PayPeriod;
use App\Models\Payslip;
use Modules\HR\Services\HRService;
use Modules\HR\Http\Requests\CreateShiftRequest;
use Modules\HR\Http\Requests\UpdateShiftRequest;
use Modules\HR\Http\Requests\AssignShiftRequest;
use Modules\HR\Http\Requests\CheckInRequest;
use Modules\HR\Http\Requests\CheckOutRequest;
use Modules\HR\Http\Requests\CreateShiftTypeRequest;
use Modules\HR\Http\Requests\UpdateShiftTypeRequest;
use Modules\HR\Http\Requests\ApplyPenaltyRequest;
use Modules\HR\Http\Requests\GeneratePayslipRequest;
use Modules\HR\Http\Requests\SubmitLeaveRequest;
use Modules\HR\Http\Requests\UpdateLeaveRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class HRController extends Controller
{
    protected HRService $hrService;

    public function __construct(HRService $hrService)
    {
        $this->hrService = $hrService;
    }

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Get all staff members
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only(['search', 'role', 'branch_id']);
        $perPage = $request->get('per_page', 15);
        
        $staff = $this->hrService->getStaff(
            $this->getTenantId(),
            $filters,
            $perPage
        );

        return response()->json([
            'success' => true,
            'data' => $staff,
        ]);
    }

    /**
     * Get staff member details
     */
    public function show(int $id): JsonResponse
    {
        $staff = $this->hrService->getStaffById($this->getTenantId(), $id);

        if (!$staff) {
            return response()->json([
                'success' => false,
                'message' => 'Staff member not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $staff,
        ]);
    }

    /**
     * Check in staff member
     */
    public function checkIn(CheckInRequest $request): JsonResponse
    {
        try {
            $attendance = $this->hrService->checkIn(
                $this->getTenantId(),
                $request->user_id ?? Auth::id(),
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Check-in successful',
                'data' => $attendance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Check-in failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check out staff member
     */
    public function checkOut(CheckOutRequest $request): JsonResponse
    {
        try {
            $attendance = $this->hrService->checkOut(
                $this->getTenantId(),
                $request->user_id ?? Auth::id(),
                $request->validated()
            );

            if (!$attendance) {
                return response()->json([
                    'success' => false,
                    'message' => 'No check-in record found for today',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Check-out successful',
                'data' => $attendance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Check-out failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get attendance records
     */
    public function attendance(Request $request): JsonResponse
    {
        $filters = $request->only(['user_id', 'branch_id', 'date_from', 'date_to', 'status']);
        $perPage = $request->get('per_page', 15);
        
        $attendance = $this->hrService->getAttendance(
            $this->getTenantId(),
            $filters,
            $perPage
        );

        return response()->json([
            'success' => true,
            'data' => $attendance,
        ]);
    }

    /**
     * Get attendance statistics
     */
    public function attendanceStats(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'branch_id' => 'nullable|integer',
        ]);

        $stats = $this->hrService->getAttendanceStats(
            $this->getTenantId(),
            $request->start_date,
            $request->end_date,
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get staff working hours for payroll
     */
    public function workingHours(Request $request, int $userId): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $workingHours = $this->hrService->getStaffWorkingHours(
            $this->getTenantId(),
            $userId,
            $request->start_date,
            $request->end_date
        );

        return response()->json([
            'success' => true,
            'data' => $workingHours,
        ]);
    }

    /**
     * Get staff schedule
     */
    public function schedule(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'branch_id' => 'nullable|integer',
        ]);

        $schedule = $this->hrService->getStaffSchedule(
            $this->getTenantId(),
            $request->start_date,
            $request->end_date,
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $schedule,
        ]);
    }

    /**
     * Get all shifts
     */
    public function shifts(Request $request): JsonResponse
    {
        $filters = $request->only(['branch_id', 'date_from', 'date_to', 'status', 'shift_type_id']);
        $perPage = $request->get('per_page', 15);
        
        $shifts = $this->hrService->getShifts(
            $this->getTenantId(),
            $filters,
            $perPage
        );

        return response()->json([
            'success' => true,
            'data' => $shifts,
        ]);
    }

    /**
     * Create a new shift
     */
    public function createShift(CreateShiftRequest $request): JsonResponse
    {
        try {
            $shift = $this->hrService->createShift(
                $this->getTenantId(),
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Shift created successfully',
                'data' => $shift,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update shift
     */
    public function updateShift(UpdateShiftRequest $request, int $id): JsonResponse
    {
        $shift = Shift::where('tenant_id', $this->getTenantId())
                     ->where('id', $id)
                     ->first();

        if (!$shift) {
            return response()->json([
                'success' => false,
                'message' => 'Shift not found',
            ], 404);
        }

        try {
            $updatedShift = $this->hrService->updateShift(
                $shift,
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Shift updated successfully',
                'data' => $updatedShift,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Assign staff to shift
     */
    public function assignShift(AssignShiftRequest $request): JsonResponse
    {
        try {
            $assignment = $this->hrService->assignShift(
                $this->getTenantId(),
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Shift assigned successfully',
                'data' => $assignment,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Accept shift assignment
     */
    public function acceptShift(int $assignmentId): JsonResponse
    {
        $assignment = ShiftAssignment::where('tenant_id', $this->getTenantId())
                                   ->where('id', $assignmentId)
                                   ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Shift assignment not found',
            ], 404);
        }

        try {
            $this->hrService->acceptShiftAssignment($assignment);

            return response()->json([
                'success' => true,
                'message' => 'Shift accepted successfully',
                'data' => $assignment->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to accept shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Decline shift assignment
     */
    public function declineShift(Request $request, int $assignmentId): JsonResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        $assignment = ShiftAssignment::where('tenant_id', $this->getTenantId())
                                   ->where('id', $assignmentId)
                                   ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Shift assignment not found',
            ], 404);
        }

        try {
            $this->hrService->declineShiftAssignment($assignment, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Shift declined successfully',
                'data' => $assignment->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to decline shift',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Request shift replacement
     */
    public function requestReplacement(Request $request, int $assignmentId): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        $assignment = ShiftAssignment::where('tenant_id', $this->getTenantId())
                                   ->where('id', $assignmentId)
                                   ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Shift assignment not found',
            ], 404);
        }

        try {
            $this->hrService->requestShiftReplacement($assignment, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Replacement request submitted successfully',
                'data' => $assignment->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to request replacement',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get shift types
     */
    public function shiftTypes(Request $request): JsonResponse
    {
        $shiftTypes = $this->hrService->getShiftTypes(
            $this->getTenantId(),
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $shiftTypes,
        ]);
    }

    /**
     * Create shift type
     */
    public function createShiftType(CreateShiftTypeRequest $request): JsonResponse
    {
        try {
            $shiftType = $this->hrService->createShiftType(
                $this->getTenantId(),
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Shift type created successfully',
                'data' => $shiftType,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create shift type',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update shift type
     */
    public function updateShiftType(UpdateShiftTypeRequest $request, int $id): JsonResponse
    {
        $shiftType = ShiftType::where('tenant_id', $this->getTenantId())
                             ->where('id', $id)
                             ->first();

        if (!$shiftType) {
            return response()->json([
                'success' => false,
                'message' => 'Shift type not found',
            ], 404);
        }

        try {
            $updatedShiftType = $this->hrService->updateShiftType(
                $shiftType,
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Shift type updated successfully',
                'data' => $updatedShiftType,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update shift type',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get understaffed shifts
     */
    public function understaffedShifts(Request $request): JsonResponse
    {
        $shifts = $this->hrService->getUnderstaffedShifts(
            $this->getTenantId(),
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $shifts,
        ]);
    }

    /**
     * Get available staff for shift
     */
    public function availableStaff(int $shiftId): JsonResponse
    {
        $staff = $this->hrService->getAvailableStaffForShift(
            $this->getTenantId(),
            $shiftId
        );

        return response()->json([
            'success' => true,
            'data' => $staff,
        ]);
    }

    /**
     * Calculate staff salary for a pay period
     */
    public function calculateSalary(Request $request, int $userId): JsonResponse
    {
        $request->validate([
            'pay_period_id' => 'required|integer|exists:pay_periods,id',
        ]);

        try {
            $payPeriod = PayPeriod::findOrFail($request->pay_period_id);
            $salaryData = $this->hrService->calculateSalary($userId, $payPeriod);

            return response()->json([
                'success' => true,
                'data' => $salaryData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate salary',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate payslip for staff
     */
    public function generatePayslip(GeneratePayslipRequest $request): JsonResponse
    {
        try {
            $payPeriod = PayPeriod::findOrFail($request->pay_period_id);
            $payslip = $this->hrService->generatePayslip($request->user_id, $payPeriod);

            return response()->json([
                'success' => true,
                'message' => 'Payslip generated successfully',
                'data' => $payslip,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate payslip',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Apply penalty to staff
     */
    public function applyPenalty(ApplyPenaltyRequest $request): JsonResponse
    {
        try {
            $result = $this->hrService->applyPenalty(
                $request->user_id,
                $request->type,
                $request->amount,
                $request->reason,
                $request->date
            );

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply penalty',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get staff penalties
     */
    public function penalties(Request $request, int $userId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $penalties = $this->hrService->getStaffPenalties(
            $userId,
            $request->start_date,
            $request->end_date
        );

        return response()->json([
            'success' => true,
            'data' => $penalties,
        ]);
    }

    /**
     * Process bulk attendance
     */
    public function bulkAttendance(Request $request): JsonResponse
    {
        $request->validate([
            'attendance_data' => 'required|array',
            'attendance_data.*.user_id' => 'required|integer|exists:users,id',
            'attendance_data.*.date' => 'required|date',
            'attendance_data.*.status' => 'required|string|in:present,absent,leave,holiday',
            'attendance_data.*.check_in_time' => 'nullable|date_format:H:i:s',
            'attendance_data.*.check_out_time' => 'nullable|date_format:H:i:s',
        ]);

        try {
            $result = $this->hrService->processBulkAttendance(
                $this->getTenantId(),
                $request->attendance_data
            );

            return response()->json([
                'success' => true,
                'message' => 'Bulk attendance processed',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process bulk attendance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get staff performance metrics
     */
    public function performanceMetrics(Request $request, int $userId): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        try {
            $metrics = $this->hrService->getStaffPerformanceMetrics(
                $userId,
                $request->start_date,
                $request->end_date
            );

            return response()->json([
                'success' => true,
                'data' => $metrics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get performance metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payslips for staff
     */
    public function payslips(Request $request, int $userId): JsonResponse
    {
        $request->validate([
            'pay_period_id' => 'nullable|integer|exists:pay_periods,id',
            'status' => 'nullable|string|in:pending,approved,paid',
        ]);

        $query = Payslip::where('user_id', $userId)
                        ->with(['payPeriod', 'user']);

        if ($request->pay_period_id) {
            $query->where('pay_period_id', $request->pay_period_id);
        }

        if ($request->status) {
            $query->where('status', $request->status);
        }

        $payslips = $query->orderBy('generated_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $payslips,
        ]);
    }

    /**
     * Get leave requests
     */
    public function leaveRequests(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'nullable|integer|exists:users,id',
            'status' => 'nullable|string|in:pending,approved,rejected,cancelled',
            'leave_type' => 'nullable|string|in:vacation,sick,personal,emergency',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $query = \App\Models\LeaveRequest::where('tenant_id', $this->getTenantId())
                        ->with(['user', 'approvedBy']);

        if ($request->user_id) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->leave_type) {
            $query->where('leave_type', $request->leave_type);
        }

        if ($request->start_date) {
            $query->where('end_date', '>=', $request->start_date);
        }

        if ($request->end_date) {
            $query->where('start_date', '<=', $request->end_date);
        }

        $leaveRequests = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $leaveRequests,
        ]);
    }

    /**
     * Submit leave request
     */
    public function submitLeaveRequest(SubmitLeaveRequest $request): JsonResponse
    {

        try {
            $leaveRequest = \App\Models\LeaveRequest::create([
                'tenant_id' => $this->getTenantId(),
                'branch_id' => Auth::user()->branch_id,
                'user_id' => Auth::id(),
                'leave_type' => $request->leave_type,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'reason' => $request->reason,
                'status' => 'pending',
                'days_requested' => \Carbon\Carbon::parse($request->start_date)
                    ->diffInDays(\Carbon\Carbon::parse($request->end_date)) + 1,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Leave request submitted successfully',
                'data' => $leaveRequest->load('user'),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit leave request',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Approve/Reject leave request
     */
    public function updateLeaveRequest(UpdateLeaveRequest $request, int $id): JsonResponse
    {

        $leaveRequest = \App\Models\LeaveRequest::where('tenant_id', $this->getTenantId())
                                              ->where('id', $id)
                                              ->first();

        if (!$leaveRequest) {
            return response()->json([
                'success' => false,
                'message' => 'Leave request not found',
            ], 404);
        }

        if ($leaveRequest->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Leave request has already been processed',
            ], 400);
        }

        try {
            $leaveRequest->update([
                'status' => $request->status,
                'admin_notes' => $request->admin_notes,
                'approved_by' => Auth::id(),
                'approved_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Leave request ' . $request->status . ' successfully',
                'data' => $leaveRequest->fresh(['user', 'approvedBy']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update leave request',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel leave request
     */
    public function cancelLeaveRequest(int $id): JsonResponse
    {
        $leaveRequest = \App\Models\LeaveRequest::where('tenant_id', $this->getTenantId())
                                              ->where('id', $id)
                                              ->where('user_id', Auth::id())
                                              ->first();

        if (!$leaveRequest) {
            return response()->json([
                'success' => false,
                'message' => 'Leave request not found',
            ], 404);
        }

        if (!in_array($leaveRequest->status, ['pending', 'approved'])) {
            return response()->json([
                'success' => false,
                'message' => 'Leave request cannot be cancelled',
            ], 400);
        }

        try {
            $leaveRequest->update(['status' => 'cancelled']);

            return response()->json([
                'success' => true,
                'message' => 'Leave request cancelled successfully',
                'data' => $leaveRequest->fresh(['user', 'approvedBy']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel leave request',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Waive penalty
     */
    public function waivePenalty(Request $request, int $penaltyId): JsonResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $result = $this->hrService->waivePenalty($penaltyId, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Penalty waived successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to waive penalty',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get penalty statistics
     */
    public function penaltyStats(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        try {
            $stats = $this->hrService->getPenaltyStats(
                $this->getTenantId(),
                $request->start_date,
                $request->end_date
            );

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get penalty statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
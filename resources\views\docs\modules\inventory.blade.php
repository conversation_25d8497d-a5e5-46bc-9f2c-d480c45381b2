@extends('docs.layout')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-amber-50">
    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Mo<PERSON>le Header -->
        <div class="mb-8">
            <div class="flex items-center mb-6">
                <div class="p-4 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 mr-6 shadow-lg">
                    <i class="fas fa-boxes text-white text-3xl"></i>
                </div>
                <div>
                    <h1 class="text-5xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent mb-2">
                        Inventory Module
                    </h1>
                    <p class="text-xl text-gray-600">Stock management, ingredient tracking, and supply chain operations</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg p-6 border border-yellow-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Endpoints</p>
                        <p class="text-3xl font-bold text-yellow-600">24</p>
                    </div>
                    <i class="fas fa-route text-yellow-500 text-2xl"></i>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-6 border border-green-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Public Routes</p>
                        <p class="text-3xl font-bold text-green-600">0</p>
                    </div>
                    <i class="fas fa-globe text-green-500 text-2xl"></i>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-6 border border-blue-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Protected Routes</p>
                        <p class="text-3xl font-bold text-blue-600">24</p>
                    </div>
                    <i class="fas fa-shield-alt text-blue-500 text-2xl"></i>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-6 border border-purple-100">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Middleware</p>
                        <p class="text-3xl font-bold text-purple-600">1</p>
                    </div>
                    <i class="fas fa-filter text-purple-500 text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="mb-8">
            <div class="relative max-w-md mx-auto">
                <input type="text" id="searchInput" placeholder="Search endpoints..." 
                       class="w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent shadow-lg">
                <div class="absolute inset-y-0 left-0 flex items-center pl-4">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- Protected Routes Section -->
        <div class="bg-white rounded-xl shadow-lg mb-8 border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-shield-alt text-blue-500 mr-2"></i>
                    Protected Routes
                </h2>
                <p class="text-gray-600">All inventory endpoints require authentication and proper permissions</p>
            </div>

            <!-- Inventory Items Management -->
            <div class="border-b border-gray-100">
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('inventory-items')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Inventory Items Management</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="inventory-items-icon"></i>
                    </div>
                </button>
                <div id="inventory-items" class="hidden px-6 pb-6">
                    <!-- Get All Items -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items</code>
                        </div>
                        <p class="text-gray-600 mb-3">Retrieve all inventory items with filtering and pagination</p>
                        <div class="bg-gray-50 p-3 rounded">
                            <p class="text-sm font-medium text-gray-700 mb-2">Query Parameters:</p>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li><code>search</code> - Search by product name or SKU</li>
                                <li><code>category</code> - Filter by product category</li>
                                <li><code>low_stock</code> - Show only low stock items</li>
                                <li><code>per_page</code> - Items per page (default: 15)</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Create Item -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">POST</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items</code>
                        </div>
                        <p class="text-gray-600 mb-3">Create a new inventory item</p>
                        <div class="bg-gray-50 p-3 rounded">
                            <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                            <pre class="text-sm text-gray-600 overflow-x-auto"><code>{
  "name": "Product Name",
  "sku": "SKU123",
  "category": "ingredients",
  "unit_id": 1,
  "initial_stock": 100,
  "minimum_stock": 10,
  "maximum_stock": 500,
  "cost_per_unit": 2.50
}</code></pre>
                        </div>
                    </div>

                    <!-- Show Item -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/{id}</code>
                        </div>
                        <p class="text-gray-600">Get detailed information about a specific inventory item</p>
                    </div>

                    <!-- Update Item -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium mr-3">PUT</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/{id}</code>
                        </div>
                        <p class="text-gray-600">Update inventory item details</p>
                    </div>

                    <!-- Delete Item -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium mr-3">DELETE</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/{id}</code>
                        </div>
                        <p class="text-gray-600">Delete an inventory item (only if stock is zero)</p>
                    </div>
                </div>
            </div>

            <!-- Stock Management -->
            <div class="border-b border-gray-100">
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('stock-management')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Stock Management</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="stock-management-icon"></i>
                    </div>
                </button>
                <div id="stock-management" class="hidden px-6 pb-6">
                    <!-- Get Low Stock Items -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/low-stock</code>
                        </div>
                        <p class="text-gray-600">Get items with stock levels at or below minimum threshold</p>
                    </div>

                    <!-- Update Stock -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">POST</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/{id}/update-stock</code>
                        </div>
                        <p class="text-gray-600 mb-3">Update stock quantity for an item</p>
                        <div class="bg-gray-50 p-3 rounded">
                            <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                            <pre class="text-sm text-gray-600 overflow-x-auto"><code>{
  "quantity": 50,
  "type": "add", // add, subtract, set
  "reason": "Stock replenishment"
}</code></pre>
                        </div>
                    </div>

                    <!-- Get Movements -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/{id}/movements</code>
                        </div>
                        <p class="text-gray-600">Get stock movement history for an item</p>
                    </div>

                    <!-- Bulk Update -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">POST</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/bulk-update</code>
                        </div>
                        <p class="text-gray-600">Update multiple items' stock levels in a single request</p>
                    </div>

                    <!-- Get Analytics -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/analytics</code>
                        </div>
                        <p class="text-gray-600">Get inventory analytics and insights</p>
                    </div>

                    <!-- Get Reorder Suggestions -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/items/reorder-suggestions</code>
                        </div>
                        <p class="text-gray-600">Get automated reorder suggestions based on usage patterns</p>
                    </div>
                </div>
            </div>

            <!-- Supplier Management -->
            <div class="border-b border-gray-100">
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('supplier-management')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Supplier Management</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="supplier-management-icon"></i>
                    </div>
                </button>
                <div id="supplier-management" class="hidden px-6 pb-6">
                    <!-- Suppliers CRUD -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers</code>
                                <span class="ml-3 text-gray-600">List all suppliers</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">POST</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers</code>
                                <span class="ml-3 text-gray-600">Create new supplier</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers/{id}</code>
                                <span class="ml-3 text-gray-600">Get supplier details</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium mr-3">PUT</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers/{id}</code>
                                <span class="ml-3 text-gray-600">Update supplier</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium mr-3">DELETE</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers/{id}</code>
                                <span class="ml-3 text-gray-600">Delete supplier</span>
                            </div>
                        </div>
                    </div>

                    <!-- Supplier Analytics -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers/{id}/products</code>
                                <span class="ml-3 text-gray-600">Get supplier products</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers/{id}/purchase-orders</code>
                                <span class="ml-3 text-gray-600">Get supplier purchase orders</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/suppliers/{id}/performance</code>
                                <span class="ml-3 text-gray-600">Get supplier performance metrics</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase Orders -->
            <div class="border-b border-gray-100">
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('purchase-orders')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Purchase Orders</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="purchase-orders-icon"></i>
                    </div>
                </button>
                <div id="purchase-orders" class="hidden px-6 pb-6">
                    <!-- Purchase Orders CRUD -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders</code>
                                <span class="ml-3 text-gray-600">List all purchase orders</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">POST</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders</code>
                                <span class="ml-3 text-gray-600">Create new purchase order</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders/{id}</code>
                                <span class="ml-3 text-gray-600">Get purchase order details</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium mr-3">PUT</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders/{id}</code>
                                <span class="ml-3 text-gray-600">Update purchase order</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium mr-3">DELETE</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders/{id}</code>
                                <span class="ml-3 text-gray-600">Delete purchase order</span>
                            </div>
                        </div>
                    </div>

                    <!-- Purchase Order Actions -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">POST</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders/{id}/approve</code>
                                <span class="ml-3 text-gray-600">Approve purchase order</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">POST</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders/{id}/receive</code>
                                <span class="ml-3 text-gray-600">Receive purchase order items</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium mr-3">PUT</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/purchase-orders/{id}/status</code>
                                <span class="ml-3 text-gray-600">Update order status</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Logs -->
            <div>
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('inventory-logs')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Inventory Logs & Reports</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="inventory-logs-icon"></i>
                    </div>
                </button>
                <div id="inventory-logs" class="hidden px-6 pb-6">
                    <!-- Logs and Reports -->
                    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/logs</code>
                                <span class="ml-3 text-gray-600">Get all inventory logs</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/logs/movements-summary</code>
                                <span class="ml-3 text-gray-600">Get movements summary</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/logs/valuation-history</code>
                                <span class="ml-3 text-gray-600">Get inventory valuation history</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/logs/waste-report</code>
                                <span class="ml-3 text-gray-600">Get waste tracking report</span>
                            </div>
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">GET</span>
                                <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">/api/inventory/logs/export</code>
                                <span class="ml-3 text-gray-600">Export logs to CSV/Excel/PDF</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Architecture -->
        <div class="bg-white rounded-xl shadow-lg mb-8 border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-cogs text-purple-500 mr-2"></i>
                    Technical Architecture
                </h2>
                <p class="text-gray-600">Core components and infrastructure of the Inventory module</p>
            </div>

            <!-- Middleware -->
            <div class="border-b border-gray-100">
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('middleware')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Middleware</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="middleware-icon"></i>
                    </div>
                </button>
                <div id="middleware" class="hidden px-6 pb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-900 mb-3">InventoryAccessMiddleware</h4>
                        <p class="text-gray-600 mb-3">Comprehensive access control for inventory operations</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Authentication verification</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Module access permissions</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Branch-specific inventory access</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Supplier and purchase order access control</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Role-based permission checking</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Database Schema -->
            <div class="border-b border-gray-100">
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('database-schema')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Database Schema</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="database-schema-icon"></i>
                    </div>
                </button>
                <div id="database-schema" class="hidden px-6 pb-6">
                    <!-- Branch Inventory Table -->
                    <div class="mb-6">
                        <h4 class="font-semibold text-gray-900 mb-3">branch_inventory</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr><td class="px-4 py-2 text-sm">id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Primary key</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">branch_id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Branch reference</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">product_id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Product reference</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">current_stock</td><td class="px-4 py-2 text-sm">decimal(10,2)</td><td class="px-4 py-2 text-sm">Current stock quantity</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">available_stock</td><td class="px-4 py-2 text-sm">decimal(10,2)</td><td class="px-4 py-2 text-sm">Available for use</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">reserved_stock</td><td class="px-4 py-2 text-sm">decimal(10,2)</td><td class="px-4 py-2 text-sm">Reserved for orders</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">minimum_stock_level</td><td class="px-4 py-2 text-sm">decimal(10,2)</td><td class="px-4 py-2 text-sm">Minimum threshold</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">maximum_stock_level</td><td class="px-4 py-2 text-sm">decimal(10,2)</td><td class="px-4 py-2 text-sm">Maximum threshold</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">reorder_point</td><td class="px-4 py-2 text-sm">decimal(10,2)</td><td class="px-4 py-2 text-sm">Reorder trigger point</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">unit_cost</td><td class="px-4 py-2 text-sm">decimal(10,4)</td><td class="px-4 py-2 text-sm">Cost per unit</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">total_value</td><td class="px-4 py-2 text-sm">decimal(12,2)</td><td class="px-4 py-2 text-sm">Total inventory value</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Inventory Movements Table -->
                    <div class="mb-6">
                        <h4 class="font-semibold text-gray-900 mb-3">inventory_movements</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr><td class="px-4 py-2 text-sm">id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Primary key</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">branch_inventory_id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Inventory item reference</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">type</td><td class="px-4 py-2 text-sm">enum</td><td class="px-4 py-2 text-sm">Movement type (add, subtract, etc.)</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">quantity</td><td class="px-4 py-2 text-sm">decimal(10,6)</td><td class="px-4 py-2 text-sm">Quantity moved</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">unit_cost</td><td class="px-4 py-2 text-sm">decimal(10,4)</td><td class="px-4 py-2 text-sm">Cost per unit</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">reason</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Movement reason</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">reference_type</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Related entity type</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">reference_id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Related entity ID</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Suppliers Table -->
                    <div class="mb-6">
                        <h4 class="font-semibold text-gray-900 mb-3">suppliers</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr><td class="px-4 py-2 text-sm">id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Primary key</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">name</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Supplier name</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">email</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Contact email</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">phone</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Contact phone</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">address</td><td class="px-4 py-2 text-sm">text</td><td class="px-4 py-2 text-sm">Physical address</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">category</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Supplier category</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">payment_terms</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Payment terms</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">credit_limit</td><td class="px-4 py-2 text-sm">decimal(12,2)</td><td class="px-4 py-2 text-sm">Credit limit</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">rating</td><td class="px-4 py-2 text-sm">tinyint</td><td class="px-4 py-2 text-sm">Performance rating (1-5)</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Purchase Orders Table -->
                    <div class="mb-6">
                        <h4 class="font-semibold text-gray-900 mb-3">purchase_orders</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr><td class="px-4 py-2 text-sm">id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Primary key</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">po_number</td><td class="px-4 py-2 text-sm">varchar</td><td class="px-4 py-2 text-sm">Purchase order number</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">supplier_id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Supplier reference</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">branch_id</td><td class="px-4 py-2 text-sm">bigint</td><td class="px-4 py-2 text-sm">Branch reference</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">status</td><td class="px-4 py-2 text-sm">enum</td><td class="px-4 py-2 text-sm">Order status</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">total_amount</td><td class="px-4 py-2 text-sm">decimal(12,2)</td><td class="px-4 py-2 text-sm">Total order amount</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">expected_delivery_date</td><td class="px-4 py-2 text-sm">date</td><td class="px-4 py-2 text-sm">Expected delivery</td></tr>
                                    <tr><td class="px-4 py-2 text-sm">notes</td><td class="px-4 py-2 text-sm">text</td><td class="px-4 py-2 text-sm">Order notes</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Helper Classes -->
            <div class="border-b border-gray-100">
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('helper-classes')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Helper Classes</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="helper-classes-icon"></i>
                    </div>
                </button>
                <div id="helper-classes" class="hidden px-6 pb-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-900 mb-3">InventoryHelper</h4>
                        <p class="text-gray-600 mb-3">Utility functions for inventory calculations and operations</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>calculateTurnoverRate()</code> - Calculate inventory turnover</li>
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>calculateDaysOfSupply()</code> - Calculate remaining supply days</li>
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>calculateReorderQuantity()</code> - Generate reorder suggestions</li>
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>isLowStock()</code> - Check low stock status</li>
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>isExpiringSoon()</code> - Check expiry status</li>
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>calculateABCClassification()</code> - ABC analysis</li>
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>generateValuationReport()</code> - Inventory valuation</li>
                            <li><i class="fas fa-function text-blue-500 mr-2"></i><code>logAction()</code> - Log inventory actions</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Service Classes -->
            <div>
                <button class="w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none" onclick="toggleSection('service-classes')">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Service Classes</h3>
                        <i class="fas fa-chevron-down transform transition-transform" id="service-classes-icon"></i>
                    </div>
                </button>
                <div id="service-classes" class="hidden px-6 pb-6">
                    <!-- Inventory Service -->
                    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-900 mb-3">InventoryService</h4>
                        <p class="text-gray-600 mb-3">Core inventory management operations</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>CRUD operations for inventory items</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Stock level management and updates</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Low stock detection and alerts</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Movement tracking and logging</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Bulk operations and analytics</li>
                        </ul>
                    </div>

                    <!-- Supplier Service -->
                    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-900 mb-3">SupplierService</h4>
                        <p class="text-gray-600 mb-3">Supplier relationship management</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Supplier CRUD operations</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Performance metrics tracking</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Product catalog management</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Purchase order history</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Rating and evaluation system</li>
                        </ul>
                    </div>

                    <!-- Purchase Order Service -->
                    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-900 mb-3">PurchaseOrderService</h4>
                        <p class="text-gray-600 mb-3">Purchase order lifecycle management</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Order creation and management</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Approval workflow handling</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Receiving and fulfillment</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Status tracking and updates</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Financial calculations</li>
                        </ul>
                    </div>

                    <!-- Inventory Log Service -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-900 mb-3">InventoryLogService</h4>
                        <p class="text-gray-600 mb-3">Comprehensive logging and reporting</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Activity logging and audit trails</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Movement history tracking</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Valuation and waste reports</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Export functionality (CSV, Excel, PDF)</li>
                            <li><i class="fas fa-cog text-green-500 mr-2"></i>Discrepancy analysis</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
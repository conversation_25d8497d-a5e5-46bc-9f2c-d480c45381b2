<?php

namespace Modules\Inventory\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateInventoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $inventoryId = $this->route('inventory'); // Assuming the route parameter is 'inventory'
        
        return [
            'name' => 'sometimes|required|string|max:255',
            'sku' => [
                'sometimes',
                'required',
                'string',
                'max:100',
                Rule::unique('products', 'sku')->ignore($this->getProductId($inventoryId))
            ],
            'description' => 'nullable|string|max:1000',
            'category' => 'sometimes|required|string|max:100',
            'unit_id' => 'sometimes|required|exists:units,id',
            'minimum_stock' => 'sometimes|required|numeric|min:0',
            'maximum_stock' => 'nullable|numeric|min:0|gte:minimum_stock',
            'reorder_point' => 'nullable|numeric|min:0',
            'cost_per_unit' => 'sometimes|required|numeric|min:0',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required.',
            'category.required' => 'Product category is required.',
            'unit_id.required' => 'Unit is required.',
            'unit_id.exists' => 'Selected unit does not exist.',
            'minimum_stock.required' => 'Minimum stock level is required.',
            'maximum_stock.gte' => 'Maximum stock must be greater than or equal to minimum stock.',
            'cost_per_unit.required' => 'Cost per unit is required.',
            'sku.unique' => 'SKU already exists.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_active')) {
            $this->merge([
                'is_active' => $this->boolean('is_active'),
            ]);
        }

        if ($this->has('reorder_point') && !$this->input('reorder_point')) {
            $this->merge([
                'reorder_point' => $this->input('minimum_stock'),
            ]);
        }
    }

    /**
     * Get the product ID for the given inventory ID.
     */
    private function getProductId($inventoryId)
    {
        if (!$inventoryId) {
            return null;
        }

        $inventory = \App\Models\BranchInventory::find($inventoryId);
        return $inventory ? $inventory->product_id : null;
    }
}
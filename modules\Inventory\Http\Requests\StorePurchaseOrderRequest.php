<?php

namespace Modules\Inventory\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePurchaseOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'supplier_id' => [
                'required',
                'string',
                Rule::exists('suppliers', 'id')->where(function ($query) {
                    return $query->where('tenant_id', auth()->user()->tenant_id)
                                 ->where('is_active', true);
                })
            ],
            'expected_delivery_date' => 'nullable|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000',
            'shipping_address' => 'nullable|string|max:500',
            'billing_address' => 'nullable|string|max:500',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'shipping_cost' => 'nullable|numeric|min:0',
            'priority' => 'nullable|in:low,medium,high,urgent',
            
            // Purchase order items
            'items' => 'required|array|min:1',
            'items.*.product_id' => [
                'required',
                'string',
                Rule::exists('products', 'id')->where(function ($query) {
                    return $query->where('tenant_id', auth()->user()->tenant_id)
                                 ->where('is_active', true);
                })
            ],
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_cost' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'supplier_id.required' => 'Please select a supplier.',
            'supplier_id.exists' => 'The selected supplier is invalid or inactive.',
            'expected_delivery_date.after_or_equal' => 'Expected delivery date cannot be in the past.',
            'payment_terms.integer' => 'Payment terms must be a number of days.',
            'payment_terms.min' => 'Payment terms cannot be negative.',
            'payment_terms.max' => 'Payment terms cannot exceed 365 days.',
            'discount_percentage.numeric' => 'Discount percentage must be a valid number.',
            'discount_percentage.min' => 'Discount percentage cannot be negative.',
            'discount_percentage.max' => 'Discount percentage cannot exceed 100%.',
            'tax_percentage.numeric' => 'Tax percentage must be a valid number.',
            'tax_percentage.min' => 'Tax percentage cannot be negative.',
            'tax_percentage.max' => 'Tax percentage cannot exceed 100%.',
            'shipping_cost.numeric' => 'Shipping cost must be a valid number.',
            'shipping_cost.min' => 'Shipping cost cannot be negative.',
            'priority.in' => 'Priority must be one of: low, medium, high, urgent.',
            
            // Items validation messages
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.*.product_id.required' => 'Product is required for each item.',
            'items.*.product_id.exists' => 'One or more selected products are invalid or inactive.',
            'items.*.quantity.required' => 'Quantity is required for each item.',
            'items.*.quantity.numeric' => 'Quantity must be a valid number.',
            'items.*.quantity.min' => 'Quantity must be greater than 0.',
            'items.*.unit_cost.required' => 'Unit cost is required for each item.',
            'items.*.unit_cost.numeric' => 'Unit cost must be a valid number.',
            'items.*.unit_cost.min' => 'Unit cost cannot be negative.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'tenant_id' => auth()->user()->tenant_id,
            'branch_id' => auth()->user()->branch_id,
            'created_by' => auth()->id(),
            'status' => 'draft',
        ]);

        // Set default priority if not provided
        if (!$this->has('priority')) {
            $this->merge(['priority' => 'medium']);
        }

        // Clean up items data
        if ($this->has('items')) {
            $items = collect($this->input('items'))->map(function ($item) {
                return array_filter($item, function ($value) {
                    return $value !== null && $value !== '';
                });
            })->filter(function ($item) {
                return isset($item['product_id']) && isset($item['quantity']) && isset($item['unit_cost']);
            })->values()->toArray();

            $this->merge(['items' => $items]);
        }
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add additional data
        $validated['tenant_id'] = auth()->user()->tenant_id;
        $validated['branch_id'] = auth()->user()->branch_id;
        $validated['created_by'] = auth()->id();
        $validated['status'] = 'draft';
        
        return $validated;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check for duplicate products in items
            if ($this->has('items')) {
                $productIds = collect($this->input('items'))->pluck('product_id');
                $duplicates = $productIds->duplicates();
                
                if ($duplicates->isNotEmpty()) {
                    $validator->errors()->add('items', 'Duplicate products are not allowed in the same purchase order.');
                }
            }
        });
    }
}
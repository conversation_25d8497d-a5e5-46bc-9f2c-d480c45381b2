@extends('docs.layout')

@section('title', 'Restaurant Management Module Documentation')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Restaurant Management Module</h1>
            <p class="text-lg text-gray-600 mb-6">
                Comprehensive restaurant management system for handling branches, seating areas, and table management across multiple locations.
            </p>
            
            <!-- Module Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-900">Total Endpoints</h3>
                    <p class="text-2xl font-bold text-blue-600">26</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-900">Public Endpoints</h3>
                    <p class="text-2xl font-bold text-green-600">8</p>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-orange-900">Protected Endpoints</h3>
                    <p class="text-2xl font-bold text-orange-600">18</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-purple-900">Authentication</h3>
                    <p class="text-sm font-medium text-purple-600">Bearer Token</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="mb-8">
            <nav class="flex space-x-4">
                <a href="#branches" class="text-blue-600 hover:text-blue-800 font-medium">Branches</a>
                <a href="#areas" class="text-blue-600 hover:text-blue-800 font-medium">Areas</a>
                <a href="#tables" class="text-blue-600 hover:text-blue-800 font-medium">Tables</a>
                <a href="#reservations" class="text-blue-600 hover:text-blue-800 font-medium">Reservations</a>
                <a href="#database" class="text-blue-600 hover:text-blue-800 font-medium">Database Schema</a>
            </nav>
        </div>

        <!-- API Endpoints -->
        <div class="space-y-8">
            <!-- Branches Section -->
            <div id="branches" class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Branches Management</h2>
                <p class="text-gray-600 mb-6">Manage restaurant branches with full CRUD operations and branch-specific configurations.</p>

                <!-- Get Branches Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/restaurant/branches</h3>
                                <span class="ml-3 text-gray-600">Get all branches</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Downtown Branch",
      "address": "123 Main St",
      "phone": "+1234567890",
      "email": "<EMAIL>",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z"
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all restaurant branches with their details and status.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Branch Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/restaurant/branches</h3>
                                <span class="ml-3 text-gray-600">Create new branch</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Uptown Branch",
  "address": "456 Oak Ave",
  "phone": "+1987654321",
  "email": "<EMAIL>",
  "is_active": true
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Branch created successfully",
  "data": {
    "id": 2,
    "name": "Uptown Branch",
    "address": "456 Oak Ave",
    "phone": "+1987654321",
    "email": "<EMAIL>",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new restaurant branch. Name and address are required fields.</p>
                        </div>
                    </div>
                </div>

                <!-- Get Single Branch Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/restaurant/branches/{id}</h3>
                                <span class="ml-3 text-gray-600">Get single branch</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Branch ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Downtown Branch",
    "address": "123 Main St",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "is_active": true,
    "areas_count": 4,
    "tables_count": 15,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves a single branch by ID with full details including area and table counts.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Branch Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/restaurant/branches/{id}</h3>
                                <span class="ml-3 text-gray-600">Update branch</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Updated Downtown Branch",
  "phone": "+1111111111",
  "is_active": false
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Branch updated successfully",
  "data": {
    "id": 1,
    "name": "Updated Downtown Branch",
    "phone": "+1111111111",
    "is_active": false,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates an existing branch. Only provided fields will be updated.</p>
                        </div>
                    </div>
                </div>

                <!-- Delete Branch Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/restaurant/branches/{id}</h3>
                                <span class="ml-3 text-gray-600">Delete branch</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Branch ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Branch deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Permanently deletes a branch and all associated areas and tables. Cannot be undone.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Areas Section -->
            <div id="areas" class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Areas Management</h2>
                <p class="text-gray-600 mb-6">Manage seating areas within branches for better organization and table management.</p>

                <!-- Get Areas Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/areas</h3>
                                <span class="ml-3 text-gray-600">Get all areas</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branch_id: 1 (optional - filter by branch)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "branch_id": 1,
      "name": "Indoor Dining",
      "description": "Main indoor seating area",
      "tables_count": 8,
      "branch": {
        "id": 1,
        "name": "Downtown Branch"
      }
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all seating areas with their branch information and table counts.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Area Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/areas</h3>
                                <span class="ml-3 text-gray-600">Create new area</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "branch_id": 1,
  "name": "VIP Section",
  "description": "Premium dining area with enhanced privacy"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Area created successfully",
  "data": {
    "id": 2,
    "branch_id": 1,
    "name": "VIP Section",
    "description": "Premium dining area with enhanced privacy",
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new seating area within a branch. Branch ID and name are required.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Area Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/areas/{id}</h3>
                                <span class="ml-3 text-gray-600">Update area</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Premium VIP Section",
  "description": "Exclusive dining area with personalized service"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Area updated successfully",
  "data": {
    "id": 2,
    "name": "Premium VIP Section",
    "description": "Exclusive dining area with personalized service",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates an existing seating area. Only provided fields will be updated.</p>
                        </div>
                    </div>
                </div>

                <!-- Delete Area Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/areas/{id}</h3>
                                <span class="ml-3 text-gray-600">Delete area</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 2 (Area ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Area deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Permanently deletes a seating area and all associated tables. Cannot be undone.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tables Section -->
            <div id="tables" class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Tables Management</h2>
                <p class="text-gray-600 mb-6">Manage individual tables within seating areas, including capacity, QR codes, and availability status.</p>

                <!-- Get Tables Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/tables</h3>
                                <span class="ml-3 text-gray-600">Get all tables</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branch_id: 1 (optional - filter by branch)
area_id: 1 (optional - filter by area)
status: available (optional - filter by status)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "branch_id": 1,
      "area_id": 1,
      "table_number": "T001",
      "table_name": "Table 1",
      "seating_capacity": 4,
      "status": "available",
      "qr_code": "QR_T001_BRANCH1",
      "branch": {
        "id": 1,
        "name": "Downtown Branch"
      },
      "area": {
        "id": 1,
        "name": "Indoor Dining"
      }
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all tables with their branch and area information, including QR codes and status.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Table Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/tables</h3>
                                <span class="ml-3 text-gray-600">Create new table</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "branch_id": 1,
  "area_id": 1,
  "table_number": "T010",
  "table_name": "Corner Table",
  "seating_capacity": 6
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Table created successfully",
  "data": {
    "id": 10,
    "branch_id": 1,
    "area_id": 1,
    "table_number": "T010",
    "table_name": "Corner Table",
    "seating_capacity": 6,
    "status": "available",
    "qr_code": "QR_T010_BRANCH1",
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new table within a specific area. QR code is automatically generated.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Table Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/tables/{id}</h3>
                                <span class="ml-3 text-gray-600">Update table</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "table_name": "Premium Corner Table",
  "seating_capacity": 8,
  "status": "occupied"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Table updated successfully",
  "data": {
    "id": 10,
    "table_name": "Premium Corner Table",
    "seating_capacity": 8,
    "status": "occupied",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates an existing table. Status can be: available, occupied, reserved, maintenance.</p>
                        </div>
                    </div>
                </div>

                <!-- Delete Table Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/tables/{id}</h3>
                                <span class="ml-3 text-gray-600">Delete table</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 10 (Table ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Table deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Permanently deletes a table. Cannot be undone.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reservations Section -->
            <div id="reservations" class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Reservations Management</h2>
                <p class="text-gray-600 mb-6">Handle table reservations, availability checking, and reservation lifecycle management.</p>

                <!-- Get Reservations Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/reservations</h3>
                                <span class="ml-3 text-gray-600">Get all reservations</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>date: 2024-01-01 (optional - filter by date)
status: confirmed (optional - filter by status)
branch_id: 1 (optional - filter by branch)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "customer_name": "John Doe",
      "customer_phone": "+1234567890",
      "party_size": 4,
      "reservation_date": "2024-01-01",
      "reservation_time": "19:00:00",
      "status": "confirmed",
      "table": {
        "id": 1,
        "table_number": "T001",
        "area": {
          "name": "Indoor Dining"
        }
      }
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all reservations with filtering options and table information.</p>
                        </div>
                    </div>
                </div>

                <!-- Check Availability Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/check-availability</h3>
                                <span class="ml-3 text-gray-600">Check table availability</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "branch_id": 1,
  "date": "2024-01-01",
  "time": "19:00",
  "party_size": 4
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "available": true,
  "available_tables": [
    {
      "id": 1,
      "table_number": "T001",
      "seating_capacity": 4,
      "area": {
        "name": "Indoor Dining"
      }
    },
    {
      "id": 5,
      "table_number": "T005",
      "seating_capacity": 6,
      "area": {
        "name": "VIP Section"
      }
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Checks table availability for a specific date, time, and party size.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Reservation Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/reservations</h3>
                                <span class="ml-3 text-gray-600">Create new reservation</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "customer_name": "Jane Smith",
  "customer_phone": "+1987654321",
  "customer_email": "<EMAIL>",
  "party_size": 6,
  "reservation_date": "2024-01-01",
  "reservation_time": "20:00",
  "table_id": 5,
  "special_requests": "Birthday celebration"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Reservation created successfully",
  "data": {
    "id": 2,
    "customer_name": "Jane Smith",
    "customer_phone": "+1987654321",
    "party_size": 6,
    "reservation_date": "2024-01-01",
    "reservation_time": "20:00:00",
    "status": "confirmed",
    "confirmation_code": "RES-2024-001",
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new table reservation with automatic confirmation code generation.</p>
                        </div>
                    </div>
                </div>

                <!-- Today's Reservations Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/today-reservations</h3>
                                <span class="ml-3 text-gray-600">Get today's reservations</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branch_id: 1 (optional - filter by branch)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "date": "2024-01-01",
  "total_reservations": 12,
  "data": [
    {
      "id": 1,
      "customer_name": "John Doe",
      "party_size": 4,
      "reservation_time": "19:00:00",
      "status": "confirmed",
      "table": {
        "table_number": "T001"
      }
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all reservations for today with summary statistics.</p>
                        </div>
                    </div>
                </div>

                <!-- Delete Reservation Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/reservation/reservations/{id}</h3>
                                <span class="ml-3 text-gray-600">Delete reservation</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Reservation ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Reservation deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Deletes a reservation by ID. This action is irreversible.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Schema Section -->
            <div id="database" class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Database Schema</h2>
                <p class="text-gray-600 mb-6">Database tables and relationships for the Restaurant Management module.</p>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Branches Table -->
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">branches</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="font-medium">id</span>
                                <span class="text-gray-600">uuid (Primary Key)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">tenant_id</span>
                                <span class="text-gray-600">uuid (Foreign Key)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">name</span>
                                <span class="text-gray-600">string(255)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">address</span>
                                <span class="text-gray-600">text</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">phone</span>
                                <span class="text-gray-600">string(20)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">description</span>
                                <span class="text-gray-600">string(255)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Tables Table -->
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">tables</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="font-medium">id</span>
                                <span class="text-gray-600">bigint (Primary Key)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">branch_id</span>
                                <span class="text-gray-600">bigint (Foreign Key)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">area_id</span>
                                <span class="text-gray-600">bigint (Foreign Key)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">table_number</span>
                                <span class="text-gray-600">string(20)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">table_name</span>
                                <span class="text-gray-600">string(255)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">seating_capacity</span>
                                <span class="text-gray-600">integer</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">status</span>
                                <span class="text-gray-600">enum</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">qr_code</span>
                                <span class="text-gray-600">string(255)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Reservations Table -->
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">reservations</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="font-medium">id</span>
                                <span class="text-gray-600">bigint (Primary Key)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">table_id</span>
                                <span class="text-gray-600">bigint (Foreign Key)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">customer_name</span>
                                <span class="text-gray-600">string(255)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">customer_phone</span>
                                <span class="text-gray-600">string(20)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">party_size</span>
                                <span class="text-gray-600">integer</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">reservation_date</span>
                                <span class="text-gray-600">date</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">reservation_time</span>
                                <span class="text-gray-600">time</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">status</span>
                                <span class="text-gray-600">enum</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Helper Classes Section -->
                <div class="mt-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Helper Classes</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">BranchHelper</h4>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li>• getBranchAreas() - Get all areas for a branch</li>
                                <li>• getBranchTables() - Get all tables for a branch</li>
                                <li>• getBranchCapacity() - Calculate total seating capacity</li>
                                <li>• isActiveBranch() - Check if branch is active</li>
                            </ul>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">TableHelper</h4>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li>• generateQRCode() - Generate unique QR code</li>
                                <li>• checkAvailability() - Check table availability</li>
                                <li>• getTableStatus() - Get current table status</li>
                                <li>• updateTableStatus() - Update table status</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Service Classes Section -->
                <div class="mt-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Service Classes</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">ReservationService</h4>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li>• checkAvailability() - Check table availability</li>
                                <li>• createReservation() - Create new reservation</li>
                                <li>• cancelReservation() - Cancel existing reservation</li>
                                <li>• seatCustomers() - Mark reservation as seated</li>
                                <li>• completeReservation() - Complete reservation</li>
                            </ul>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">TableManagementService</h4>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li>• createDefaultTables() - Create default tables for new branch</li>
                                <li>• assignTableToArea() - Assign table to specific area</li>
                                <li>• updateTableLayout() - Update table layout configuration</li>
                                <li>• generateTableReports() - Generate table utilization reports</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection<
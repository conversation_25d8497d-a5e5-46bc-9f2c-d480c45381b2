# Kitchen Module

The Kitchen Module is a comprehensive solution for managing kitchen operations in a restaurant POS system. It handles kitchen stations, menu item assignments, KOT (Kitchen Order Ticket) processing, and real-time order preparation workflow.

## Features

### Kitchen Management
- Create and manage multiple kitchen stations per branch
- Support for different station types (grill, fryer, salad, beverage, dessert, etc.)
- Configure operating hours, equipment lists, and capacity settings
- Assign managers and staff to specific kitchen stations

### Menu Item Assignment
- Assign menu items to specific kitchen stations
- Set preparation times and priority levels for each assignment
- Ensure unique assignment (one menu item per kitchen per tenant)
- Manage special instructions and preparation notes

### KOT Processing
- Automatic KOT generation from orders
- Intelligent routing of order items to appropriate kitchens
- Real-time status tracking (pending → preparing → ready → completed)
- Priority management with visual indicators
- Time tracking for preparation efficiency

### Dashboard & Analytics
- Real-time kitchen dashboard with active orders
- Performance metrics and preparation time analysis
- Order queue management and status overview
- Kitchen efficiency tracking and reporting

## Installation

The Kitchen module is automatically registered through the `KitchenServiceProvider`. Ensure it's included in your `AppServiceProvider`:

```php
protected $moduleProviders = [
    // ... other providers
    \Modules\Kitchen\Providers\KitchenServiceProvider::class,
];
```

## Database Setup

Run the migrations to create the required tables:

```bash
php artisan migrate
```

Seed default kitchen data:

```bash
php artisan db:seed --class=KitchenModuleSeeder
```

## Configuration

The module configuration is located at `modules/Kitchen/Config/kitchen.php`. Key settings include:

- **Station Types**: Available kitchen station types
- **KOT Settings**: Status options, priorities, auto-creation settings
- **Performance Settings**: Cache TTL, pagination limits
- **Display Settings**: Color coding, refresh intervals

## API Endpoints

### Kitchen Management

```http
GET    /api/kitchens                           # List all kitchens
POST   /api/kitchens                           # Create new kitchen
GET    /api/kitchens/{id}                      # Get kitchen details
PUT    /api/kitchens/{id}                      # Update kitchen
DELETE /api/kitchens/{id}                      # Delete kitchen
PATCH  /api/kitchens/{id}/toggle-status        # Toggle active status
```

### Menu Item Assignment

```http
POST   /api/kitchens/{id}/assign-menu-item     # Assign menu item
DELETE /api/kitchens/{id}/remove-menu-item/{menuItemId}  # Remove assignment
GET    /api/kitchens/{id}/menu-items           # Get assigned items
```

### Dashboard & Monitoring

```http
GET    /api/kitchens/dashboard                 # Kitchen dashboard data
GET    /api/kitchens/active-kots               # Active KOTs
```

### KOT Operations

```http
GET    /api/kots                               # List KOTs with filters
GET    /api/kots/{id}                          # Get KOT details
POST   /api/kots                               # Create KOT from order
PATCH  /api/kots/{id}/status                   # Update KOT status
PATCH  /api/kot-items/{id}/status               # Update item status
GET    /api/kots/statistics                    # KOT statistics
```

## Usage Examples

### Creating a Kitchen

```php
use Modules\Kitchen\Services\KitchenService;

$kitchenService = app(KitchenService::class);

$kitchen = $kitchenService->createKitchen([
    'branch_id' => $branchId,
    'name' => 'Main Grill Station',
    'code' => 'GRILL-01',
    'station_type' => 'grill',
    'max_concurrent_orders' => 10,
    'average_prep_time_minutes' => 15,
    'manager_id' => $managerId,
    'equipment_list' => ['Gas Grill', 'Char Broiler'],
    'operating_hours' => [
        'monday' => ['start' => '06:00', 'end' => '23:00'],
        // ... other days
    ]
]);
```

### Assigning Menu Items

```php
$kitchenService->assignMenuItem($kitchenId, [
    'menu_item_id' => $menuItemId,
    'prep_time_minutes' => 12,
    'priority_level' => 2, // Normal priority
    'special_instructions' => 'Cook to medium-rare'
]);
```

### Creating KOT from Order

```php
$kot = $kitchenService->createKotFromOrder($orderId, [
    'priority' => 'high',
    'special_instructions' => 'Rush order for VIP customer'
]);
```

### Updating KOT Status

```php
$kitchenService->updateKotStatus($kotId, 'preparing', [
    'notes' => 'Started preparation at grill station'
]);
```

## Models

### Kitchen
Represents a kitchen station with configuration and capacity settings.

**Key Relationships:**
- `belongsTo(Tenant::class)`
- `belongsTo(Branch::class)`
- `belongsTo(User::class, 'manager_id')`
- `hasMany(KitchenMenuItem::class)`
- `hasMany(KotOrder::class)`

### KitchenMenuItem
Manages the assignment of menu items to specific kitchens.

**Key Relationships:**
- `belongsTo(Kitchen::class)`
- `belongsTo(MenuItem::class)`
- `belongsTo(Tenant::class)`

### KotOrder
Represents a Kitchen Order Ticket with status tracking.

**Key Relationships:**
- `belongsTo(Kitchen::class)`
- `belongsTo(Order::class)`
- `hasMany(KotOrderItem::class)`

### KotOrderItem
Individual items within a KOT order.

**Key Relationships:**
- `belongsTo(KotOrder::class)`
- `belongsTo(OrderItem::class)`
- `belongsTo(MenuItem::class)`

## Integration

### With Orders Module
The Kitchen module automatically integrates with the Orders module:
- KOTs are created when orders are placed
- Order items are routed to appropriate kitchens
- Order status updates reflect kitchen preparation progress

### With Menu Module
Menu items can be assigned to kitchens with specific preparation requirements:
- Each menu item can be assigned to one kitchen per tenant
- Preparation times and priorities are configurable
- Special instructions can be set per assignment

### With User Management
Kitchen staff and managers are linked through the User model:
- Kitchen managers can be assigned to stations
- Staff can be assigned to specific KOT orders
- Role-based access control for kitchen operations

## Performance Considerations

- **Caching**: Kitchen assignments and configurations are cached for performance
- **Indexing**: Database indexes on frequently queried fields (status, kitchen_id, etc.)
- **Pagination**: Large result sets are paginated to improve response times
- **Real-time Updates**: Consider implementing WebSocket connections for real-time dashboard updates

## Security

- All API endpoints require authentication (`auth:sanctum`)
- Tenant isolation ensures data separation (`tenant.check` middleware)
- Role-based permissions control access to kitchen operations
- Input validation through dedicated request classes

## Testing

Run the module tests:

```bash
php artisan test --filter=Kitchen
```

## Contributing

When contributing to the Kitchen module:

1. Follow the existing code structure and naming conventions
2. Add appropriate validation for new endpoints
3. Update documentation for any new features
4. Ensure proper error handling and logging
5. Add tests for new functionality

## Support

For issues or questions related to the Kitchen module, please refer to the main project documentation or contact the development team.
<?php

namespace Modules\Inventory\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreInventoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'sku' => 'nullable|string|max:100|unique:products,sku',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|string|max:100',
            'unit_id' => 'required|exists:units,id',
            'initial_stock' => 'nullable|numeric|min:0',
            'minimum_stock' => 'required|numeric|min:0',
            'maximum_stock' => 'nullable|numeric|min:0|gte:minimum_stock',
            'reorder_point' => 'nullable|numeric|min:0',
            'cost_per_unit' => 'required|numeric|min:0',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required.',
            'category.required' => 'Product category is required.',
            'unit_id.required' => 'Unit is required.',
            'unit_id.exists' => 'Selected unit does not exist.',
            'minimum_stock.required' => 'Minimum stock level is required.',
            'maximum_stock.gte' => 'Maximum stock must be greater than or equal to minimum stock.',
            'cost_per_unit.required' => 'Cost per unit is required.',
            'sku.unique' => 'SKU already exists.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_active' => $this->boolean('is_active', true),
            'initial_stock' => $this->input('initial_stock', 0),
            'reorder_point' => $this->input('reorder_point') ?? $this->input('minimum_stock'),
        ]);
    }
}
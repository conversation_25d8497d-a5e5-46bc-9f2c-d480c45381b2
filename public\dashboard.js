class Dashboard {
    constructor() {
        this.charts = {};
        this.init();
    }

    init() {
        this.initTheme();
        this.initSidebar();
        this.initDropdowns();
        this.initCharts();
        this.initNotifications();
        this.initSearch();
        this.loadDashboardData();
    }

    // Theme Management
    initTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);

        // Theme toggle button
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                this.setTheme(newTheme);
                this.saveThemePreference(newTheme);
            });
        }
    }

    setTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        localStorage.setItem('theme', theme);
        this.updateChartThemes();
    }

    saveThemePreference(theme) {
        fetch('/toggle-theme', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ theme })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Theme preference saved');
            }
        })
        .catch(error => console.error('Error saving theme:', error));
    }

    // Sidebar Management
    initSidebar() {
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const mobileOverlay = document.getElementById('mobile-overlay');

        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                if (window.innerWidth >= 768) {
                    // Desktop: collapse/expand
                    console.log('Desktop toggle clicked');
                    sidebar.classList.toggle('collapsed');
                    if (mainContent) {
                        mainContent.classList.toggle('sidebar-collapsed');
                    }
                    localStorage.setItem('sidebar-collapsed', sidebar.classList.contains('collapsed'));
                } else {
                    // Mobile: show/hide
                    console.log('Mobile toggle clicked');
                    sidebar.classList.toggle('mobile-open');
                    if (mobileOverlay) {
                        mobileOverlay.classList.toggle('hidden');
                    }
                }
            });
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && sidebar && sidebarToggle && 
                !sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                console.log('Clicked outside mobile sidebar');
                sidebar.classList.remove('mobile-open');
                if (mobileOverlay) {
                    mobileOverlay.classList.add('hidden');
                }
            }
        });
        
        // Close mobile menu when clicking overlay
        if (mobileOverlay) {
            mobileOverlay.addEventListener('click', () => {
                console.log('Mobile overlay clicked');
                sidebar.classList.remove('mobile-open');
                mobileOverlay.classList.add('hidden');
            });
        }

        // Restore sidebar state
        const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
        if (isCollapsed && sidebar && window.innerWidth >= 768) {
            sidebar.classList.add('collapsed');
            if (mainContent) {
                mainContent.classList.add('sidebar-collapsed');
            }
        }

        // Submenu toggles
        document.querySelectorAll('.submenu-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                const submenu = toggle.nextElementSibling;
                const icon = toggle.querySelector('.submenu-icon');
                
                if (submenu) {
                    submenu.classList.toggle('hidden');
                    if (icon) {
                        icon.style.transform = submenu.classList.contains('hidden') ? 'rotate(0deg)' : 'rotate(90deg)';
                    }
                }
            });
        });
    }

    // Dropdown Management
    initDropdowns() {
        document.querySelectorAll('[data-dropdown-toggle]').forEach(trigger => {
            const targetId = trigger.getAttribute('data-dropdown-toggle');
            const dropdown = document.getElementById(targetId);
            
            if (dropdown) {
                trigger.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDropdown(dropdown);
                });
            }
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', () => {
            document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        });
    }

    toggleDropdown(dropdown) {
        // Close other dropdowns
        document.querySelectorAll('.dropdown-menu').forEach(otherDropdown => {
            if (otherDropdown !== dropdown) {
                otherDropdown.classList.add('hidden');
            }
        });
        
        dropdown.classList.toggle('hidden');
    }

    // Chart Management
    initCharts() {
        this.initSalesChart();
        this.initCategoryChart();
    }

    initSalesChart() {
        const ctx = document.getElementById('salesChart');
        if (!ctx) return;

        const isDark = document.documentElement.classList.contains('dark');
        const textColor = isDark ? '#e5e7eb' : '#374151';
        const gridColor = isDark ? '#374151' : '#e5e7eb';

        this.charts.sales = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Sales',
                    data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: textColor
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: textColor
                        },
                        grid: {
                            color: gridColor
                        }
                    },
                    y: {
                        ticks: {
                            color: textColor
                        },
                        grid: {
                            color: gridColor
                        }
                    }
                }
            }
        });
    }

    initCategoryChart() {
        const ctx = document.getElementById('categoryChart');
        if (!ctx) return;

        const isDark = document.documentElement.classList.contains('dark');
        const textColor = isDark ? '#e5e7eb' : '#374151';

        this.charts.category = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Main Dishes', 'Appetizers', 'Desserts', 'Beverages', 'Specials'],
                datasets: [{
                    data: [35, 25, 15, 20, 5],
                    backgroundColor: [
                        '#ef4444',
                        '#f97316',
                        '#eab308',
                        '#22c55e',
                        '#3b82f6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: textColor,
                            padding: 20
                        }
                    }
                }
            }
        });
    }

    updateChartThemes() {
        const isDark = document.documentElement.classList.contains('dark');
        const textColor = isDark ? '#e5e7eb' : '#374151';
        const gridColor = isDark ? '#374151' : '#e5e7eb';

        Object.values(this.charts).forEach(chart => {
            if (chart.options.plugins && chart.options.plugins.legend) {
                chart.options.plugins.legend.labels.color = textColor;
            }
            if (chart.options.scales) {
                ['x', 'y'].forEach(axis => {
                    if (chart.options.scales[axis]) {
                        chart.options.scales[axis].ticks.color = textColor;
                        if (chart.options.scales[axis].grid) {
                            chart.options.scales[axis].grid.color = gridColor;
                        }
                    }
                });
            }
            chart.update();
        });
    }

    // Notification Management
    initNotifications() {
        document.querySelectorAll('.notification-item').forEach(item => {
            const markReadBtn = item.querySelector('.mark-read-btn');
            if (markReadBtn) {
                markReadBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.markNotificationRead(item.dataset.notificationId, item);
                });
            }
        });
    }

    markNotificationRead(notificationId, element) {
        fetch('/notifications/mark-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ notification_id: notificationId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                element.classList.add('opacity-50');
                element.querySelector('.mark-read-btn').style.display = 'none';
            }
        })
        .catch(error => console.error('Error marking notification as read:', error));
    }

    // Search Functionality
    initSearch() {
        const searchInput = document.getElementById('global-search');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });
        }
    }

    performSearch(query) {
        if (query.length < 2) return;
        
        // Implement search functionality here
        console.log('Searching for:', query);
    }



    // Load Dashboard Data
    loadDashboardData() {
        this.refreshStats();
        this.refreshChartData();
    }

    refreshStats() {
        fetch('/dashboard/stats')
            .then(response => response.json())
            .then(data => {
                this.updateStatsCards(data);
            })
            .catch(error => console.error('Error loading stats:', error));
    }

    refreshChartData() {
        // Refresh sales chart
        fetch('/dashboard/chart-data?type=sales')
            .then(response => response.json())
            .then(data => {
                if (this.charts.sales) {
                    this.charts.sales.data = data;
                    this.charts.sales.update();
                }
            })
            .catch(error => console.error('Error loading sales chart data:', error));

        // Refresh category chart
        fetch('/dashboard/chart-data?type=category')
            .then(response => response.json())
            .then(data => {
                if (this.charts.category) {
                    this.charts.category.data = data;
                    this.charts.category.update();
                }
            })
            .catch(error => console.error('Error loading category chart data:', error));
    }

    updateStatsCards(data) {
        Object.keys(data).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                element.textContent = data[key];
            }
        });
    }

    // Utility Methods
    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container');
        if (!alertContainer) return;

        const alert = document.createElement('div');
        alert.className = `alert alert-${type} mb-4`;
        alert.innerHTML = `
            <span>${message}</span>
            <button type="button" class="alert-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        alertContainer.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});

// Language switcher functionality
function switchLanguage(locale) {
    const currentUrl = window.location.pathname;
    const segments = currentUrl.split('/');
    
    // Remove current locale if present
    if (segments[1] === 'en' || segments[1] === 'ar') {
        segments.splice(1, 1);
    }
    
    // Add new locale
    segments.splice(1, 0, locale);
    
    const newUrl = segments.join('/');
    window.location.href = newUrl;
}

// Export for global access
window.switchLanguage = switchLanguage;
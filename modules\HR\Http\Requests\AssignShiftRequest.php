<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class AssignShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'shift_id' => 'required|integer|exists:shifts,id',
            'shift_type_id' => 'required|integer|exists:shift_types,id',
            'user_id' => 'required|integer|exists:users,id',
            'role' => 'required|string|max:100',
            'notes' => 'nullable|string|max:500',
            'requires_approval' => 'boolean',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'shift_id.required' => 'Shift is required.',
            'shift_id.exists' => 'Selected shift does not exist.',
            'shift_type_id.required' => 'Shift type is required.',
            'shift_type_id.exists' => 'Selected shift type does not exist.',
            'user_id.required' => 'Staff member is required.',
            'user_id.exists' => 'Selected staff member does not exist.',
            'role.required' => 'Role is required.',
            'role.max' => 'Role cannot exceed 100 characters.',
            'notes.max' => 'Notes cannot exceed 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'requires_approval' => $this->boolean('requires_approval'),
        ]);
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add tenant_id and branch_id from shift
        $validated['tenant_id'] = Auth::user()->tenant_id;
        $validated['status'] = $validated['requires_approval'] ? 'pending' : 'assigned';
        
        return $validated;
    }
}
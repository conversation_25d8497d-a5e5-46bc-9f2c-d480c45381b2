<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItem;

class MenuItemSeeder extends Seeder
{
    public function run()
    {
        if (class_exists(\Database\Factories\MenuItemFactory::class)) {
            MenuItem::factory()->count(10)->create();
        } else {
            MenuItem::create([
                'name' => 'Grilled Chicken',
                'code' => 'GRILLED_CHICKEN',
                'description' => 'Juicy grilled chicken with herbs.',
                'price' => 12.99,
                'menu_id' => 1,
                'category_id' => 1,
                'is_active' => true,
                'is_featured' => false,
                'preparation_time' => 20,
                'image' => 'grilled_chicken.jpg',
                'calories' => 350,
                'allergens' => 'None',
                'dietary_info' => 'Gluten-free, Dairy-free',
                'SKU' => 'GC001',
                'barcode' => '1234567890123',
                'stock_quantity' => 100,
                'min_stock_level' => 10,
                'supplier_id' => 1,
                'unit_id' => 1,
                'purchase_price' => 8.00,
                'production_cost' => 5.00,
                'profit_margin' => 0.40,
                'notes' => 'Popular dish.',
            ]);
        }
    }
}
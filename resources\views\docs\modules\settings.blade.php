@extends('docs.layout')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <div class="p-4 rounded-full bg-slate-100 mr-4">
                <i class="fas fa-cog text-slate-600 text-2xl"></i>
            </div>
            <div>
                <h1 class="text-4xl font-bold text-gray-900">Settings Module</h1>
                <p class="text-xl text-gray-600 mt-2">System configuration, preferences, and application settings management</p>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-blue-600">5</div>
                <div class="text-sm text-gray-600">Total Endpoints</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-green-600">0</div>
                <div class="text-sm text-gray-600">Public Routes</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-orange-600">5</div>
                <div class="text-sm text-gray-600">Protected Routes</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-purple-600">1</div>
                <div class="text-sm text-gray-600">Categories</div>
            </div>
        </div>
    </div>

    <!-- Search Bar -->
    <div class="mb-8">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="Search endpoints, methods, or descriptions..." 
                   class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
        </div>
    </div>

    <!-- Protected Routes -->
    <div class="space-y-8">
        <!-- Settings Management -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-orange-500 to-orange-600 px-6 py-4">
                <h2 class="text-2xl font-bold text-white flex items-center">
                    <i class="fas fa-cog mr-3"></i>
                    Settings Management
                </h2>
                <p class="text-orange-100 mt-1">Complete CRUD operations for system settings</p>
            </div>
            
            <div class="p-6 space-y-6">
                <!-- List Settings -->
                <div class="border-l-4 border-blue-500 pl-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">List Settings</h3>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">GET</span>
                    </div>
                    <p class="text-gray-600 mb-3">Retrieve all settings with optional filtering by tenant, branch, or category</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="font-medium text-gray-900 mb-2">Endpoint:</p>
                        <code class="text-sm bg-gray-200 px-2 py-1 rounded">GET /api/settings</code>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Query Parameters:</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><code>tenant_id</code> (optional) - Filter by tenant ID</li>
                            <li><code>branch_id</code> (optional) - Filter by branch ID</li>
                            <li><code>category</code> (optional) - Filter by setting category</li>
                        </ul>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Response Example:</p>
                        <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">[
  {
    "id": 1,
    "tenant_id": 1,
    "branch_id": null,
    "category": "general",
    "key": "app_name",
    "value": "Restaurant POS",
    "data_type": "string",
    "description": "Application name",
    "is_public": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
]</pre>
                    </div>
                </div>

                <!-- Get Setting -->
                <div class="border-l-4 border-green-500 pl-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">Get Setting</h3>
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">GET</span>
                    </div>
                    <p class="text-gray-600 mb-3">Retrieve a specific setting by key with optional scope filtering</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="font-medium text-gray-900 mb-2">Endpoint:</p>
                        <code class="text-sm bg-gray-200 px-2 py-1 rounded">GET /api/settings/{key}</code>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Path Parameters:</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><code>key</code> (required) - Setting key identifier</li>
                        </ul>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Query Parameters:</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><code>tenant_id</code> (optional) - Filter by tenant ID</li>
                            <li><code>branch_id</code> (optional) - Filter by branch ID</li>
                            <li><code>category</code> (optional) - Filter by setting category</li>
                        </ul>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Response Example:</p>
                        <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">{
  "id": 1,
  "tenant_id": 1,
  "branch_id": null,
  "category": "general",
  "key": "app_name",
  "value": "Restaurant POS",
  "data_type": "string",
  "description": "Application name",
  "is_public": true,
  "logo_url": null,
  "support_email": "<EMAIL>",
  "support_phone": "+1234567890",
  "address": "123 Main St, City",
  "website_url": "https://restaurant.com",
  "social_links": {
    "facebook": "https://facebook.com/restaurant",
    "twitter": "https://twitter.com/restaurant"
  },
  "created_at": "2024-01-01T00:00:00.000000Z",
  "updated_at": "2024-01-01T00:00:00.000000Z"
}</pre>
                    </div>
                </div>

                <!-- Create Setting -->
                <div class="border-l-4 border-yellow-500 pl-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">Create Setting</h3>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">POST</span>
                    </div>
                    <p class="text-gray-600 mb-3">Create a new setting or update existing one</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="font-medium text-gray-900 mb-2">Endpoint:</p>
                        <code class="text-sm bg-gray-200 px-2 py-1 rounded">POST /api/settings</code>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Request Body:</p>
                        <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">{
  "tenant_id": 1,
  "branch_id": null,
  "category": "general",
  "key": "app_name",
  "value": "My Restaurant",
  "data_type": "string",
  "description": "Application name",
  "is_public": true,
  "logo_url": "https://example.com/logo.png",
  "support_email": "<EMAIL>",
  "support_phone": "+1234567890",
  "address": "123 Main St, City",
  "website_url": "https://restaurant.com",
  "social_links": {
    "facebook": "https://facebook.com/restaurant",
    "twitter": "https://twitter.com/restaurant"
  }
}</pre>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Response Example:</p>
                        <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">{
  "id": 1,
  "tenant_id": 1,
  "branch_id": null,
  "category": "general",
  "key": "app_name",
  "value": "My Restaurant",
  "data_type": "string",
  "description": "Application name",
  "is_public": true,
  "created_at": "2024-01-01T00:00:00.000000Z",
  "updated_at": "2024-01-01T00:00:00.000000Z"
}</pre>
                    </div>
                </div>

                <!-- Update Setting -->
                <div class="border-l-4 border-purple-500 pl-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">Update Setting</h3>
                        <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">PUT</span>
                    </div>
                    <p class="text-gray-600 mb-3">Update an existing setting by key</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="font-medium text-gray-900 mb-2">Endpoint:</p>
                        <code class="text-sm bg-gray-200 px-2 py-1 rounded">PUT /api/settings/{key}</code>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Path Parameters:</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><code>key</code> (required) - Setting key identifier</li>
                        </ul>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Request Body:</p>
                        <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">{
  "tenant_id": 1,
  "branch_id": null,
  "category": "general",
  "value": "Updated Restaurant Name",
  "data_type": "string",
  "description": "Updated application name",
  "is_public": true
}</pre>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Response Example:</p>
                        <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">{
  "id": 1,
  "tenant_id": 1,
  "branch_id": null,
  "category": "general",
  "key": "app_name",
  "value": "Updated Restaurant Name",
  "data_type": "string",
  "description": "Updated application name",
  "is_public": true,
  "updated_at": "2024-01-01T12:00:00.000000Z"
}</pre>
                    </div>
                </div>

                <!-- Delete Setting -->
                <div class="border-l-4 border-red-500 pl-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">Delete Setting</h3>
                        <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">DELETE</span>
                    </div>
                    <p class="text-gray-600 mb-3">Delete a setting by key with optional scope filtering</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="font-medium text-gray-900 mb-2">Endpoint:</p>
                        <code class="text-sm bg-gray-200 px-2 py-1 rounded">DELETE /api/settings/{key}</code>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Path Parameters:</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><code>key</code> (required) - Setting key identifier</li>
                        </ul>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Query Parameters:</p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><code>tenant_id</code> (optional) - Filter by tenant ID</li>
                            <li><code>branch_id</code> (optional) - Filter by branch ID</li>
                            <li><code>category</code> (optional) - Filter by setting category</li>
                        </ul>
                        
                        <p class="font-medium text-gray-900 mt-4 mb-2">Response:</p>
                        <pre class="text-sm bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">HTTP 204 No Content</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Technical Architecture -->
    <div class="mt-12 space-y-8">
        <h2 class="text-3xl font-bold text-gray-900 border-b-2 border-gray-200 pb-4">Technical Architecture</h2>
        
        <!-- Database Schema -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-database mr-3"></i>
                    Database Schema
                </h3>
            </div>
            
            <div class="p-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-3">settings Table</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <p class="font-medium text-gray-700">Core Fields:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• id (Primary Key)</li>
                                <li>• tenant_id (Foreign Key)</li>
                                <li>• branch_id (Foreign Key)</li>
                                <li>• category (String)</li>
                                <li>• key (String)</li>
                                <li>• value (Text)</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-medium text-gray-700">Metadata Fields:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• data_type (String)</li>
                                <li>• description (Text)</li>
                                <li>• is_public (Boolean)</li>
                                <li>• created_at (Timestamp)</li>
                                <li>• updated_at (Timestamp)</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-medium text-gray-700">Extended Fields:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• logo_url (String)</li>
                                <li>• support_email (String)</li>
                                <li>• support_phone (String)</li>
                                <li>• address (String)</li>
                                <li>• website_url (String)</li>
                                <li>• social_links (JSON)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Classes -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-cogs mr-3"></i>
                    Service Classes
                </h3>
            </div>
            
            <div class="p-6 space-y-4">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-2">SettingService</h4>
                    <p class="text-gray-600 mb-3">Enhanced service for settings management with comprehensive validation and error handling</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="font-medium text-gray-700">Core Methods:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• getAll($filters) - Retrieve filtered settings (ordered)</li>
                                <li>• get($key, $tenantId, $branchId, $category) - Get specific setting</li>
                                <li>• set($data) - Create or update setting with validation</li>
                                <li>• delete($key, $tenantId, $branchId, $category) - Delete setting</li>
                            </ul>
                            <p class="font-medium text-gray-700 mt-3">Additional Methods:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• getByCategory($category, $tenantId, $branchId) - Category filter</li>
                                <li>• exists($key, $tenantId, $branchId, $category) - Check existence</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-medium text-gray-700">Enhanced Features:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• Comprehensive error handling</li>
                                <li>• Input validation with logging</li>
                                <li>• Multi-tenant support</li>
                                <li>• Data integrity protection</li>
                                <li>• Ordered results by category/key</li>
                                <li>• Null value filtering</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4 mt-4">
                    <h4 class="font-semibold text-gray-900 mb-2">SettingController</h4>
                    <p class="text-gray-600 mb-3">Enhanced controller with improved validation and structured responses</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="font-medium text-gray-700">Validation Features:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• Request validation for all endpoints</li>
                                <li>• Foreign key existence validation</li>
                                <li>• Data type and format validation</li>
                                <li>• Comprehensive error responses</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-medium text-gray-700">Response Structure:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• Structured JSON responses</li>
                                <li>• Success/error status indicators</li>
                                <li>• Detailed validation messages</li>
                                <li>• Consistent format across endpoints</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Features -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-cube mr-3"></i>
                    Model Features
                </h3>
            </div>
            
            <div class="p-6 space-y-4">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 mb-2">Setting Model</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="font-medium text-gray-700">Relationships:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• belongsTo(Tenant::class)</li>
                                <li>• belongsTo(Branch::class)</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-medium text-gray-700">Special Features:</p>
                            <ul class="text-gray-600 mt-1 space-y-1">
                                <li>• Automatic type casting based on data_type</li>
                                <li>• JSON handling for social_links</li>
                                <li>• Value accessor/mutator for type conversion</li>
                                <li>• Unique constraint on tenant/branch/category/key</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Search functionality
document.getElementById('searchInput').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const sections = document.querySelectorAll('.border-l-4');
    
    sections.forEach(section => {
        const text = section.textContent.toLowerCase();
        const parent = section.closest('.space-y-6, .space-y-4');
        if (text.includes(searchTerm)) {
            section.style.display = 'block';
            if (parent) parent.style.display = 'block';
        } else {
            section.style.display = 'none';
        }
    });
    
    // Hide empty containers
    document.querySelectorAll('.space-y-6, .space-y-4').forEach(container => {
        const visibleSections = container.querySelectorAll('.border-l-4[style="display: block"], .border-l-4:not([style*="display: none"])');
        if (visibleSections.length === 0 && searchTerm !== '') {
            container.closest('.bg-white').style.display = 'none';
        } else {
            container.closest('.bg-white').style.display = 'block';
        }
    });
});
</script>
@endsection
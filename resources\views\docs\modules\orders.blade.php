@extends('docs.layout')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- Module Header -->
        <div class="mb-8">
            <div class="flex items-center mb-6">
                <div class="p-4 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 shadow-lg mr-6">
                    <i class="fas fa-shopping-cart text-white text-3xl"></i>
                </div>
                <div>
                    <h1 class="text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">Orders Module</h1>
                    <p class="text-xl text-gray-600">Complete order management system for creating, updating, and tracking customer orders</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 mr-4">
                        <i class="fas fa-list text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Endpoints</p>
                        <p class="text-2xl font-bold text-gray-900">15</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 mr-4">
                        <i class="fas fa-unlock text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Public Routes</p>
                        <p class="text-2xl font-bold text-gray-900">2</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 mr-4">
                        <i class="fas fa-lock text-orange-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Protected Routes</p>
                        <p class="text-2xl font-bold text-gray-900">13</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 mr-4">
                        <i class="fas fa-shield-alt text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Middleware</p>
                        <p class="text-2xl font-bold text-gray-900">1</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="mb-8">
            <div class="relative max-w-md mx-auto">
                <input type="text" id="endpoint-search" placeholder="Search endpoints..." 
                       class="w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white border border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all">
                <div class="absolute inset-y-0 left-0 flex items-center pl-4">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- Public Routes Section -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-globe text-green-600 mr-3"></i>
                Public Routes
            </h2>
            <div class="grid gap-6">
                <!-- Get Order Statuses -->
                <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mr-3">GET</span>
                                <h3 class="text-xl font-semibold text-gray-900">Get Order Statuses</h3>
                            </div>
                            <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="get-order-statuses">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <p class="text-gray-600 mb-4">Retrieve all available order status options</p>
                        <div class="bg-gray-50 rounded-lg p-3 mb-4">
                            <code class="text-sm text-gray-800">/api/orders/statuses</code>
                        </div>
                        <div id="get-order-statuses" class="details-content hidden">
                            <div class="border-t pt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Response Example:</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "pending": "Pending",
  "confirmed": "Confirmed",
  "preparing": "Preparing",
  "ready": "Ready",
  "served": "Served",
  "completed": "Completed",
  "cancelled": "Cancelled"
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Order Types -->
                <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mr-3">GET</span>
                                <h3 class="text-xl font-semibold text-gray-900">Get Order Types</h3>
                            </div>
                            <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="get-order-types">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <p class="text-gray-600 mb-4">Retrieve all available order type options</p>
                        <div class="bg-gray-50 rounded-lg p-3 mb-4">
                            <code class="text-sm text-gray-800">/api/orders/types</code>
                        </div>
                        <div id="get-order-types" class="details-content hidden">
                            <div class="border-t pt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Response Example:</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "dine_in": "Dine In",
  "takeaway": "Takeaway",
  "delivery": "Delivery",
  "online": "Online Order"
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Protected Routes Section -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-shield-alt text-orange-600 mr-3"></i>
                Protected Routes
            </h2>

            <!-- CRUD Operations -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-database text-blue-600 mr-2"></i>
                    CRUD Operations
                </h3>
                <div class="grid gap-6">
                    <!-- List Orders -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mr-3">GET</span>
                                    <h4 class="text-xl font-semibold text-gray-900">List Orders</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="list-orders">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Retrieve paginated list of orders with filtering options</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders</code>
                            </div>
                            <div id="list-orders" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Query Parameters:</h5>
                                    <ul class="list-disc list-inside text-gray-600 mb-4 space-y-1">
                                        <li><code>status</code> - Filter by order status</li>
                                        <li><code>customer_id</code> - Filter by customer</li>
                                        <li><code>table_id</code> - Filter by table</li>
                                        <li><code>date_from</code> - Filter from date</li>
                                        <li><code>date_to</code> - Filter to date</li>
                                        <li><code>per_page</code> - Items per page (default: 15)</li>
                                    </ul>
                                    <h5 class="font-semibold text-gray-900 mb-2">Response Example:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "data": [
    {
      "id": 1,
      "order_number": "ORD20241201123456001",
      "customer_id": 1,
      "table_id": 5,
      "order_type": "dine_in",
      "status": "confirmed",
      "subtotal": 25.50,
      "tax_amount": 2.55,
      "total_amount": 28.05,
      "created_at": "2024-12-01T12:34:56Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "total": 50
  }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Create Order -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full mr-3">POST</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Create Order</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="create-order">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Create a new order with items and calculate totals</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders</code>
                            </div>
                            <div id="create-order" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Request Body:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "customer_id": 1,
  "table_id": 5,
  "order_type": "dine_in",
  "notes": "No spicy food",
  "items": [
    {
      "menu_item_id": 10,
      "quantity": 2,
      "unit_price": 12.50,
      "notes": "Medium rare",
      "variant_id": 3,
      "addons": [
        {
          "addon_id": 5,
          "quantity": 1,
          "unit_price": 2.00,
          "total_price": 2.00
        }
      ]
    }
  ]
}</code></pre>
                                    <h5 class="font-semibold text-gray-900 mb-2">Response Example:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "id": 1,
  "order_number": "ORD20241201123456001",
  "status": "pending",
  "total_amount": 28.05,
  "created_at": "2024-12-01T12:34:56Z"
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Show Order -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mr-3">GET</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Show Order</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="show-order">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Get detailed information about a specific order</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{id}</code>
                            </div>
                            <div id="show-order" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Response Example:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "id": 1,
  "order_number": "ORD20241201123456001",
  "customer": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "table": {
    "id": 5,
    "name": "Table 5",
    "area": "Main Hall"
  },
  "order_items": [
    {
      "id": 1,
      "menu_item": {
        "name": "Grilled Chicken",
        "price": 12.50
      },
      "quantity": 2,
      "total_price": 25.00
    }
  ],
  "subtotal": 25.00,
  "tax_amount": 2.50,
  "total_amount": 27.50
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Update Order -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full mr-3">PUT</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Update Order</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="update-order">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Update order details and items</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{id}</code>
                            </div>
                            <div id="update-order" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Request Body:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "status": "confirmed",
  "notes": "Updated notes",
  "items": [
    {
      "menu_item_id": 10,
      "quantity": 3,
      "unit_price": 12.50
    }
  ]
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Order -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-full mr-3">DELETE</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Delete Order</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="delete-order">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Delete an order and all associated items</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{id}</code>
                            </div>
                            <div id="delete-order" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <p class="text-gray-600">Returns 204 No Content on successful deletion</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Management -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-tasks text-purple-600 mr-2"></i>
                    Order Management
                </h3>
                <div class="grid gap-6">
                    <!-- Update Order Status -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full mr-3">PATCH</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Update Order Status</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="update-status">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Update the status of an order</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{id}/status</code>
                            </div>
                            <div id="update-status" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Request Body:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "status": "preparing"
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Get Order Items -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mr-3">GET</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Get Order Items</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="get-items">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Retrieve all items for a specific order</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{id}/items</code>
                            </div>
                            <div id="get-items" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Response Example:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>[
  {
    "id": 1,
    "menu_item_id": 10,
    "quantity": 2,
    "unit_price": 12.50,
    "total_price": 25.00,
    "status": "preparing",
    "menu_item": {
      "name": "Grilled Chicken",
      "description": "Tender grilled chicken breast"
    }
  }
]</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Order Item -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full mr-3">POST</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Add Order Item</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="add-item">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Add a new item to an existing order</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{id}/items</code>
                            </div>
                            <div id="add-item" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Request Body:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "menu_item_id": 15,
  "quantity": 1,
  "unit_price": 8.50,
  "notes": "Extra sauce"
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Remove Order Item -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-full mr-3">DELETE</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Remove Order Item</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="remove-item">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Remove an item from an order</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{id}/items/{itemId}</code>
                            </div>
                            <div id="remove-item" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <p class="text-gray-600">Returns 204 No Content on successful removal</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>

            <!-- Order Item Status Management -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-clipboard-check text-green-600 mr-2"></i>
                    Order Item Status Management
                </h3>
                <div class="grid gap-6">
                    <!-- Update Order Item Status -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full mr-3">PATCH</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Update Order Item Status</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="update-order-item-status">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Update the status of a specific item within an order</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{orderId}/items/{itemId}/status</code>
                            </div>
                            <div id="update-order-item-status" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Path Parameters:</h5>
                                    <ul class="list-disc list-inside text-gray-600 mb-4">
                                        <li><code>orderId</code> (integer): The ID of the order.</li>
                                        <li><code>itemId</code> (integer): The ID of the order item.</li>
                                    </ul>
                                    <h5 class="font-semibold text-gray-900 mb-2">Request Body:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "status": "preparing"
}</code></pre>
                                    <h5 class="font-semibold text-gray-900 mb-2">Response Example:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "id": 1,
  "order_id": 1,
  "menu_item_id": 10,
  "quantity": 2,
  "unit_price": 12.50,
  "total_price": 25.00,
  "status": "preparing",
  "menu_item": {
    "name": "Grilled Chicken"
  }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Item Addon Management -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-plus-circle text-orange-600 mr-2"></i>
                    Order Item Addon Management
                </h3>
                <div class="grid gap-6">
                    <!-- Attach Addons to Order Item -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full mr-3">POST</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Attach Addons to Order Item</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="attach-addons-order-item">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Attach one or more addons to a specific order item</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{orderId}/items/{itemId}/addons</code>
                            </div>
                            <div id="attach-addons-order-item" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Path Parameters:</h5>
                                    <ul class="list-disc list-inside text-gray-600 mb-4">
                                        <li><code>orderId</code> (integer): The ID of the order.</li>
                                        <li><code>itemId</code> (integer): The ID of the order item.</li>
                                    </ul>
                                    <h5 class="font-semibold text-gray-900 mb-2">Request Body:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "addons": [
    {
      "addon_id": 5,
      "quantity": 1,
      "unit_price": 2.00
    },
    {
      "addon_id": 6,
      "quantity": 2,
      "unit_price": 1.50
    }
  ]
}</code></pre>
                                    <h5 class="font-semibold text-gray-900 mb-2">Response Example:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "message": "Addons attached successfully",
  "order_item": {
    "id": 1,
    "menu_item_id": 10,
    "quantity": 2,
    "total_price": 28.00,
    "addons": [
      {
        "id": 1,
        "addon_id": 5,
        "quantity": 1,
        "unit_price": 2.00,
        "total_price": 2.00
      },
      {
        "id": 2,
        "addon_id": 6,
        "quantity": 2,
        "unit_price": 1.50,
        "total_price": 3.00
      }
    ]
  }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detach Addons from Order Item -->
                    <div class="endpoint-card bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-full mr-3">DELETE</span>
                                    <h4 class="text-xl font-semibold text-gray-900">Detach Addons from Order Item</h4>
                                </div>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="detach-addons-order-item">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Detach one or more addons from a specific order item</p>
                            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                <code class="text-sm text-gray-800">/api/orders/{orderId}/items/{itemId}/addons</code>
                            </div>
                            <div id="detach-addons-order-item" class="details-content hidden">
                                <div class="border-t pt-4">
                                    <h5 class="font-semibold text-gray-900 mb-2">Path Parameters:</h5>
                                    <ul class="list-disc list-inside text-gray-600 mb-4">
                                        <li><code>orderId</code> (integer): The ID of the order.</li>
                                        <li><code>itemId</code> (integer): The ID of the order item.</li>
                                    </ul>
                                    <h5 class="font-semibold text-gray-900 mb-2">Request Body:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "addon_ids": [5, 6]
}</code></pre>
                                    <h5 class="font-semibold text-gray-900 mb-2">Response Example:</h5>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "message": "Addons detached successfully",
  "order_item": {
    "id": 1,
    "menu_item_id": 10,
    "quantity": 2,
    "total_price": 25.00,
    "addons": []
  }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Architecture Section -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-cogs text-indigo-600 mr-3"></i>
                Technical Architecture
            </h2>

            <!-- Middleware -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-shield-alt text-blue-600 mr-2"></i>
                    Middleware
                </h3>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6">
                        <div class="mb-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">OrderAccessMiddleware</h4>
                            <p class="text-gray-600 mb-3">Controls access to order management endpoints based on user permissions</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-medium text-gray-900 mb-2">Features:</h5>
                                <ul class="list-disc list-inside text-gray-600 space-y-1">
                                    <li>Validates user authentication</li>
                                    <li>Checks for 'manage-orders' or 'view-orders' permissions</li>
                                    <li>Returns 401 for unauthenticated users</li>
                                    <li>Returns 403 for insufficient permissions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Schema -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-database text-green-600 mr-2"></i>
                    Database Schema
                </h3>
                <div class="space-y-6">
                    <!-- Orders Table -->
                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-semibold text-gray-900">orders</h4>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="orders-table">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Main orders table storing order information and metadata</p>
                            <div id="orders-table" class="details-content hidden">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Primary key</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">branch_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to branches</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">customer_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to customers (nullable)</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">table_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to tables (nullable)</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">order_number</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(50)</td><td class="px-6 py-4 text-sm text-gray-500">Unique order identifier</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">order_type</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">enum</td><td class="px-6 py-4 text-sm text-gray-500">dine_in, takeaway, delivery, online</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">status</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">enum</td><td class="px-6 py-4 text-sm text-gray-500">pending, confirmed, preparing, ready, served, completed, cancelled</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">subtotal</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Order subtotal amount</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">tax_amount</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Tax amount</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">discount_amount</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Discount amount</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">service_charge</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Service charge amount</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">delivery_fee</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Delivery fee amount</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">total_amount</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Final total amount</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">customer_info</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">json</td><td class="px-6 py-4 text-sm text-gray-500">Guest customer information</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">delivery_address</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td><td class="px-6 py-4 text-sm text-gray-500">Delivery address</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">delivery_coordinates</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">json</td><td class="px-6 py-4 text-sm text-gray-500">Latitude and longitude</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">special_instructions</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td><td class="px-6 py-4 text-sm text-gray-500">Order notes and instructions</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">server_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to users (server)</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">cashier_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to users (cashier)</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">delivery_man_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to users (delivery)</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">order_time</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td><td class="px-6 py-4 text-sm text-gray-500">Order creation time</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">estimated_ready_time</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td><td class="px-6 py-4 text-sm text-gray-500">Estimated completion time</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">actual_ready_time</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td><td class="px-6 py-4 text-sm text-gray-500">Actual completion time</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">served_time</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td><td class="px-6 py-4 text-sm text-gray-500">Time order was served</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items Table -->
                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-semibold text-gray-900">order_items</h4>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="order-items-table">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Individual items within orders</p>
                            <div id="order-items-table" class="details-content hidden">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Primary key</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">order_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to orders</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">menu_item_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to menu_items</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">variant_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to menu_item_variants</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">quantity</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">integer</td><td class="px-6 py-4 text-sm text-gray-500">Item quantity</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">unit_price</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(8,2)</td><td class="px-6 py-4 text-sm text-gray-500">Price per unit</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">total_price</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Total price for quantity</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">special_instructions</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td><td class="px-6 py-4 text-sm text-gray-500">Item-specific notes</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">status</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">enum</td><td class="px-6 py-4 text-sm text-gray-500">pending, preparing, ready, served, cancelled</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">sent_to_kitchen_at</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td><td class="px-6 py-4 text-sm text-gray-500">Kitchen notification time</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ready_at</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td><td class="px-6 py-4 text-sm text-gray-500">Item ready time</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">served_at</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td><td class="px-6 py-4 text-sm text-gray-500">Item served time</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Item Addons Table -->
                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-semibold text-gray-900">order_item_addons</h4>
                                <button class="toggle-btn text-gray-400 hover:text-gray-600 transition-colors" data-target="order-addons-table">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <p class="text-gray-600 mb-4">Addons and modifiers for order items</p>
                            <div id="order-addons-table" class="details-content hidden">
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Primary key</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">order_item_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to order_items</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">addon_id</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td><td class="px-6 py-4 text-sm text-gray-500">Foreign key to menu_item_addons</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">quantity</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">integer</td><td class="px-6 py-4 text-sm text-gray-500">Addon quantity</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">unit_price</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(8,2)</td><td class="px-6 py-4 text-sm text-gray-500">Addon unit price</td></tr>
                                            <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">total_price</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(10,2)</td><td class="px-6 py-4 text-sm text-gray-500">Total addon price</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Helper Classes -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-tools text-yellow-600 mr-2"></i>
                    Helper Classes
                </h3>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6">
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">OrderHelper</h4>
                            <p class="text-gray-600 mb-4">Utility functions for order management and calculations</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-medium text-gray-900 mb-2">Methods:</h5>
                                <ul class="list-disc list-inside text-gray-600 space-y-1">
                                    <li><code>generateOrderNumber()</code> - Generate unique order numbers</li>
                                    <li><code>calculateTax($subtotal, $taxRate)</code> - Calculate tax amount</li>
                                    <li><code>calculateDiscount($subtotal, $discountPercent)</code> - Calculate discount</li>
                                    <li><code>getOrderStatuses()</code> - Get available order statuses</li>
                                    <li><code>getOrderTypes()</code> - Get available order types</li>
                                    <li><code>formatOrderNumber($orderNumber)</code> - Format order number for display</li>
                                    <li><code>canBeCancelled($status)</code> - Check if order can be cancelled</li>
                                    <li><code>canBeModified($status)</code> - Check if order can be modified</li>
                                    <li><code>getNextStatuses($currentStatus)</code> - Get possible next statuses</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Classes -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-cog text-purple-600 mr-2"></i>
                    Service Classes
                </h3>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                    <div class="p-6">
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">OrderService</h4>
                            <p class="text-gray-600 mb-4">Core business logic for order management operations</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-medium text-gray-900 mb-2">Methods:</h5>
                                <ul class="list-disc list-inside text-gray-600 space-y-1">
                                    <li><code>getAllOrders($filters)</code> - Get paginated orders with filters</li>
                                    <li><code>getOrderById($id)</code> - Get order by ID with relationships</li>
                                    <li><code>createOrder($data)</code> - Create new order with items</li>
                                    <li><code>updateOrder($id, $data)</code> - Update existing order</li>
                                    <li><code>deleteOrder($id)</code> - Delete order and items</li>
                                    <li><code>updateOrderStatus($id, $status)</code> - Update order status</li>
                                    <li><code>addOrderItems($order, $items)</code> - Add items to order</li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 rounded-lg p-4 mt-4">
                                <h5 class="font-medium text-gray-900 mb-2">Features:</h5>
                                <ul class="list-disc list-inside text-gray-600 space-y-1">
                                    <li>Automatic order number generation</li>
                                    <li>Tax and total calculation</li>
                                    <li>Kitchen integration (KOT generation)</li>
                                    <li>Inventory deduction on completion</li>
                                    <li>Order item addon management</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
<?php

namespace Modules\Payroll\Helpers;

use Carbon\Carbon;
use App\Models\PayPeriod;
use App\Models\Payslip;
use App\Models\User;

class PayrollHelper
{
    /**
     * Calculate tax based on gross pay and tax brackets.
     */
    public static function calculateTax(float $grossPay, array $taxBrackets = null): float
    {
        if (!$taxBrackets) {
            $taxBrackets = self::getDefaultTaxBrackets();
        }

        $tax = 0;
        $remainingIncome = $grossPay;

        foreach ($taxBrackets as $bracket) {
            if ($remainingIncome <= 0) {
                break;
            }

            $taxableInThisBracket = min($remainingIncome, $bracket['max'] - $bracket['min']);
            $tax += $taxableInThisBracket * $bracket['rate'];
            $remainingIncome -= $taxableInThisBracket;
        }

        return round($tax, 2);
    }

    /**
     * Get default tax brackets (this should be configurable).
     */
    public static function getDefaultTaxBrackets(): array
    {
        return [
            ['min' => 0, 'max' => 10000, 'rate' => 0.10],
            ['min' => 10000, 'max' => 40000, 'rate' => 0.12],
            ['min' => 40000, 'max' => 85000, 'rate' => 0.22],
            ['min' => 85000, 'max' => 163000, 'rate' => 0.24],
            ['min' => 163000, 'max' => 207000, 'rate' => 0.32],
            ['min' => 207000, 'max' => 518000, 'rate' => 0.35],
            ['min' => 518000, 'max' => PHP_FLOAT_MAX, 'rate' => 0.37],
        ];
    }

    /**
     * Calculate social security contribution.
     */
    public static function calculateSocialSecurity(float $grossPay, float $rate = 0.062, float $maxWage = 147000): float
    {
        // Social Security is capped at a maximum wage base
        $taxableWage = min($grossPay, $maxWage);
        return round($taxableWage * $rate, 2);
    }

    /**
     * Calculate Medicare contribution.
     */
    public static function calculateMedicare(float $grossPay, float $rate = 0.0145): float
    {
        return round($grossPay * $rate, 2);
    }

    /**
     * Calculate overtime pay based on regular rate and overtime hours.
     */
    public static function calculateOvertimePay(float $regularRate, float $overtimeHours, float $multiplier = 1.5): float
    {
        return round($regularRate * $multiplier * $overtimeHours, 2);
    }

    /**
     * Calculate holiday pay based on regular rate and holiday hours.
     */
    public static function calculateHolidayPay(float $regularRate, float $holidayHours, float $multiplier = 2.0): float
    {
        return round($regularRate * $multiplier * $holidayHours, 2);
    }

    /**
     * Calculate prorated salary for a specific period.
     */
    public static function calculateProratedSalary(float $annualSalary, Carbon $startDate, Carbon $endDate): float
    {
        $daysInPeriod = $startDate->diffInDays($endDate) + 1;
        $daysInYear = $startDate->isLeapYear() ? 366 : 365;
        
        return round(($annualSalary / $daysInYear) * $daysInPeriod, 2);
    }

    /**
     * Generate pay period dates based on type and start date.
     */
    public static function generatePayPeriodDates(string $periodType, Carbon $startDate): array
    {
        $endDate = null;

        switch ($periodType) {
            case 'weekly':
                $endDate = $startDate->copy()->addDays(6);
                break;
            case 'bi_weekly':
                $endDate = $startDate->copy()->addDays(13);
                break;
            case 'monthly':
                $endDate = $startDate->copy()->endOfMonth();
                break;
            case 'quarterly':
                $endDate = $startDate->copy()->endOfQuarter();
                break;
            default:
                throw new \InvalidArgumentException("Invalid period type: {$periodType}");
        }

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];
    }

    /**
     * Format currency amount for display.
     */
    public static function formatCurrency(float $amount, string $currency = 'USD'): string
    {
        return number_format($amount, 2, '.', ',');
    }

    /**
     * Calculate year-to-date totals for an employee.
     */
    public static function calculateYearToDateTotals(int $userId, int $year): array
    {
        $payslips = Payslip::where('user_id', $userId)
            ->whereHas('payPeriod', function ($query) use ($year) {
                $query->whereYear('start_date', $year);
            })
            ->where('status', '!=', 'cancelled')
            ->get();

        return [
            'gross_pay' => $payslips->sum('gross_pay'),
            'net_pay' => $payslips->sum('net_pay'),
            'total_deductions' => $payslips->sum('total_deductions'),
            'tax_deduction' => $payslips->sum('tax_deduction'),
            'social_security' => $payslips->sum('social_security'),
            'health_insurance' => $payslips->sum('health_insurance'),
            'retirement_contribution' => $payslips->sum('retirement_contribution'),
            'regular_hours' => $payslips->sum('regular_hours'),
            'overtime_hours' => $payslips->sum('overtime_hours'),
            'holiday_hours' => $payslips->sum('holiday_hours'),
        ];
    }

    /**
     * Validate pay period dates don't overlap with existing periods.
     */
    public static function validatePayPeriodDates(int $tenantId, Carbon $startDate, Carbon $endDate, int $excludeId = null): bool
    {
        $query = PayPeriod::where('tenant_id', $tenantId)
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                      ->orWhereBetween('end_date', [$startDate, $endDate])
                      ->orWhere(function ($q) use ($startDate, $endDate) {
                          $q->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                      });
            });

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Calculate effective hourly rate from salary.
     */
    public static function calculateEffectiveHourlyRate(float $annualSalary, int $hoursPerWeek = 40): float
    {
        $weeksPerYear = 52;
        $totalHoursPerYear = $hoursPerWeek * $weeksPerYear;
        
        return round($annualSalary / $totalHoursPerYear, 2);
    }

    /**
     * Get pay frequency multiplier for salary calculations.
     */
    public static function getPayFrequencyMultiplier(string $periodType): float
    {
        switch ($periodType) {
            case 'weekly':
                return 52; // 52 weeks per year
            case 'bi_weekly':
                return 26; // 26 bi-weekly periods per year
            case 'monthly':
                return 12; // 12 months per year
            case 'quarterly':
                return 4; // 4 quarters per year
            default:
                return 12; // Default to monthly
        }
    }

    /**
     * Calculate net pay from gross pay and deductions.
     */
    public static function calculateNetPay(float $grossPay, array $deductions): float
    {
        $totalDeductions = array_sum($deductions);
        return round($grossPay - $totalDeductions, 2);
    }

    /**
     * Generate payslip number with a specific format.
     */
    public static function generatePayslipNumber(int $tenantId, int $payPeriodId, int $sequence = null): string
    {
        $payPeriod = PayPeriod::find($payPeriodId);
        if (!$payPeriod) {
            throw new \InvalidArgumentException("Pay period not found: {$payPeriodId}");
        }

        $year = $payPeriod->start_date->year;
        $month = $payPeriod->start_date->format('m');
        
        if ($sequence === null) {
            // Get the next sequence number
            $lastPayslip = Payslip::where('pay_period_id', $payPeriodId)
                ->orderBy('id', 'desc')
                ->first();
            
            $sequence = $lastPayslip ? (int)substr($lastPayslip->payslip_number, -4) + 1 : 1;
        }
        
        return sprintf('PS%s%s%04d', $year, $month, $sequence);
    }

    /**
     * Check if a date falls on a weekend.
     */
    public static function isWeekend(Carbon $date): bool
    {
        return $date->isWeekend();
    }

    /**
     * Check if a date is a public holiday.
     */
    public static function isPublicHoliday(Carbon $date, array $holidays = null): bool
    {
        if (!$holidays) {
            $holidays = self::getDefaultHolidays($date->year);
        }

        $dateString = $date->format('m-d');
        return in_array($dateString, $holidays);
    }

    /**
     * Get default holidays for a year.
     */
    public static function getDefaultHolidays(int $year): array
    {
        return [
            '01-01', // New Year's Day
            '07-04', // Independence Day
            '11-11', // Veterans Day
            '12-25', // Christmas Day
            // Add more holidays as needed
        ];
    }

    /**
     * Calculate business days between two dates.
     */
    public static function calculateBusinessDays(Carbon $startDate, Carbon $endDate): int
    {
        $businessDays = 0;
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            if (!self::isWeekend($currentDate) && !self::isPublicHoliday($currentDate)) {
                $businessDays++;
            }
            $currentDate->addDay();
        }

        return $businessDays;
    }

    /**
     * Validate payroll data integrity.
     */
    public static function validatePayrollData(array $payrollData): array
    {
        $errors = [];

        // Check for negative values
        $numericFields = ['regular_hours', 'overtime_hours', 'gross_pay', 'net_pay'];
        foreach ($numericFields as $field) {
            if (isset($payrollData[$field]) && $payrollData[$field] < 0) {
                $errors[] = "{$field} cannot be negative";
            }
        }

        // Check that net pay doesn't exceed gross pay
        if (isset($payrollData['net_pay']) && isset($payrollData['gross_pay'])) {
            if ($payrollData['net_pay'] > $payrollData['gross_pay']) {
                $errors[] = "Net pay cannot exceed gross pay";
            }
        }

        // Check reasonable hour limits
        $totalHours = ($payrollData['regular_hours'] ?? 0) + ($payrollData['overtime_hours'] ?? 0);
        if ($totalHours > 168) { // 24 hours * 7 days
            $errors[] = "Total hours cannot exceed 168 hours per week";
        }

        return $errors;
    }
}
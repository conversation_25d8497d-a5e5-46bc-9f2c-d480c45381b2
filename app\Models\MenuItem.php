<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MenuItem extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'menu_id',
        'category_id',
        'name',
        'code',
        'description',
        'short_description',
        'base_price',
        'cost_price',
        'image_urls',
        'prep_time_minutes',
        'calories',
        'nutritional_info',
        'allergens',
        'dietary_info',
        'recipe_id',
        'barcode',
        'sku',
        'is_active',
        'is_featured',
        'is_spicy',
        'spice_level',
        'sort_order',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'base_price' => 'decimal:2',
            'cost_price' => 'decimal:2',
            'image_urls' => 'array',
            'nutritional_info' => 'array',
            'allergens' => 'array',
            'dietary_info' => 'array',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_spicy' => 'boolean',
        ];
    }

    // Relationships
    public function menu()
    {
        return $this->belongsTo(Menu::class);
    }

    public function tenant()
    {
        return $this->menu->tenant();
    }

    public function branch()
    {
        return $this->menu->branch();
    }

    public function category()
    {
        return $this->belongsTo(MenuCategory::class);
    }

    public function recipe()
    {
        return $this->belongsTo(Recipe::class);
    }

    public function variants()
    {
        return $this->hasMany(MenuItemVariant::class);
    }

    public function addons()
    {
        return $this->hasMany(MenuItemAddon::class);
    }

    /**
     * Alias for addons relationship to match service usage.
     */
    public function modifiers()
    {
        return $this->addons();
    }

    public function branches()
    {
        return $this->hasMany(MenuItemBranch::class);
    }

    public function availability()
    {
        return $this->hasMany(MenuAvailability::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the kitchen menu item assignment for this menu item.
     */
    public function kitchenMenuItem()
    {
        return $this->hasOne(\Modules\Kitchen\Models\KitchenMenuItem::class);
    }

    /**
     * Get the kitchen assigned to this menu item.
     */
    public function assignedKitchen()
    {
        return $this->hasOneThrough(
            \Modules\Kitchen\Models\Kitchen::class,
            \Modules\Kitchen\Models\KitchenMenuItem::class,
            'menu_item_id', // Foreign key on kitchen_menu_items table
            'id', // Foreign key on kitchens table
            'id', // Local key on menu_items table
            'kitchen_id' // Local key on kitchen_menu_items table
        );
    }

    /**
     * Get KOT order items for this menu item.
     */
    public function kotOrderItems()
    {
        return $this->hasMany(\Modules\Kitchen\Models\KotOrderItem::class);
    }

    /**
     * Check if this menu item is assigned to a kitchen.
     */
    public function isAssignedToKitchen(): bool
    {
        return $this->kitchenMenuItem()->exists();
    }

    /**
     * Get the kitchen this menu item is assigned to for a specific tenant.
     */
    public function getAssignedKitchen($tenantId = null)
    {
        $query = $this->assignedKitchen();
        
        if ($tenantId) {
            $query->where('tenant_id', $tenantId);
        }
        
        return $query->first();
    }
}
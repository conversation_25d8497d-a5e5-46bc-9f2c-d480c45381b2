<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null'); // User who performed the action
            $table->string('action_type'); // e.g., 'purchase', 'sale', 'adjustment', 'transfer', 'waste'
            $table->decimal('quantity_change', 15, 3); // The amount by which inventory changed
            $table->decimal('new_quantity', 15, 3); // The quantity after the change
            $table->decimal('old_quantity', 15, 3); // The quantity before the change
            $table->string('reason')->nullable(); // Optional reason for the change
            $table->unsignedBigInteger('source_document_id')->nullable(); // ID of related document (e.g., purchase order, sale order)
            $table->timestamps();
            
            // Performance indexes
            $table->index(['tenant_id', 'branch_id', 'created_at']);
            $table->index(['product_id', 'action_type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['action_type', 'created_at']);
            $table->index(['source_document_id', 'action_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_logs');
    }
};

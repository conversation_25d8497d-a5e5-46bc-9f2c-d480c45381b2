<?php

namespace Modules\Payment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Implement your authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'transactionable_type' => 'required|string|max:255',
            'transactionable_id' => 'required|string|max:255',
            'type' => [
                'required',
                'string',
                Rule::in([
                    'sale',
                    'refund',
                    'adjustment',
                    'fee',
                    'discount',
                    'tax',
                    'tip',
                    'deposit',
                    'withdrawal',
                    'transfer',
                    'subscription',
                    'penalty',
                    'reward',
                    'cashback',
                    'commission'
                ])
            ],
            'amount' => 'required|numeric|min:0.01|max:999999.99',
            'currency' => 'sometimes|string|size:3|in:USD,EUR,GBP,JPY,CAD,AUD,CHF,CNY,INR,BRL',
            'status' => [
                'sometimes',
                'string',
                Rule::in(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded', 'partially_paid', 'partially_refunded'])
            ],
            'transaction_date' => 'sometimes|date',
            'description' => 'sometimes|string|max:500',
            'reference_number' => 'sometimes|string|max:100',
            'notes' => 'sometimes|string|max:1000',
            'metadata' => 'sometimes|array',
            
            // Additional context fields
            'branch_id' => 'sometimes|uuid|exists:branches,id',
            'customer_id' => 'sometimes|uuid|exists:customers,id',
            'order_id' => 'sometimes|uuid|exists:orders,id',
            'invoice_number' => 'sometimes|string|max:100',
            'po_number' => 'sometimes|string|max:100',
            
            // Tax and fee information
            'tax_amount' => 'sometimes|numeric|min:0|max:999999.99',
            'tax_rate' => 'sometimes|numeric|min:0|max:100',
            'fee_amount' => 'sometimes|numeric|min:0|max:999999.99',
            'discount_amount' => 'sometimes|numeric|min:0|max:999999.99',
            
            // Due date for pending transactions
            'due_date' => 'sometimes|date|after_or_equal:today',
            
            // Recurring transaction fields
            'is_recurring' => 'sometimes|boolean',
            'recurring_frequency' => 'sometimes|string|in:daily,weekly,monthly,quarterly,yearly',
            'recurring_end_date' => 'sometimes|date|after:today',
            
            // Authorization and approval
            'requires_approval' => 'sometimes|boolean',
            'approved_by' => 'sometimes|uuid|exists:users,id',
            'approval_notes' => 'sometimes|string|max:500'
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'transactionable_type.required' => 'Entity type is required.',
            'transactionable_id.required' => 'Entity ID is required.',
            'type.required' => 'Transaction type is required.',
            'type.in' => 'Invalid transaction type selected.',
            'amount.required' => 'Transaction amount is required.',
            'amount.min' => 'Transaction amount must be at least $0.01.',
            'amount.max' => 'Transaction amount cannot exceed $999,999.99.',
            'currency.size' => 'Currency code must be exactly 3 characters.',
            'currency.in' => 'Currency is not supported.',
            'status.in' => 'Invalid transaction status.',
            'transaction_date.date' => 'Transaction date must be a valid date.',
            'description.max' => 'Description cannot exceed 500 characters.',
            'reference_number.max' => 'Reference number cannot exceed 100 characters.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'order_id.exists' => 'Selected order does not exist.',
            'tax_amount.min' => 'Tax amount cannot be negative.',
            'tax_rate.between' => 'Tax rate must be between 0 and 100 percent.',
            'fee_amount.min' => 'Fee amount cannot be negative.',
            'discount_amount.min' => 'Discount amount cannot be negative.',
            'due_date.after_or_equal' => 'Due date cannot be in the past.',
            'recurring_frequency.in' => 'Invalid recurring frequency.',
            'recurring_end_date.after' => 'Recurring end date must be in the future.',
            'approved_by.exists' => 'Approver does not exist.'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate entity exists
            $this->validateEntityExists($validator);
            
            // Validate amount calculations
            $this->validateAmountCalculations($validator);
            
            // Validate recurring transaction fields
            $this->validateRecurringFields($validator);
            
            // Validate approval requirements
            $this->validateApprovalRequirements($validator);
            
            // Validate transaction type specific rules
            $this->validateTransactionTypeRules($validator);
        });
    }

    /**
     * Validate that the referenced entity exists
     */
    protected function validateEntityExists($validator): void
    {
        $entityType = $this->transactionable_type;
        $entityId = $this->transactionable_id;
        
        // Map entity types to their corresponding models
        $entityModels = [
            'App\\Models\\Order' => 'orders',
            'App\\Models\\Invoice' => 'invoices',
            'App\\Models\\Subscription' => 'subscriptions',
            'App\\Models\\Customer' => 'customers',
            'App\\Models\\Reservation' => 'reservations',
            'App\\Models\\PurchaseOrder' => 'purchase_orders'
        ];
        
        if (isset($entityModels[$entityType])) {
            $tableName = $entityModels[$entityType];
            $exists = \DB::table($tableName)->where('id', $entityId)->exists();
            
            if (!$exists) {
                $validator->errors()->add('transactionable_id', 'Referenced entity does not exist.');
            }
        }
    }

    /**
     * Validate amount calculations
     */
    protected function validateAmountCalculations($validator): void
    {
        $amount = $this->amount ?? 0;
        $taxAmount = $this->tax_amount ?? 0;
        $feeAmount = $this->fee_amount ?? 0;
        $discountAmount = $this->discount_amount ?? 0;
        
        // Validate that discount doesn't exceed the base amount
        if ($discountAmount > $amount) {
            $validator->errors()->add('discount_amount', 'Discount amount cannot exceed transaction amount.');
        }
        
        // Validate tax rate calculation if both tax_amount and tax_rate are provided
        if ($this->has('tax_amount') && $this->has('tax_rate')) {
            $calculatedTax = ($amount - $discountAmount) * ($this->tax_rate / 100);
            if (abs($calculatedTax - $taxAmount) > 0.01) {
                $validator->errors()->add('tax_amount', 'Tax amount does not match the calculated tax based on tax rate.');
            }
        }
        
        // Validate total amount makes sense
        $totalCalculated = $amount + $taxAmount + $feeAmount - $discountAmount;
        if ($totalCalculated < 0) {
            $validator->errors()->add('amount', 'Total calculated amount cannot be negative.');
        }
    }

    /**
     * Validate recurring transaction fields
     */
    protected function validateRecurringFields($validator): void
    {
        if ($this->is_recurring) {
            if (!$this->has('recurring_frequency')) {
                $validator->errors()->add('recurring_frequency', 'Recurring frequency is required for recurring transactions.');
            }
            
            if ($this->has('recurring_end_date') && $this->has('transaction_date')) {
                $transactionDate = \Carbon\Carbon::parse($this->transaction_date);
                $endDate = \Carbon\Carbon::parse($this->recurring_end_date);
                
                if ($endDate->lte($transactionDate)) {
                    $validator->errors()->add('recurring_end_date', 'Recurring end date must be after transaction date.');
                }
            }
        }
    }

    /**
     * Validate approval requirements
     */
    protected function validateApprovalRequirements($validator): void
    {
        if ($this->requires_approval && $this->has('approved_by')) {
            // Check if the approver has the necessary permissions
            $approver = \App\Models\User::find($this->approved_by);
            if ($approver && !$approver->can('approve_transactions')) {
                $validator->errors()->add('approved_by', 'Selected user does not have permission to approve transactions.');
            }
        }
        
        // Check if transaction amount requires approval
        $approvalThreshold = config('payment.approval_threshold', 1000);
        if ($this->amount >= $approvalThreshold && !$this->requires_approval) {
            $validator->errors()->add('requires_approval', 'Transactions above $' . number_format($approvalThreshold, 2) . ' require approval.');
        }
    }

    /**
     * Validate transaction type specific rules
     */
    protected function validateTransactionTypeRules($validator): void
    {
        switch ($this->type) {
            case 'refund':
                // Refunds should have negative amounts or reference original transaction
                if ($this->amount > 0 && !$this->has('reference_number')) {
                    $validator->errors()->add('reference_number', 'Refund transactions require a reference to the original transaction.');
                }
                break;
                
            case 'deposit':
                // Deposits should be positive amounts
                if ($this->amount < 0) {
                    $validator->errors()->add('amount', 'Deposit amounts must be positive.');
                }
                break;
                
            case 'withdrawal':
                // Withdrawals should be positive amounts (will be handled as negative in business logic)
                if ($this->amount < 0) {
                    $validator->errors()->add('amount', 'Withdrawal amounts must be positive.');
                }
                break;
                
            case 'subscription':
                // Subscription transactions should have recurring settings
                if (!$this->is_recurring) {
                    $validator->errors()->add('is_recurring', 'Subscription transactions must be recurring.');
                }
                break;
        }
    }

    /**
     * Get validated data with defaults
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();
        
        // Set default values
        $validated['currency'] = $validated['currency'] ?? config('app.default_currency', 'USD');
        $validated['status'] = $validated['status'] ?? 'pending';
        $validated['transaction_date'] = $validated['transaction_date'] ?? now();
        $validated['metadata'] = $validated['metadata'] ?? [];
        
        // Add system metadata
        $validated['metadata']['created_via'] = 'api';
        $validated['metadata']['request_ip'] = $this->ip();
        $validated['metadata']['user_agent'] = $this->userAgent();
        $validated['metadata']['timestamp'] = now()->toISOString();
        
        return $validated;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure amount is properly formatted
        if ($this->has('amount')) {
            $this->merge([
                'amount' => round((float) $this->amount, 2)
            ]);
        }
        
        // Format optional amount fields
        foreach (['tax_amount', 'fee_amount', 'discount_amount'] as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => round((float) $this->$field, 2)
                ]);
            }
        }
        
        // Uppercase currency code
        if ($this->has('currency')) {
            $this->merge([
                'currency' => strtoupper($this->currency)
            ]);
        }
        
        // Ensure boolean fields are properly cast
        foreach (['is_recurring', 'requires_approval'] as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->$field, FILTER_VALIDATE_BOOLEAN)
                ]);
            }
        }
    }
}
<?php

use Illuminate\Support\Facades\Route;
use Modules\Customer\Http\Controllers\CustomerController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('api/customers')->group(function () {
    // Customer CRUD operations
    Route::get('/', [CustomerController::class, 'index']);
    Route::post('/', [CustomerController::class, 'store']);
    Route::get('/{id}', [CustomerController::class, 'show']);
    Route::put('/{id}', [CustomerController::class, 'update']);
    Route::delete('/{id}', [CustomerController::class, 'destroy']);
    
    // Customer search and utilities
    Route::post('/find-by-contact', [CustomerController::class, 'findByContact']);
    Route::get('/stats/overview', [CustomerController::class, 'stats']);
    Route::get('/top-customers', [CustomerController::class, 'topCustomers']);
    
    // Customer status management
    Route::patch('/{id}/activate', [CustomerController::class, 'activate']);
    Route::patch('/{id}/deactivate', [CustomerController::class, 'deactivate']);
    Route::patch('/{id}/update-visit', [CustomerController::class, 'updateVisit']);
    
    // Loyalty points management
    Route::post('/{id}/loyalty/add-points', [CustomerController::class, 'addLoyaltyPoints']);
    Route::post('/{id}/loyalty/redeem-points', [CustomerController::class, 'redeemLoyaltyPoints']);
    Route::get('/{id}/loyalty/history', [CustomerController::class, 'loyaltyHistory']);
});

// Public routes (for customer portal or kiosk)
Route::prefix('public/customers')->group(function () {
    Route::post('/find-by-contact', [CustomerController::class, 'findByContact']);
});

<?php

namespace Modules\Reservation\Services;

use App\Models\Reservation;
use App\Models\Table;
use App\Models\ReservationStatus;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Collection;

class ReservationService
{
    /**
     * Create a new reservation with business logic
     */
    public function createReservation(array $data): Reservation
    {
        // Generate reservation number if not provided
        if (!isset($data['reservation_number'])) {
            $data['reservation_number'] = $this->generateReservationNumber();
        }

        // Set default status if not provided
        if (!isset($data['reservation_status_id'])) {
            $defaultStatus = ReservationStatus::where('name', 'confirmed')->first();
            $data['reservation_status_id'] = $defaultStatus?->id ?? 1;
        }

        // Set default duration if not provided
        if (!isset($data['duration_minutes'])) {
            $data['duration_minutes'] = 120; // 2 hours default
        }

        return Reservation::create($data);
    }

    /**
     * Update reservation with business logic
     */
    public function updateReservation(Reservation $reservation, array $data): Reservation
    {
        // Handle status transitions
        if (isset($data['reservation_status_id'])) {
            $this->handleStatusTransition($reservation, $data['reservation_status_id']);
        }

        $reservation->update($data);
        return $reservation->fresh();
    }

    /**
     * Cancel a reservation
     */
    public function cancelReservation(Reservation $reservation, string $reason = null): Reservation
    {
        $cancelledStatus = ReservationStatus::where('name', 'cancelled')->first();
        
        $reservation->update([
            'reservation_status_id' => $cancelledStatus?->id,
            'notes' => $reservation->notes . "\nCancelled: " . ($reason ?? 'No reason provided'),
            'completed_at' => now(),
        ]);

        return $reservation;
    }

    /**
     * Mark reservation as seated
     */
    public function seatReservation(Reservation $reservation, int $tableId = null): Reservation
    {
        $seatedStatus = ReservationStatus::where('name', 'seated')->first();
        
        $updateData = [
            'reservation_status_id' => $seatedStatus?->id,
            'seated_at' => now(),
        ];

        if ($tableId) {
            $updateData['table_id'] = $tableId;
        }

        $reservation->update($updateData);
        return $reservation;
    }

    /**
     * Complete a reservation
     */
    public function completeReservation(Reservation $reservation): Reservation
    {
        $completedStatus = ReservationStatus::where('name', 'completed')->first();
        
        $reservation->update([
            'reservation_status_id' => $completedStatus?->id,
            'completed_at' => now(),
        ]);

        return $reservation;
    }

    /**
     * Check table availability for given datetime and duration
     */
    public function checkTableAvailability(int $branchId, Carbon $datetime, int $durationMinutes, int $partySize, int $excludeReservationId = null): Collection
    {
        $endTime = $datetime->copy()->addMinutes($durationMinutes);
        
        // Get all tables in the branch that can accommodate the party size
        $availableTables = Table::where('branch_id', $branchId)
            ->where('seating_capacity', '>=', $partySize)
            ->where('is_active', true)
            ->whereDoesntHave('reservations', function ($query) use ($datetime, $endTime, $excludeReservationId) {
                $query->where(function ($q) use ($datetime, $endTime) {
                    // Check for overlapping reservations
                    $q->whereBetween('reservation_datetime', [$datetime, $endTime])
                      ->orWhere(function ($subQ) use ($datetime, $endTime) {
                          $subQ->where('reservation_datetime', '<=', $datetime)
                               ->whereRaw('DATE_ADD(reservation_datetime, INTERVAL duration_minutes MINUTE) > ?', [$datetime]);
                      });
                })
                ->whereNotIn('reservation_status_id', function ($statusQuery) {
                    $statusQuery->select('id')
                               ->from('reservation_statuses')
                               ->whereIn('name', ['cancelled', 'completed', 'no_show']);
                });
                
                if ($excludeReservationId) {
                    $query->where('id', '!=', $excludeReservationId);
                }
            })
            ->with(['area', 'branch'])
            ->get();

        return $availableTables;
    }

    /**
     * Get reservations for a specific date range
     */
    public function getReservationsByDateRange(int $branchId, Carbon $startDate, Carbon $endDate): Collection
    {
        return Reservation::where('branch_id', $branchId)
            ->whereBetween('reservation_datetime', [$startDate, $endDate])
            ->with(['table', 'area', 'reservationStatus', 'customer'])
            ->orderBy('reservation_datetime')
            ->get();
    }

    /**
     * Get today's reservations for a branch
     */
    public function getTodayReservations(int $branchId): Collection
    {
        $today = Carbon::today();
        $tomorrow = Carbon::tomorrow();
        
        return $this->getReservationsByDateRange($branchId, $today, $tomorrow);
    }

    /**
     * Generate unique reservation number
     */
    private function generateReservationNumber(): string
    {
        do {
            $number = 'RES-' . Carbon::now()->format('Ymd') . '-' . strtoupper(Str::random(4));
        } while (Reservation::where('reservation_number', $number)->exists());
        
        return $number;
    }

    /**
     * Handle status transitions with business logic
     */
    private function handleStatusTransition(Reservation $reservation, int $newStatusId): void
    {
        $newStatus = ReservationStatus::find($newStatusId);
        
        if (!$newStatus) {
            return;
        }

        // Auto-set timestamps based on status
        switch ($newStatus->name) {
            case 'seated':
                if (!$reservation->seated_at) {
                    $reservation->seated_at = now();
                }
                break;
            case 'completed':
            case 'cancelled':
            case 'no_show':
                if (!$reservation->completed_at) {
                    $reservation->completed_at = now();
                }
                break;
        }
    }
}
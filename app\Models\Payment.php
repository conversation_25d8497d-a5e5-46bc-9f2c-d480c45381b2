<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'payment_number',
        'payment_method_uuid',
        'amount',
        'change_amount',
        'status',
        'transaction_uuid',
        'reference_number',
        'gateway_response',
        'notes',
        'processed_by',
        'processed_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'change_amount' => 'decimal:2',
            'gateway_response' => 'array',
            'processed_at' => 'datetime',
        ];
    }

    // Relationships
    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_uuid', 'uuid');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'transaction_uuid');
    }

    public function processedBy()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }
}
<?php

namespace Modules\Inventory\Console\Commands;

use Illuminate\Console\Command;
use Modules\Inventory\Jobs\ProcessInventoryAlerts;
use App\Models\Branch;
use Illuminate\Support\Facades\Log;

class ProcessInventoryAlertsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'inventory:process-alerts 
                            {--branch= : Process alerts for specific branch ID}
                            {--types=* : Alert types to process (low_stock, out_of_stock, overstocked, expiring_soon, expired)}
                            {--sync : Run synchronously instead of queuing}';

    /**
     * The console command description.
     */
    protected $description = 'Process inventory alerts for low stock, out of stock, and expiring items';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting inventory alerts processing...');
        
        $branchId = $this->option('branch');
        $alertTypes = $this->option('types');
        $sync = $this->option('sync');
        
        // Validate branch if provided
        if ($branchId && !Branch::find($branchId)) {
            $this->error("Branch with ID {$branchId} not found.");
            return 1;
        }
        
        // Validate alert types
        $validTypes = ['low_stock', 'out_of_stock', 'overstocked', 'expiring_soon', 'expired'];
        if (!empty($alertTypes)) {
            $invalidTypes = array_diff($alertTypes, $validTypes);
            if (!empty($invalidTypes)) {
                $this->error('Invalid alert types: ' . implode(', ', $invalidTypes));
                $this->info('Valid types: ' . implode(', ', $validTypes));
                return 1;
            }
        }
        
        try {
            if ($sync) {
                // Run synchronously
                $this->info('Running alerts processing synchronously...');
                $job = new ProcessInventoryAlerts($branchId, $alertTypes);
                $job->handle();
                $this->info('Inventory alerts processing completed successfully.');
            } else {
                // Queue the job
                $this->info('Queueing inventory alerts processing job...');
                ProcessInventoryAlerts::dispatch($branchId, $alertTypes);
                $this->info('Inventory alerts job has been queued.');
            }
            
            // Show summary
            $this->showSummary($branchId, $alertTypes);
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Error processing inventory alerts: ' . $e->getMessage());
            Log::error('Inventory alerts command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }
    
    /**
     * Show processing summary.
     */
    protected function showSummary(?string $branchId, array $alertTypes): void
    {
        $this->newLine();
        $this->info('Processing Summary:');
        $this->line('- Branch: ' . ($branchId ? "ID {$branchId}" : 'All branches'));
        $this->line('- Alert Types: ' . (empty($alertTypes) ? 'All types' : implode(', ', $alertTypes)));
        $this->line('- Time: ' . now()->format('Y-m-d H:i:s'));
        
        if (!$this->option('sync')) {
            $this->newLine();
            $this->comment('Note: The job has been queued. Check your queue worker to ensure it processes.');
            $this->comment('You can monitor the job status in your application logs.');
        }
    }
}
<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CheckInRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => 'nullable|integer|exists:users,id',
            'branch_id' => 'required|integer|exists:branches,id',
            'check_in_time' => 'nullable|date_format:Y-m-d H:i:s',
            'location_latitude' => 'nullable|numeric|between:-90,90',
            'location_longitude' => 'nullable|numeric|between:-180,180',
            'notes' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.exists' => 'Selected staff member does not exist.',
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'check_in_time.date_format' => 'Check-in time must be in Y-m-d H:i:s format.',
            'location_latitude.numeric' => 'Latitude must be a number.',
            'location_latitude.between' => 'Latitude must be between -90 and 90.',
            'location_longitude.numeric' => 'Longitude must be a number.',
            'location_longitude.between' => 'Longitude must be between -180 and 180.',
            'notes.max' => 'Notes cannot exceed 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default check-in time to now if not provided
        if (!$this->has('check_in_time') || !$this->check_in_time) {
            $this->merge([
                'check_in_time' => now()->format('Y-m-d H:i:s'),
            ]);
        }

        // Set user_id to current user if not provided (for self check-in)
        if (!$this->has('user_id') || !$this->user_id) {
            $this->merge([
                'user_id' => Auth::id(),
            ]);
        }
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add tenant_id
        $validated['tenant_id'] = Auth::user()->tenant_id;
        $validated['date'] = now()->format('Y-m-d');
        
        return $validated;
    }
}
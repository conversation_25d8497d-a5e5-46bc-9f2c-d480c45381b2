<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Modules\Inventory\Http\Requests\StoreSupplierRequest;
use Modules\Inventory\Http\Requests\UpdateSupplierRequest;
use Modules\Inventory\Services\SupplierService;

class SupplierController extends Controller
{
    protected $supplierService;

    public function __construct(SupplierService $supplierService)
    {
        $this->supplierService = $supplierService;
    }

    /**
     * Display a listing of suppliers.
     */
    public function index(Request $request)
    {
        $suppliers = $this->supplierService->getAllSuppliers($request->all());
        return response()->json($suppliers);
    }

    /**
     * Store a newly created supplier.
     */
    public function store(StoreSupplierRequest $request)
    {
        $supplier = $this->supplierService->createSupplier($request->validated());
        return response()->json($supplier, 201);
    }

    /**
     * Display the specified supplier.
     */
    public function show(string $id)
    {
        $supplier = $this->supplierService->getSupplierById($id);
        return response()->json($supplier);
    }

    /**
     * Update the specified supplier.
     */
    public function update(UpdateSupplierRequest $request, string $id)
    {
        $supplier = $this->supplierService->updateSupplier($id, $request->validated());
        return response()->json($supplier);
    }

    /**
     * Remove the specified supplier.
     */
    public function destroy(string $id)
    {
        $this->supplierService->deleteSupplier($id);
        return response()->json(null, 204);
    }

    /**
     * Get supplier products
     */
    public function getProducts(string $id)
    {
        $products = $this->supplierService->getSupplierProducts($id);
        return response()->json($products);
    }

    /**
     * Get supplier purchase orders
     */
    public function getPurchaseOrders(string $id)
    {
        $orders = $this->supplierService->getSupplierPurchaseOrders($id);
        return response()->json($orders);
    }

    /**
     * Get supplier performance metrics
     */
    public function getPerformance(string $id)
    {
        $performance = $this->supplierService->getSupplierPerformance($id);
        return response()->json($performance);
    }

    /**
     * Get supplier products
     */
    public function getSupplierProducts(string $id)
    {
        $products = $this->supplierService->getSupplierProducts($id);
        return response()->json($products);
    }

    /**
     * Get supplier purchase orders
     */
    public function getSupplierPurchaseOrders(string $id)
    {
        $orders = $this->supplierService->getSupplierPurchaseOrders($id);
        return response()->json($orders);
    }

    /**
     * Add product to supplier
     */
    public function addProductToSupplier(Request $request, string $id)
    {
        $request->validate([
            'product_id' => 'required|integer|exists:products,id',
            'supplier_sku' => 'nullable|string|max:100',
            'cost_per_unit' => 'required|numeric|min:0',
            'minimum_order_quantity' => 'nullable|integer|min:1',
            'lead_time_days' => 'nullable|integer|min:0'
        ]);

        $result = $this->supplierService->addProductToSupplier($id, $request->validated());
        return response()->json($result, 201);
    }

    /**
     * Get supplier categories
     */
    public function getSupplierCategories()
    {
        $categories = [
            'food_ingredients' => 'Food Ingredients',
            'beverages' => 'Beverages',
            'packaging' => 'Packaging Materials',
            'cleaning_supplies' => 'Cleaning Supplies',
            'equipment' => 'Equipment & Tools',
            'maintenance' => 'Maintenance Services',
            'utilities' => 'Utilities',
            'general' => 'General Supplies'
        ];
        return response()->json($categories);
    }
}
<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreAddonRequest;
use Modules\Menu\Http\Requests\UpdateAddonRequest;

class AddonWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of addons for a menu item
     */
    public function index(string $menuItemId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        $addons = $this->menuService->getMenuItemAddons($menuItemId);
        return view('menu::addons.index', compact('menuItem', 'addons'));
    }

    /**
     * Show the form for creating a new addon
     */
    public function create(string $menuItemId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        return view('menu::addons.create', compact('menuItem'));
    }

    /**
     * Store a newly created addon
     */
    public function store(StoreAddonRequest $request): RedirectResponse
    {
        try {
            $addon = $this->menuService->createAddon($request->menu_item_id, $request->validated());
            return redirect()->route('menu.web.addons.index', $request->menu_item_id)
                ->with('success', 'Addon created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to create addon: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified addon
     */
    public function show(string $menuItemId, string $addonId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        $addon = $this->menuService->getAddonById($addonId);
        return view('menu::addons.show', compact('menuItem', 'addon'));
    }

    /**
     * Show the form for editing the specified addon
     */
    public function edit(string $menuItemId, string $addonId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        $addon = $this->menuService->getAddonById($addonId);
        return view('menu::addons.edit', compact('menuItem', 'addon'));
    }

    /**
     * Update the specified addon
     */
    public function update(UpdateAddonRequest $request, string $menuItemId, string $addonId): RedirectResponse
    {
        try {
            $addon = $this->menuService->updateAddon($addonId, $request->validated());
            return redirect()->route('menu.web.addons.index', $menuItemId)
                ->with('success', 'Addon updated successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to update addon: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified addon
     */
    public function destroy(string $menuItemId, string $addonId): RedirectResponse
    {
        try {
            $this->menuService->deleteAddon($addonId);
            return redirect()->route('menu.web.addons.index', $menuItemId)
                ->with('success', 'Addon deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete addon: ' . $e->getMessage());
        }
    }
}
<?php

namespace Modules\Orders\Helpers;

use Carbon\Carbon;

class OrderHelper
{
    /**
     * Generate a unique order number
     */
    public static function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $timestamp = Carbon::now()->format('YmdHis');
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }

    /**
     * Calculate tax amount
     */
    public static function calculateTax(float $subtotal, float $taxRate = 0.1): float
    {
        return round($subtotal * $taxRate, 2);
    }

    /**
     * Calculate discount amount
     */
    public static function calculateDiscount(float $subtotal, float $discountPercent): float
    {
        return round($subtotal * ($discountPercent / 100), 2);
    }

    /**
     * Get order status options
     */
    public static function getOrderStatuses(): array
    {
        return [
            'pending' => 'Pending',
            'confirmed' => 'Confirmed',
            'preparing' => 'Preparing',
            'ready' => 'Ready',
            'served' => 'Served',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
        ];
    }

    /**
     * Get order type options
     */
    public static function getOrderTypes(): array
    {
        return [
            'dine_in' => 'Dine In',
            'takeaway' => 'Takeaway',
            'delivery' => 'Delivery',
            'online' => 'Online Order',
        ];
    }

    /**
     * Format order number for display
     */
    public static function formatOrderNumber(string $orderNumber): string
    {
        return '#' . $orderNumber;
    }

    /**
     * Check if order can be cancelled
     */
    public static function canBeCancelled(string $status): bool
    {
        return in_array($status, ['pending', 'confirmed']);
    }

    /**
     * Check if order can be modified
     */
    public static function canBeModified(string $status): bool
    {
        return in_array($status, ['pending', 'confirmed']);
    }

    /**
     * Get next possible statuses for an order
     */
    public static function getNextStatuses(string $currentStatus): array
    {
        $statusFlow = [
            'pending' => ['confirmed', 'cancelled'],
            'confirmed' => ['preparing', 'cancelled'],
            'preparing' => ['ready'],
            'ready' => ['served'],
            'served' => ['completed'],
            'completed' => [],
            'cancelled' => [],
        ];

        return $statusFlow[$currentStatus] ?? [];
    }
}
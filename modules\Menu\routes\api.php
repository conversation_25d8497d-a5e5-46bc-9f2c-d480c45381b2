<?php

use Illuminate\Support\Facades\Route;
use Modules\Menu\Http\Controllers\MenuController;
use Modules\Menu\Http\Controllers\CategoryController;
use Modules\Menu\Http\Controllers\MenuItemController;
use Modules\Menu\Http\Middleware\MenuAccessMiddleware;

/*
|--------------------------------------------------------------------------
| API Routes for Menu Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Menu module.
|
*/

Route::prefix('api/menu')
    ->middleware(['auth:sanctum', MenuAccessMiddleware::class])
    ->group(function () {
        
        // ==================== MENU ROUTES ====================
        Route::prefix('menus')->group(function () {
            Route::get('/', [MenuController::class, 'getMenus'])->name('menu.menus.index');
            Route::post('/', [MenuController::class, 'storeMenu'])->name('menu.menus.store');
            Route::get('/{id}', [MenuController::class, 'showMenu'])->name('menu.menus.show');
            Route::put('/{id}', [MenuController::class, 'updateMenu'])->name('menu.menus.update');
            Route::delete('/{id}', [MenuController::class, 'destroyMenu'])->name('menu.menus.destroy');
            Route::get('/branch/{branchId}', [MenuController::class, 'getMenusForBranch'])->name('menu.menus.branch');
        });
        
        // ==================== CATEGORY ROUTES ====================
        Route::prefix('categories')->group(function () {
            Route::get('/', [CategoryController::class, 'index'])->name('menu.categories.index');
            Route::post('/', [CategoryController::class, 'store'])->name('menu.categories.store');
            Route::get('/{id}', [CategoryController::class, 'show'])->name('menu.categories.show');
            Route::put('/{id}', [CategoryController::class, 'update'])->name('menu.categories.update');
            Route::delete('/{id}', [CategoryController::class, 'destroy'])->name('menu.categories.destroy');
            Route::get('/{id}/with-items', [CategoryController::class, 'getCategoryWithItems'])->name('menu.categories.with-items');
            Route::get('/{categoryId}/items', [CategoryController::class, 'getItemsByCategory'])->name('menu.categories.items');
        });
        
        // ==================== MENU ITEM ROUTES ====================
        Route::prefix('items')->group(function () {
            // Basic CRUD operations
            Route::get('/', [MenuItemController::class, 'index'])->name('menu.items.index');
            Route::post('/', [MenuItemController::class, 'store'])->name('menu.items.store');
            Route::get('/{id}', [MenuItemController::class, 'show'])->name('menu.items.show');
            Route::put('/{id}', [MenuItemController::class, 'update'])->name('menu.items.update');
            Route::delete('/{id}', [MenuItemController::class, 'destroy'])->name('menu.items.destroy');
            
            // Menu item specific operations
            Route::patch('/{id}/toggle-availability', [MenuItemController::class, 'toggleAvailability'])->name('menu.items.toggle-availability');
            Route::patch('/bulk-status', [MenuItemController::class, 'bulkUpdateStatus'])->name('menu.items.bulk-status');
            Route::get('/featured', [MenuItemController::class, 'getFeaturedItems'])->name('menu.items.featured');
            Route::get('/search', [MenuItemController::class, 'searchMenuItems'])->name('menu.items.search');
            Route::get('/statistics', [MenuItemController::class, 'getMenuStatistics'])->name('menu.items.statistics');

            // Variant operations (moved to VariantController)
            Route::get('/{menuItemId}/variants', [\Modules\Menu\Http\Controllers\VariantController::class, 'index'])->name('menu.variants.index');
            Route::post('/variants', [\Modules\Menu\Http\Controllers\VariantController::class, 'store'])->name('menu.variants.store');
            Route::put('/variants/{variantId}', [\Modules\Menu\Http\Controllers\VariantController::class, 'update'])->name('menu.variants.update');
            Route::delete('/variants/{variantId}', [\Modules\Menu\Http\Controllers\VariantController::class, 'destroy'])->name('menu.variants.destroy');

            // Addon operations (moved to AddonController)
            Route::get('/{menuItemId}/addons', [\Modules\Menu\Http\Controllers\AddonController::class, 'index'])->name('menu.addons.index');
            Route::post('/addons', [\Modules\Menu\Http\Controllers\AddonController::class, 'store'])->name('menu.addons.store');
            Route::put('/addons/{addonId}', [\Modules\Menu\Http\Controllers\AddonController::class, 'update'])->name('menu.addons.update');
            Route::delete('/addons/{addonId}', [\Modules\Menu\Http\Controllers\AddonController::class, 'destroy'])->name('menu.addons.destroy');

            // Availability operations
            Route::post('/availability', [MenuItemController::class, 'setAvailability'])->name('menu.availability.set');
            Route::post('/{menuItemId}/check-availability', [MenuItemController::class, 'checkAvailability'])->name('menu.availability.check');
            Route::get('/available', [MenuItemController::class, 'getAvailableMenuItems'])->name('menu.items.available');
            Route::get('/{menuItemId}/forecast', [MenuItemController::class, 'forecastWeeklyAvailability'])->name('menu.items.forecast');
            Route::post('/branch-availability', [MenuItemController::class, 'setBranchAvailability'])->name('menu.items.branch-availability');
            Route::post('/bulk-availability', [MenuItemController::class, 'bulkUpdateAvailability'])->name('menu.items.bulk-availability');
            Route::get('/{menuItemId}/availability-conflicts', [MenuItemController::class, 'checkAvailabilityConflicts'])->name('menu.items.availability.conflicts');
            Route::get('/availability/statistics', [MenuItemController::class, 'getAvailabilityStatistics'])->name('menu.availability.statistics');

            // Branch-specific operations
            Route::post('/branch-settings', [MenuItemController::class, 'setBranchSettings'])->name('menu.branch.settings');
            Route::get('/{menuItemId}/branches/{branchId}/price', [MenuItemController::class, 'getMenuItemPriceForBranch'])->name('menu.branch.price');
        });
        
        // ==================== BRANCH ROUTES ====================
        Route::prefix('branches')->group(function () {
            Route::get('/{branchId}/menu', [MenuItemController::class, 'getMenuForBranch'])->name('menu.branch.menu');
            Route::get('/{branchId}/low-stock', [MenuItemController::class, 'getLowStockItems'])->name('menu.branch.low-stock');
        });
    });

// ==================== PUBLIC ROUTES (No Authentication Required) ====================
Route::prefix('api/menu/public')
    ->group(function () {
        
        // Public categories
        Route::prefix('categories')->group(function () {
            Route::get('/', [CategoryController::class, 'index'])->name('menu.public.categories.index');
            Route::get('/{categoryId}/items', [CategoryController::class, 'getItemsByCategory'])->name('menu.public.categories.items');
        });
        
        // Public menu items
        Route::prefix('items')->group(function () {
            Route::get('/', [MenuItemController::class, 'index'])->name('menu.public.items.index');
            Route::get('/featured', [MenuItemController::class, 'getFeaturedItems'])->name('menu.public.items.featured');
            Route::get('/search', [MenuItemController::class, 'searchMenuItems'])->name('menu.public.items.search');
            Route::get('/available', [MenuItemController::class, 'getAvailableMenuItems'])->name('menu.public.items.available');
            Route::get('/{menuItemId}/forecast', [MenuItemController::class, 'forecastWeeklyAvailability'])->name('menu.public.items.forecast');
            Route::get('/{menuItemId}/variants', [MenuItemController::class, 'getMenuItemVariants'])->name('menu.public.variants');
            Route::get('/{menuItemId}/addons', [MenuItemController::class, 'getMenuItemAddons'])->name('menu.public.addons');
            Route::post('/{menuItemId}/check-availability', [MenuItemController::class, 'checkAvailability'])->name('menu.public.availability');
            Route::get('/{menuItemId}/branches/{branchId}/price', [MenuItemController::class, 'getMenuItemPriceForBranch'])->name('menu.public.branch.price');
        });
        
        // Public branch menu
        Route::prefix('branches')->group(function () {
            Route::get('/{branchId}/menu', [MenuItemController::class, 'getMenuForBranch'])->name('menu.public.branch.menu');
        });
        
        // Dietary and allergen information
        Route::get('/dietary-options', function () {
            return response()->json(\Modules\Menu\Helpers\MenuHelper::getDietaryOptions());
        })->name('menu.dietary-options');
        
        Route::get('/allergens', function () {
            return response()->json(\Modules\Menu\Helpers\MenuHelper::getAllergens());
        })->name('menu.allergens');
    });
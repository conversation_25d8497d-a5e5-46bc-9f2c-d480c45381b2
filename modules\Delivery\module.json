{"name": "Delivery", "alias": "delivery", "description": "Comprehensive delivery management module for restaurant POS system with personnel tracking, zone management, real-time GPS tracking, reviews, and analytics.", "keywords": ["delivery", "logistics", "tracking", "gps", "restaurant", "pos", "food delivery", "personnel management", "zone management", "real-time tracking"], "priority": 5, "providers": ["Modules\\Delivery\\Providers\\DeliveryServiceProvider"], "aliases": {"DeliveryService": "Modules\\Delivery\\Services\\DeliveryService", "DeliveryHelper": "Modules\\Delivery\\Helpers\\DeliveryHelper"}, "files": ["start.php"], "requires": {"php": "^8.1", "laravel/framework": "^10.0", "spatie/laravel-permission": "^5.0"}, "suggests": {"pusher/pusher-php-server": "For real-time notifications", "guzzlehttp/guzzle": "For external API integrations", "intervention/image": "For image processing (delivery proof photos)", "league/csv": "For data export functionality"}, "version": "1.0.0", "license": "MIT", "author": {"name": "EPSIS Development Team", "email": "<EMAIL>"}, "support": {"email": "<EMAIL>", "docs": "https://docs.epsis.com/modules/delivery", "issues": "https://github.com/epsis/delivery-module/issues"}, "autoload": {"psr-4": {"Modules\\Delivery\\": ""}}, "extra": {"laravel": {"providers": ["Modules\\Delivery\\Providers\\DeliveryServiceProvider"]}, "module": {"config": {"publish": true, "merge": false}, "migrations": {"publish": true, "path": "Database/Migrations"}, "seeders": {"publish": true, "path": "Database/Seeders"}, "factories": {"publish": true, "path": "Database/Factories"}, "views": {"publish": true, "path": "Resources/views"}, "translations": {"publish": true, "path": "Resources/lang"}, "assets": {"publish": true, "path": "Resources/assets"}}}, "config": {"delivery": {"path": "config/config.php", "merge": false}}, "routes": {"api": {"path": "routes/api.php", "middleware": ["api"], "prefix": "api/delivery"}, "web": {"path": "routes/web.php", "middleware": ["web"], "prefix": "delivery"}}, "migrations": ["2024_01_01_000001_create_delivery_personnel_table", "2024_01_01_000002_create_delivery_zones_table", "2024_01_01_000003_create_delivery_assignments_table", "2024_01_01_000004_create_delivery_tracking_table", "2024_01_01_000005_create_delivery_reviews_table", "2024_01_01_000006_create_delivery_tips_table"], "seeders": ["DeliveryModuleSeeder"], "factories": ["DeliveryPersonnelFactory", "DeliveryZoneFactory"], "models": ["DeliveryPersonnel", "DeliveryZone", "DeliveryAssignment", "DeliveryTracking", "DeliveryReview", "DeliveryTip"], "controllers": ["DeliveryController"], "services": ["DeliveryService"], "helpers": ["DeliveryHelper"], "requests": ["CreateDeliveryPersonnelRequest", "UpdateDeliveryPersonnelRequest", "CreateDeliveryZoneRequest", "UpdateDeliveryZoneRequest", "CreateDeliveryReviewRequest", "CreateDeliveryTipRequest", "UpdateDeliveryStatusRequest"], "tests": ["DeliveryModuleTest"], "permissions": ["manage_delivery_personnel", "view_delivery_personnel", "create_delivery_personnel", "update_delivery_personnel", "delete_delivery_personnel", "manage_delivery_zones", "view_delivery_zones", "create_delivery_zones", "update_delivery_zones", "delete_delivery_zones", "manage_deliveries", "view_deliveries", "assign_deliveries", "update_delivery_status", "track_deliveries", "view_delivery_analytics", "manage_delivery_reviews", "verify_delivery_reviews", "manage_delivery_tips", "process_delivery_payments"], "features": ["Personnel Management", "Zone Management", "Assignment Management", "Real-time GPS Tracking", "Route Optimization", "Customer Reviews", "Tip Management", "Performance Analytics", "Delivery Notifications", "Multi-branch Support", "Mobile App Integration", "Payment Processing", "Delivery Proof Collection", "Working Hours Management", "Emergency Contact System"], "api_endpoints": {"personnel": "/api/delivery/personnel", "zones": "/api/delivery/zones", "assignments": "/api/delivery/assignments", "tracking": "/api/delivery/tracking", "reviews": "/api/delivery/reviews", "tips": "/api/delivery/tips", "analytics": "/api/delivery/analytics"}, "database_tables": ["delivery_personnel", "delivery_zones", "delivery_assignments", "delivery_tracking", "delivery_reviews", "delivery_tips"], "dependencies": {"core_modules": ["User", "Branch", "Customer", "Order"], "external_services": ["Google Maps API (optional)", "SMS Gateway (optional)", "Push Notification Service (optional)", "Payment Gateway (optional)"]}, "changelog": {"1.0.0": {"date": "2024-01-01", "changes": ["Initial release", "Personnel management system", "Delivery zone management", "Real-time GPS tracking", "Assignment management", "Review and tip system", "Analytics and reporting", "Multi-branch support", "Mobile app ready APIs"]}}, "roadmap": {"1.1.0": ["Route optimization algorithms", "Advanced analytics dashboard", "Integration with external mapping services", "Automated dispatch system"], "1.2.0": ["Machine learning for delivery time prediction", "Customer preference learning", "Dynamic pricing based on demand", "Advanced reporting features"]}}
<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AddonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'menu_item_id' => $this->menu_item_id,
            'addon_group_name' => $this->addon_group_name,
            'name' => $this->name,
            'code' => $this->code,
            'price' => $this->price,
            'cost' => $this->cost,
            'is_required' => $this->is_required,
            'max_quantity' => $this->max_quantity,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'menu_item' => $this->whenLoaded('menuItem', function() {
                return [
                    'id' => $this->menuItem->id,
                    'name' => $this->menuItem->name,
                    'code' => $this->menuItem->code,
                    'base_price' => $this->menuItem->base_price,
                ];
            }),
        ];
    }
}
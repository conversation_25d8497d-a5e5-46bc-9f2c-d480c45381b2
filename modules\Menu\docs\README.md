# Menu Module Documentation

This document provides an overview of the Menu module, its architecture, and key functionalities.

## 1. Introduction

The Menu module is responsible for managing all aspects of menu items, categories, variants, and addons within the EPSIS system. It integrates with pricing and availability services to provide comprehensive menu management capabilities.

## 2. Architecture Overview

The Menu module follows a modular architecture, separating concerns into distinct directories:

- `Config`: Contains configuration files specific to the Menu module.
- `Database`: Houses migrations and seeders for the module's database tables.
- `Helpers`: Provides utility functions used across the module.
- `Http`: Contains controllers, middleware, and form requests for handling HTTP requests.
- `Providers`: Registers services and other module-specific components.
- `Resources`: Holds views and other assets.
- `Services`: Encapsulates the business logic for menu-related operations.
- `routes`: Defines API and web routes for the module.

## 3. Key Components

### 3.1. Services

- <mcsymbol name="MenuService" filename="MenuService.php" path="d:\tasks\epsis\modules\Menu\Services\MenuService.php" startline="15" type="class"></mcsymbol>: The core service for managing menu items, categories, variants, and addons. It orchestrates interactions with models and other services.
  - **Methods:**
    - `getAllMenuItems(array $filters)`: Retrieves all menu items with optional filters and pagination.
    - `getMenuItemById(string $id)`: Fetches a single menu item by its ID.
    - `createMenuItem(array $data)`: Creates a new menu item.
    - `updateMenuItem(string $id, array $data)`: Updates an existing menu item.
    - `deleteMenuItem(string $id)`: Deletes a menu item.
    - `getCategories()`: Retrieves all menu categories.
    - `getCategoryById(string $id)`: Fetches a single menu category by its ID.
    - `getItemsByCategory(string $categoryId)`: Gets menu items belonging to a specific category.
    - `toggleAvailability(string $id)`: Toggles the availability status of a menu item.
    - `getFeaturedItems()`: Retrieves featured menu items.
    - `searchMenuItems(string $query)`: Searches for menu items based on a query string.
    - `createCategory(array $data)`: Creates a new menu category.
    - `updateCategory(string $id, array $data)`: Updates an existing menu category.
    - `deleteCategory(string $id)`: Deletes a menu category (with checks for associated items).
    - `getCategoryWithItems(string $id)`: Retrieves a category along with its associated menu items.
    - `createVariant(string $menuItemId, array $data)`: Creates a new variant for a menu item.
    - `updateVariant(string $variantId, array $data)`: Updates an existing menu item variant.
    - `deleteVariant(string $variantId)`: Deletes a menu item variant.
    - `getMenuItemVariants(string $menuItemId)`: Gets all variants for a specific menu item.
    - `createAddon(string $menuItemId, array $data)`: Creates a new addon for a menu item.
    - `updateAddon(string $addonId, array $data)`: Updates an existing menu item addon.
    - `deleteAddon(string $addonId)`: Deletes a menu item addon.
    - `getMenuItemAddons(string $menuItemId)`: Gets all addons for a specific menu item.
    - `setAvailabilitySchedule(string $menuItemId, array $scheduleData)`: Sets the availability schedule for a menu item (delegates to <mcsymbol name="AvailabilityService" filename="AvailabilityService.php" path="d:\tasks\epsis\Services\AvailabilityService.php" startline="1" type="class"></mcsymbol>).
    - `isAvailableAt(string $menuItemId, string $date, string $time = null, ?string $branchId = null)`: Checks if a menu item is available at a specific time and branch (delegates to <mcsymbol name="AvailabilityService" filename="AvailabilityService.php" path="d:\tasks\epsis\Services\AvailabilityService.php" startline="1" type="class"></mcsymbol>).
    - `setBranchSettings(string $menuItemId, string $branchId, array $data)`: Sets branch-specific settings for a menu item.
    - `getMenuForBranch(string $branchId, array $filters)`: Retrieves menu items available for a specific branch.
    - `getMenuItemPriceForBranch(string $menuItemId, string $branchId)`: Gets the price of a menu item for a specific branch (delegates to <mcsymbol name="PricingService" filename="PricingService.php" path="d:\tasks\epsis\Services\PricingService.php" startline="1" type="class"></mcsymbol>).
    - `calculateTotalPrice(string $menuItemId, array $variants = [], array $addons = [], ?string $branchId = null)`: Calculates the total price of a menu item with variants and addons (delegates to <mcsymbol name="PricingService" filename="PricingService.php" path="d:\tasks\epsis\Services\PricingService.php" startline="1" type="class"></mcsymbol>).
    - `getPricingBreakdown(string $menuItemId, array $variants = [], array $addons = [], ?string $branchId = null)`: Provides a detailed pricing breakdown (delegates to <mcsymbol name="PricingService" filename="PricingService.php" path="d:\tasks\epsis\Services\PricingService.php" startline="1" type="class"></mcsymbol>).
    - `bulkUpdateStatus(array $menuItemIds, bool $isActive)`: Bulk updates the active status of menu items.
    - `getMenuStatistics()`: Provides various statistics about the menu.
    - `getLowStockItems(string $branchId)`: Retrieves menu items with low stock for a given branch.

- <mcsymbol name="PricingService" filename="PricingService.php" path="d:\tasks\epsis\modules\Menu\Services\PricingService.php" startline="8" type="class"></mcsymbol>: Handles all pricing-related calculations and logic.
  - **Methods:**
    - `calculateTotalPrice(string $menuItemId, array $variants = [], array $addons = [], ?string $branchId = null)`: Calculates the total price of a menu item including variants and addons.
    - `getMenuItemPrice(string $menuItemId, ?string $branchId = null)`: Retrieves the base price of a menu item, considering branch-specific overrides.
    - `calculateVariantAdjustment(array $variantIds)`: Calculates the price adjustment based on selected variants.
    - `calculateAddonPrice(array $addons)`: Calculates the total price of selected addons.
    - `getPricingBreakdown(string $menuItemId, array $variants = [], array $addons = [], ?string $branchId = null)`: Provides a detailed breakdown of the menu item's price.
    - `getVariantPriceBreakdown(array $variantIds)`: Returns a breakdown of variant prices.
    - `getAddonPriceBreakdown(array $addons)`: Returns a breakdown of addon prices.
    - `updateBranchPricing(string $menuItemId, string $branchId, ?float $priceOverride)`: Updates branch-specific price overrides for a menu item.
    - `getBranchPricing(string $menuItemId)`: Retrieves all branch-specific pricing for a menu item.
    - `updateBranchCost(string $menuItemId, string $branchId, ?float $costOverride)`: Updates branch-specific cost overrides for a menu item.
    - `getBranchCost(string $menuItemId)`: Retrieves all branch-specific costs for a menu item.
    - `calculateProfitMargin(float $price, float $cost)`: Calculates the profit margin for a given price and cost.
    - `getProfitMarginBreakdown(string $menuItemId, array $variants = [], array $addons = [], ?string $branchId = null)`: Provides a detailed profit margin breakdown.

- <mcsymbol name="AvailabilityService" filename="AvailabilityService.php" path="d:\tasks\epsis\modules\Menu\Services\AvailabilityService.php" startline="8" type="class"></mcsymbol>: Manages the availability logic for menu items.
  - **Methods:**
    - `checkItemAvailability(string $menuItemId, ?string $branchId = null)`: Checks the overall availability of a menu item.
    - `isAvailableAt(string $menuItemId, string $date, ?string $time = null, ?string $branchId = null)`: Determines if a menu item is available at a specific date, time, and branch.
    - `isAvailableNow(string $menuItemId, ?string $branchId = null)`: Checks if a menu item is currently available.
    - `getWeeklyAvailability(string $menuItemId, ?string $branchId = null)`: Retrieves the weekly availability schedule for a menu item.
    - `setAvailability(string $menuItemId, array $schedule, ?string $branchId = null)`: Sets the availability schedule for a menu item.
    - `updateAvailability(string $menuItemId, array $schedule, ?string $branchId = null)`: Updates the availability schedule for a menu item.
    - `getAvailableMenuItems(array $filters = [])`: Retrieves menu items that are currently available based on filters.
    - `forecastWeeklyAvailability(string $menuItemId, ?string $branchId = null)`: Forecasts the weekly availability of a menu item.
    - `setBranchAvailability(string $menuItemId, string $branchId, bool $isAvailable)`: Sets branch-specific availability for a menu item.
    - `bulkUpdateAvailability(array $menuItemIds, bool $isAvailable, ?string $branchId = null)`: Bulk updates the availability status of multiple menu items.
    - `checkAvailabilityConflicts(string $menuItemId, array $schedule, ?string $branchId = null)`: Checks for conflicts in the availability schedule.
    - `getAvailabilityStatistics(?string $branchId = null)`: Provides statistics related to menu item availability.

### 3.2. Controllers

- <mcsymbol name="CategoryController" filename="CategoryController.php" path="d:\tasks\epsis\modules\Menu\Http\Controllers\CategoryController.php" startline="1" type="class"></mcsymbol>: Handles HTTP requests related to menu categories.
  - **Methods:**
    - `index()`: Displays a listing of menu categories.
    - `show(string $id)`: Displays the specified menu category.
    - `store(StoreCategoryRequest $request)`: Stores a newly created menu category.
    - `update(UpdateCategoryRequest $request, string $id)`: Updates the specified menu category.
    - `destroy(string $id)`: Removes the specified menu category.
    - `getItemsByCategory(string $id)`: Retrieves menu items belonging to a specific category.

- <mcsymbol name="MenuItemController" filename="MenuItemController.php" path="d:\tasks\epsis\modules\Menu\Http\Controllers\MenuItemController.php" startline="1" type="class"></mcsymbol>: Handles HTTP requests related to menu items.
  - **Methods:**
    - `index(Request $request)`: Displays a listing of menu items.
    - `store(StoreMenuItemRequest $request)`: Stores a newly created menu item.
    - `show(string $id)`: Displays the specified menu item.
    - `update(UpdateMenuItemRequest $request, string $id)`: Updates the specified menu item.
    - `destroy(string $id)`: Removes the specified menu item.
    - `toggleAvailability(string $id)`: Toggles the availability of a menu item.
    - `getFeaturedItems()`: Retrieves featured menu items.
    - `searchMenuItems(Request $request)`: Searches for menu items.
    - `storeVariant(StoreVariantRequest $request)`: Stores a new variant for a menu item.
    - `updateVariant(UpdateVariantRequest $request, string $variantId)`: Updates a menu item variant.
    - `deleteVariant(string $variantId)`: Deletes a menu item variant.
    - `getMenuItemVariants(string $menuItemId)`: Retrieves variants for a menu item.
    - `storeAddon(StoreAddonRequest $request)`: Stores a new addon for a menu item.
    - `updateAddon(UpdateAddonRequest $request, string $addonId)`: Updates a menu item addon.
    - `deleteAddon(string $addonId)`: Deletes a menu item addon.
    - `getMenuItemAddons(string $menuItemId)`: Retrieves addons for a menu item.
    - `setAvailabilitySchedule(SetAvailabilityRequest $request, string $menuItemId)`: Sets the availability schedule for a menu item.
    - `setBranchSettings(SetBranchSettingsRequest $request, string $menuItemId, string $branchId)`: Sets branch-specific settings for a menu item.
    - `getMenuForBranch(string $branchId, Request $request)`: Retrieves the menu for a specific branch.
    - `getMenuItemPriceForBranch(string $menuItemId, string $branchId)`: Gets the price of a menu item for a specific branch.
    - `calculateTotalPrice(Request $request, string $menuItemId)`: Calculates the total price of a menu item.
    - `getPricingBreakdown(Request $request, string $menuItemId)`: Provides a pricing breakdown for a menu item.

- <mcsymbol name="MenuController" filename="MenuController.php" path="d:\tasks\epsis\modules\Menu\Http\Controllers\MenuController.php" startline="1" type="class"></mcsymbol>: (Deprecated/Refactored) This controller was previously used for all menu-related operations but has been refactored into <mcsymbol name="CategoryController" filename="CategoryController.php" path="d:\tasks\epsis\modules\Menu\Http\Controllers\CategoryController.php" startline="1" type="class"></mcsymbol> and <mcsymbol name="MenuItemController" filename="MenuItemController.php" path="d:\tasks\epsis\modules\Menu\Http\Controllers\MenuItemController.php" startline="1" type="class"></mcsymbol> for better organization.

### 3.3. Requests

- `StoreMenuItemRequest`, `UpdateMenuItemRequest`: Validation for creating and updating menu items.
- `StoreCategoryRequest`, `UpdateCategoryRequest`: Validation for creating and updating menu categories.
- `StoreVariantRequest`, `UpdateVariantRequest`: Validation for creating and updating menu item variants.
- `StoreAddonRequest`, `UpdateAddonRequest`: Validation for creating and updating menu item addons.
- `SetAvailabilityRequest`: Validation for setting menu item availability schedules.
- `SetBranchSettingsRequest`: Validation for setting branch-specific menu item settings.

### 3.4. Helpers

- <mcsymbol name="MenuHelper" filename="MenuHelper.php" path="d:\tasks\epsis\modules\Menu\Helpers\MenuHelper.php" startline="5" type="class"></mcsymbol>: Provides static helper methods for common menu-related tasks.
  - **Methods:**
    - `calculateProfitMargin(float $price, float $costPrice)`: Calculates the profit margin.
    - `formatPrice(float $price, string $currency = '$')`: Formats a price for display.
    - `getDietaryOptions()`: Returns a list of available dietary options.
    - `getAllergens()`: Returns a list of common allergens.
    - `generateSku(string $name)`: Generates a SKU for a menu item.
    - `getPreparationTimeCategory(int $timeInMinutes)`: Categorizes preparation time.
    - `getCalorieCategory(int $calories)`: Categorizes calorie count.
    - `validateMenuItemData(array $data)`: Validates menu item data (example, actual validation is in requests).
    - `getStatusBadge(bool $status)`: Returns an HTML badge for status display.
    - `calculateTotalPriceWithModifiers(float $basePrice, array $variants, array $addons)`: Calculates total price with modifiers (logic moved to <mcsymbol name="PricingService" filename="PricingService.php" path="d:\tasks\epsis\modules\Menu\Services\PricingService.php" startline="8" type="class"></mcsymbol>).
    - `formatDietaryInfo(string $json)`: Formats dietary information for display.
    - `formatAllergens(string $json)`: Formats allergens for display.

### 3.5. Routes

- `api.php`: Defines API routes for the Menu module, including authenticated and public endpoints for categories, menu items, variants, addons, availability, and branch-specific operations.
- `web.php`: Defines web routes for the Menu module, including public routes for menu display and protected admin routes for managing menu data.

## 4. Data Models

The Menu module interacts with the following key models:

- `MenuItem`: Represents a single menu item.
- `MenuCategory`: Represents a category for menu items.
- `MenuItemVariant`: Represents a variant option for a menu item (e.g., size, flavor).
- `MenuItemAddon`: Represents an addon that can be added to a menu item (e.g., extra cheese).
- `MenuAvailability`: Stores the availability schedule for menu items.
- `MenuItemBranch`: Stores branch-specific settings for menu items (e.g., price overrides, stock).

## 5. Configuration

The module's configuration is managed in <mcfile name="menu.php" path="d:\tasks\epsis\modules\Menu\Config\menu.php"></mcfile>. Key configurable aspects include:

- Default currency and price precision.
- Image settings (max size, allowed types, storage path).
- Availability settings (default times, timezone, advance booking days).
- Pricing settings (tax rate, service charge, profit margin).
- Category settings (max depth, sorting).
- Menu item settings (SKU generation, max variants/addons).
- Search settings (results per page, search fields).
- Cache settings (enabled, TTL, prefix).
- Validation rules (max lengths, price ranges).
- Dietary options, allergens, and spice levels.
- Branch-specific settings (price/availability overrides).

## 6. Setup and Usage

1. **Installation:** Ensure the Menu module is enabled in your application.
2. **Migrations:** Run `php artisan migrate` to create the necessary database tables.
3. **Seeding:** Use the <mcsymbol name="MenuSeeder" filename="MenuSeeder.php" path="d:\tasks\epsis\modules\Menu\Database\Seeders\MenuSeeder.php" startline="1" type="class"></mcsymbol> to populate sample menu data: `php artisan db:seed --class=Modules\\Menu\\Database\\Seeders\\MenuSeeder`.
4. **Configuration:** Review and adjust settings in <mcfile name="menu.php" path="d:\tasks\epsis\modules\Menu\Config\menu.php"></mcfile> as needed.
5. **API Usage:** Refer to `routes/api.php` for available API endpoints and their expected request/response formats.
6. **Web Usage:** Refer to `routes/web.php` for available web routes.

## 7. Future Enhancements

- Implement advanced search filters.
- Add support for menu item reviews and ratings.
- Integrate with inventory management for real-time stock updates.
- Enhance reporting and analytics for menu performance.
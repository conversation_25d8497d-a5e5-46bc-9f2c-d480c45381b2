<?php

namespace Modules\Tenant\Services;

use App\Models\Branch;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

class BranchService
{
    /**
     * Create a new branch
     */
    public function createBranch(array $data): Branch
    {
        $branch = Branch::create([
            'tenant_id' => $data['tenant_id'],
            'name' => $data['name'],
            'code' => $data['code'] ?? $this->generateBranchCode($data['name']),
            'address' => $data['address'] ?? null,
            'phone' => $data['phone'] ?? null,
            'email' => $data['email'] ?? null,
            'manager_name' => $data['manager_name'] ?? null,
            'operating_hours' => $data['opening_hours'] ?? null,
            'timezone' => $data['timezone'] ?? 'UTC',
            'latitude' => $data['latitude'] ?? null,
            'longitude' => $data['longitude'] ?? null,
            'status' => ($data['is_active'] ?? true) ? 'active' : 'inactive',
            'is_delivery_enabled' => $data['is_delivery_enabled'] ?? true,
            'is_takeaway_enabled' => $data['is_takeaway_enabled'] ?? true,
            'is_dine_in_enabled' => $data['is_dine_in_enabled'] ?? true,
            'is_online_ordering_enabled' => $data['is_online_ordering_enabled'] ?? true,
        ]);

        return $branch;
    }

    /**
     * Update branch
     */
    public function updateBranch(Branch $branch, array $data): Branch
    {
        // Convert is_active to status if provided
        if (isset($data['is_active'])) {
            $data['status'] = $data['is_active'] ? 'active' : 'inactive';
            unset($data['is_active']);
        }
        
        // Convert opening_hours to operating_hours
        if (isset($data['opening_hours'])) {
            $data['operating_hours'] = $data['opening_hours'];
            unset($data['opening_hours']);
        }
        
        $branch->update($data);
        return $branch->fresh();
    }

    /**
     * Delete branch (soft delete)
     */
    public function deleteBranch(Branch $branch): bool
    {
        // Check if this is the last active branch for the tenant
        $activeBranchesCount = Branch::where('tenant_id', $branch->tenant_id)
            ->where('is_active', true)
            ->where('id', '!=', $branch->id)
            ->count();

        if ($activeBranchesCount === 0) {
            throw new \Exception('Cannot delete the last active branch for this tenant.');
        }

        return $branch->delete();
    }

    /**
     * Activate branch
     */
    public function activateBranch(Branch $branch): Branch
    {
        $branch->update(['status' => 'active']);
        return $branch->fresh();
    }

    /**
     * Deactivate branch
     */
    public function deactivateBranch(Branch $branch): Branch
    {
        // Check if this is the last active branch for the tenant
        $activeBranchesCount = Branch::where('tenant_id', $branch->tenant_id)
            ->where('status', 'active')
            ->where('id', '!=', $branch->id)
            ->count();

        if ($activeBranchesCount === 0) {
            throw new \Exception('Cannot deactivate the last active branch for this tenant.');
        }

        $branch->update(['status' => 'inactive']);
        return $branch->fresh();
    }

    /**
     * Get branch statistics
     */
    public function getBranchStatistics(Branch $branch): array
    {
        $stats = [
            'total_menu_items' => 0,
            'active_menu_items' => 0,
            'total_categories' => 0,
            'active_categories' => 0,
            'total_orders_today' => 0,
            'total_revenue_today' => 0,
            'total_orders_this_month' => 0,
            'total_revenue_this_month' => 0,
            'average_order_value' => 0,
        ];

        // Get today's date range
        $today = Carbon::now()->startOfDay();
        $endOfDay = Carbon::now()->endOfDay();

        // Get this month's date range
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();

        // Menu statistics (if menu module exists)
        if (class_exists('\App\Models\MenuItem')) {
            $stats['total_menu_items'] = \App\Models\MenuItem::where('branch_id', $branch->id)->count();
            $stats['active_menu_items'] = \App\Models\MenuItem::where('branch_id', $branch->id)
                ->where('is_active', true)->count();
        }

        if (class_exists('\App\Models\MenuCategory')) {
            $stats['total_categories'] = \App\Models\MenuCategory::whereHas('menu', function($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })->count();
            
            $stats['active_categories'] = \App\Models\MenuCategory::whereHas('menu', function($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })->where('is_active', true)->count();
        }

        // Order statistics (if order module exists)
        if (class_exists('\App\Models\Order')) {
            // Today's orders
            $todayOrders = \App\Models\Order::where('branch_id', $branch->id)
                ->whereBetween('created_at', [$today, $endOfDay]);
            
            $stats['total_orders_today'] = $todayOrders->count();
            $stats['total_revenue_today'] = $todayOrders->where('status', 'completed')->sum('total_amount');

            // This month's orders
            $monthOrders = \App\Models\Order::where('branch_id', $branch->id)
                ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);
            
            $stats['total_orders_this_month'] = $monthOrders->count();
            $stats['total_revenue_this_month'] = $monthOrders->where('status', 'completed')->sum('total_amount');

            // Average order value
            $completedOrders = $monthOrders->where('status', 'completed');
            if ($completedOrders->count() > 0) {
                $stats['average_order_value'] = $stats['total_revenue_this_month'] / $completedOrders->count();
            }
        }

        return $stats;
    }

    /**
     * Get branches for a specific tenant
     */
    public function getBranchesForTenant(int $tenantId, array $filters = []): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = Branch::where('tenant_id', $tenantId)->with(['tenant']);

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        $perPage = $filters['per_page'] ?? 15;
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Generate unique branch code
     */
    protected function generateBranchCode(string $name): string
    {
        $baseCode = Str::slug(Str::limit($name, 20, ''));
        $code = $baseCode;
        $counter = 1;

        while (Branch::where('code', $code)->exists()) {
            $code = $baseCode . '-' . $counter;
            $counter++;
        }

        return $code;
    }

    /**
     * Check if branch belongs to tenant
     */
    public function belongsToTenant(Branch $branch, int $tenantId): bool
    {
        return $branch->tenant_id === $tenantId;
    }

    /**
     * Get active branches for tenant
     */
    public function getActiveBranchesForTenant(int $tenantId): \Illuminate\Database\Eloquent\Collection
    {
        return Branch::where('tenant_id', $tenantId)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
    }
}
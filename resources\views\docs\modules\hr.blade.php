@extends('docs.layout')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <div class="p-4 rounded-full bg-purple-100 mr-4">
                <i class="fas fa-users-cog text-purple-600 text-2xl"></i>
            </div>
            <div>
                <h1 class="text-4xl font-bold text-gray-900">HR Module</h1>
                <p class="text-xl text-gray-600 mt-2">Comprehensive Human Resources management with payroll, attendance, and performance tracking</p>
            </div>
        </div>
    </div>

    <!-- Overview -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Overview</h2>
        <p class="text-gray-600 mb-6">The HR Module provides comprehensive human resources management capabilities including staff management, payroll processing, attendance tracking, shift scheduling, performance monitoring, and penalty management. It's designed to streamline HR operations and provide insights into workforce productivity.</p>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-purple-50 p-4 rounded-lg">
                <i class="fas fa-user-friends text-purple-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">Staff Management</h3>
                <p class="text-sm text-gray-600">Complete employee lifecycle management</p>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
                <i class="fas fa-money-bill-wave text-green-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">Payroll System</h3>
                <p class="text-sm text-gray-600">Automated salary calculations and payslips</p>
            </div>
            <div class="bg-blue-50 p-4 rounded-lg">
                <i class="fas fa-clock text-blue-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">Time & Attendance</h3>
                <p class="text-sm text-gray-600">Real-time attendance tracking and reporting</p>
            </div>
            <div class="bg-orange-50 p-4 rounded-lg">
                <i class="fas fa-chart-line text-orange-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">Performance Analytics</h3>
                <p class="text-sm text-gray-600">Comprehensive performance metrics and insights</p>
            </div>
        </div>
    </div>

    <!-- Key Features -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>
        
        <div class="grid md:grid-cols-2 gap-8">
            <div>
                <h3 class="text-lg font-semibold text-purple-800 mb-4">👥 Staff Management</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Employee profile management with detailed information</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Role-based access control and permissions</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Department and position management</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Employee search and filtering capabilities</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-green-800 mb-4">💰 Payroll & Compensation</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Automated salary calculations based on attendance</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Overtime pay calculation (1.5x rate)</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Performance-based bonuses and incentives</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Digital payslip generation and management</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-blue-800 mb-4">⏰ Time & Attendance</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Digital check-in/check-out with location tracking</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Real-time attendance monitoring and reporting</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Late arrival and early departure tracking</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Bulk attendance processing capabilities</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-orange-800 mb-4">📊 Performance & Analytics</h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Comprehensive performance scoring system</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Attendance rate and punctuality metrics</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Working hours and productivity analysis</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Penalty tracking and management</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- API Endpoints -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">API Endpoints</h2>
        
        <!-- Staff Management -->
        <div class="mb-8">
            <h3 class="text-xl font-semibold text-purple-800 mb-4">👥 Staff Management</h3>
            <div class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/staff</code>
                    </div>
                    <p class="text-gray-600 mb-3">Get all staff members with filtering and pagination</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Query Parameters:</p>
                        <code class="text-xs text-gray-600">?search=john&role=manager&branch_id=1&per_page=15</code>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "employee_id": "EMP001",
      "position": "Manager",
      "department": "HR",
      "is_active": true
    }
  ],
  "first_page_url": "http://localhost/api/hr/staff?page=1",
  "from": 1,
  "last_page": 1,
  "last_page_url": "http://localhost/api/hr/staff?page=1",
  "next_page_url": null,
  "path": "http://localhost/api/hr/staff",
  "per_page": 15,
  "prev_page_url": null,
  "to": 1,
  "total": 1
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/staff/{id}</code>
                    </div>
                    <p class="text-gray-600">Get detailed staff member information</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "employee_id": "EMP001",
  "position": "Manager",
  "department": "HR",
  "hire_date": "2023-01-15",
  "date_of_birth": "1990-05-20",
  "address": "123 Main St, Anytown USA",
  "phone_number": "******-123-4567",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_number": "******-987-6543",
  "bank_account_number": "**********",
  "tax_id_number": "TAXID123",
  "social_security_number": "SSN123",
  "hourly_rate": "25.00",
  "base_salary": "4000.00",
  "is_active": true,
  "created_at": "2023-01-15T10:00:00.000000Z",
  "updated_at": "2023-01-15T10:00:00.000000Z"
}</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Attendance Management -->
        <div class="mb-8">
            <h3 class="text-xl font-semibold text-blue-800 mb-4">⏰ Attendance Management</h3>
            <div class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/check-in</code>
                    </div>
                    <p class="text-gray-600 mb-3">Check in staff member</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                        <pre class="text-xs text-gray-600">{
  "user_id": 123,
  "location": "Main Branch",
  "branch_id": 1
}</pre>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Staff checked in successfully.",
  "attendance": {
    "user_id": 123,
    "date": "2025-01-15",
    "check_in_time": "09:00:00",
    "status": "present",
    "updated_at": "2025-01-15T09:00:00.000000Z",
    "created_at": "2025-01-15T09:00:00.000000Z",
    "id": 1
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/check-out</code>
                    </div>
                    <p class="text-gray-600">Check out staff member</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Staff checked out successfully.",
  "attendance": {
    "user_id": 123,
    "date": "2025-01-15",
    "check_in_time": "09:00:00",
    "check_out_time": "17:00:00",
    "status": "present",
    "updated_at": "2025-01-15T17:00:00.000000Z",
    "created_at": "2025-01-15T09:00:00.000000Z",
    "id": 1
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/attendance</code>
                    </div>
                    <p class="text-gray-600">Get attendance records with filtering</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "date": "2025-01-15",
      "status": "present",
      "check_in_time": "09:00:00",
      "check_out_time": "17:00:00",
      "late_minutes": 0,
      "overtime_minutes": 60,
      "created_at": "2025-01-15T09:00:00.000000Z",
      "updated_at": "2025-01-15T17:00:00.000000Z"
    }
  ],
  "first_page_url": "http://localhost/api/hr/attendance?page=1",
  "from": 1,
  "last_page": 1,
  "last_page_url": "http://localhost/api/hr/attendance?page=1",
  "next_page_url": null,
  "path": "http://localhost/api/hr/attendance",
  "per_page": 15,
  "prev_page_url": null,
  "to": 1,
  "total": 1
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/bulk-attendance</code>
                    </div>
                    <p class="text-gray-600 mb-3">Process bulk attendance data</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                        <pre class="text-xs text-gray-600">{
  "attendance_data": [
    {
      "user_id": 123,
      "date": "2025-01-15",
      "status": "present",
      "check_in_time": "09:00:00",
      "check_out_time": "17:00:00"
    }
  ]
}</pre>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Bulk attendance processed successfully.",
  "processed_records": 1
}</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Payroll Management -->
        <div class="mb-8">
            <h3 class="text-xl font-semibold text-green-800 mb-4">💰 Payroll Management</h3>
            <div class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/staff/{id}/calculate-salary</code>
                    </div>
                    <p class="text-gray-600 mb-3">Calculate staff salary for a pay period</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Query Parameters:</p>
                        <code class="text-xs text-gray-600">?pay_period_id=1</code>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "user_id": 123,
  "pay_period_id": 1,
  "total_hours_worked": 160,
  "overtime_hours": 10,
  "base_salary": 4000.00,
  "overtime_pay": 375.00, 
  "gross_salary": 4375.00,
  "deductions": 200.00,
  "net_salary": 4175.00
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/generate-payslip</code>
                    </div>
                    <p class="text-gray-600 mb-3">Generate payslip for staff member</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                        <pre class="text-xs text-gray-600">{
  "user_id": 123,
  "pay_period_id": 1
}</pre>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Payslip generated successfully.",
  "payslip": {
    "id": 1,
    "user_id": 123,
    "pay_period_id": 1,
    "gross_salary": 4375.00,
    "deductions": 200.00,
    "net_salary": 4175.00,
    "generated_date": "2025-01-31",
    "status": "generated"
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/staff/{id}/payslips</code>
                    </div>
                    <p class="text-gray-600">Get payslips for staff member</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "pay_period_id": 1,
      "gross_salary": 4375.00,
      "deductions": 200.00,
      "net_salary": 4175.00,
      "generated_date": "2025-01-31",
      "status": "generated",
      "created_at": "2025-01-31T10:00:00.000000Z",
      "updated_at": "2025-01-31T10:00:00.000000Z"
    }
  ],
  "first_page_url": "http://localhost/api/hr/staff/1/payslips?page=1",
  "from": 1,
  "last_page": 1,
  "last_page_url": "http://localhost/api/hr/staff/1/payslips?page=1",
  "next_page_url": null,
  "path": "http://localhost/api/hr/staff/1/payslips",
  "per_page": 15,
  "prev_page_url": null,
  "to": 1,
  "total": 1
}</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Penalty Management -->
        <div class="mb-8">
            <h3 class="text-xl font-semibold text-red-800 mb-4">⚠️ Penalty Management</h3>
            <div class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/apply-penalty</code>
                    </div>
                    <p class="text-gray-600 mb-3">Apply penalty to staff member</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                        <pre class="text-xs text-gray-600">{
  "user_id": 123,
  "type": "late",
  "amount": 50.00,
  "reason": "Late arrival - 30 minutes",
  "date": "2025-01-15"
}</pre>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Penalty applied successfully.",
  "penalty": {
    "id": 1,
    "user_id": 123,
    "type": "late",
    "amount": 50.00,
    "reason": "Late arrival - 30 minutes",
    "date": "2025-01-15",
    "created_at": "2025-01-15T10:00:00.000000Z",
    "updated_at": "2025-01-15T10:00:00.000000Z"
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/staff/{id}/penalties</code>
                    </div>
                    <p class="text-gray-600">Get penalties for staff member</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "type": "late",
      "amount": 50.00,
      "reason": "Late arrival - 30 minutes",
      "date": "2025-01-15",
      "created_at": "2025-01-15T10:00:00.000000Z",
      "updated_at": "2025-01-15T10:00:00.000000Z"
    }
  ],
  "first_page_url": "http://localhost/api/hr/staff/1/penalties?page=1",
  "from": 1,
  "last_page": 1,
  "last_page_url": "http://localhost/api/hr/staff/1/penalties?page=1",
  "next_page_url": null,
  "path": "http://localhost/api/hr/staff/1/penalties",
  "per_page": 15,
  "prev_page_url": null,
  "to": 1,
  "total": 1
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/penalties/{penaltyId}/waive</code>
                    </div>
                    <p class="text-gray-600">Waive a staff penalty</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Penalty waived successfully.",
  "penalty": {
    "id": 1,
    "user_id": 123,
    "type": "late",
    "amount": 50.00,
    "reason": "Late arrival - 30 minutes",
    "date": "2025-01-15",
    "status": "waived",
    "created_at": "2025-01-15T10:00:00.000000Z",
    "updated_at": "2025-01-15T10:00:00.000000Z"
  }
}</pre>
                    </div>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/penalty-stats</code>
                    </div>
                    <p class="text-gray-600 mb-3">Get penalty statistics for a given period</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Query Parameters:</p>
                        <code class="text-xs text-gray-600">?start_date=2025-01-01&end_date=2025-01-31</code>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "total_penalties": 5,
  "total_penalty_amount": 250.00,
  "penalties_by_type": {
    "late": 3,
    "absence": 2
  },
  "top_penalized_staff": [
    {
      "user_id": 123,
      "name": "John Doe",
      "penalty_count": 3,
      "total_amount": 150.00
    }
  ]
}</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Leave Management -->
        <div class="mb-8">
            <h3 class="text-xl font-semibold text-purple-800 mb-4">🏖️ Leave Management</h3>
            <div class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/leave/requests</code>
                    </div>
                    <p class="text-gray-600 mb-3">Get all leave requests with filtering and pagination</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Query Parameters:</p>
                        <code class="text-xs text-gray-600">?user_id=123&status=pending&type=vacation&start_date=2025-01-01&end_date=2025-01-31</code>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "leave_type": "vacation",
      "start_date": "2025-03-01",
      "end_date": "2025-03-07",
      "reason": "Annual leave",
      "status": "approved",
      "admin_notes": "Approved as per policy",
      "created_at": "2025-02-10T10:00:00.000000Z",
      "updated_at": "2025-02-15T10:00:00.000000Z"
    }
  ],
  "first_page_url": "http://localhost/api/hr/leave/requests?page=1",
  "from": 1,
  "last_page": 1,
  "last_page_url": "http://localhost/api/hr/leave/requests?page=1",
  "next_page_url": null,
  "path": "http://localhost/api/hr/leave/requests",
  "per_page": 15,
  "prev_page_url": null,
  "to": 1,
  "total": 1
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/leave/requests</code>
                    </div>
                    <p class="text-gray-600 mb-3">Submit a new leave request</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                        <pre class="text-xs text-gray-600">{
  "leave_type": "vacation",
  "start_date": "2025-03-01",
  "end_date": "2025-03-07",
  "reason": "Annual leave"
}</pre>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (201 Created):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Leave request submitted successfully.",
  "leave_request": {
    "user_id": 123,
    "leave_type": "vacation",
    "start_date": "2025-03-01",
    "end_date": "2025-03-07",
    "reason": "Annual leave",
    "status": "pending",
    "updated_at": "2025-02-10T10:00:00.000000Z",
    "created_at": "2025-02-10T10:00:00.000000Z",
    "id": 2
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm font-medium mr-3">PUT</span>
                        <code class="text-gray-800">/api/hr/leave/requests/{id}</code>
                    </div>
                    <p class="text-gray-600 mb-3">Update (approve/reject) a leave request</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Request Body:</p>
                        <pre class="text-xs text-gray-600">{
  "status": "approved",
  "admin_notes": "Approved as per policy"
}</pre>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Leave request updated successfully.",
  "leave_request": {
    "id": 1,
    "user_id": 123,
    "leave_type": "vacation",
    "start_date": "2025-03-01",
    "end_date": "2025-03-07",
    "reason": "Annual leave",
    "status": "approved",
    "admin_notes": "Approved as per policy",
    "created_at": "2025-02-10T10:00:00.000000Z",
    "updated_at": "2025-02-15T10:00:00.000000Z"
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium mr-3">POST</span>
                        <code class="text-gray-800">/api/hr/leave/requests/{id}/cancel</code>
                    </div>
                    <p class="text-gray-600">Cancel a pending or approved leave request</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "message": "Leave request cancelled successfully.",
  "leave_request": {
    "id": 1,
    "user_id": 123,
    "leave_type": "vacation",
    "start_date": "2025-03-01",
    "end_date": "2025-03-07",
    "reason": "Annual leave",
    "status": "cancelled",
    "admin_notes": null,
    "created_at": "2025-02-10T10:00:00.000000Z",
    "updated_at": "2025-02-16T10:00:00.000000Z"
  }
}</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Analytics -->
        <div class="mb-8">
            <h3 class="text-xl font-semibold text-orange-800 mb-4">📊 Performance Analytics</h3>
            <div class="space-y-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/staff/{id}/performance-metrics</code>
                    </div>
                    <p class="text-gray-600 mb-3">Get comprehensive performance metrics</p>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm font-medium text-gray-700 mb-2">Query Parameters:</p>
                        <code class="text-xs text-gray-600">?start_date=2025-01-01&end_date=2025-01-31</code>
                    </div>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "user_id": 123,
  "name": "John Doe",
  "performance_score": 92.5,
  "attendance_rate": 98.5,
  "punctuality_score": 95,
  "total_penalties": 2,
  "total_overtime_hours": 15,
  "average_daily_hours": 7.8,
  "metrics_period": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-31"
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/attendance-stats</code>
                    </div>
                    <p class="text-gray-600">Get attendance statistics and analytics</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "total_staff": 50,
  "present_today": 45,
  "absent_today": 3,
  "on_leave_today": 2,
  "attendance_rate_today": 90.0,
  "average_daily_attendance_rate_last_30_days": 92.5,
  "late_arrivals_last_30_days": 15,
  "early_departures_last_30_days": 8,
  "attendance_by_department": {
    "Kitchen": {
      "present": 15,
      "absent": 1
    },
    "Service": {
      "present": 20,
      "absent": 2
    }
  }
}</pre>
                    </div>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">GET</span>
                        <code class="text-gray-800">/api/hr/staff/{id}/working-hours</code>
                    </div>
                    <p class="text-gray-600">Get working hours summary for payroll</p>
                    <div class="bg-gray-50 p-3 rounded mt-3">
                        <p class="text-sm font-medium text-gray-700 mb-2">Example Response (200 OK):</p>
                        <pre class="text-xs text-gray-600">{
  "user_id": 123,
  "name": "John Doe",
  "pay_period_id": 1,
  "start_date": "2025-01-01",
  "end_date": "2025-01-31",
  "total_scheduled_hours": 160,
  "total_actual_hours": 165,
  "total_overtime_hours": 5,
  "total_late_minutes": 30,
  "total_absent_days": 0,
  "total_leave_days": 0
}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Models -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Data Models</h2>
        
        <div class="grid md:grid-cols-2 gap-6">
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-purple-800 mb-3">User (Staff)</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>id:</strong> Unique identifier</li>
                    <li><strong>name:</strong> Full name</li>
                    <li><strong>email:</strong> Email address</li>
                    <li><strong>employee_id:</strong> Employee ID</li>
                    <li><strong>position:</strong> Job position</li>
                    <li><strong>department:</strong> Department</li>
                    <li><strong>hourly_rate:</strong> Hourly pay rate</li>
                    <li><strong>base_salary:</strong> Base monthly salary</li>
                    <li><strong>is_active:</strong> Employment status</li>
                </ul>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-blue-800 mb-3">StaffAttendance</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>id:</strong> Unique identifier</li>
                    <li><strong>user_id:</strong> Staff member ID</li>
                    <li><strong>date:</strong> Attendance date</li>
                    <li><strong>status:</strong> present, absent, leave, holiday</li>
                    <li><strong>check_in_time:</strong> Check-in timestamp</li>
                    <li><strong>check_out_time:</strong> Check-out timestamp</li>
                    <li><strong>late_minutes:</strong> Minutes late</li>
                    <li><strong>overtime_minutes:</strong> Overtime minutes</li>
                </ul>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-green-800 mb-3">Payslip</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>id:</strong> Unique identifier</li>
                    <li><strong>user_id:</strong> Staff member ID</li>
                    <li><strong>pay_period_id:</strong> Pay period reference</li>
                    <li><strong>base_salary:</strong> Base salary amount</li>
                    <li><strong>overtime_pay:</strong> Overtime compensation</li>
                    <li><strong>bonuses:</strong> Performance bonuses</li>
                    <li><strong>deductions:</strong> Penalties and deductions</li>
                    <li><strong>net_salary:</strong> Final pay amount</li>
                </ul>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-red-800 mb-3">StaffPenalty</h3>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li><strong>id:</strong> Unique identifier</li>
                    <li><strong>user_id:</strong> Staff member ID</li>
                    <li><strong>type:</strong> late, absence, misconduct, other</li>
                    <li><strong>amount:</strong> Penalty amount</li>
                    <li><strong>reason:</strong> Penalty reason</li>
                    <li><strong>applied_date:</strong> Date applied</li>
                    <li><strong>status:</strong> active, waived, paid</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Integration -->
    <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Module Integration</h2>
        
        <div class="grid md:grid-cols-3 gap-6">
            <div class="bg-blue-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-900 mb-2">Orders Module</h3>
                <p class="text-sm text-gray-600">Staff members are linked to orders as servers and cashiers for performance tracking and commission calculations.</p>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-900 mb-2">Kitchen Module</h3>
                <p class="text-sm text-gray-600">Kitchen staff assignments and performance metrics are integrated for comprehensive workforce management.</p>
            </div>
            
            <div class="bg-purple-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-900 mb-2">Reports Module</h3>
                <p class="text-sm text-gray-600">HR data feeds into comprehensive reporting for labor cost analysis and productivity insights.</p>
            </div>
        </div>
    </div>
</div>
@endsection
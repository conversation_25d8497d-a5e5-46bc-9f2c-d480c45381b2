<?php

namespace Modules\Kitchen\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssignMenuItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'menu_item_id' => 'required|exists:menu_items,id',
            'prep_time_minutes' => 'nullable|integer|min:1|max:300',
            'priority_level' => 'integer|min:1|max:10',
            'is_active' => 'boolean',
            'special_instructions' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'menu_item_id.required' => 'Menu item is required.',
            'menu_item_id.exists' => 'Selected menu item does not exist.',
            'prep_time_minutes.integer' => 'Preparation time must be a number.',
            'prep_time_minutes.min' => 'Preparation time must be at least 1 minute.',
            'prep_time_minutes.max' => 'Preparation time cannot exceed 300 minutes.',
            'priority_level.integer' => 'Priority level must be a number.',
            'priority_level.min' => 'Priority level must be at least 1.',
            'priority_level.max' => 'Priority level cannot exceed 10.',
            'special_instructions.max' => 'Special instructions cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'menu_item_id' => 'menu item',
            'prep_time_minutes' => 'preparation time',
            'priority_level' => 'priority level',
            'special_instructions' => 'special instructions',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'priority_level' => $this->priority_level ?? 5,
            'is_active' => $this->is_active ?? true,
        ]);
    }
}
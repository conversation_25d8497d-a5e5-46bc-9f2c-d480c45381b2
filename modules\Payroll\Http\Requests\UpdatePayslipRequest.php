<?php

namespace Modules\Payroll\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\Payslip;

class UpdatePayslipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (!Auth::check() || !in_array(Auth::user()->role, ['admin', 'manager'])) {
            return false;
        }

        // Check if the payslip exists and belongs to the user's tenant
        $payslipId = $this->route('id');
        if ($payslipId) {
            $payslip = Payslip::where('id', $payslipId)
                ->where('tenant_id', Auth::user()->tenant_id)
                ->first();
            
            return $payslip && $payslip->canBeApproved();
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'regular_hours' => 'nullable|numeric|min:0|max:168', // Max hours in a week
            'overtime_hours' => 'nullable|numeric|min:0|max:168',
            'holiday_hours' => 'nullable|numeric|min:0|max:168',
            'sick_hours' => 'nullable|numeric|min:0|max:168',
            'vacation_hours' => 'nullable|numeric|min:0|max:168',
            'bonus' => 'nullable|numeric|min:0|max:999999.99',
            'commission' => 'nullable|numeric|min:0|max:999999.99',
            'allowances' => 'nullable|numeric|min:0|max:999999.99',
            'tax_deduction' => 'nullable|numeric|min:0|max:999999.99',
            'social_security' => 'nullable|numeric|min:0|max:999999.99',
            'health_insurance' => 'nullable|numeric|min:0|max:999999.99',
            'retirement_contribution' => 'nullable|numeric|min:0|max:999999.99',
            'other_deductions' => 'nullable|numeric|min:0|max:999999.99',
            'notes' => 'nullable|string|max:1000',
            'earnings_breakdown' => 'nullable|array',
            'earnings_breakdown.*.description' => 'required_with:earnings_breakdown|string|max:255',
            'earnings_breakdown.*.amount' => 'required_with:earnings_breakdown|numeric|min:0',
            'deductions_breakdown' => 'nullable|array',
            'deductions_breakdown.*.description' => 'required_with:deductions_breakdown|string|max:255',
            'deductions_breakdown.*.amount' => 'required_with:deductions_breakdown|numeric|min:0',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'regular_hours.numeric' => 'Regular hours must be a valid number.',
            'regular_hours.min' => 'Regular hours cannot be negative.',
            'regular_hours.max' => 'Regular hours cannot exceed 168 hours (1 week).',
            'overtime_hours.numeric' => 'Overtime hours must be a valid number.',
            'overtime_hours.min' => 'Overtime hours cannot be negative.',
            'overtime_hours.max' => 'Overtime hours cannot exceed 168 hours (1 week).',
            'holiday_hours.numeric' => 'Holiday hours must be a valid number.',
            'holiday_hours.min' => 'Holiday hours cannot be negative.',
            'holiday_hours.max' => 'Holiday hours cannot exceed 168 hours (1 week).',
            'sick_hours.numeric' => 'Sick hours must be a valid number.',
            'sick_hours.min' => 'Sick hours cannot be negative.',
            'sick_hours.max' => 'Sick hours cannot exceed 168 hours (1 week).',
            'vacation_hours.numeric' => 'Vacation hours must be a valid number.',
            'vacation_hours.min' => 'Vacation hours cannot be negative.',
            'vacation_hours.max' => 'Vacation hours cannot exceed 168 hours (1 week).',
            'bonus.numeric' => 'Bonus must be a valid number.',
            'bonus.min' => 'Bonus cannot be negative.',
            'bonus.max' => 'Bonus amount is too large.',
            'commission.numeric' => 'Commission must be a valid number.',
            'commission.min' => 'Commission cannot be negative.',
            'commission.max' => 'Commission amount is too large.',
            'allowances.numeric' => 'Allowances must be a valid number.',
            'allowances.min' => 'Allowances cannot be negative.',
            'allowances.max' => 'Allowances amount is too large.',
            'tax_deduction.numeric' => 'Tax deduction must be a valid number.',
            'tax_deduction.min' => 'Tax deduction cannot be negative.',
            'tax_deduction.max' => 'Tax deduction amount is too large.',
            'social_security.numeric' => 'Social security must be a valid number.',
            'social_security.min' => 'Social security cannot be negative.',
            'social_security.max' => 'Social security amount is too large.',
            'health_insurance.numeric' => 'Health insurance must be a valid number.',
            'health_insurance.min' => 'Health insurance cannot be negative.',
            'health_insurance.max' => 'Health insurance amount is too large.',
            'retirement_contribution.numeric' => 'Retirement contribution must be a valid number.',
            'retirement_contribution.min' => 'Retirement contribution cannot be negative.',
            'retirement_contribution.max' => 'Retirement contribution amount is too large.',
            'other_deductions.numeric' => 'Other deductions must be a valid number.',
            'other_deductions.min' => 'Other deductions cannot be negative.',
            'other_deductions.max' => 'Other deductions amount is too large.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'earnings_breakdown.array' => 'Earnings breakdown must be an array.',
            'earnings_breakdown.*.description.required_with' => 'Description is required for each earnings breakdown item.',
            'earnings_breakdown.*.description.max' => 'Earnings breakdown description may not be greater than 255 characters.',
            'earnings_breakdown.*.amount.required_with' => 'Amount is required for each earnings breakdown item.',
            'earnings_breakdown.*.amount.numeric' => 'Earnings breakdown amount must be a valid number.',
            'earnings_breakdown.*.amount.min' => 'Earnings breakdown amount cannot be negative.',
            'deductions_breakdown.array' => 'Deductions breakdown must be an array.',
            'deductions_breakdown.*.description.required_with' => 'Description is required for each deductions breakdown item.',
            'deductions_breakdown.*.description.max' => 'Deductions breakdown description may not be greater than 255 characters.',
            'deductions_breakdown.*.amount.required_with' => 'Amount is required for each deductions breakdown item.',
            'deductions_breakdown.*.amount.numeric' => 'Deductions breakdown amount must be a valid number.',
            'deductions_breakdown.*.amount.min' => 'Deductions breakdown amount cannot be negative.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate that total hours don't exceed reasonable limits
            $this->validateTotalHours($validator);
            
            // Validate earnings breakdown totals
            $this->validateEarningsBreakdown($validator);
            
            // Validate deductions breakdown totals
            $this->validateDeductionsBreakdown($validator);
        });
    }

    /**
     * Validate that total hours don't exceed reasonable limits.
     */
    private function validateTotalHours($validator): void
    {
        $regularHours = (float) ($this->input('regular_hours') ?? 0);
        $overtimeHours = (float) ($this->input('overtime_hours') ?? 0);
        $holidayHours = (float) ($this->input('holiday_hours') ?? 0);
        $sickHours = (float) ($this->input('sick_hours') ?? 0);
        $vacationHours = (float) ($this->input('vacation_hours') ?? 0);
        
        $totalHours = $regularHours + $overtimeHours + $holidayHours + $sickHours + $vacationHours;
        
        // Maximum reasonable hours for a pay period (assuming monthly max)
        $maxHours = 744; // 31 days * 24 hours
        
        if ($totalHours > $maxHours) {
            $validator->errors()->add(
                'total_hours',
                "Total hours ({$totalHours}) exceed the maximum reasonable limit ({$maxHours} hours)."
            );
        }
    }

    /**
     * Validate earnings breakdown totals.
     */
    private function validateEarningsBreakdown($validator): void
    {
        $earningsBreakdown = $this->input('earnings_breakdown', []);
        
        if (empty($earningsBreakdown)) {
            return;
        }
        
        $totalBreakdownAmount = 0;
        foreach ($earningsBreakdown as $item) {
            if (isset($item['amount'])) {
                $totalBreakdownAmount += (float) $item['amount'];
            }
        }
        
        // Calculate expected total earnings
        $bonus = (float) ($this->input('bonus') ?? 0);
        $commission = (float) ($this->input('commission') ?? 0);
        $allowances = (float) ($this->input('allowances') ?? 0);
        
        $expectedTotal = $bonus + $commission + $allowances;
        
        // Allow small discrepancies due to rounding
        $tolerance = 0.01;
        
        if (abs($totalBreakdownAmount - $expectedTotal) > $tolerance) {
            $validator->errors()->add(
                'earnings_breakdown',
                "Earnings breakdown total ({$totalBreakdownAmount}) does not match the sum of bonus, commission, and allowances ({$expectedTotal})."
            );
        }
    }

    /**
     * Validate deductions breakdown totals.
     */
    private function validateDeductionsBreakdown($validator): void
    {
        $deductionsBreakdown = $this->input('deductions_breakdown', []);
        
        if (empty($deductionsBreakdown)) {
            return;
        }
        
        $totalBreakdownAmount = 0;
        foreach ($deductionsBreakdown as $item) {
            if (isset($item['amount'])) {
                $totalBreakdownAmount += (float) $item['amount'];
            }
        }
        
        // Calculate expected total deductions
        $taxDeduction = (float) ($this->input('tax_deduction') ?? 0);
        $socialSecurity = (float) ($this->input('social_security') ?? 0);
        $healthInsurance = (float) ($this->input('health_insurance') ?? 0);
        $retirementContribution = (float) ($this->input('retirement_contribution') ?? 0);
        $otherDeductions = (float) ($this->input('other_deductions') ?? 0);
        
        $expectedTotal = $taxDeduction + $socialSecurity + $healthInsurance + $retirementContribution + $otherDeductions;
        
        // Allow small discrepancies due to rounding
        $tolerance = 0.01;
        
        if (abs($totalBreakdownAmount - $expectedTotal) > $tolerance) {
            $validator->errors()->add(
                'deductions_breakdown',
                "Deductions breakdown total ({$totalBreakdownAmount}) does not match the sum of individual deductions ({$expectedTotal})."
            );
        }
    }
}
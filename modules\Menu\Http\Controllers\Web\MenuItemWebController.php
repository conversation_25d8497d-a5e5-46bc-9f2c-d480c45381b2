<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreMenuItemRequest;
use Modules\Menu\Http\Requests\UpdateMenuItemRequest;
use Modules\Menu\Http\Requests\SetAvailabilityRequest;
use Modules\Menu\Http\Requests\SetBranchSettingsRequest;

class MenuItemWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of menu items
     */
    public function index(Request $request): View
    {
        $menuItems = $this->menuService->getAllMenuItems($request->all());
        return view('menu::menu-items.index', compact('menuItems'));
    }

    /**
     * Show the form for creating a new menu item
     */
    public function create(): View
    {
        $menus = $this->menuService->getAllMenus();
        $categories = $this->menuService->getCategories();
        return view('menu::menu-items.create', compact('menus', 'categories'));
    }

    /**
     * Store a newly created menu item
     */
    public function store(StoreMenuItemRequest $request): RedirectResponse
    {
        try {
            $menuItem = $this->menuService->createMenuItem($request->validated());
            return redirect()->route('menu.web.menu-items.index')
                ->with('success', 'Menu item created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to create menu item: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified menu item
     */
    public function show(string $id): View
    {
        $menuItem = $this->menuService->getMenuItemById($id);
        $addons = $this->menuService->getMenuItemAddons($id);
        $variants = $this->menuService->getMenuItemVariants($id);
        return view('menu::menu-items.show', compact('menuItem', 'addons', 'variants'));
    }

    /**
     * Show the form for editing the specified menu item
     */
    public function edit(string $id): View
    {
        $menuItem = $this->menuService->getMenuItemById($id);
        $menus = $this->menuService->getAllMenus();
        $categories = $this->menuService->getCategories();
        return view('menu::menu-items.edit', compact('menuItem', 'menus', 'categories'));
    }

    /**
     * Update the specified menu item
     */
    public function update(UpdateMenuItemRequest $request, string $id): RedirectResponse
    {
        try {
            $menuItem = $this->menuService->updateMenuItem($id, $request->validated());
            return redirect()->route('menu.web.menu-items.index')
                ->with('success', 'Menu item updated successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to update menu item: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified menu item
     */
    public function destroy(string $id): RedirectResponse
    {
        try {
            $this->menuService->deleteMenuItem($id);
            return redirect()->route('menu.web.menu-items.index')
                ->with('success', 'Menu item deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete menu item: ' . $e->getMessage());
        }
    }

    /**
     * Toggle menu item availability
     */
    public function toggleAvailability(string $id): RedirectResponse
    {
        try {
            $menuItem = $this->menuService->toggleAvailability($id);
            $status = $menuItem->is_active ? 'activated' : 'deactivated';
            return back()->with('success', "Menu item {$status} successfully.");
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to toggle availability: ' . $e->getMessage());
        }
    }

    /**
     * Show availability management form
     */
    public function availability(string $id): View
    {
        $menuItem = $this->menuService->getMenuItemById($id);
        return view('menu::menu-items.availability', compact('menuItem'));
    }

    /**
     * Set menu item availability schedule
     */
    public function setAvailability(SetAvailabilityRequest $request): RedirectResponse
    {
        try {
            $schedule = $this->menuService->setAvailabilitySchedule($request->menu_item_id, $request->validated());
            return back()->with('success', 'Availability schedule set successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to set availability: ' . $e->getMessage());
        }
    }
}
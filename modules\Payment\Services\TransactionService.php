<?php

namespace Modules\Payment\Services;

use App\Models\Transaction;
use App\Models\Payment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Exception;

class TransactionService
{
    /**
     * Create a new transaction
     */
    public function createTransaction(array $data): Transaction
    {
        return DB::transaction(function () use ($data) {
            $transaction = Transaction::create([
                'id' => Str::uuid(),
                'transaction_number' => $this->generateTransactionNumber(),
                'transactionable_type' => $data['transactionable_type'],
                'transactionable_id' => $data['transactionable_id'],
                'type' => $data['type'],
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? 'USD',
                'status' => $data['status'] ?? 'pending',
                'transaction_date' => $data['transaction_date'] ?? now(),
                'description' => $data['description'] ?? null,
                'reference_number' => $data['reference_number'] ?? null,
                'notes' => $data['notes'] ?? null,
                'metadata' => $data['metadata'] ?? null,
                'created_by' => Auth::id()
            ]);

            // Log transaction creation
            $this->logTransactionActivity($transaction, 'transaction_created');

            return $transaction;
        });
    }

    /**
     * Update transaction
     */
    public function updateTransaction(Transaction $transaction, array $data): Transaction
    {
        return DB::transaction(function () use ($transaction, $data) {
            $oldStatus = $transaction->status;
            
            $transaction->update($data);
            
            // Log status change if applicable
            if (isset($data['status']) && $data['status'] !== $oldStatus) {
                $this->logTransactionActivity($transaction, 'status_changed', [
                    'old_status' => $oldStatus,
                    'new_status' => $data['status']
                ]);
            }
            
            return $transaction->fresh();
        });
    }

    /**
     * Cancel transaction
     */
    public function cancelTransaction(Transaction $transaction): Transaction
    {
        if (!in_array($transaction->status, ['pending', 'processing', 'partially_paid'])) {
            throw new Exception('Transaction cannot be cancelled in current status: ' . $transaction->status);
        }

        return DB::transaction(function () use ($transaction) {
            // Cancel any pending payments
            $transaction->payments()
                ->whereIn('status', ['pending', 'processing'])
                ->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now(),
                    'cancelled_by' => Auth::id()
                ]);

            // Update transaction status
            $transaction->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancelled_by' => Auth::id()
            ]);

            $this->logTransactionActivity($transaction, 'transaction_cancelled');

            return $transaction;
        });
    }

    /**
     * Get transaction summary
     */
    public function getTransactionSummary(array $filters = []): array
    {
        $query = Transaction::query();
        
        // Apply filters
        $this->applyFilters($query, $filters);
        
        $transactions = $query->get();
        
        return [
            'total_transactions' => $transactions->count(),
            'total_amount' => $transactions->sum('amount'),
            'completed_transactions' => $transactions->where('status', 'completed')->count(),
            'completed_amount' => $transactions->where('status', 'completed')->sum('amount'),
            'pending_transactions' => $transactions->where('status', 'pending')->count(),
            'pending_amount' => $transactions->where('status', 'pending')->sum('amount'),
            'cancelled_transactions' => $transactions->where('status', 'cancelled')->count(),
            'cancelled_amount' => $transactions->where('status', 'cancelled')->sum('amount'),
            'average_transaction_amount' => $transactions->count() > 0 ? $transactions->avg('amount') : 0,
            'status_breakdown' => $transactions->groupBy('status')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('amount')
                ];
            }),
            'type_breakdown' => $transactions->groupBy('type')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => $group->sum('amount')
                ];
            })
        ];
    }

    /**
     * Get transaction analytics
     */
    public function getTransactionAnalytics(array $filters = []): array
    {
        $query = Transaction::query();
        
        // Apply filters
        $this->applyFilters($query, $filters);
        
        // Get daily trends
        $dailyTrends = $query->selectRaw('DATE(transaction_date) as date, count(*) as count, sum(amount) as total_amount, avg(amount) as avg_amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
        
        // Get hourly trends for today
        $hourlyTrends = Transaction::whereDate('transaction_date', today())
            ->selectRaw('HOUR(transaction_date) as hour, count(*) as count, sum(amount) as total_amount')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();
        
        // Get monthly trends
        $monthlyTrends = $query->selectRaw('YEAR(transaction_date) as year, MONTH(transaction_date) as month, count(*) as count, sum(amount) as total_amount')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();
        
        // Get transaction type performance
        $typePerformance = $query->groupBy('type')
            ->selectRaw('type, count(*) as count, sum(amount) as total_amount, avg(amount) as avg_amount')
            ->get();
        
        // Get entity type breakdown
        $entityBreakdown = $query->groupBy('transactionable_type')
            ->selectRaw('transactionable_type, count(*) as count, sum(amount) as total_amount')
            ->get();
        
        return [
            'daily_trends' => $dailyTrends,
            'hourly_trends' => $hourlyTrends,
            'monthly_trends' => $monthlyTrends,
            'type_performance' => $typePerformance,
            'entity_breakdown' => $entityBreakdown,
            'summary' => $this->getTransactionSummary($filters)
        ];
    }

    /**
     * Get transactions by entity
     */
    public function getTransactionsByEntity(string $entityType, string $entityId, array $filters = []): array
    {
        $query = Transaction::where('transactionable_type', $entityType)
            ->where('transactionable_id', $entityId)
            ->with(['payments.paymentMethod', 'createdBy']);
        
        // Apply additional filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        $transactions = $query->orderBy('transaction_date', 'desc')->get();
        
        return [
            'transactions' => $transactions,
            'summary' => [
                'total_count' => $transactions->count(),
                'total_amount' => $transactions->sum('amount'),
                'completed_amount' => $transactions->where('status', 'completed')->sum('amount'),
                'pending_amount' => $transactions->where('status', 'pending')->sum('amount')
            ]
        ];
    }

    /**
     * Reconcile transactions
     */
    public function reconcileTransactions(array $filters = []): array
    {
        $query = Transaction::with(['payments']);
        
        // Apply filters
        $this->applyFilters($query, $filters);
        
        $transactions = $query->get();
        
        $reconciliation = [
            'total_transactions' => $transactions->count(),
            'reconciled_transactions' => 0,
            'discrepancies' => [],
            'summary' => [
                'expected_total' => 0,
                'actual_total' => 0,
                'difference' => 0
            ]
        ];
        
        foreach ($transactions as $transaction) {
            $expectedAmount = $transaction->amount;
            $actualAmount = $transaction->payments()->where('status', 'completed')->sum('amount');
            
            $reconciliation['summary']['expected_total'] += $expectedAmount;
            $reconciliation['summary']['actual_total'] += $actualAmount;
            
            if (abs($expectedAmount - $actualAmount) < 0.01) {
                $reconciliation['reconciled_transactions']++;
            } else {
                $reconciliation['discrepancies'][] = [
                    'transaction_id' => $transaction->id,
                    'transaction_number' => $transaction->transaction_number,
                    'expected_amount' => $expectedAmount,
                    'actual_amount' => $actualAmount,
                    'difference' => $expectedAmount - $actualAmount,
                    'status' => $transaction->status
                ];
            }
        }
        
        $reconciliation['summary']['difference'] = 
            $reconciliation['summary']['expected_total'] - $reconciliation['summary']['actual_total'];
        
        return $reconciliation;
    }

    /**
     * Export transactions
     */
    public function exportTransactions(array $filters = []): array
    {
        $query = Transaction::with(['payments.paymentMethod', 'createdBy', 'transactionable']);
        
        // Apply filters
        $this->applyFilters($query, $filters);
        
        $transactions = $query->orderBy('transaction_date', 'desc')->get();
        
        $exportData = $transactions->map(function ($transaction) {
            return [
                'transaction_number' => $transaction->transaction_number,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'status' => $transaction->status,
                'transaction_date' => $transaction->transaction_date->format('Y-m-d H:i:s'),
                'description' => $transaction->description,
                'reference_number' => $transaction->reference_number,
                'entity_type' => $transaction->transactionable_type,
                'entity_id' => $transaction->transactionable_id,
                'payments_count' => $transaction->payments->count(),
                'total_paid' => $transaction->payments()->where('status', 'completed')->sum('amount'),
                'created_by' => $transaction->createdBy->name ?? 'System',
                'created_at' => $transaction->created_at->format('Y-m-d H:i:s')
            ];
        });
        
        return [
            'data' => $exportData,
            'summary' => [
                'total_records' => $exportData->count(),
                'export_date' => now()->format('Y-m-d H:i:s'),
                'filters_applied' => $filters
            ]
        ];
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters): void
    {
        if (isset($filters['date_from'])) {
            $query->whereDate('transaction_date', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->whereDate('transaction_date', '<=', $filters['date_to']);
        }
        
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
        
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }
        
        if (isset($filters['entity_type'])) {
            $query->where('transactionable_type', $filters['entity_type']);
        }
        
        if (isset($filters['amount_min'])) {
            $query->where('amount', '>=', $filters['amount_min']);
        }
        
        if (isset($filters['amount_max'])) {
            $query->where('amount', '<=', $filters['amount_max']);
        }
    }

    /**
     * Generate unique transaction number
     */
    protected function generateTransactionNumber(): string
    {
        $prefix = 'TXN';
        $date = now()->format('Ymd');
        $sequence = Transaction::whereDate('created_at', today())->count() + 1;
        
        return $prefix . $date . str_pad($sequence, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Log transaction activity
     */
    protected function logTransactionActivity(Transaction $transaction, string $action, array $metadata = []): void
    {
        // Log to audit trail or activity log
        // This can be implemented based on your logging requirements
    }
}
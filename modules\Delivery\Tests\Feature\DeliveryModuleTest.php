<?php

namespace Modules\Delivery\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Models\User;
use App\Models\Branch;
use App\Models\Customer;
use App\Models\Order;
use Modules\Delivery\Models\DeliveryPersonnel;
use Modules\Delivery\Models\DeliveryZone;
use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryReview;
use Modules\Delivery\Models\DeliveryTip;
use Modules\Delivery\Models\DeliveryTracking;
use Modules\Delivery\Services\DeliveryService;
use Carbon\Carbon;

class DeliveryModuleTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $deliveryService;
    protected $user;
    protected $branch;
    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->deliveryService = app(DeliveryService::class);
        $this->user = User::factory()->create();
        $this->branch = Branch::factory()->create();
        $this->customer = Customer::factory()->create();
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_create_delivery_personnel()
    {
        $personnelData = [
            'user_id' => $this->user->id,
            'branch_id' => $this->branch->id,
            'license_number' => 'DL123456',
            'license_expiry_date' => now()->addYears(2)->format('Y-m-d'),
            'vehicle_type' => 'motorcycle',
            'vehicle_model' => 'Honda CB150R',
            'vehicle_plate_number' => 'B1234DEL',
            'phone_number' => '+6281234567890',
            'emergency_contact_name' => 'Emergency Contact',
            'emergency_contact_phone' => '+6281234567891',
            'max_concurrent_deliveries' => 3,
            'working_hours' => [
                ['day' => 'monday', 'start_time' => '09:00', 'end_time' => '18:00'],
                ['day' => 'tuesday', 'start_time' => '09:00', 'end_time' => '18:00'],
            ]
        ];

        $response = $this->postJson('/api/delivery/personnel', $personnelData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'user_id',
                        'branch_id',
                        'license_number',
                        'vehicle_type',
                        'status',
                        'is_available',
                        'rating'
                    ]
                ]);

        $this->assertDatabaseHas('delivery_personnel', [
            'license_number' => 'DL123456',
            'vehicle_type' => 'motorcycle'
        ]);
    }

    /** @test */
    public function it_can_create_delivery_zone()
    {
        $zoneData = [
            'branch_id' => $this->branch->id,
            'name' => 'Test Zone',
            'description' => 'Test delivery zone',
            'polygon_coordinates' => [
                [-6.1744, 106.8227],
                [-6.1744, 106.8427],
                [-6.1944, 106.8427],
                [-6.1944, 106.8227]
            ],
            'delivery_fee' => 15.00,
            'minimum_order_amount' => 50.00,
            'estimated_delivery_time_minutes' => 30,
            'priority' => 1
        ];

        $response = $this->postJson('/api/delivery/zones', $zoneData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'branch_id',
                        'name',
                        'delivery_fee',
                        'polygon_coordinates',
                        'is_active'
                    ]
                ]);

        $this->assertDatabaseHas('delivery_zones', [
            'name' => 'Test Zone',
            'delivery_fee' => 15.00
        ]);
    }

    /** @test */
    public function it_can_assign_delivery_to_personnel()
    {
        $personnel = DeliveryPersonnel::factory()->active()->create([
            'branch_id' => $this->branch->id
        ]);
        
        $zone = DeliveryZone::factory()->active()->create([
            'branch_id' => $this->branch->id
        ]);
        
        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'order_type' => 'delivery'
        ]);

        $assignmentData = [
            'order_id' => $order->id,
            'delivery_personnel_id' => $personnel->id,
            'pickup_latitude' => -6.2,
            'pickup_longitude' => 106.8,
            'pickup_address' => 'Restaurant Address',
            'delivery_latitude' => -6.21,
            'delivery_longitude' => 106.81,
            'delivery_address' => 'Customer Address',
            'customer_phone' => '+6281234567890',
            'customer_name' => 'John Doe'
        ];

        $response = $this->postJson('/api/delivery/assignments', $assignmentData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'order_id',
                        'delivery_personnel_id',
                        'status',
                        'delivery_fee',
                        'estimated_duration_minutes'
                    ]
                ]);

        $this->assertDatabaseHas('delivery_assignments', [
            'order_id' => $order->id,
            'delivery_personnel_id' => $personnel->id,
            'status' => 'assigned'
        ]);
    }

    /** @test */
    public function it_can_auto_assign_delivery()
    {
        $personnel = DeliveryPersonnel::factory()->active()->available()->create([
            'branch_id' => $this->branch->id,
            'current_latitude' => -6.2,
            'current_longitude' => 106.8
        ]);
        
        $zone = DeliveryZone::factory()->active()->create([
            'branch_id' => $this->branch->id
        ]);
        
        $order = Order::factory()->create([
            'branch_id' => $this->branch->id,
            'order_type' => 'delivery'
        ]);

        $assignmentData = [
            'order_id' => $order->id,
            'pickup_latitude' => -6.2,
            'pickup_longitude' => 106.8,
            'pickup_address' => 'Restaurant Address',
            'delivery_latitude' => -6.21,
            'delivery_longitude' => 106.81,
            'delivery_address' => 'Customer Address',
            'customer_phone' => '+6281234567890',
            'customer_name' => 'John Doe'
        ];

        $response = $this->postJson('/api/delivery/assignments/auto-assign', $assignmentData);

        $response->assertStatus(201);
        
        $this->assertDatabaseHas('delivery_assignments', [
            'order_id' => $order->id,
            'delivery_personnel_id' => $personnel->id
        ]);
    }

    /** @test */
    public function it_can_update_delivery_status()
    {
        $personnel = DeliveryPersonnel::factory()->create();
        $assignment = DeliveryAssignment::factory()->create([
            'delivery_personnel_id' => $personnel->id,
            'status' => 'assigned'
        ]);

        $updateData = [
            'status' => 'picked_up',
            'delivery_notes' => 'Package picked up successfully',
            'current_latitude' => -6.2,
            'current_longitude' => 106.8
        ];

        $response = $this->putJson("/api/delivery/assignments/{$assignment->id}/status", $updateData);

        $response->assertStatus(200);
        
        $assignment->refresh();
        $this->assertEquals('picked_up', $assignment->status);
        $this->assertNotNull($assignment->picked_up_at);
    }

    /** @test */
    public function it_can_track_delivery_location()
    {
        $assignment = DeliveryAssignment::factory()->create([
            'status' => 'in_transit'
        ]);

        $trackingData = [
            'latitude' => -6.2,
            'longitude' => 106.8,
            'accuracy' => 5.0,
            'speed' => 25.5,
            'bearing' => 45.0
        ];

        $response = $this->postJson("/api/delivery/assignments/{$assignment->id}/location", $trackingData);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('delivery_tracking', [
            'delivery_assignment_id' => $assignment->id,
            'latitude' => -6.2,
            'longitude' => 106.8
        ]);
    }

    /** @test */
    public function it_can_create_delivery_review()
    {
        $assignment = DeliveryAssignment::factory()->create([
            'status' => 'delivered',
            'delivered_at' => now()->subHours(1)
        ]);
        
        $order = $assignment->order;
        $order->update(['customer_id' => $this->customer->id]);

        $reviewData = [
            'delivery_assignment_id' => $assignment->id,
            'rating' => 5,
            'comment' => 'Excellent delivery service!',
            'review_categories' => ['punctuality', 'professionalism'],
            'is_anonymous' => false
        ];

        $this->actingAs($this->customer, 'customer');
        $response = $this->postJson('/api/delivery/reviews', $reviewData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'delivery_assignment_id',
                        'customer_id',
                        'rating',
                        'comment'
                    ]
                ]);

        $this->assertDatabaseHas('delivery_reviews', [
            'delivery_assignment_id' => $assignment->id,
            'rating' => 5
        ]);
    }

    /** @test */
    public function it_can_create_delivery_tip()
    {
        $assignment = DeliveryAssignment::factory()->create([
            'status' => 'delivered',
            'delivered_at' => now()->subHours(1)
        ]);
        
        $order = $assignment->order;
        $order->update(['customer_id' => $this->customer->id]);

        $tipData = [
            'delivery_assignment_id' => $assignment->id,
            'amount' => 5.00,
            'payment_method' => 'card',
            'transaction_reference' => 'TXN123456',
            'notes' => 'Great service!'
        ];

        $this->actingAs($this->customer, 'customer');
        $response = $this->postJson('/api/delivery/tips', $tipData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'delivery_assignment_id',
                        'customer_id',
                        'amount',
                        'payment_method',
                        'status'
                    ]
                ]);

        $this->assertDatabaseHas('delivery_tips', [
            'delivery_assignment_id' => $assignment->id,
            'amount' => 5.00
        ]);
    }

    /** @test */
    public function it_can_get_delivery_analytics()
    {
        // Create test data
        $personnel = DeliveryPersonnel::factory()->create([
            'branch_id' => $this->branch->id
        ]);
        
        DeliveryAssignment::factory()->count(5)->create([
            'delivery_personnel_id' => $personnel->id,
            'status' => 'delivered',
            'delivered_at' => now()->subDays(1)
        ]);

        $response = $this->getJson('/api/delivery/analytics/stats');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'total_deliveries',
                        'completed_deliveries',
                        'pending_deliveries',
                        'average_delivery_time',
                        'completion_rate'
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_personnel_performance()
    {
        $personnel = DeliveryPersonnel::factory()->create([
            'branch_id' => $this->branch->id,
            'total_deliveries' => 100,
            'successful_deliveries' => 95,
            'rating' => 4.5
        ]);

        $response = $this->getJson("/api/delivery/analytics/personnel/{$personnel->id}/performance");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        'personnel_id',
                        'total_deliveries',
                        'successful_deliveries',
                        'completion_rate',
                        'average_rating',
                        'on_time_rate'
                    ]
                ]);
    }

    /** @test */
    public function it_validates_delivery_zone_coordinates()
    {
        $zoneData = [
            'branch_id' => $this->branch->id,
            'name' => 'Invalid Zone',
            'polygon_coordinates' => [
                [-6.1744, 106.8227],
                [-6.1744, 106.8427] // Only 2 points, minimum 3 required
            ],
            'delivery_fee' => 15.00,
            'minimum_order_amount' => 50.00,
            'estimated_delivery_time_minutes' => 30
        ];

        $response = $this->postJson('/api/delivery/zones', $zoneData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['polygon_coordinates']);
    }

    /** @test */
    public function it_prevents_duplicate_reviews_for_same_delivery()
    {
        $assignment = DeliveryAssignment::factory()->create([
            'status' => 'delivered',
            'delivered_at' => now()->subHours(1)
        ]);
        
        $order = $assignment->order;
        $order->update(['customer_id' => $this->customer->id]);

        // Create first review
        DeliveryReview::factory()->create([
            'delivery_assignment_id' => $assignment->id,
            'customer_id' => $this->customer->id
        ]);

        $reviewData = [
            'delivery_assignment_id' => $assignment->id,
            'rating' => 4,
            'comment' => 'Second review attempt'
        ];

        $this->actingAs($this->customer, 'customer');
        $response = $this->postJson('/api/delivery/reviews', $reviewData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['delivery_assignment_id']);
    }

    /** @test */
    public function it_calculates_delivery_distance_correctly()
    {
        $lat1 = -6.2;
        $lng1 = 106.8;
        $lat2 = -6.21;
        $lng2 = 106.81;

        $distance = $this->deliveryService->calculateDistance($lat1, $lng1, $lat2, $lng2);

        $this->assertIsFloat($distance);
        $this->assertGreaterThan(0, $distance);
        $this->assertLessThan(2, $distance); // Should be less than 2km for this small distance
    }

    /** @test */
    public function it_finds_best_delivery_zone_for_location()
    {
        $zone1 = DeliveryZone::factory()->active()->create([
            'branch_id' => $this->branch->id,
            'polygon_coordinates' => [
                [-6.19, 106.79],
                [-6.19, 106.81],
                [-6.21, 106.81],
                [-6.21, 106.79]
            ],
            'priority' => 1
        ]);

        $zone2 = DeliveryZone::factory()->active()->create([
            'branch_id' => $this->branch->id,
            'polygon_coordinates' => [
                [-6.20, 106.80],
                [-6.20, 106.82],
                [-6.22, 106.82],
                [-6.22, 106.80]
            ],
            'priority' => 2
        ]);

        $bestZone = DeliveryZone::findBestZoneForLocation(-6.205, 106.805, $this->branch->id);

        $this->assertNotNull($bestZone);
        $this->assertEquals($zone2->id, $bestZone->id); // Should pick zone2 due to higher priority
    }

    /** @test */
    public function it_updates_personnel_location()
    {
        $personnel = DeliveryPersonnel::factory()->create();

        $locationData = [
            'latitude' => -6.2,
            'longitude' => 106.8,
            'accuracy' => 5.0
        ];

        $response = $this->putJson("/api/delivery/personnel/{$personnel->id}/location", $locationData);

        $response->assertStatus(200);
        
        $personnel->refresh();
        $this->assertEquals(-6.2, $personnel->current_latitude);
        $this->assertEquals(106.8, $personnel->current_longitude);
        $this->assertNotNull($personnel->last_location_update);
    }

    /** @test */
    public function it_gets_available_personnel_in_radius()
    {
        $personnel1 = DeliveryPersonnel::factory()->active()->available()->create([
            'branch_id' => $this->branch->id,
            'current_latitude' => -6.2,
            'current_longitude' => 106.8
        ]);

        $personnel2 = DeliveryPersonnel::factory()->active()->available()->create([
            'branch_id' => $this->branch->id,
            'current_latitude' => -6.3, // Further away
            'current_longitude' => 106.9
        ]);

        $response = $this->getJson('/api/delivery/personnel/available?' . http_build_query([
            'latitude' => -6.2,
            'longitude' => 106.8,
            'radius' => 5 // 5km radius
        ]));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'user_id',
                            'vehicle_type',
                            'current_latitude',
                            'current_longitude',
                            'distance_km'
                        ]
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData); // Only personnel1 should be in range
        $this->assertEquals($personnel1->id, $responseData[0]['id']);
    }
}
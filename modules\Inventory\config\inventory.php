<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Inventory Management Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Inventory module
    | including stock levels, movement types, and other settings.
    |
    */

    'stock_levels' => [
        'low_stock_threshold_percentage' => 20, // Alert when stock is below 20% of max level
        'critical_stock_threshold_percentage' => 10, // Critical alert when below 10%
        'auto_reorder_enabled' => true,
        'auto_reorder_threshold_percentage' => 15, // Auto-reorder when below 15%
        'default_reorder_quantity_days' => 30, // Default reorder for 30 days supply
    ],

    'movement_types' => [
        'inbound' => [
            'add' => 'Stock Addition',
            'initial_stock' => 'Initial Stock',
            'return' => 'Customer Return',
            'transfer_in' => 'Transfer In',
            'adjustment_in' => 'Positive Adjustment',
        ],
        'outbound' => [
            'subtract' => 'Stock Reduction',
            'waste' => 'Waste/Spoilage',
            'transfer_out' => 'Transfer Out',
            'consumption' => 'Recipe Consumption',
            'adjustment_out' => 'Negative Adjustment',
        ],
    ],

    'log_actions' => [
        'stock_update' => 'Stock Updated',
        'stock_add' => 'Stock Added',
        'stock_subtract' => 'Stock Subtracted',
        'low_stock_alert' => 'Low Stock Alert',
        'reorder_suggestion' => 'Reorder Suggested',
        'inventory_count' => 'Physical Count',
        'waste_recorded' => 'Waste Recorded',
        'transfer' => 'Stock Transfer',
        'adjustment' => 'Stock Adjustment',
    ],

    'purchase_order' => [
        'statuses' => [
            'draft' => 'Draft',
            'pending' => 'Pending Approval',
            'approved' => 'Approved',
            'sent' => 'Sent to Supplier',
            'partially_received' => 'Partially Received',
            'received' => 'Fully Received',
            'cancelled' => 'Cancelled',
            'rejected' => 'Rejected',
        ],
        'priorities' => [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ],
        'auto_generate_po_number' => true,
        'po_number_prefix' => 'PO',
        'po_number_length' => 8,
        'default_payment_terms' => 30, // days
        'require_approval_above_amount' => 1000.00,
    ],

    'supplier' => [
        'categories' => [
            'food_beverage' => 'Food & Beverage',
            'packaging' => 'Packaging',
            'cleaning' => 'Cleaning Supplies',
            'equipment' => 'Equipment',
            'maintenance' => 'Maintenance',
            'office' => 'Office Supplies',
            'other' => 'Other',
        ],
        'default_payment_terms' => 30, // days
        'default_credit_limit' => 5000.00,
        'rating_scale' => [
            1 => 'Poor',
            2 => 'Fair',
            3 => 'Good',
            4 => 'Very Good',
            5 => 'Excellent',
        ],
    ],

    'units' => [
        'weight' => [
            'kg' => 'Kilogram',
            'g' => 'Gram',
            'lb' => 'Pound',
            'oz' => 'Ounce',
        ],
        'volume' => [
            'l' => 'Liter',
            'ml' => 'Milliliter',
            'gal' => 'Gallon',
            'qt' => 'Quart',
            'pt' => 'Pint',
            'cup' => 'Cup',
            'fl_oz' => 'Fluid Ounce',
        ],
        'count' => [
            'pcs' => 'Pieces',
            'box' => 'Box',
            'case' => 'Case',
            'pack' => 'Pack',
            'dozen' => 'Dozen',
        ],
        'length' => [
            'm' => 'Meter',
            'cm' => 'Centimeter',
            'ft' => 'Foot',
            'in' => 'Inch',
        ],
    ],

    'categories' => [
        'ingredients' => 'Ingredients',
        'beverages' => 'Beverages',
        'packaging' => 'Packaging',
        'cleaning' => 'Cleaning Supplies',
        'equipment' => 'Equipment',
        'office' => 'Office Supplies',
        'maintenance' => 'Maintenance',
        'other' => 'Other',
    ],

    'waste_reasons' => [
        'expired' => 'Expired',
        'damaged' => 'Damaged',
        'spoiled' => 'Spoiled',
        'contaminated' => 'Contaminated',
        'overcooked' => 'Overcooked',
        'dropped' => 'Dropped/Spilled',
        'quality_issue' => 'Quality Issue',
        'customer_return' => 'Customer Return',
        'other' => 'Other',
    ],

    'alerts' => [
        'low_stock_enabled' => true,
        'expiry_alerts_enabled' => true,
        'expiry_alert_days' => 7, // Alert 7 days before expiry
        'overstock_alerts_enabled' => true,
        'overstock_threshold_percentage' => 150, // Alert when stock is 150% above max level
    ],

    'analytics' => [
        'default_period_days' => 30,
        'turnover_calculation_period' => 90, // days
        'abc_analysis_enabled' => true,
        'seasonal_analysis_enabled' => true,
    ],

    'export' => [
        'formats' => ['csv', 'excel', 'pdf'],
        'default_format' => 'csv',
        'max_export_records' => 10000,
    ],

    'import' => [
        'allowed_formats' => ['csv', 'xlsx'],
        'max_file_size' => 5120, // KB
        'batch_size' => 100, // Process in batches of 100 records
        'required_columns' => [
            'name',
            'sku',
            'category',
            'unit',
            'current_stock',
            'minimum_stock_level',
            'cost_per_unit',
        ],
    ],

    'permissions' => [
        'view_inventory' => 'View Inventory',
        'create_inventory' => 'Create Inventory Items',
        'edit_inventory' => 'Edit Inventory Items',
        'delete_inventory' => 'Delete Inventory Items',
        'update_stock' => 'Update Stock Levels',
        'view_suppliers' => 'View Suppliers',
        'manage_suppliers' => 'Manage Suppliers',
        'view_purchase_orders' => 'View Purchase Orders',
        'create_purchase_orders' => 'Create Purchase Orders',
        'approve_purchase_orders' => 'Approve Purchase Orders',
        'receive_purchase_orders' => 'Receive Purchase Orders',
        'view_inventory_reports' => 'View Inventory Reports',
        'export_inventory_data' => 'Export Inventory Data',
        'import_inventory_data' => 'Import Inventory Data',
    ],

    'cache' => [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'keys' => [
            'low_stock_items' => 'inventory:low_stock',
            'dashboard_stats' => 'inventory:dashboard_stats',
            'supplier_performance' => 'inventory:supplier_performance',
        ],
    ],
];
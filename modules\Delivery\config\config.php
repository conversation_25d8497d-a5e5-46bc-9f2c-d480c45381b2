<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Delivery Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Delivery module.
    | You can customize various aspects of delivery management here.
    |
    */

    'name' => 'Delivery',

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        'delivery_fee' => 5.00,
        'minimum_order_amount' => 15.00,
        'estimated_delivery_time_minutes' => 30,
        'max_delivery_radius_km' => 15,
        'personnel_commission_rate' => 0.70, // 70% of delivery fee goes to personnel
    ],

    /*
    |--------------------------------------------------------------------------
    | Vehicle Types
    |--------------------------------------------------------------------------
    */
    'vehicle_types' => [
        'motorcycle' => [
            'name' => 'Motorcycle',
            'max_concurrent_deliveries' => 3,
            'average_speed_kmh' => 35,
        ],
        'bicycle' => [
            'name' => 'Bicycle',
            'max_concurrent_deliveries' => 2,
            'average_speed_kmh' => 15,
        ],
        'car' => [
            'name' => 'Car',
            'max_concurrent_deliveries' => 5,
            'average_speed_kmh' => 30,
        ],
        'scooter' => [
            'name' => 'Scooter',
            'max_concurrent_deliveries' => 2,
            'average_speed_kmh' => 25,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Status Configurations
    |--------------------------------------------------------------------------
    */
    'statuses' => [
        'personnel' => [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'on_delivery' => 'On Delivery',
            'break' => 'On Break',
            'suspended' => 'Suspended',
        ],
        'assignment' => [
            'assigned' => 'Assigned',
            'picked_up' => 'Picked Up',
            'in_transit' => 'In Transit',
            'delivered' => 'Delivered',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled',
        ],
        'tip' => [
            'pending' => 'Pending',
            'paid' => 'Paid',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Methods
    |--------------------------------------------------------------------------
    */
    'payment_methods' => [
        'cash' => 'Cash',
        'card' => 'Credit/Debit Card',
        'digital_wallet' => 'Digital Wallet',
        'app_credit' => 'App Credit',
    ],

    /*
    |--------------------------------------------------------------------------
    | Review Categories
    |--------------------------------------------------------------------------
    */
    'review_categories' => [
        'punctuality' => 'Punctuality',
        'professionalism' => 'Professionalism',
        'food_condition' => 'Food Condition',
        'communication' => 'Communication',
        'politeness' => 'Politeness',
        'vehicle_cleanliness' => 'Vehicle Cleanliness',
    ],

    /*
    |--------------------------------------------------------------------------
    | Time Limits
    |--------------------------------------------------------------------------
    */
    'time_limits' => [
        'review_window_days' => 30, // Days after delivery to allow reviews
        'tip_window_days' => 7, // Days after delivery to allow tips
        'assignment_timeout_minutes' => 15, // Minutes before auto-cancelling unaccepted assignments
        'location_update_interval_seconds' => 30, // How often to update location during delivery
    ],

    /*
    |--------------------------------------------------------------------------
    | Distance and Location Settings
    |--------------------------------------------------------------------------
    */
    'location' => [
        'coordinate_precision' => 6, // Decimal places for lat/lng
        'minimum_accuracy_meters' => 50, // Minimum GPS accuracy required
        'geofence_radius_meters' => 100, // Radius for delivery completion detection
        'speed_limit_kmh' => 80, // Maximum reasonable speed for filtering GPS errors
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Metrics
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'minimum_rating' => 3.0, // Minimum average rating to maintain active status
        'minimum_completion_rate' => 0.85, // 85% minimum completion rate
        'maximum_late_deliveries_percentage' => 0.20, // 20% maximum late deliveries
        'rating_weight_recent_deliveries' => 0.7, // Weight for recent deliveries in rating calculation
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'assignment_timeout_warning_minutes' => 5, // Warn personnel before auto-cancellation
        'customer_eta_update_threshold_minutes' => 10, // Update customer if ETA changes by this much
        'personnel_break_reminder_hours' => 4, // Remind personnel to take breaks
    ],

    /*
    |--------------------------------------------------------------------------
    | API Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limits' => [
        'location_updates_per_minute' => 10,
        'status_updates_per_minute' => 5,
        'review_submissions_per_day' => 20,
        'tip_submissions_per_day' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Settings
    |--------------------------------------------------------------------------
    */
    'uploads' => [
        'delivery_proof' => [
            'max_size_mb' => 5,
            'allowed_types' => ['jpg', 'jpeg', 'png', 'pdf'],
            'storage_disk' => 'public',
            'storage_path' => 'delivery/proof',
        ],
        'personnel_documents' => [
            'max_size_mb' => 10,
            'allowed_types' => ['jpg', 'jpeg', 'png', 'pdf'],
            'storage_disk' => 'private',
            'storage_path' => 'delivery/personnel/documents',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'maps' => [
            'provider' => env('DELIVERY_MAPS_PROVIDER', 'google'), // google, mapbox, osm
            'api_key' => env('DELIVERY_MAPS_API_KEY'),
        ],
        'sms' => [
            'provider' => env('DELIVERY_SMS_PROVIDER', 'twilio'),
            'enabled' => env('DELIVERY_SMS_ENABLED', true),
        ],
        'push_notifications' => [
            'provider' => env('DELIVERY_PUSH_PROVIDER', 'fcm'),
            'enabled' => env('DELIVERY_PUSH_ENABLED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'require_photo_proof' => true,
        'require_signature_proof' => false,
        'encrypt_location_data' => true,
        'anonymize_reviews_after_days' => 365,
        'personnel_background_check_required' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Rules
    |--------------------------------------------------------------------------
    */
    'business_rules' => [
        'allow_cash_tips' => true,
        'allow_multiple_zones_per_personnel' => true,
        'require_zone_assignment' => false,
        'auto_assign_enabled' => true,
        'peak_hour_multiplier' => 1.3, // Increase delivery time estimate during peak hours
        'weather_delay_multiplier' => 1.2, // Increase delivery time during bad weather
    ],
];
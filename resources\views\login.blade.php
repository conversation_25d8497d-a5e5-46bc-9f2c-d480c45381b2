<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ __('Login') }} - {{ config('app.name', 'EPSIS') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .animate-pulse-slow {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* Input focus styles */
        .input-focus:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        /* Button hover effects */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        
        .btn-primary:active {
            transform: translateY(0);
        }
        
        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Background pattern */
        .bg-pattern {
            background-image: 
                radial-gradient(circle at 25px 25px, rgba(59, 130, 246, 0.1) 2px, transparent 0),
                radial-gradient(circle at 75px 75px, rgba(139, 92, 246, 0.1) 2px, transparent 0);
            background-size: 100px 100px;
        }
        
        /* RTL Support */
        [dir="rtl"] .rtl\:text-right {
            text-align: right;
        }
        
        [dir="rtl"] .rtl\:mr-2 {
            margin-right: 0.5rem;
        }
        
        [dir="rtl"] .rtl\:ml-2 {
            margin-left: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-50 bg-pattern min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <!-- Language Switcher -->
    <div class="absolute top-4 right-4 rtl:right-auto rtl:left-4">
        <div class="relative">
            <button id="languageToggle" class="flex items-center space-x-2 rtl:space-x-reverse bg-white rounded-lg px-3 py-2 shadow-sm border border-gray-200 hover:bg-gray-50 transition-colors">
                <i class="fas fa-globe text-gray-600"></i>
                <span class="text-sm font-medium text-gray-700" id="currentLanguage">
                    {{ app()->getLocale() === 'ar' ? 'العربية' : 'English' }}
                </span>
                <i class="fas fa-chevron-down text-xs text-gray-500"></i>
            </button>
            
            <div id="languageDropdown" class="hidden absolute top-full mt-1 right-0 rtl:right-auto rtl:left-0 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-[120px] z-10">
                <a href="?lang=en" class="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <span class="w-4 h-4 rounded-sm bg-blue-500"></span>
                    <span>English</span>
                </a>
                <a href="?lang=ar" class="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <span class="w-4 h-4 rounded-sm bg-green-500"></span>
                    <span>العربية</span>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Main Login Container -->
    <div class="max-w-md w-full space-y-8 animate-fade-in-up">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto h-16 w-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 animate-pulse-slow">
                <i class="fas fa-utensils text-white text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900">{{ __('Welcome Back') }}</h2>
            <p class="mt-2 text-sm text-gray-600">{{ __('Sign in to your restaurant dashboard') }}</p>
        </div>
        
        <!-- Login Form -->
        <form class="mt-8 space-y-6" action="{{ route('login') }}" method="POST" id="loginForm">
            @csrf
            
            <!-- Hidden field to remember form state -->
            <input type="hidden" name="remember_form" value="1">
            
            <div class="space-y-4">
                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('Email Address') }}
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 rtl:left-auto rtl:right-0 pl-3 rtl:pl-0 rtl:pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            autocomplete="email" 
                            required 
                            value="{{ old('email') }}"
                            class="input-focus block w-full pl-10 rtl:pl-3 rtl:pr-10 pr-3 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 @error('email') border-red-500 @enderror" 
                            placeholder="{{ __('Enter your email') }}"
                        >
                    </div>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1 rtl:mr-0 rtl:ml-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>
                
                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('Password') }}
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 rtl:left-auto rtl:right-0 pl-3 rtl:pl-0 rtl:pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            autocomplete="current-password" 
                            required 
                            class="input-focus block w-full pl-10 rtl:pl-3 rtl:pr-10 pr-10 rtl:pr-3 rtl:pl-10 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 @error('password') border-red-500 @enderror" 
                            placeholder="{{ __('Enter your password') }}"
                        >
                        <button 
                            type="button" 
                            id="togglePassword" 
                            class="absolute inset-y-0 right-0 rtl:right-auto rtl:left-0 pr-3 rtl:pr-0 rtl:pl-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                    @error('password')
                        <p class="mt-1 text-sm text-red-600 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1 rtl:mr-0 rtl:ml-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            </div>
            
            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input 
                        id="remember" 
                        name="remember" 
                        type="checkbox" 
                        {{ old('remember') ? 'checked' : '' }}
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
                    >
                    <label for="remember" class="ml-2 rtl:ml-0 rtl:mr-2 block text-sm text-gray-700">
                        {{ __('Remember me') }}
                    </label>
                </div>
                
                <div class="text-sm">
                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                        {{ __('Forgot password?') }}
                    </a>
                </div>
            </div>
            
            <!-- Submit Button -->
            <div>
                <button 
                    type="submit" 
                    id="submitButton"
                    class="btn-primary group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <span id="buttonText">{{ __('Sign In') }}</span>
                    <div id="loadingSpinner" class="hidden spinner ml-2 rtl:ml-0 rtl:mr-2"></div>
                </button>
            </div>
            
            <!-- General Error Messages -->
            @if($errors->any() && !$errors->has('email') && !$errors->has('password'))
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3 rtl:ml-0 rtl:mr-3">
                            <h3 class="text-sm font-medium text-red-800">
                                {{ __('There were some problems with your input.') }}
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </form>
        
        <!-- Footer -->
        <div class="text-center">
            <p class="text-xs text-gray-500">
                {{ __('© :year :app_name. All rights reserved.', ['year' => date('Y'), 'app_name' => config('app.name', 'EPSIS')]) }}
            </p>
        </div>
    </div>
    
    <script>
        // Language switcher functionality
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('languageToggle');
            const languageDropdown = document.getElementById('languageDropdown');
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            const loginForm = document.getElementById('loginForm');
            const submitButton = document.getElementById('submitButton');
            const buttonText = document.getElementById('buttonText');
            const loadingSpinner = document.getElementById('loadingSpinner');
            
            // Language dropdown toggle
            languageToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                languageDropdown.classList.toggle('hidden');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                languageDropdown.classList.add('hidden');
            });
            
            // Password visibility toggle
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                if (type === 'password') {
                    passwordIcon.classList.remove('fa-eye-slash');
                    passwordIcon.classList.add('fa-eye');
                } else {
                    passwordIcon.classList.remove('fa-eye');
                    passwordIcon.classList.add('fa-eye-slash');
                }
            });
            
            // Form submission with loading state
            loginForm.addEventListener('submit', function() {
                submitButton.disabled = true;
                buttonText.textContent = 'Signing In...';
                loadingSpinner.classList.remove('hidden');
            });
            
            // Auto-focus on email field if empty
            const emailInput = document.getElementById('email');
            if (!emailInput.value) {
                emailInput.focus();
            }
            
            // Remember form state in localStorage
            const rememberCheckbox = document.getElementById('remember');
            const savedRememberState = localStorage.getItem('loginRememberState');
            
            if (savedRememberState === 'true') {
                rememberCheckbox.checked = true;
            }
            
            rememberCheckbox.addEventListener('change', function() {
                localStorage.setItem('loginRememberState', this.checked);
            });
            
            // Save email in localStorage if remember is checked
            emailInput.addEventListener('input', function() {
                if (rememberCheckbox.checked) {
                    localStorage.setItem('loginEmail', this.value);
                }
            });
            
            // Load saved email if remember was checked
            const savedEmail = localStorage.getItem('loginEmail');
            if (savedEmail && rememberCheckbox.checked) {
                emailInput.value = savedEmail;
            }
            
            // Clear saved data if remember is unchecked
            rememberCheckbox.addEventListener('change', function() {
                if (!this.checked) {
                    localStorage.removeItem('loginEmail');
                }
            });
            
            // Enhanced form validation
            const inputs = [emailInput, passwordInput];
            
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });
                
                input.addEventListener('input', function() {
                    clearFieldError(this);
                });
            });
            
            function validateField(field) {
                const value = field.value.trim();
                const fieldName = field.name;
                
                if (fieldName === 'email') {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        showFieldError(field, 'Please enter a valid email address');
                        return false;
                    }
                }
                
                if (fieldName === 'password' && value.length < 6) {
                    showFieldError(field, 'Password must be at least 6 characters');
                    return false;
                }
                
                clearFieldError(field);
                return true;
            }
            
            function showFieldError(field, message) {
                field.classList.add('border-red-500');
                field.classList.remove('border-gray-300');
                
                // Remove existing error message
                const existingError = field.parentNode.querySelector('.field-error');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add new error message
                const errorElement = document.createElement('p');
                errorElement.className = 'field-error mt-1 text-sm text-red-600 flex items-center';
                errorElement.innerHTML = `<i class="fas fa-exclamation-circle mr-1 rtl:mr-0 rtl:ml-1"></i>${message}`;
                field.parentNode.appendChild(errorElement);
            }
            
            function clearFieldError(field) {
                field.classList.remove('border-red-500');
                field.classList.add('border-gray-300');
                
                const errorElement = field.parentNode.querySelector('.field-error');
                if (errorElement) {
                    errorElement.remove();
                }
            }
            
            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Enter key to submit form
                if (e.key === 'Enter' && !submitButton.disabled) {
                    loginForm.submit();
                }
                
                // Escape key to clear form
                if (e.key === 'Escape') {
                    emailInput.value = '';
                    passwordInput.value = '';
                    emailInput.focus();
                }
            });
        });
    </script>
</body>
</html>
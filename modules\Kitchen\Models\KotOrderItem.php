<?php

namespace Modules\Kitchen\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\MenuItem;
use App\Models\OrderItem;

class KotOrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'kot_order_id',
        'order_item_id',
        'menu_item_id',
        'quantity',
        'status',
        'special_instructions',
        'modifications',
        'prep_time_minutes',
        'started_at',
        'completed_at',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'modifications' => 'array',
            'quantity' => 'integer',
            'prep_time_minutes' => 'integer',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
        ];
    }

    // Relationships
    public function kotOrder()
    {
        return $this->belongsTo(KotOrder::class);
    }

    public function orderItem()
    {
        return $this->belongsTo(OrderItem::class);
    }

    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Helper methods
    public function getElapsedTime()
    {
        if (!$this->started_at) {
            return 0;
        }

        $endTime = $this->completed_at ?? now();
        return $this->started_at->diffInMinutes($endTime);
    }

    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    public function isPreparing()
    {
        return $this->status === 'preparing';
    }

    public function isPending()
    {
        return $this->status === 'pending';
    }

    public function markAsStarted()
    {
        $this->update([
            'status' => 'preparing',
            'started_at' => now()
        ]);
    }

    public function markAsCompleted()
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now()
        ]);
    }

    public function getDisplayName()
    {
        return $this->menuItem->name ?? 'Unknown Item';
    }

    public function getModificationsText()
    {
        if (!$this->modifications || empty($this->modifications)) {
            return '';
        }

        return implode(', ', $this->modifications);
    }

    public function getFullInstructions()
    {
        $instructions = [];
        
        if ($this->special_instructions) {
            $instructions[] = $this->special_instructions;
        }
        
        if ($this->getModificationsText()) {
            $instructions[] = 'Modifications: ' . $this->getModificationsText();
        }
        
        return implode(' | ', $instructions);
    }
}
@extends('docs.layout')

@section('title', 'Menu Module Documentation')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Module Header -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-orange-500 to-red-600 p-3 rounded-lg mr-4">
                        <i class="fas fa-utensils text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Menu Module</h1>
                        <p class="text-gray-600 mt-1">Complete menu management system with categories, items, and branch-specific configurations</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="bg-gradient-to-r from-orange-100 to-red-100 px-4 py-2 rounded-lg">
                        <span class="text-orange-800 font-semibold">v2.1.0</span>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                        <div class="flex items-center">
                            <i class="fas fa-globe text-blue-600 text-xl mr-3"></i>
                            <div>
                                <p class="text-blue-800 font-semibold">85 Total Endpoints</p>
                            <p class="text-blue-600 text-sm">API & Web Routes</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                        <div class="flex items-center">
                            <i class="fas fa-unlock text-green-600 text-xl mr-3"></i>
                            <div>
                                <p class="text-green-800 font-semibold">19 Public Routes</p>
                            <p class="text-green-600 text-sm">No Auth Required</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-red-50 to-red-100 p-4 rounded-lg border border-red-200">
                        <div class="flex items-center">
                            <i class="fas fa-lock text-red-600 text-xl mr-3"></i>
                            <div>
                                <p class="text-red-800 font-semibold">66 Protected Routes</p>
                            <p class="text-red-600 text-sm">Auth Required</p>
                            </div>
                        </div>
                    </div>
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                    <div class="flex items-center">
                        <i class="fas fa-layer-group text-purple-600 text-xl mr-3"></i>
                        <div>
                            <p class="text-purple-800 font-semibold">6 Categories</p>
                            <p class="text-purple-600 text-sm">Menu Features</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="bg-white rounded-lg shadow p-4 mb-8 border border-gray-100">
            <div class="flex items-center">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <input type="text" id="endpointSearch" placeholder="Search endpoints, methods, or descriptions..." 
                       class="flex-1 border-0 focus:ring-0 focus:outline-none text-gray-700 placeholder-gray-400">
            </div>
        </div>

        <!-- Public Routes Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-globe text-green-600 mr-3"></i>
                Public Routes
                <span class="ml-3 bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded">No Auth Required</span>
            </h2>

            <!-- Get Public Menu Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/branches/{branchId}/menu</h3>
                            <span class="ml-3 text-gray-600">Get public menu for a specific branch</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Path Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branchId: 1 (Branch ID)</code></pre>
                            <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>?category_id=1&available_only=true</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Appetizers",
        "description": "Start your meal right",
        "items": [
          {
            "id": 1,
            "name": "Caesar Salad",
            "description": "Fresh romaine lettuce",
            "price": 12.99,
            "is_available": true
          }
        ]
      }
    ]
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves the complete menu structure for a specific branch, including categories and available items. This endpoint is publicly accessible.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Categories Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/categories</h3>
                            <span class="ml-3 text-gray-600">Get public menu categories</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Query Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>?active_only=true</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Appetizers",
      "description": "Start your meal right",
      "image_url": "https://example.com/appetizers.jpg",
      "sort_order": 1
    }
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves all active menu categories for public display.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Items Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/items</h3>
                            <span class="ml-3 text-gray-600">Get public menu items</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Query Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>?category_id=1&branch_id=1&available_only=true</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Caesar Salad",
      "description": "Fresh romaine lettuce with parmesan",
      "price": 12.99,
      "image_url": "https://example.com/caesar.jpg",
      "category": {
        "id": 1,
        "name": "Appetizers"
      },
      "is_available": true
    }
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves all available menu items for public display with optional filtering.</p>
                    </div>
                </div>
            </div>

            <!-- Search Public Menu Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/items/search</h3>
                            <span class="ml-3 text-gray-600">Search public menu items</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Query Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>?q=caesar&branch_id=1&category_id=1</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Caesar Salad",
        "description": "Fresh romaine lettuce",
        "price": 12.99,
        "category": "Appetizers",
        "is_available": true
      }
    ],
    "total": 1
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Searches menu items by name or description for public display.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Featured Items Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/items/featured</h3>
                            <span class="ml-3 text-gray-600">Get featured menu items</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Query Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>?limit=10&branch_id=1</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Signature Burger",
      "description": "Our most popular burger",
      "price": 15.99,
      "is_featured": true,
      "category": "Main Course"
    }
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves featured menu items for promotional display.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Category Items Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/categories/{categoryId}/items</h3>
                            <span class="ml-3 text-gray-600">Get items by category</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Path Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>categoryId: 1 (Category ID)</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Caesar Salad",
      "description": "Fresh romaine lettuce",
      "price": 12.99,
      "is_available": true
    }
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves all items belonging to a specific category.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Menu Item Variants Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/items/{menuItemId}/variants</h3>
                            <span class="ml-3 text-gray-600">Get menu item variants</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Path Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>menuItemId: 1 (Menu Item ID)</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Large",
      "price_modifier": 3.00,
      "is_available": true
    }
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves all variants for a specific menu item.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Menu Item Addons Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/items/{menuItemId}/addons</h3>
                            <span class="ml-3 text-gray-600">Get menu item addons</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Path Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>menuItemId: 1 (Menu Item ID)</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Extra Cheese",
      "price": 2.50,
      "is_available": true
    }
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves all addons available for a specific menu item.</p>
                    </div>
                </div>
            </div>

            <!-- Check Public Availability Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/items/{menuItemId}/check-availability</h3>
                            <span class="ml-3 text-gray-600">Check item availability</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Path Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>menuItemId: 1 (Menu Item ID)</code></pre>
                            <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "branch_id": 1,
  "quantity": 2
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "is_available": true,
    "quantity_available": 10,
    "estimated_wait_time": 15
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Checks real-time availability of a menu item for a specific branch.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Branch Price Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/items/{menuItemId}/branches/{branchId}/price</h3>
                            <span class="ml-3 text-gray-600">Get branch-specific price</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Path Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>menuItemId: 1 (Menu Item ID)
branchId: 1 (Branch ID)</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "base_price": 12.99,
    "branch_price": 13.99,
    "currency": "USD"
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Gets the specific price for a menu item at a particular branch.</p>
                    </div>
                </div>
            </div>

            <!-- Get Public Branch Menu Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/public/branches/{branchId}/menu</h3>
                            <span class="ml-3 text-gray-600">Get branch menu</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Path Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branchId: 1 (Branch ID)</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "branch": {
      "id": 1,
      "name": "Downtown Branch"
    },
    "categories": [
      {
        "id": 1,
        "name": "Appetizers",
        "items": [...]
      }
    ]
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves the complete menu for a specific branch.</p>
                    </div>
                </div>
            </div>

            <!-- Get Dietary Options Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/dietary-options</h3>
                            <span class="ml-3 text-gray-600">Get dietary options</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">No Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>No parameters required</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    "Vegetarian",
    "Vegan",
    "Gluten-Free",
    "Dairy-Free",
    "Keto"
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves all available dietary options for menu filtering.</p>
                    </div>
                </div>
            </div>

            <!-- Get Allergens Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/menu/allergens</h3>
                            <span class="ml-3 text-gray-600">Get allergen information</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">No Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>No parameters required</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    "Nuts",
    "Dairy",
    "Gluten",
    "Shellfish",
    "Eggs"
  ]
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves all allergen types for menu item labeling.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Protected Routes Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-lock text-red-600 mr-3"></i>
                Protected Routes
                <span class="ml-3 bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded">Auth Required</span>
            </h2>

            <!-- Menu Categories Management -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-list text-purple-600 mr-2"></i>
                    Menu Categories
                </h3>

                <!-- Get Categories Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/categories</h3>
                                <span class="ml-3 text-gray-600">Get all menu categories</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Appetizers",
      "description": "Start your meal right",
      "image_url": "https://example.com/appetizers.jpg",
      "is_active": true,
      "sort_order": 1,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all menu categories with management access. Includes inactive categories for administrative purposes.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Category Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/categories</h3>
                                <span class="ml-3 text-gray-600">Create new menu category</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Desserts",
  "description": "Sweet endings to your meal",
  "image_url": "https://example.com/desserts.jpg",
  "is_active": true,
  "sort_order": 5
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Category created successfully",
  "data": {
    "id": 5,
    "name": "Desserts",
    "description": "Sweet endings to your meal",
    "image_url": "https://example.com/desserts.jpg",
    "is_active": true,
    "sort_order": 5,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new menu category. Name is required, other fields are optional.</p>
                        </div>
                    </div>
                </div>

                <!-- Get Single Category Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/categories/{id}</h3>
                                <span class="ml-3 text-gray-600">Get single category</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Category ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Appetizers",
    "description": "Start your meal right",
    "image_url": "https://example.com/appetizers.jpg",
    "is_active": true,
    "sort_order": 1,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves a single category by ID with full details.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Category Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/categories/{id}</h3>
                                <span class="ml-3 text-gray-600">Update category</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Updated Appetizers",
  "description": "Updated description",
  "is_active": false
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Category updated successfully",
  "data": {
    "id": 1,
    "name": "Updated Appetizers",
    "description": "Updated description",
    "is_active": false,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates an existing category. Only provided fields will be updated.</p>
                        </div>
                    </div>
                </div>

                <!-- Delete Category Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/categories/{id}</h3>
                                <span class="ml-3 text-gray-600">Delete category</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Category ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Category deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Permanently deletes a category. Cannot be undone.</p>
                        </div>
                    </div>
                </div>

                <!-- Get Category with Items Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/categories/{id}/with-items</h3>
                                <span class="ml-3 text-gray-600">Get category with items</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Category ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Appetizers",
    "description": "Start your meal right",
    "items": [
      {
        "id": 1,
        "name": "Caesar Salad",
        "price": 12.99,
        "is_active": true
      }
    ]
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves a category with all its associated menu items.</p>
                        </div>
                    </div>
                </div>

                <!-- Get Items by Category Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/categories/{categoryId}/items</h3>
                                <span class="ml-3 text-gray-600">Get items by category</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>categoryId: 1 (Category ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Caesar Salad",
      "description": "Fresh romaine lettuce",
      "price": 12.99,
      "category_id": 1,
      "is_active": true
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all menu items belonging to a specific category.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Menu Items Section -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Menu Items</h3>

                <!-- Menu Item Addon Management -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-plus-circle text-purple-600 mr-2"></i>
                        Menu Item Addon Management
                    </h3>

                    <!-- Get All Addons for a Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/addons</h3>
                                    <span class="ml-3 text-gray-600">Get all addons for a menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Extra Cheese",
      "price": 1.50,
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    {
      "id": 2,
      "name": "Bacon Strips",
      "price": 2.00,
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ]
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves all addons associated with a specific menu item.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Attach Addons to Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/addons</h3>
                                    <span class="ml-3 text-gray-600">Attach addons to a menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "addons": [1, 2] // Array of addon IDs to attach
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Addons attached successfully"
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Attaches one or more existing addons to a specific menu item.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Detach Addons from Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/addons/{addonId}</h3>
                                    <span class="ml-3 text-gray-600">Detach addon from a menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)
addonId: 1 (Addon ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Addon detached successfully"
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Detaches a specific addon from a menu item.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Update Addon for Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/addons/{addonId}</h3>
                                    <span class="ml-3 text-gray-600">Update addon for a menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)
addonId: 1 (Addon ID)</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "price": 1.75,
  "is_active": false
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Addon updated successfully",
  "data": {
    "id": 1,
    "name": "Extra Cheese",
    "price": 1.75,
    "is_active": false,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Updates an existing addon for a specific menu item. Only provided fields will be updated.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Menu Item Variant Management -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-tags text-purple-600 mr-2"></i>
                        Menu Item Variant Management
                    </h3>

                    <!-- Get All Variants for a Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/variants</h3>
                                    <span class="ml-3 text-gray-600">Get all variants for a menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Small",
      "price_offset": 0.00,
      "menu_item_id": 1,
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    {
      "id": 2,
      "name": "Large",
      "price_offset": 2.50,
      "menu_item_id": 1,
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ]
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves all variants associated with a specific menu item.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Create Menu Item Variant Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/variants</h3>
                                    <span class="ml-3 text-gray-600">Create new menu item variant</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Extra Large",
  "price_offset": 4.00,
  "is_active": true
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item variant created successfully",
  "data": {
    "id": 3,
    "name": "Extra Large",
    "price_offset": 4.00,
    "menu_item_id": 1,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Creates a new variant for a specific menu item. Name and price_offset are required.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Get Single Menu Item Variant Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/variants/{variantId}</h3>
                                    <span class="ml-3 text-gray-600">Get single menu item variant</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)
variantId: 1 (Variant ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Small",
    "price_offset": 0.00,
    "menu_item_id": 1,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves a single menu item variant by ID for a specific menu item.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Update Menu Item Variant Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/variants/{variantId}</h3>
                                    <span class="ml-3 text-gray-600">Update menu item variant</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)
variantId: 1 (Variant ID)</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Medium",
  "price_offset": 1.00,
  "is_active": false
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item variant updated successfully",
  "data": {
    "id": 1,
    "name": "Medium",
    "price_offset": 1.00,
    "menu_item_id": 1,
    "is_active": false,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Updates an existing menu item variant. Only provided fields will be updated.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Menu Item Variant Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/variants/{variantId}</h3>
                                    <span class="ml-3 text-gray-600">Delete menu item variant</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)
variantId: 1 (Variant ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item variant deleted successfully"
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Permanently deletes a menu item variant. Cannot be undone.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Menu Item Availability Management -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-calendar-check text-purple-600 mr-2"></i>
                        Menu Item Availability Management
                    </h3>

                    <!-- Set Menu Item Availability Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PATCH</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/availability</h3>
                                    <span class="ml-3 text-gray-600">Set menu item availability</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "is_available": true,
  "branch_id": 1 // Optional: if setting availability for a specific branch
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item availability updated successfully"
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Sets the availability status of a menu item, optionally for a specific branch.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Get Menu Item Availability Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{itemId}/availability</h3>
                                    <span class="ml-3 text-gray-600">Get menu item availability</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>itemId: 1 (Menu Item ID)</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branch_id: 1 (Optional: if checking availability for a specific branch)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "is_available": true,
    "branch_id": 1 // Present if branch_id was provided in query
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves the availability status of a menu item, optionally for a specific branch. If no branch_id is provided, it returns the global availability.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Menu Item Management -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-utensils text-purple-600 mr-2"></i>
                        Menu Item Management
                    </h3>

                    <!-- Get All Menu Items Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items</h3>
                                    <span class="ml-3 text-gray-600">Get all menu items</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>per_page: 15 (optional, items per page)
page: 1 (optional, current page)
search: "burger" (optional, search by name or description)
category_id: 1 (optional, filter by category)
menu_id: 1 (optional, filter by menu)
branch_id: 1 (optional, filter by branch)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Classic Burger",
      "description": "Beef patty, lettuce, tomato, cheese",
      "price": 12.99,
      "category_id": 1,
      "menu_id": 1,
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ],
  "meta": {
    "total": 1,
    "per_page": 15,
    "current_page": 1,
    "last_page": 1,
    "from": 1,
    "to": 1
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves a paginated list of all menu items, with optional filters.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Create Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items</h3>
                                    <span class="ml-3 text-gray-600">Create new menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Spicy Chicken Sandwich",
  "description": "Grilled chicken, hot sauce, pickles",
  "price": 10.50,
  "category_id": 2,
  "menu_id": 1,
  "is_active": true,
  "image_url": "https://example.com/spicy-chicken.jpg",
  "calories": 550,
  "dietary_options": ["spicy", "poultry"],
  "allergens": ["gluten", "dairy"]
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item created successfully",
  "data": {
    "id": 2,
    "name": "Spicy Chicken Sandwich",
    "description": "Grilled chicken, hot sauce, pickles",
    "price": 10.50,
    "category_id": 2,
    "menu_id": 1,
    "is_active": true,
    "image_url": "https://example.com/spicy-chicken.jpg",
    "calories": 550,
    "dietary_options": ["spicy", "poultry"],
    "allergens": ["gluten", "dairy"],
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Creates a new menu item. Name, price, category_id, and menu_id are required.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Get Single Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{id}</h3>
                                    <span class="ml-3 text-gray-600">Get single menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Menu Item ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Classic Burger",
    "description": "Beef patty, lettuce, tomato, cheese",
    "price": 12.99,
    "category_id": 1,
    "menu_id": 1,
    "is_active": true,
    "image_url": "https://example.com/classic-burger.jpg",
    "calories": 700,
    "dietary_options": ["meat"],
    "allergens": ["gluten", "dairy"],
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves a single menu item by ID with full details.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Update Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{id}</h3>
                                    <span class="ml-3 text-gray-600">Update menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "price": 13.99,
  "is_active": false,
  "calories": 720
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item updated successfully",
  "data": {
    "id": 1,
    "name": "Classic Burger",
    "price": 13.99,
    "is_active": false,
    "calories": 720,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Updates an existing menu item. Only provided fields will be updated.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Menu Item Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{id}</h3>
                                    <span class="ml-3 text-gray-600">Delete menu item</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Menu Item ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item deleted successfully"
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Permanently deletes a menu item. Cannot be undone.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Menu Management -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-book-open text-purple-600 mr-2"></i>
                        Menus
                    </h3>

                    <!-- Get Menus Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/menus</h3>
                                    <span class="ml-3 text-gray-600">Get all menus</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Main Menu",
      "description": "Standard menu for all branches",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  ]
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves all menus available in the system.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Create Menu Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/menus</h3>
                                    <span class="ml-3 text-gray-600">Create new menu</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Seasonal Menu",
  "description": "Special menu for seasonal offerings",
  "is_active": true
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu created successfully",
  "data": {
    "id": 2,
    "name": "Seasonal Menu",
    "description": "Special menu for seasonal offerings",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Creates a new menu. Name is required, other fields are optional.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Get Single Menu Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/menus/{id}</h3>
                                    <span class="ml-3 text-gray-600">Get single menu</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Menu ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Main Menu",
    "description": "Standard menu for all branches",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves a single menu by ID with full details.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Update Menu Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/menus/{id}</h3>
                                    <span class="ml-3 text-gray-600">Update menu</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Updated Seasonal Menu",
  "description": "Updated description for seasonal offerings",
  "is_active": false
}</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu updated successfully",
  "data": {
    "id": 2,
    "name": "Updated Seasonal Menu",
    "description": "Updated description for seasonal offerings",
    "is_active": false,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Updates an existing menu. Only provided fields will be updated.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Delete Menu Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/menus/{id}</h3>
                                    <span class="ml-3 text-gray-600">Delete menu</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Menu ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu deleted successfully"
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Permanently deletes a menu. Cannot be undone.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Get Menus for Branch Endpoint -->
                    <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                        <div class="cursor-pointer" onclick="toggleDropdown(this)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                    <h3 class="text-lg font-semibold text-gray-900">/api/menu/menus/branch/{branchId}</h3>
                                    <span class="ml-3 text-gray-600">Get menus for a specific branch</span>
                                </div>
                                <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                            </div>
                        </div>
                        <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                    <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                    <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branchId: 1 (Branch ID)</code></pre>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                    <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Main Menu",
      "description": "Standard menu for this branch",
      "is_active": true
    }
  ]
}</code></pre>
                                </div>
                            </div>
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-600">Retrieves all menus associated with a specific branch.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Menu Items Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items</h3>
                                <span class="ml-3 text-gray-600">Get all menu items</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>category_id: 1 (optional)
per_page: 15 (optional)
page: 1 (optional)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Caesar Salad",
      "description": "Fresh romaine lettuce",
      "price": 12.99,
      "category_id": 1,
      "is_active": true
    }
  ],
  "pagination": {
    "current_page": 1,
    "total": 25
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all menu items with optional filtering and pagination.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Menu Item Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items</h3>
                                <span class="ml-3 text-gray-600">Create new menu item</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Grilled Chicken",
  "description": "Tender grilled chicken breast",
  "price": 18.99,
  "category_id": 2,
  "image_url": "https://example.com/chicken.jpg",
  "is_active": true,
  "is_featured": false
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item created successfully",
  "data": {
    "id": 15,
    "name": "Grilled Chicken",
    "description": "Tender grilled chicken breast",
    "price": 18.99,
    "category_id": 2,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new menu item. Name, price, and category_id are required.</p>
                        </div>
                    </div>
                </div>

                <!-- Get Single Menu Item Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{id}</h3>
                                <span class="ml-3 text-gray-600">Get single menu item</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Menu Item ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Caesar Salad",
    "description": "Fresh romaine lettuce",
    "price": 12.99,
    "category_id": 1,
    "image_url": "https://example.com/caesar.jpg",
    "is_active": true,
    "is_featured": false,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves a single menu item by ID with full details.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Menu Item Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{id}</h3>
                                <span class="ml-3 text-gray-600">Update menu item</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Updated Caesar Salad",
  "price": 14.99,
  "is_featured": true
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item updated successfully",
  "data": {
    "id": 1,
    "name": "Updated Caesar Salad",
    "price": 14.99,
    "is_featured": true,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates an existing menu item. Only provided fields will be updated.</p>
                        </div>
                    </div>
                </div>

                <!-- Delete Menu Item Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{id}</h3>
                                <span class="ml-3 text-gray-600">Delete menu item</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Menu Item ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Permanently deletes a menu item. Cannot be undone.</p>
                        </div>
                    </div>
                </div>

                <!-- Search Menu Items Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/search</h3>
                                <span class="ml-3 text-gray-600">Search menu items</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>q: "chicken" (search term)
category_id: 2 (optional)
min_price: 10.00 (optional)
max_price: 25.00 (optional)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 15,
      "name": "Grilled Chicken",
      "description": "Tender grilled chicken breast",
      "price": 18.99,
      "category_id": 2,
      "is_active": true
    }
  ],
  "total": 1
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Searches menu items by name, description, or other criteria.</p>
                        </div>
                    </div>
                </div>

                <!-- Toggle Item Availability Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PATCH</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{id}/toggle-availability</h3>
                                <span class="ml-3 text-gray-600">Toggle item availability</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: 1 (Menu Item ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Item availability toggled",
  "data": {
    "id": 1,
    "is_active": false,
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Toggles the availability status of a menu item (active/inactive).</p>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Variants & Addons Section -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Variants & Addons</h3>

                <!-- Get Item Variants Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{menuItemId}/variants</h3>
                                <span class="ml-3 text-gray-600">Get item variants</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>menuItemId: 1 (Menu Item ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Small",
      "price_modifier": -2.00,
      "is_active": true
    },
    {
      "id": 2,
      "name": "Large",
      "price_modifier": 3.00,
      "is_active": true
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all variants for a specific menu item with price modifiers.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Item Variant Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{menuItemId}/variants</h3>
                                <span class="ml-3 text-gray-600">Create item variant</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Extra Large",
  "price_modifier": 5.00,
  "is_active": true
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Variant created successfully",
  "data": {
    "id": 3,
    "name": "Extra Large",
    "price_modifier": 5.00,
    "menu_item_id": 1,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new variant for a menu item with price modifier.</p>
                        </div>
                    </div>
                </div>

                <!-- Get Item Addons Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{menuItemId}/addons</h3>
                                <span class="ml-3 text-gray-600">Get item addons</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>menuItemId: 1 (Menu Item ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Extra Cheese",
      "price": 2.50,
      "is_active": true
    },
    {
      "id": 2,
      "name": "Bacon",
      "price": 3.00,
      "is_active": true
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves all available addons for a specific menu item.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Item Addon Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{menuItemId}/addons</h3>
                                <span class="ml-3 text-gray-600">Create item addon</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Avocado",
  "price": 2.00,
  "is_active": true
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Addon created successfully",
  "data": {
    "id": 3,
    "name": "Avocado",
    "price": 2.00,
    "menu_item_id": 1,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new addon for a menu item with additional price.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Availability & Pricing Section -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Availability & Pricing</h3>

                <!-- Check Item Availability Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{menuItemId}/check-availability</h3>
                                <span class="ml-3 text-gray-600">Check item availability</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branch_id: 1 (optional)
date: "2024-01-01" (optional)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "item_id": 1,
    "is_available": true,
    "availability_reason": "In stock",
    "estimated_wait_time": null,
    "branch_specific": {
      "branch_id": 1,
      "local_availability": true
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Checks real-time availability of a menu item, optionally for a specific branch.</p>
                        </div>
                    </div>
                </div>

                <!-- Get Branch-Specific Price Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/menu/items/{menuItemId}/branches/{branchId}/price</h3>
                                <span class="ml-3 text-gray-600">Get branch-specific price</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>menuItemId: 1 (Menu Item ID)
branchId: 1 (Branch ID)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "item_id": 1,
    "branch_id": 1,
    "base_price": 12.99,
    "branch_price": 13.99,
    "price_modifier": 1.00,
    "currency": "USD",
    "effective_date": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves the specific price for a menu item at a particular branch, including any location-based modifiers.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Architecture Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-cogs text-indigo-600 mr-3"></i>
                Technical Architecture
            </h2>

            <!-- Middleware Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-shield-alt text-blue-600 mr-2"></i>
                    Middleware
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-key text-green-500 mr-2"></i>
                            MenuAuthMiddleware
                        </h4>
                        <p class="text-gray-600 mb-3">Handles authentication for menu management endpoints</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>validateMenuAccess()</li>
                            <li>checkBranchPermissions()</li>
                            <li>verifyManagerRole()</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-clock text-orange-500 mr-2"></i>
                            MenuCacheMiddleware
                        </h4>
                        <p class="text-gray-600 mb-3">Manages caching for menu data and public endpoints</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>cacheMenuData()</li>
                            <li>invalidateCache()</li>
                            <li>setCacheHeaders()</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Database Schema Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-database text-green-600 mr-2"></i>
                    Database Schema
                </h3>
                <div class="space-y-6">
                    <!-- Menu Categories Table -->
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-table text-blue-500 mr-2"></i>
                            menu_categories
                        </h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Primary key</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">name</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Category name</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">description</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Category description</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">image_url</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(500)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Category image URL</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">is_active</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">boolean</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Category status</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">sort_order</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">integer</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Display order</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Menu Items Table -->
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-table text-green-500 mr-2"></i>
                            menu_items
                        </h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Primary key</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">category_id</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Foreign key to categories</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">name</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Item name</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">description</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Item description</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">price</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">decimal(8,2)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Base price</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">image_url</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(500)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Item image URL</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">is_active</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">boolean</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Item status</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">preparation_time</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">integer</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Prep time in minutes</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Helper Classes Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-tools text-yellow-600 mr-2"></i>
                    Helper Classes
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-utensils text-orange-500 mr-2"></i>
                            MenuHelper
                        </h4>
                        <p class="text-gray-600 mb-3">Utility functions for menu operations</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>formatMenuStructure()</li>
                            <li>calculateItemAvailability()</li>
                            <li>generateMenuCache()</li>
                            <li>validateMenuData()</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-store text-red-500 mr-2"></i>
                            BranchMenuHelper
                        </h4>
                        <p class="text-gray-600 mb-3">Branch-specific menu management utilities</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>getBranchMenu()</li>
                            <li>updateBranchSettings()</li>
                            <li>syncMenuChanges()</li>
                            <li>validateBranchAccess()</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Service Classes Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-cog text-purple-600 mr-2"></i>
                    Service Classes
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-list text-blue-500 mr-2"></i>
                            MenuService
                        </h4>
                        <p class="text-gray-600 mb-3">Core menu management service</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>createCategory()</li>
                            <li>updateCategory()</li>
                            <li>deleteCategory()</li>
                            <li>getMenuStructure()</li>
                            <li>searchMenuItems()</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-clock text-green-500 mr-2"></i>
                            MenuAvailabilityService
                        </h4>
                        <p class="text-gray-600 mb-3">Handles menu item availability and scheduling</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>setItemSchedule()</li>
                            <li>checkAvailability()</li>
                            <li>updateAvailabilityStatus()</li>
                            <li>getAvailabilityReport()</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
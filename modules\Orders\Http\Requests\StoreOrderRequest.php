<?php

namespace Modules\Orders\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'customer_id' => 'nullable|exists:customers,id',
            'order_type' => 'required|in:dine_in,takeaway,delivery,online',
            'status' => 'nullable|in:pending,confirmed,preparing,ready,served,completed,cancelled',
            'notes' => 'nullable|string|max:500',
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string|max:255',
            'items.*.variant_id' => 'nullable|exists:menu_item_variants,id',
            'items.*.addons' => 'nullable|array',
            'items.*.addons.*.addon_id' => 'required_with:items.*.addons|exists:menu_item_addons,id',
            'items.*.addons.*.quantity' => 'required_with:items.*.addons|integer|min:1',
            'items.*.addons.*.unit_price' => 'required_with:items.*.addons|numeric|min:0',
            'items.*.addons.*.total_price' => 'required_with:items.*.addons|numeric|min:0',
        ];

        // Conditional rules
        if ($this->input('order_type') === 'dine_in') {
            $rules['table_id'] = 'required|exists:tables,id';
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
        } elseif ($this->input('order_type') === 'delivery') {
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['delivery_man_id'] = 'required|exists:users,id';
        } elseif ($this->input('order_type') === 'takeaway') {
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
        } else {
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'table_id.required' => 'Table selection is required.',
            'table_id.exists' => 'Selected table does not exist.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'order_type.required' => 'Order type is required.',
            'order_type.in' => 'Invalid order type selected.',
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.*.menu_item_id.required' => 'Menu item is required for each order item.',
            'items.*.menu_item_id.exists' => 'Selected menu item does not exist.',
            'items.*.quantity.required' => 'Quantity is required for each item.',
            'items.*.quantity.min' => 'Quantity must be at least 1.',
            'items.*.unit_price.required' => 'Unit price is required for each item.',
            'items.*.unit_price.min' => 'Unit price must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'customer_id' => 'customer',
            'table_id' => 'table',
            'order_type' => 'order type',
            'items.*.menu_item_id' => 'menu item',
            'items.*.quantity' => 'quantity',
            'items.*.unit_price' => 'unit price',
        ];
    }
}
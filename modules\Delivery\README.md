# Delivery Management Module

A comprehensive delivery management system for the EPSIS restaurant management platform. This module handles delivery personnel management, zone configuration, order assignments, real-time tracking, customer reviews, and tip processing.

## Features

### 🚚 Delivery Personnel Management
- Personnel registration and verification
- Vehicle and license management
- Working hours configuration
- Performance tracking and ratings
- Real-time location tracking
- Earnings and tip management

### 🗺️ Delivery Zone Management
- Polygon-based delivery zones
- Zone-specific pricing and timing
- Priority-based zone assignment
- Coverage area optimization

### 📋 Assignment Management
- Manual and automatic order assignment
- Smart personnel selection algorithm
- Real-time status tracking
- Delivery proof collection
- Route optimization

### 📍 Real-time Tracking
- GPS location updates
- Route recording and playback
- ETA calculations
- Customer notifications
- Geofencing for delivery completion

### ⭐ Review System
- Customer feedback collection
- Multi-category rating system
- Anonymous review options
- Personnel performance impact
- Review verification

### 💰 Tip Management
- Multiple payment methods
- Secure tip processing
- Personnel earnings tracking
- Tip analytics and reporting

## Installation

### 1. Database Migration

Run the migrations to create the necessary database tables:

```bash
php artisan migrate
```

This will create the following tables:
- `delivery_personnel`
- `delivery_zones`
- `delivery_assignments`
- `delivery_reviews`
- `delivery_tips`
- `delivery_tracking`

### 2. Service Provider Registration

The module is automatically registered via the `DeliveryServiceProvider`. Ensure it's included in your application's service providers.

### 3. Configuration

Publish the configuration file:

```bash
php artisan vendor:publish --tag=config
```

Customize the settings in `config/delivery.php` according to your business needs.

## API Documentation

### Authentication

Most endpoints require authentication using Laravel Sanctum. Include the bearer token in the Authorization header:

```
Authorization: Bearer {your-token}
```

### Delivery Personnel Endpoints

#### Get Personnel List
```http
GET /api/delivery/personnel
```

**Query Parameters:**
- `branch_id` (optional): Filter by branch
- `status` (optional): Filter by status (active, inactive, on_delivery, break)
- `available` (optional): Filter available personnel only

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "user_id": 15,
      "branch_id": 2,
      "license_number": "DL123456",
      "vehicle_type": "motorcycle",
      "vehicle_model": "Honda CB150R",
      "status": "active",
      "rating": 4.8,
      "total_deliveries": 245,
      "user": {
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "total": 10
  }
}
```

#### Create Personnel
```http
POST /api/delivery/personnel
```

**Request Body:**
```json
{
  "user_id": 15,
  "branch_id": 2,
  "license_number": "DL123456",
  "license_expiry_date": "2025-12-31",
  "vehicle_type": "motorcycle",
  "vehicle_model": "Honda CB150R",
  "vehicle_plate_number": "B1234XYZ",
  "phone_number": "+**********",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_phone": "+**********",
  "max_concurrent_deliveries": 3,
  "working_hours": [
    {
      "day": "monday",
      "start_time": "09:00",
      "end_time": "18:00"
    }
  ]
}
```

### Delivery Zone Endpoints

#### Get Zones
```http
GET /api/delivery/zones
```

#### Create Zone
```http
POST /api/delivery/zones
```

**Request Body:**
```json
{
  "branch_id": 1,
  "name": "Downtown Area",
  "description": "Central business district",
  "polygon_coordinates": [
    [-6.2088, 106.8456],
    [-6.2100, 106.8500],
    [-6.2150, 106.8480],
    [-6.2120, 106.8420]
  ],
  "delivery_fee": 5.00,
  "minimum_order_amount": 15.00,
  "estimated_delivery_time_minutes": 30,
  "priority": 1
}
```

### Assignment Endpoints

#### Assign Delivery
```http
POST /api/delivery/assignments/assign
```

**Request Body:**
```json
{
  "order_id": 123,
  "delivery_personnel_id": 5
}
```

#### Auto-assign Delivery
```http
POST /api/delivery/assignments/auto-assign
```

**Request Body:**
```json
{
  "order_id": 123
}
```

#### Update Delivery Status
```http
PUT /api/delivery/assignments/{id}/status
```

**Request Body:**
```json
{
  "status": "delivered",
  "delivery_notes": "Delivered to front door",
  "delivery_proof": {
    "photo": "base64_encoded_image",
    "recipient_name": "John Smith",
    "notes": "Customer was present"
  }
}
```

### Tracking Endpoints

#### Update Location
```http
POST /api/delivery/tracking/{assignmentId}/location
```

**Request Body:**
```json
{
  "latitude": -6.2088,
  "longitude": 106.8456,
  "accuracy": 10,
  "speed": 25.5,
  "bearing": 180
}
```

#### Get Tracking Data
```http
GET /api/delivery/tracking/{assignmentId}/tracking
```

### Review Endpoints

#### Create Review
```http
POST /api/delivery/reviews
```

**Request Body:**
```json
{
  "delivery_assignment_id": 123,
  "rating": 5,
  "comment": "Excellent service, very professional!",
  "review_categories": ["punctuality", "professionalism"],
  "is_anonymous": false
}
```

### Tip Endpoints

#### Create Tip
```http
POST /api/delivery/tips
```

**Request Body:**
```json
{
  "delivery_assignment_id": 123,
  "amount": 5.00,
  "payment_method": "card",
  "transaction_reference": "TXN123456",
  "notes": "Great service!"
}
```

## Business Logic

### Personnel Selection Algorithm

The auto-assignment system uses a scoring algorithm that considers:

1. **Distance (40% weight)**: Closer personnel get higher scores
2. **Rating (30% weight)**: Higher-rated personnel get preference
3. **Current Load (30% weight)**: Personnel with fewer active deliveries get priority

### Zone Matching

Delivery zones are matched using point-in-polygon algorithms. The system:

1. Checks if delivery coordinates fall within any active zone
2. Selects the highest priority zone if multiple matches exist
3. Falls back to default pricing if no zone matches

### Status Transitions

Delivery assignments follow a strict state machine:

```
assigned → picked_up → in_transit → delivered
    ↓         ↓           ↓
cancelled  failed    failed
```

### Performance Metrics

Personnel performance is calculated based on:

- **Completion Rate**: Percentage of successful deliveries
- **Average Rating**: Customer review ratings
- **On-time Rate**: Deliveries completed within estimated time
- **Total Distance**: Cumulative delivery distance
- **Earnings**: Total delivery fees and tips earned

## Configuration Options

### Default Settings

```php
'defaults' => [
    'delivery_fee' => 5.00,
    'minimum_order_amount' => 15.00,
    'estimated_delivery_time_minutes' => 30,
    'max_delivery_radius_km' => 15,
    'personnel_commission_rate' => 0.70,
],
```

### Vehicle Types

Supported vehicle types with their characteristics:

- **Motorcycle**: Fast, 3 concurrent deliveries, 35 km/h average
- **Bicycle**: Eco-friendly, 2 concurrent deliveries, 15 km/h average
- **Car**: High capacity, 5 concurrent deliveries, 30 km/h average
- **Scooter**: Efficient, 2 concurrent deliveries, 25 km/h average

### Time Limits

- **Review Window**: 30 days after delivery
- **Tip Window**: 7 days after delivery
- **Assignment Timeout**: 15 minutes for acceptance
- **Location Updates**: Every 30 seconds during delivery

## Security Features

### Data Protection

- Location data encryption
- Secure file uploads for delivery proof
- Anonymous review options
- Personnel document privacy

### Access Control

- Role-based permissions
- Personnel can only update their own assignments
- Customers can only review their own deliveries
- Admin oversight for all operations

### Validation

- Comprehensive request validation
- Business rule enforcement
- GPS accuracy requirements
- File type and size restrictions

## Error Handling

The module provides detailed error responses:

```json
{
  "message": "Validation failed",
  "errors": {
    "rating": ["Rating must be between 1 and 5"]
  }
}
```

Common error codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (resource doesn't exist)
- `422`: Unprocessable Entity (business rule violations)

## Performance Considerations

### Database Optimization

- Indexed columns for fast queries
- Efficient polygon queries for zone matching
- Optimized distance calculations
- Proper relationship eager loading

### Caching Strategy

- Zone data caching for faster lookups
- Personnel availability caching
- Performance metrics caching
- Route data optimization

### Scalability

- Horizontal scaling support
- Queue-based processing for heavy operations
- Rate limiting for API endpoints
- Efficient pagination for large datasets

## Testing

The module includes comprehensive tests:

```bash
# Run all delivery module tests
php artisan test --filter=Delivery

# Run specific test suites
php artisan test tests/Feature/Delivery/
php artisan test tests/Unit/Delivery/
```

## Contributing

When contributing to this module:

1. Follow PSR-12 coding standards
2. Write comprehensive tests
3. Update documentation
4. Follow the existing architecture patterns
5. Ensure backward compatibility

## License

This module is part of the EPSIS platform and follows the same licensing terms.

## Support

For support and questions:

- Check the documentation
- Review the API examples
- Contact the development team
- Submit issues through the project repository

---

**Version**: 1.0.0  
**Last Updated**: January 2025  
**Compatibility**: Laravel 10+, PHP 8.1+
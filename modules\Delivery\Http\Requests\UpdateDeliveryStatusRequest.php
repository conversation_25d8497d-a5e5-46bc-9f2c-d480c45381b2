<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Delivery\Models\DeliveryAssignment;

class UpdateDeliveryStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if user has permission to update delivery status
        if (auth()->user()->hasPermission('manage_deliveries')) {
            return true;
        }

        // Check if the assignment belongs to the authenticated delivery personnel
        $assignment = DeliveryAssignment::find($this->route('id'));
        
        return $assignment && 
               $assignment->delivery_personnel_id === auth()->user()->deliveryPersonnel?->id;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $currentStatus = $this->getCurrentAssignmentStatus();
        $allowedStatuses = $this->getAllowedStatusTransitions($currentStatus);

        return [
            'status' => [
                'required',
                'string',
                Rule::in($allowedStatuses)
            ],
            'delivery_notes' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'failure_reason' => [
                'required_if:status,failed',
                'nullable',
                'string',
                'max:500'
            ],
            'delivery_proof' => [
                'required_if:status,delivered',
                'nullable',
                'array'
            ],
            'delivery_proof.photo' => [
                'sometimes',
                'string', // Base64 encoded image or file path
                'max:10000' // Limit base64 string length
            ],
            'delivery_proof.signature' => [
                'sometimes',
                'string', // Base64 encoded signature
                'max:5000'
            ],
            'delivery_proof.recipient_name' => [
                'sometimes',
                'string',
                'max:100'
            ],
            'delivery_proof.notes' => [
                'sometimes',
                'string',
                'max:500'
            ],
            'current_latitude' => [
                'sometimes',
                'numeric',
                'between:-90,90'
            ],
            'current_longitude' => [
                'sometimes',
                'numeric',
                'between:-180,180'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.in' => 'Invalid status transition. Current status does not allow this change.',
            'failure_reason.required_if' => 'Failure reason is required when marking delivery as failed.',
            'delivery_proof.required_if' => 'Delivery proof is required when marking delivery as delivered.',
            'delivery_notes.max' => 'Delivery notes cannot exceed 1000 characters.',
            'failure_reason.max' => 'Failure reason cannot exceed 500 characters.',
            'current_latitude.between' => 'Latitude must be between -90 and 90 degrees.',
            'current_longitude.between' => 'Longitude must be between -180 and 180 degrees.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'delivery_notes' => 'delivery notes',
            'failure_reason' => 'failure reason',
            'delivery_proof' => 'delivery proof',
            'current_latitude' => 'current latitude',
            'current_longitude' => 'current longitude',
        ];
    }

    /**
     * Get the current assignment status.
     */
    protected function getCurrentAssignmentStatus(): ?string
    {
        $assignment = DeliveryAssignment::find($this->route('id'));
        return $assignment?->status;
    }

    /**
     * Get allowed status transitions based on current status.
     */
    protected function getAllowedStatusTransitions(?string $currentStatus): array
    {
        $transitions = [
            'assigned' => ['picked_up', 'cancelled'],
            'picked_up' => ['in_transit', 'failed', 'cancelled'],
            'in_transit' => ['delivered', 'failed', 'cancelled'],
            'delivered' => [], // Final state
            'failed' => ['assigned'], // Can be reassigned
            'cancelled' => [], // Final state
        ];

        return $transitions[$currentStatus] ?? [];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $assignment = DeliveryAssignment::find($this->route('id'));
            
            if (!$assignment) {
                $validator->errors()->add('status', 'Delivery assignment not found.');
                return;
            }

            // Additional validation for delivered status
            if ($this->status === 'delivered') {
                // Ensure delivery proof contains at least one form of proof
                $proof = $this->delivery_proof ?? [];
                if (empty($proof['photo']) && empty($proof['signature']) && empty($proof['recipient_name'])) {
                    $validator->errors()->add(
                        'delivery_proof',
                        'At least one form of delivery proof is required (photo, signature, or recipient name).'
                    );
                }
            }

            // Validate location update if provided
            if ($this->has('current_latitude') && $this->has('current_longitude')) {
                // Check if personnel is authorized to update this assignment
                if (!auth()->user()->hasPermission('manage_deliveries') && 
                    $assignment->delivery_personnel_id !== auth()->user()->deliveryPersonnel?->id) {
                    $validator->errors()->add(
                        'current_latitude',
                        'You are not authorized to update location for this delivery.'
                    );
                }
            }

            // Validate business rules
            if ($this->status === 'picked_up' && $assignment->status !== 'assigned') {
                $validator->errors()->add(
                    'status',
                    'Delivery can only be picked up from assigned status.'
                );
            }

            if ($this->status === 'in_transit' && $assignment->status !== 'picked_up') {
                $validator->errors()->add(
                    'status',
                    'Delivery can only be marked in transit after being picked up.'
                );
            }
        });
    }
}
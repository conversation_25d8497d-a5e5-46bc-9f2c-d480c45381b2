<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Menu;
use App\Models\MenuCategory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MenuItem>
 */
class MenuItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->randomElement([
            'Grilled Chicken Breast', 'Caesar Salad', 'Margherita Pizza', 'Beef Burger',
            'Fish and Chips', 'Pasta Carbonara', 'Chocolate Cake', 'Coffee',
            'Fresh Orange Juice', 'Tomato Soup', 'Garlic Bread', 'Ice Cream'
        ]);
        
        $basePrice = fake()->randomFloat(2, 5, 50);
        
        return [
            'menu_id' => Menu::factory(),
            'category_id' => MenuCategory::factory(),
            'name' => $name,
            'code' => Str::slug($name) . '-' . fake()->randomNumber(3),
            'description' => fake()->paragraph(),
            'short_description' => fake()->sentence(),
            'base_price' => $basePrice,
            'cost_price' => $basePrice * fake()->randomFloat(2, 0.3, 0.7), // Cost is 30-70% of price
            'prep_time_minutes' => fake()->numberBetween(5, 45),
            'calories' => fake()->numberBetween(100, 800),
            'nutritional_info' => [
                'protein' => fake()->numberBetween(5, 50) . 'g',
                'carbs' => fake()->numberBetween(10, 80) . 'g',
                'fat' => fake()->numberBetween(2, 30) . 'g'
            ],
            'allergens' => fake()->randomElements(['nuts', 'dairy', 'gluten', 'eggs', 'soy'], fake()->numberBetween(0, 3)),
            'dietary_info' => fake()->randomElements(['vegetarian', 'vegan', 'gluten-free', 'dairy-free'], fake()->numberBetween(0, 2)),
            'barcode' => fake()->ean13(),
            'sku' => strtoupper(fake()->bothify('??###')),
            'is_active' => true,
            'is_featured' => fake()->boolean(20), // 20% chance of being featured
            'is_spicy' => fake()->boolean(30), // 30% chance of being spicy
            'spice_level' => fake()->randomElement([null, 1, 2, 3, 4, 5]),
            'sort_order' => fake()->numberBetween(1, 50),
        ];
    }

    /**
     * Indicate that the menu item should be featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the menu item should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the menu item should be spicy.
     */
    public function spicy(string $level = 'medium'): static
    {
        $spiceLevels = config('menu.spice_levels');
        $spiceLevelInt = array_search(ucfirst($level), $spiceLevels) ?: 2; // Default to medium (2) if not found

        return $this->state(fn (array $attributes) => [
            'is_spicy' => true,
            'spice_level' => $spiceLevelInt,
        ]);
    }

    /**
     * Indicate that the menu item should be vegetarian.
     */
    public function vegetarian(): static
    {
        return $this->state(fn (array $attributes) => [
            'dietary_info' => ['vegetarian'],
        ]);
    }
}
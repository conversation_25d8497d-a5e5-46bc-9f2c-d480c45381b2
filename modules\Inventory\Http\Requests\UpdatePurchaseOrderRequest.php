<?php

namespace Modules\Inventory\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePurchaseOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'supplier_id' => [
                'sometimes',
                'required',
                'string',
                Rule::exists('suppliers', 'id')->where(function ($query) {
                    return $query->where('tenant_id', auth()->user()->tenant_id)
                                 ->where('is_active', true);
                })
            ],
            'expected_delivery_date' => 'nullable|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000',
            'shipping_address' => 'nullable|string|max:500',
            'billing_address' => 'nullable|string|max:500',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'shipping_cost' => 'nullable|numeric|min:0',
            'priority' => 'nullable|in:low,medium,high,urgent',
            
            // Purchase order items (optional for updates)
            'items' => 'sometimes|array|min:1',
            'items.*.id' => 'nullable|string|exists:purchase_order_items,id',
            'items.*.product_id' => [
                'required_with:items',
                'string',
                Rule::exists('products', 'id')->where(function ($query) {
                    return $query->where('tenant_id', auth()->user()->tenant_id)
                                 ->where('is_active', true);
                })
            ],
            'items.*.quantity' => 'required_with:items|numeric|min:0.01',
            'items.*.unit_cost' => 'required_with:items|numeric|min:0',
            'items.*.notes' => 'nullable|string|max:255',
            'items.*.received_quantity' => 'nullable|numeric|min:0',
            'items.*._delete' => 'nullable|boolean', // For marking items for deletion
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'supplier_id.required' => 'Please select a supplier.',
            'supplier_id.exists' => 'The selected supplier is invalid or inactive.',
            'expected_delivery_date.after_or_equal' => 'Expected delivery date cannot be in the past.',
            'payment_terms.integer' => 'Payment terms must be a number of days.',
            'payment_terms.min' => 'Payment terms cannot be negative.',
            'payment_terms.max' => 'Payment terms cannot exceed 365 days.',
            'discount_percentage.numeric' => 'Discount percentage must be a valid number.',
            'discount_percentage.min' => 'Discount percentage cannot be negative.',
            'discount_percentage.max' => 'Discount percentage cannot exceed 100%.',
            'tax_percentage.numeric' => 'Tax percentage must be a valid number.',
            'tax_percentage.min' => 'Tax percentage cannot be negative.',
            'tax_percentage.max' => 'Tax percentage cannot exceed 100%.',
            'shipping_cost.numeric' => 'Shipping cost must be a valid number.',
            'shipping_cost.min' => 'Shipping cost cannot be negative.',
            'priority.in' => 'Priority must be one of: low, medium, high, urgent.',
            
            // Items validation messages
            'items.min' => 'At least one item is required when updating items.',
            'items.*.id.exists' => 'One or more purchase order items are invalid.',
            'items.*.product_id.required_with' => 'Product is required for each item.',
            'items.*.product_id.exists' => 'One or more selected products are invalid or inactive.',
            'items.*.quantity.required_with' => 'Quantity is required for each item.',
            'items.*.quantity.numeric' => 'Quantity must be a valid number.',
            'items.*.quantity.min' => 'Quantity must be greater than 0.',
            'items.*.unit_cost.required_with' => 'Unit cost is required for each item.',
            'items.*.unit_cost.numeric' => 'Unit cost must be a valid number.',
            'items.*.unit_cost.min' => 'Unit cost cannot be negative.',
            'items.*.received_quantity.numeric' => 'Received quantity must be a valid number.',
            'items.*.received_quantity.min' => 'Received quantity cannot be negative.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up items data if provided
        if ($this->has('items')) {
            $items = collect($this->input('items'))->map(function ($item, $index) {
                // Clean up the item data
                $cleanItem = array_filter($item, function ($value, $key) {
                    return $value !== null && $value !== '' && $key !== '_delete';
                }, ARRAY_FILTER_USE_BOTH);
                
                // Keep the _delete flag if it exists
                if (isset($item['_delete'])) {
                    $cleanItem['_delete'] = (bool) $item['_delete'];
                }
                
                return $cleanItem;
            })->values()->toArray();

            $this->merge(['items' => $items]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if the purchase order can be updated
            $purchaseOrder = $this->route('purchaseOrder');
            if ($purchaseOrder && in_array($purchaseOrder->status, ['received', 'cancelled'])) {
                $validator->errors()->add('status', 'Cannot update a purchase order that has been received or cancelled.');
            }
            
            // Check for duplicate products in items (excluding items marked for deletion)
            if ($this->has('items')) {
                $activeItems = collect($this->input('items'))
                    ->filter(function ($item) {
                        return !isset($item['_delete']) || !$item['_delete'];
                    });
                    
                $productIds = $activeItems->pluck('product_id')->filter();
                $duplicates = $productIds->duplicates();
                
                if ($duplicates->isNotEmpty()) {
                    $validator->errors()->add('items', 'Duplicate products are not allowed in the same purchase order.');
                }
                
                // Ensure at least one item remains after deletions
                if ($activeItems->isEmpty()) {
                    $validator->errors()->add('items', 'At least one item must remain in the purchase order.');
                }
            }
            
            // Validate received quantities don't exceed ordered quantities
            if ($this->has('items')) {
                foreach ($this->input('items') as $index => $item) {
                    if (isset($item['received_quantity']) && isset($item['quantity'])) {
                        if ($item['received_quantity'] > $item['quantity']) {
                            $validator->errors()->add(
                                "items.{$index}.received_quantity",
                                'Received quantity cannot exceed ordered quantity.'
                            );
                        }
                    }
                }
            }
        });
    }
}
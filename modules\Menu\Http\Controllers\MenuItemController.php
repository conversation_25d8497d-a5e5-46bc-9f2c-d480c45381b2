<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreMenuItemRequest;
use Modules\Menu\Http\Requests\UpdateMenuItemRequest;
use Modules\Menu\Http\Requests\SetAvailabilityRequest;
use Modules\Menu\Http\Requests\SetBranchSettingsRequest;
use Modules\Menu\Http\Resources\MenuItemResource;

class MenuItemController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of menu items.
     */
    public function index(Request $request): JsonResponse
    {
        $menuItems = $this->menuService->getAllMenuItems($request->all());
        return response()->json(MenuItemResource::collection($menuItems));
    }

    /**
     * Store a newly created menu item.
     */
    public function store(StoreMenuItemRequest $request): JsonResponse
    {
        $menuItem = $this->menuService->createMenuItem($request->validated());
        return response()->json(new MenuItemResource($menuItem), 201);
    }

    /**
     * Display the specified menu item.
     */
    public function show(string $id): JsonResponse
    {
        $menuItem = $this->menuService->getMenuItemById($id);
        return response()->json(new MenuItemResource($menuItem));
    }

    /**
     * Update the specified menu item.
     */
    public function update(UpdateMenuItemRequest $request, string $id): JsonResponse
    {
        $menuItem = $this->menuService->updateMenuItem($id, $request->validated());
        return response()->json(new MenuItemResource($menuItem));
    }

    /**
     * Remove the specified menu item.
     */
    public function destroy(string $id): JsonResponse
    {
        $this->menuService->deleteMenuItem($id);
        return response()->json(null, 204);
    }

    /**
     * Toggle menu item availability.
     */
    public function toggleAvailability(string $id): JsonResponse
    {
        $menuItem = $this->menuService->toggleAvailability($id);
        return response()->json(new MenuItemResource($menuItem));
    }

    /**
     * Get featured menu items.
     */
    public function getFeaturedItems(): JsonResponse
    {
        $items = $this->menuService->getFeaturedItems();
        return response()->json(MenuItemResource::collection($items));
    }

    /**
     * Search menu items.
     */
    public function searchMenuItems(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $items = $this->menuService->searchMenuItems($query);
        return response()->json(MenuItemResource::collection($items));
    }

    // ==================== AVAILABILITY MANAGEMENT ====================

    /**
     * Set menu item availability schedule.
     */
    public function setAvailability(SetAvailabilityRequest $request): JsonResponse
    {
        $schedule = $this->menuService->setAvailabilitySchedule($request->menu_item_id, $request->validated());
        return response()->json($schedule);
    }

    /**
     * Check if menu item is available at specific time.
     */
    public function checkAvailability(Request $request, string $menuItemId): JsonResponse
    {
        $request->validate([
            'date' => 'required|date',
            'time' => 'nullable|date_format:H:i:s',
        ]);

        $isAvailable = $this->menuService->isAvailableAt(
            $menuItemId, 
            $request->date, 
            $request->time
        );
        
        return response()->json(['is_available' => $isAvailable]);
    }

    /**
     * Get available menu items.
     */
    public function getAvailableMenuItems(Request $request): JsonResponse
    {
        $request->validate([
            'date' => 'nullable|date',
            'time' => 'nullable|date_format:H:i:s',
            'branch_id' => 'nullable|string',
        ]);

        $items = $this->menuService->getAvailableMenuItems(
            $request->date,
            $request->time,
            $request->branch_id
        );
        
        return response()->json(MenuItemResource::collection($items));
    }

    /**
     * Get weekly availability forecast for menu item.
     */
    public function forecastWeeklyAvailability(string $menuItemId): JsonResponse
    {
        $forecast = $this->menuService->forecastWeeklyAvailability($menuItemId);
        return response()->json($forecast);
    }

    /**
     * Set branch-specific availability.
     */
    public function setBranchAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'menu_item_id' => 'required|exists:menu_items,id',
            'branch_id' => 'required|string',
            'is_available' => 'required|boolean',
        ]);

        $result = $this->menuService->setBranchAvailability(
            $request->menu_item_id,
            $request->branch_id,
            $request->is_available
        );
        
        return response()->json($result);
    }

    /**
     * Bulk update availability status.
     */
    public function bulkUpdateAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'menu_item_ids' => 'required|array',
            'menu_item_ids.*' => 'exists:menu_items,id',
            'is_active' => 'required|boolean',
        ]);

        $result = $this->menuService->bulkUpdateAvailability(
            $request->menu_item_ids,
            $request->is_active
        );
        
        return response()->json($result);
    }



    /**
     * Get availability statistics.
     */
    public function getAvailabilityStatistics(): JsonResponse
    {
        $statistics = $this->menuService->getAvailabilityStatistics();
        return response()->json($statistics);
    }

    // ==================== BRANCH-SPECIFIC OPERATIONS ====================

    /**
     * Set branch-specific settings for menu item.
     */
    public function setBranchSettings(SetBranchSettingsRequest $request): JsonResponse
    {
        $settings = $this->menuService->setBranchSettings(
            $request->menu_item_id, 
            $request->branch_id, 
            $request->validated()
        );
        return response()->json($settings);
    }

    /**
     * Get menu for specific branch.
     */
    public function getMenuForBranch(string $branchId): JsonResponse
    {
        $menu = $this->menuService->getMenuForBranch($branchId);
        return response()->json($menu);
    }

    /**
     * Get menu item price for specific branch.
     */
    public function getMenuItemPriceForBranch(string $menuItemId, string $branchId): JsonResponse
    {
        $price = $this->menuService->getMenuItemPriceForBranch($menuItemId, $branchId);
        return response()->json(['price' => $price]);
    }

    // ==================== UTILITY FUNCTIONS ====================

    /**
     * Bulk update menu item status.
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $request->validate([
            'menu_item_ids' => 'required|array',
            'menu_item_ids.*' => 'exists:menu_items,id',
            'is_active' => 'required|boolean',
        ]);

        $updatedCount = $this->menuService->bulkUpdateStatus(
            $request->menu_item_ids, 
            $request->is_active
        );
        
        return response()->json([
            'message' => "Updated {$updatedCount} menu items",
            'updated_count' => $updatedCount
        ]);
    }

    /**
     * Get menu statistics.
     */
    public function getMenuStatistics(): JsonResponse
    {
        $statistics = $this->menuService->getMenuStatistics();
        return response()->json($statistics);
    }

    /**
     * Get low stock menu items for branch.
     */
    public function getLowStockItems(string $branchId): JsonResponse
    {
        $items = $this->menuService->getLowStockItems($branchId);
        return response()->json($items);
    }
}
<?php

namespace Modules\Customer\Services;

use App\Models\Customer;
use App\Models\LoyaltyTransaction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Exception;

class CustomerService
{
    /**
     * Get all customers for a tenant with pagination
     */
    public function getCustomers(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Customer::where('tenant_id', $tenantId);

        // Apply filters
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (!empty($filters['city'])) {
            $query->where('city', $filters['city']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Create a new customer
     */
    public function createCustomer(int $tenantId, array $data): Customer
    {
        $data['tenant_id'] = $tenantId;
        
        return Customer::create($data);
    }

    /**
     * Update customer information
     */
    public function updateCustomer(Customer $customer, array $data): Customer
    {
        $customer->update($data);
        return $customer->fresh();
    }

    /**
     * Get customer by ID for a specific tenant
     */
    public function getCustomerById(int $tenantId, int $customerId): ?Customer
    {
        return Customer::where('tenant_id', $tenantId)
                      ->where('id', $customerId)
                      ->with(['orders', 'reservations', 'loyaltyTransactions'])
                      ->first();
    }

    /**
     * Find customer by phone or email
     */
    public function findCustomerByContact(int $tenantId, string $contact): ?Customer
    {
        return Customer::where('tenant_id', $tenantId)
                      ->where(function ($query) use ($contact) {
                          $query->where('phone', $contact)
                                ->orWhere('email', $contact);
                      })
                      ->first();
    }

    /**
     * Add loyalty points to customer
     */
    public function addLoyaltyPoints(Customer $customer, float $points, string $description = 'Points earned'): void
    {
        DB::transaction(function () use ($customer, $points, $description) {
            // Update customer loyalty points
            $customer->increment('loyalty_points', $points);

            // Create loyalty transaction record
            LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'tenant_id' => $customer->tenant_id,
                'transaction_type' => 'earned',
                'points' => $points,
                'description' => $description,
                'balance_after' => $customer->fresh()->loyalty_points,
            ]);
        });
    }

    /**
     * Redeem loyalty points
     */
    public function redeemLoyaltyPoints(Customer $customer, float $points, string $description = 'Points redeemed'): bool
    {
        if ($customer->loyalty_points < $points) {
            return false;
        }

        DB::transaction(function () use ($customer, $points, $description) {
            // Deduct points from customer
            $customer->decrement('loyalty_points', $points);

            // Create loyalty transaction record
            LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'tenant_id' => $customer->tenant_id,
                'transaction_type' => 'redeemed',
                'points' => -$points,
                'description' => $description,
                'balance_after' => $customer->fresh()->loyalty_points,
            ]);
        });

        return true;
    }

    /**
     * Update customer visit information
     */
    public function updateVisitInfo(Customer $customer): void
    {
        $customer->update([
            'last_visit_at' => now(),
        ]);
    }

    /**
     * Get customer statistics
     */
    public function getCustomerStats(int $tenantId): array
    {
        $totalCustomers = Customer::where('tenant_id', $tenantId)->count();
        $activeCustomers = Customer::where('tenant_id', $tenantId)
                                 ->where('is_active', true)
                                 ->count();
        $newCustomersThisMonth = Customer::where('tenant_id', $tenantId)
                                        ->whereMonth('created_at', now()->month)
                                        ->whereYear('created_at', now()->year)
                                        ->count();

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'inactive_customers' => $totalCustomers - $activeCustomers,
            'new_customers_this_month' => $newCustomersThisMonth,
        ];
    }

    /**
     * Get top customers by loyalty points
     */
    public function getTopCustomers(int $tenantId, int $limit = 10): Collection
    {
        return Customer::where('tenant_id', $tenantId)
                      ->where('is_active', true)
                      ->orderBy('loyalty_points', 'desc')
                      ->limit($limit)
                      ->get();
    }

    /**
     * Deactivate customer
     */
    public function deactivateCustomer(Customer $customer): void
    {
        $customer->update(['is_active' => false]);
    }

    /**
     * Activate customer
     */
    public function activateCustomer(Customer $customer): void
    {
        $customer->update(['is_active' => true]);
    }

    /**
     * Delete customer (soft delete)
     */
    public function deleteCustomer(Customer $customer): void
    {
        $customer->delete();
    }

    /**
     * Get customer loyalty history
     */
    public function getLoyaltyHistory(Customer $customer, int $perPage = 15): LengthAwarePaginator
    {
        return $customer->loyaltyTransactions()
                       ->orderBy('created_at', 'desc')
                       ->paginate($perPage);
    }
}
@extends('menu::layouts.app')

@section('title', 'Menus')

@section('header-actions')
    <a href="{{ route('menu.web.menus.create') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i>
        Create Menu
    </a>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">All Menus</h5>
            </div>
            <div class="card-body">
                @if($menus->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Code</th>
                                    <th>Type</th>
                                    <th>Branch</th>
                                    <th>Status</th>
                                    <th>Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($menus as $menu)
                                    <tr>
                                        <td>
                                            <strong>{{ $menu->name }}</strong>
                                            @if($menu->description)
                                                <br><small class="text-muted">{{ Str::limit($menu->description, 50) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <code>{{ $menu->code ?? 'N/A' }}</code>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $menu->menu_type)) }}</span>
                                        </td>
                                        <td>
                                            {{ $menu->branch->name ?? 'N/A' }}
                                        </td>
                                        <td>
                                            @if($menu->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                            @if($menu->is_default)
                                                <span class="badge bg-primary ms-1">Default</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($menu->start_time && $menu->end_time)
                                                <small>
                                                    {{ $menu->start_time }} - {{ $menu->end_time }}
                                                </small>
                                            @else
                                                <small class="text-muted">All Day</small>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ route('menu.web.menus.show', $menu->id) }}" 
                                                   class="btn btn-outline-primary" title="View">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('menu.web.menus.edit', $menu->id) }}" 
                                                   class="btn btn-outline-secondary" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        title="Delete" onclick="deleteMenu({{ $menu->id }})">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="bi bi-journal-text display-1 text-muted"></i>
                        <h4 class="mt-3">No Menus Found</h4>
                        <p class="text-muted">Get started by creating your first menu.</p>
                        <a href="{{ route('menu.web.menus.create') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Create Menu
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this menu? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteMenu(menuId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/menu/menus/${menuId}`;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
@endpush
@extends('docs.layout')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50">
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- Module Header -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-emerald-100">
            <div class="flex items-center mb-6">
                <div class="p-4 rounded-full bg-gradient-to-r from-emerald-500 to-teal-500 mr-6">
                    <i class="fas fa-credit-card text-white text-3xl"></i>
                </div>
                <div>
                    <h1 class="text-5xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-2">Payment Module (Global)</h1>
                    <p class="text-xl text-gray-600">Global payment processing system for all modules - transactions, refunds, multi-gateway support, and comprehensive financial operations</p>
                </div>
            </div>

            <!-- Global Features Overview -->
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-bold text-gray-800 mb-3">🌐 Global Payment System Features</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i>Multi-Module Integration</div>
                    <div class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i>Global Transaction Management</div>
                    <div class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i>Multi-Gateway Support</div>
                    <div class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i>Comprehensive Refund System</div>
                    <div class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i>Payment Analytics & Reports</div>
                    <div class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i>Widget System for Modules</div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
                <div class="bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl p-6 text-white">
                    <div class="text-3xl font-bold">35+</div>
                    <div class="text-emerald-100">Total Endpoints</div>
                </div>
                <div class="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl p-6 text-white">
                    <div class="text-3xl font-bold">6</div>
                    <div class="text-blue-100">Payment Gateways</div>
                </div>
                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-6 text-white">
                    <div class="text-3xl font-bold">8</div>
                    <div class="text-purple-100">Payment Methods</div>
                </div>
                <div class="bg-gradient-to-r from-orange-500 to-red-500 rounded-xl p-6 text-white">
                    <div class="text-3xl font-bold">Global</div>
                    <div class="text-orange-100">Module Access</div>
                </div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 border border-emerald-100">
            <div class="relative">
                <input type="text" id="searchInput" placeholder="Search endpoints, methods, or descriptions..." 
                       class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>

        <!-- API Documentation -->
        <div class="space-y-8">
            <!-- Public Routes -->
            <div class="bg-white rounded-xl shadow-lg border border-emerald-100 overflow-hidden">
                <div class="bg-gradient-to-r from-emerald-500 to-teal-500 px-6 py-4">
                    <h2 class="text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-globe mr-3"></i>
                        Public Routes
                    </h2>
                </div>
                <div class="p-6">
                    <!-- Get Payment Methods -->
                    <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4 mb-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                            <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payment-methods</code>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">Get Payment Methods</h4>
                        <p class="text-gray-600 mb-3">Retrieve all active payment methods available for transactions.</p>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h5 class="font-semibold mb-2">Response Example:</h5>
                            <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "data": [
    {
      "id": 1,
      "name": "Cash",
      "type": "cash",
      "is_active": true,
      "provider": null
    },
    {
      "id": 2,
      "name": "Credit Card",
      "type": "credit_card",
      "is_active": true,
      "provider": "Stripe"
    }
  ]
}</code></pre>
                        </div>
                    </div>

                    <!-- Get Payment Status -->
                    <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                            <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments/{id}/status</code>
                        </div>
                        <h4 class="font-semibold text-lg mb-2">Get Payment Status</h4>
                        <p class="text-gray-600 mb-3">Check the current status of a payment transaction.</p>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h5 class="font-semibold mb-2">Response Example:</h5>
                            <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "data": {
    "id": 1,
    "payment_number": "PAY-2024-001",
    "status": "completed",
    "amount": 150.00,
    "processed_at": "2024-01-15T10:30:00Z"
  }
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Protected Routes -->
            <div class="bg-white rounded-xl shadow-lg border border-emerald-100 overflow-hidden">
                <div class="bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-4 cursor-pointer" onclick="toggleSection('protected-routes')">
                    <h2 class="text-2xl font-bold text-white flex items-center justify-between">
                        <span class="flex items-center">
                            <i class="fas fa-shield-alt mr-3"></i>
                            Protected Routes
                        </span>
                        <i class="fas fa-chevron-down transition-transform duration-200" id="protected-routes-icon"></i>
                    </h2>
                </div>
                <div class="p-6" id="protected-routes-content">
                    <!-- Payment Management -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-money-bill-wave mr-2 text-emerald-500"></i>
                            Payment Management
                        </h3>
                        
                        <!-- Process Payment -->
                        <div class="endpoint-item border-l-4 border-green-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">POST</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Process Payment</h4>
                            <p class="text-gray-600 mb-3">Process a new payment transaction for an order.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Request Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "transaction_id": 123,
  "payment_method_id": 2,
  "amount": 150.00,
  "change_amount": 0.00,
  "reference_number": "REF-2024-001",
  "notes": "Payment for order #ORD-001"
}</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Payment processed successfully",
  "data": {
    "id": 1,
    "payment_number": "PAY-2024-001",
    "amount": 150.00,
    "status": "completed",
    "processed_at": "2024-01-15T10:30:00Z",
    "gateway_response": {
      "transaction_id": "txn_1234567890",
      "status": "success"
    }
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Get Payment Details -->
                        <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments/{id}</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Get Payment Details</h4>
                            <p class="text-gray-600 mb-3">Retrieve detailed information about a specific payment.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "payment_number": "PAY-2024-001",
    "amount": 150.00,
    "change_amount": 0.00,
    "status": "completed",
    "reference_number": "REF-2024-001",
    "notes": "Payment for order #ORD-001",
    "processed_at": "2024-01-15T10:30:00Z",
    "payment_method": {
      "id": 2,
      "name": "Credit Card",
      "type": "credit_card",
      "provider": "Stripe"
    },
    "transaction": {
      "id": 123,
      "order_id": 456
    },
    "gateway_response": {
      "transaction_id": "txn_1234567890",
      "status": "success",
      "fee": 4.50
    }
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Update Payment -->
                        <div class="endpoint-item border-l-4 border-yellow-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">PUT</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments/{id}</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Update Payment</h4>
                            <p class="text-gray-600 mb-3">Update payment details or status.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Request Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "status": "refunded",
  "notes": "Customer requested refund due to order cancellation",
  "reference_number": "REF-2024-001-REFUND"
}</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Payment updated successfully",
  "data": {
    "id": 1,
    "payment_number": "PAY-2024-001",
    "status": "refunded",
    "notes": "Customer requested refund due to order cancellation",
    "updated_at": "2024-01-15T11:45:00Z"
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Cancel Payment -->
                        <div class="endpoint-item border-l-4 border-red-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">DELETE</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments/{id}</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Cancel Payment</h4>
                            <p class="text-gray-600 mb-3">Cancel a pending payment transaction.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Payment cancelled successfully",
  "data": {
    "id": 1,
    "payment_number": "PAY-2024-001",
    "status": "cancelled",
    "cancelled_at": "2024-01-15T12:00:00Z",
    "refund_amount": 150.00
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- List Payments -->
                        <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">List Payments</h4>
                            <p class="text-gray-600 mb-3">Retrieve paginated list of payments with filtering options.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Query Parameters:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>?page=1&per_page=10&status=completed&date_from=2024-01-01&date_to=2024-01-31&payment_method_id=2</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "payment_number": "PAY-2024-001",
      "amount": 150.00,
      "status": "completed",
      "payment_method": {
        "name": "Credit Card",
        "type": "credit_card"
      },
      "processed_at": "2024-01-15T10:30:00Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 10,
    "total": 25,
    "last_page": 3
  }
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Refund Management -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-undo mr-2 text-orange-500"></i>
                            Refund Management
                        </h3>
                        
                        <!-- Process Refund -->
                        <div class="endpoint-item border-l-4 border-green-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">POST</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments/{id}/refund</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Process Refund</h4>
                            <p class="text-gray-600 mb-3">Process a full or partial refund for a completed payment.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Request Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "amount": 75.00,
  "reason": "Customer requested partial refund",
  "refund_type": "partial"
}</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Refund processed successfully",
  "data": {
    "refund_id": "REF-2024-001",
    "payment_id": 1,
    "amount": 75.00,
    "status": "completed",
    "reason": "Customer requested partial refund",
    "processed_at": "2024-01-15T13:15:00Z",
    "gateway_response": {
      "refund_id": "re_1234567890",
      "status": "succeeded"
    }
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Get Refund Status -->
                        <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/refunds/{id}</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Get Refund Status</h4>
                            <p class="text-gray-600 mb-3">Check the status of a refund transaction.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "refund_id": "REF-2024-001",
    "payment_id": 1,
    "amount": 75.00,
    "status": "completed",
    "reason": "Customer requested partial refund",
    "processed_at": "2024-01-15T13:15:00Z",
    "estimated_arrival": "2024-01-17T00:00:00Z",
    "gateway_response": {
      "refund_id": "re_1234567890",
      "status": "succeeded",
      "failure_reason": null
    }
  }
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method Management -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-credit-card mr-2 text-blue-500"></i>
                            Payment Method Management
                        </h3>
                        
                        <!-- Create Payment Method -->
                        <div class="endpoint-item border-l-4 border-green-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">POST</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payment-methods</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Create Payment Method</h4>
                            <p class="text-gray-600 mb-3">Add a new payment method to the system.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Request Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "name": "Mobile Money",
  "type": "mobile_money",
  "provider": "M-Pesa",
  "is_active": true,
  "credentials": {
    "api_key": "encrypted_api_key",
    "secret_key": "encrypted_secret",
    "shortcode": "174379"
  }
}</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Payment method created successfully",
  "data": {
    "id": 3,
    "name": "Mobile Money",
    "type": "mobile_money",
    "provider": "M-Pesa",
    "is_active": true,
    "created_at": "2024-01-15T14:00:00Z"
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Update Payment Method -->
                        <div class="endpoint-item border-l-4 border-yellow-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">PUT</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payment-methods/{id}</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Update Payment Method</h4>
                            <p class="text-gray-600 mb-3">Update payment method configuration and settings.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Request Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "name": "Mobile Money - Updated",
  "is_active": false,
  "credentials": {
    "api_key": "new_encrypted_api_key",
    "secret_key": "new_encrypted_secret",
    "shortcode": "174379"
  }
}</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Payment method updated successfully",
  "data": {
    "id": 3,
    "name": "Mobile Money - Updated",
    "type": "mobile_money",
    "provider": "M-Pesa",
    "is_active": false,
    "updated_at": "2024-01-15T14:30:00Z"
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Delete Payment Method -->
                        <div class="endpoint-item border-l-4 border-red-500 pl-6 py-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">DELETE</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payment-methods/{id}</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Delete Payment Method</h4>
                            <p class="text-gray-600 mb-3">Remove a payment method from the system.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Payment method deleted successfully"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction Management -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-exchange-alt mr-2 text-purple-500"></i>
                            Transaction Management
                        </h3>
                        
                        <!-- Get Transaction History -->
                        <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/transactions</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Get Transaction History</h4>
                            <p class="text-gray-600 mb-3">Retrieve transaction history with filtering and pagination.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Query Parameters:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>?status=completed&date_from=2024-01-01&date_to=2024-01-31&page=1&per_page=20</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": 1,
        "payment_number": "PAY-2024-001",
        "amount": 150.00,
        "status": "completed",
        "payment_method": "Credit Card",
        "processed_at": "2024-01-15T10:30:00Z",
        "reference_number": "TXN123456789"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 45,
      "last_page": 3
    }
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Get Transaction Details -->
                        <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/transactions/{id}</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Get Transaction Details</h4>
                            <p class="text-gray-600 mb-3">Retrieve detailed information about a specific transaction.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "payment_number": "PAY-2024-001",
    "amount": 150.00,
    "change_amount": 0.00,
    "status": "completed",
    "payment_method": {
      "id": 1,
      "name": "Credit Card",
      "type": "card",
      "provider": "Stripe"
    },
    "reference_number": "TXN123456789",
    "gateway_response": {
      "charge_id": "ch_1234567890",
      "status": "succeeded",
      "receipt_url": "https://pay.stripe.com/receipts/..."
    },
    "processed_by": {
      "id": 1,
      "name": "John Cashier"
    },
    "processed_at": "2024-01-15T10:30:00Z",
    "created_at": "2024-01-15T10:29:45Z"
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Reconcile Transactions -->
                        <div class="endpoint-item border-l-4 border-green-500 pl-6 py-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">POST</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/transactions/reconcile</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Reconcile Transactions</h4>
                            <p class="text-gray-600 mb-3">Reconcile payment transactions with gateway records.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Request Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "date_from": "2024-01-01",
  "date_to": "2024-01-31",
  "payment_method_id": 2,
  "auto_resolve": true
}</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Reconciliation completed successfully",
  "data": {
    "total_transactions": 150,
    "matched_transactions": 145,
    "unmatched_transactions": 5,
    "discrepancies": [
      {
        "payment_id": 123,
        "local_amount": 100.00,
        "gateway_amount": 95.50,
        "difference": 4.50,
        "reason": "Gateway fee not recorded"
      }
    ],
    "reconciliation_report_url": "/reports/reconciliation/2024-01-31.pdf"
  }
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Reports & Analytics -->
                    <div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-chart-bar mr-2 text-indigo-500"></i>
                            Reports & Analytics
                        </h3>
                        
                        <!-- Payment Summary -->
                        <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4 mb-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments/summary</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Payment Summary</h4>
                            <p class="text-gray-600 mb-3">Get payment summary statistics and analytics.</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Query Parameters:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>?date_from=2024-01-01&date_to=2024-01-31&payment_method_id=2</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "total_payments": 1250,
    "total_amount": 125000.00,
    "successful_payments": 1200,
    "failed_payments": 30,
    "refunded_payments": 20,
    "average_payment_amount": 100.00,
    "payment_methods": [
      {
        "method": "Credit Card",
        "count": 800,
        "amount": 95000.00,
        "percentage": 76.0
      },
      {
        "method": "Cash",
        "count": 400,
        "amount": 25000.00,
        "percentage": 20.0
      }
    ],
    "daily_totals": [
      {
        "date": "2024-01-15",
        "count": 45,
        "amount": 4500.00
      }
    ]
  }
}</code></pre>
                            </div>
                        </div>

                        <!-- Export Payment Report -->
                        <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4">
                            <div class="flex items-center mb-3">
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                                <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/payments/export</code>
                            </div>
                            <h4 class="font-semibold text-lg mb-2">Export Payment Report</h4>
                            <p class="text-gray-600 mb-3">Export payment data in various formats (CSV, PDF, Excel).</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold mb-2">Query Parameters:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>?format=csv&date_from=2024-01-01&date_to=2024-01-31&status=completed</code></pre>
                                <h5 class="font-semibold mb-2">Response Example:</h5>
                                <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Export generated successfully",
  "data": {
    "download_url": "/downloads/payments-export-2024-01-31.csv",
    "file_size": "2.5MB",
    "record_count": 1250,
    "expires_at": "2024-01-31T23:59:59Z",
    "format": "csv"
  }
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Architecture -->
            <div class="bg-white rounded-xl shadow-lg border border-emerald-100 overflow-hidden">
                <div class="bg-gradient-to-r from-indigo-500 to-purple-500 px-6 py-4 cursor-pointer" onclick="toggleSection('technical-architecture')">
                    <h2 class="text-2xl font-bold text-white flex items-center justify-between">
                        <span class="flex items-center">
                            <i class="fas fa-cogs mr-3"></i>
                            Technical Architecture
                        </span>
                        <i class="fas fa-chevron-down transition-transform duration-200" id="technical-architecture-icon"></i>
                    </h2>
                </div>
                <div class="p-6" id="technical-architecture-content">
                    <!-- Middleware -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-shield-alt mr-2 text-blue-500"></i>
                            Middleware
                        </h3>
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h4 class="font-semibold text-lg mb-3">PaymentAccessMiddleware</h4>
                            <p class="text-gray-600 mb-4">Handles payment access control and validation for secure payment processing.</p>
                            <div class="bg-gray-800 rounded-lg p-4">
                                <pre class="text-green-400 text-sm"><code>// Features:
// - Payment authorization validation
// - Transaction security checks
// - Gateway access control
// - Fraud detection integration
// - Rate limiting for payment requests</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- Global Integration Guide -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-globe mr-2 text-blue-500"></i>
                            Global Integration Guide
                        </h3>
                        <p class="text-gray-600 mb-6">The Payment module is designed as a global service that can be integrated across all modules in the POS system. Here's how other modules can leverage its capabilities:</p>
                        
                        <!-- Module Integration Examples -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                            <div class="bg-blue-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3 text-blue-800">Orders Module Integration</h4>
                                <div class="bg-white rounded p-4">
                                    <pre class="text-sm"><code class="language-php">// In OrderController
use Modules\Payment\Services\PaymentService;
use Modules\Payment\Helpers\PaymentHelper;

public function processOrderPayment(Order $order, Request $request)
{
    $paymentService = app(PaymentService::class);
    
    $payment = $paymentService->processPayment([
        'amount' => $order->total_amount,
        'payment_method_id' => $request->payment_method_id,
        'reference_type' => 'order',
        'reference_id' => $order->id,
        'metadata' => [
            'order_number' => $order->order_number,
            'customer_id' => $order->customer_id
        ]
    ]);
    
    if ($payment->status === 'completed') {
        $order->update(['status' => 'paid']);
        return PaymentHelper::generateReceipt($payment);
    }
}</code></pre>
                                </div>
                            </div>
                            
                            <div class="bg-green-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3 text-green-800">Inventory Module Integration</h4>
                                <div class="bg-white rounded p-4">
                                    <pre class="text-sm"><code class="language-php">// In InventoryController
use Modules\Payment\Services\TransactionService;

public function recordSupplierPayment(Purchase $purchase)
{
    $transactionService = app(TransactionService::class);
    
    $transaction = $transactionService->createTransaction([
        'type' => 'expense',
        'amount' => $purchase->total_amount,
        'description' => 'Supplier payment for purchase #' . $purchase->id,
        'reference_type' => 'purchase',
        'reference_id' => $purchase->id,
        'category' => 'inventory_purchase'
    ]);
    
    return $transaction;
}</code></pre>
                                </div>
                            </div>
                            
                            <div class="bg-purple-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3 text-purple-800">Staff Module Integration</h4>
                                <div class="bg-white rounded p-4">
                                    <pre class="text-sm"><code class="language-php">// In StaffController
use Modules\Payment\Services\PayrollService;

public function processSalaryPayment(Staff $staff, $amount)
{
    $payrollService = app(PayrollService::class);
    
    $payment = $payrollService->processSalaryPayment([
        'staff_id' => $staff->id,
        'amount' => $amount,
        'payment_period' => now()->format('Y-m'),
        'payment_method' => 'bank_transfer'
    ]);
    
    return $payment;
}</code></pre>
                                </div>
                            </div>
                            
                            <div class="bg-yellow-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3 text-yellow-800">Global Helper Functions</h4>
                                <div class="bg-white rounded p-4">
                                    <pre class="text-sm"><code class="language-php">// Available globally in any module

// Format currency
$formatted = payment_format_currency(1250.50); // $1,250.50

// Calculate fees
$fees = payment_calculate_fees(1000, 'credit_card'); // 2.9%

// Validate payment method
$isValid = payment_method_available('stripe', 'credit_card');

// Get payment status
$status = payment_get_status('PAY-123456');

// Generate payment link
$link = payment_generate_link([
    'amount' => 500,
    'description' => 'Order payment',
    'return_url' => route('orders.success')
]);</code></pre>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Widgets -->
                        <div class="mb-6">
                            <h4 class="font-semibold text-lg mb-3">Payment Widgets</h4>
                            <p class="text-gray-600 mb-4">Embed payment functionality anywhere in your application:</p>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <pre class="text-sm"><code class="language-blade">{{-- In any Blade template --}}

{{-- Transaction history --}}
</code></pre>
                            </div>
                        </div>
                        
                        <!-- API Integration -->
                        <div class="mb-6">
                            <h4 class="font-semibold text-lg mb-3">API Integration Examples</h4>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h5 class="font-medium mb-2">Process Payment via API</h5>
                                    <pre class="text-xs"><code class="language-javascript">// JavaScript/AJAX example
fetch('/api/payments/process', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        amount: 1250.00,
        payment_method_id: 1,
        reference_type: 'order',
        reference_id: 123,
        customer_email: '<EMAIL>'
    })
})
.then(response => response.json())
.then(data => {
    if (data.status === 'success') {
        window.location.href = data.redirect_url;
    }
});</code></pre>
                                </div>
                                
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h5 class="font-medium mb-2">Get Transaction Status</h5>
                                    <pre class="text-xs"><code class="language-javascript">// Check payment status
fetch('/api/payments/status/PAY-123456')
.then(response => response.json())
.then(data => {
    console.log('Payment Status:', data.status);
    console.log('Amount:', data.amount);
    console.log('Gateway Response:', data.gateway_response);
});</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Database Schema -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-database mr-2 text-green-500"></i>
                            Database Schema
                        </h3>
                        
                        <!-- Payment Methods Table -->
                        <div class="mb-6">
                            <h4 class="font-semibold text-lg mb-3">payment_methods</h4>
                            <div class="bg-gray-50 rounded-lg p-4 overflow-x-auto">
                                <table class="min-w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-2 px-3 font-semibold">Column</th>
                                            <th class="text-left py-2 px-3 font-semibold">Type</th>
                                            <th class="text-left py-2 px-3 font-semibold">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-sm">
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">id</td>
                                            <td class="py-2 px-3">bigint</td>
                                            <td class="py-2 px-3">Primary key</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">name</td>
                                            <td class="py-2 px-3">string</td>
                                            <td class="py-2 px-3">Payment method name</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">type</td>
                                            <td class="py-2 px-3">string</td>
                                            <td class="py-2 px-3">Method type (cash, credit_card, mobile_money)</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">credentials</td>
                                            <td class="py-2 px-3">json</td>
                                            <td class="py-2 px-3">Encrypted credentials or API keys</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">is_active</td>
                                            <td class="py-2 px-3">boolean</td>
                                            <td class="py-2 px-3">Active status</td>
                                        </tr>
                                        <tr>
                                            <td class="py-2 px-3 font-mono">provider</td>
                                            <td class="py-2 px-3">string</td>
                                            <td class="py-2 px-3">Payment provider (Stripe, PayPal, M-Pesa)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Payments Table -->
                        <div>
                            <h4 class="font-semibold text-lg mb-3">payments</h4>
                            <div class="bg-gray-50 rounded-lg p-4 overflow-x-auto">
                                <table class="min-w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-2 px-3 font-semibold">Column</th>
                                            <th class="text-left py-2 px-3 font-semibold">Type</th>
                                            <th class="text-left py-2 px-3 font-semibold">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-sm">
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">id</td>
                                            <td class="py-2 px-3">bigint</td>
                                            <td class="py-2 px-3">Primary key</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">payment_number</td>
                                            <td class="py-2 px-3">string(50)</td>
                                            <td class="py-2 px-3">Unique payment identifier</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">payment_method_id</td>
                                            <td class="py-2 px-3">bigint</td>
                                            <td class="py-2 px-3">Foreign key to payment_methods</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">amount</td>
                                            <td class="py-2 px-3">decimal(10,2)</td>
                                            <td class="py-2 px-3">Payment amount</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">change_amount</td>
                                            <td class="py-2 px-3">decimal(10,2)</td>
                                            <td class="py-2 px-3">Change amount for cash payments</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">status</td>
                                            <td class="py-2 px-3">enum</td>
                                            <td class="py-2 px-3">Payment status (pending, completed, failed, refunded, cancelled)</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">transaction_id</td>
                                            <td class="py-2 px-3">bigint</td>
                                            <td class="py-2 px-3">Foreign key to transactions</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">reference_number</td>
                                            <td class="py-2 px-3">string</td>
                                            <td class="py-2 px-3">External reference number</td>
                                        </tr>
                                        <tr class="border-b border-gray-100">
                                            <td class="py-2 px-3 font-mono">gateway_response</td>
                                            <td class="py-2 px-3">json</td>
                                            <td class="py-2 px-3">Payment gateway response data</td>
                                        </tr>
                                        <tr>
                                            <td class="py-2 px-3 font-mono">processed_by</td>
                                            <td class="py-2 px-3">bigint</td>
                                            <td class="py-2 px-3">Foreign key to users (processor)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Helper Classes -->
                    <div class="mb-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-tools mr-2 text-yellow-500"></i>
                            Helper Classes
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3">PaymentHelper</h4>
                                <ul class="text-sm text-gray-600 space-y-2">
                                    <li>• generatePaymentNumber()</li>
                                    <li>• validatePaymentAmount()</li>
                                    <li>• formatCurrency()</li>
                                    <li>• calculateChange()</li>
                                    <li>• getPaymentStatusLabel()</li>
                                    <li>• isRefundable()</li>
                                </ul>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3">GatewayHelper</h4>
                                <ul class="text-sm text-gray-600 space-y-2">
                                    <li>• encryptCredentials()</li>
                                    <li>• decryptCredentials()</li>
                                    <li>• validateGatewayResponse()</li>
                                    <li>• parseWebhookData()</li>
                                    <li>• generateSignature()</li>
                                    <li>• verifyWebhookSignature()</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Service Classes -->
                    <div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-layer-group mr-2 text-purple-500"></i>
                            Service Classes
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3">PaymentService</h4>
                                <ul class="text-sm text-gray-600 space-y-2">
                                    <li>• processPayment()</li>
                                    <li>• validateTransaction()</li>
                                    <li>• updatePaymentStatus()</li>
                                    <li>• calculateTotalAmount()</li>
                                    <li>• handleFailedPayment()</li>
                                    <li>• generateReceipt()</li>
                                </ul>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3">RefundService</h4>
                                <ul class="text-sm text-gray-600 space-y-2">
                                    <li>• processRefund()</li>
                                    <li>• validateRefundRequest()</li>
                                    <li>• calculateRefundAmount()</li>
                                    <li>• updateRefundStatus()</li>
                                    <li>• notifyCustomer()</li>
                                    <li>• generateRefundReceipt()</li>
                                </ul>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3">GatewayService</h4>
                                <ul class="text-sm text-gray-600 space-y-2">
                                    <li>• initializeGateway()</li>
                                    <li>• processGatewayPayment()</li>
                                    <li>• handleWebhook()</li>
                                    <li>• syncTransactionStatus()</li>
                                    <li>• validateGatewayConfig()</li>
                                    <li>• handleGatewayErrors()</li>
                                </ul>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="font-semibold text-lg mb-3">ReconciliationService</h4>
                                <ul class="text-sm text-gray-600 space-y-2">
                                    <li>• reconcileTransactions()</li>
                                    <li>• identifyDiscrepancies()</li>
                                    <li>• generateReconciliationReport()</li>
                                    <li>• autoResolveMatches()</li>
                                    <li>• flagUnmatchedTransactions()</li>
                                    <li>• exportReconciliationData()</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
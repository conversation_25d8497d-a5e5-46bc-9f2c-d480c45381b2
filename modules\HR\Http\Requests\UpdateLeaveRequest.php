<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateLeaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin and manager roles can approve/reject leave requests
        return Auth::check() && in_array(Auth::user()->role, ['admin', 'manager']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => [
                'required',
                'string',
                Rule::in(['approved', 'rejected'])
            ],
            'admin_notes' => [
                'nullable',
                'string',
                'max:1000'
            ],
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.required' => 'Status is required.',
            'status.in' => 'Invalid status. Must be either approved or rejected.',
            'admin_notes.max' => 'Admin notes cannot exceed 1000 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if the leave request exists and belongs to the same tenant
            $leaveRequestId = $this->route('id');
            if ($leaveRequestId && Auth::check()) {
                $leaveRequest = \App\Models\LeaveRequest::where('id', $leaveRequestId)
                    ->where('tenant_id', Auth::user()->tenant_id)
                    ->first();

                if (!$leaveRequest) {
                    $validator->errors()->add('id', 'Leave request not found.');
                    return;
                }

                // Check if the leave request is still pending
                if ($leaveRequest->status !== 'pending') {
                    $validator->errors()->add('status', 'This leave request has already been processed.');
                }

                // If rejecting, admin notes should be provided
                if ($this->status === 'rejected' && empty($this->admin_notes)) {
                    $validator->errors()->add('admin_notes', 'Admin notes are required when rejecting a leave request.');
                }
            }
        });
    }
}
<?php

namespace Modules\Menu\Services\Web;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\MenuCategory;
use App\Models\MenuItemVariant;
use App\Models\MenuItemAddon;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Menu\Services\MenuService;

class MenuWebService
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    // ==================== MENU MANAGEMENT ====================

    /**
     * Get all menus with pagination and filters
     */
    public function getAllMenus(array $filters = []): LengthAwarePaginator
    {
        $query = Menu::with(['categories']);

        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('code', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        return $query->orderBy('sort_order')->orderBy('name')->paginate(15);
    }

    /**
     * Get menu by ID
     */
    public function getMenuById(string $id): Menu
    {
        return Menu::with(['categories.menuItems'])->findOrFail($id);
    }

    /**
     * Create new menu
     */
    public function createMenu(array $data): Menu
    {
        return $this->menuService->createMenu($data);
    }

    /**
     * Update menu
     */
    public function updateMenu(string $id, array $data): Menu
    {
        return $this->menuService->updateMenu($id, $data);
    }

    /**
     * Delete menu
     */
    public function deleteMenu(string $id): bool
    {
        return $this->menuService->deleteMenu($id);
    }

    // ==================== CATEGORY MANAGEMENT ====================

    /**
     * Get all categories with pagination and filters
     */
    public function getAllCategories(array $filters = []): LengthAwarePaginator
    {
        $query = MenuCategory::with(['menu'])->withCount('menuItems');

        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (!empty($filters['menu_id'])) {
            $query->where('menu_id', $filters['menu_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        return $query->orderBy('sort_order')->orderBy('name')->paginate(15);
    }

    /**
     * Get category by ID
     */
    public function getCategoryById(string $id): MenuCategory
    {
        return MenuCategory::with(['menu', 'menuItems'])->findOrFail($id);
    }

    /**
     * Create new category
     */
    public function createCategory(array $data): MenuCategory
    {
        return $this->menuService->createCategory($data);
    }

    /**
     * Update category
     */
    public function updateCategory(string $id, array $data): MenuCategory
    {
        return $this->menuService->updateCategory($id, $data);
    }

    /**
     * Delete category
     */
    public function deleteCategory(string $id): bool
    {
        return $this->menuService->deleteCategory($id);
    }

    // ==================== MENU ITEM MANAGEMENT ====================

    /**
     * Get all menu items with pagination and filters
     */
    public function getAllMenuItems(array $filters = []): LengthAwarePaginator
    {
        $query = MenuItem::with(['category.menu', 'variants', 'addons']);

        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('code', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (!empty($filters['menu_id'])) {
            $query->whereHas('category', function($q) use ($filters) {
                $q->where('menu_id', $filters['menu_id']);
            });
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['is_featured'])) {
            $query->where('is_featured', $filters['is_featured']);
        }

        return $query->orderBy('sort_order')->orderBy('name')->paginate(15);
    }

    /**
     * Get menu item by ID
     */
    public function getMenuItemById(string $id): MenuItem
    {
        return MenuItem::with(['category.menu', 'variants', 'addons'])->findOrFail($id);
    }

    /**
     * Create new menu item
     */
    public function createMenuItem(array $data): MenuItem
    {
        return $this->menuService->createMenuItem($data);
    }

    /**
     * Update menu item
     */
    public function updateMenuItem(string $id, array $data): MenuItem
    {
        return $this->menuService->updateMenuItem($id, $data);
    }

    /**
     * Delete menu item
     */
    public function deleteMenuItem(string $id): bool
    {
        return $this->menuService->deleteMenuItem($id);
    }

    /**
     * Toggle menu item availability
     */
    public function toggleAvailability(string $id): MenuItem
    {
        return $this->menuService->toggleAvailability($id);
    }

    /**
     * Bulk update menu items
     */
    public function bulkUpdateMenuItems(array $itemIds, string $action): bool
    {
        return $this->menuService->bulkUpdateMenuItems($itemIds, $action);
    }

    // ==================== VARIANT MANAGEMENT ====================

    /**
     * Get menu item variants
     */
    public function getMenuItemVariants(string $menuItemId): Collection
    {
        return $this->menuService->getMenuItemVariants($menuItemId);
    }

    /**
     * Get all variants with pagination and filters
     */
    public function getAllVariants(array $filters = []): LengthAwarePaginator
    {
        $query = MenuItemVariant::with(['menuItem.category']);

        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('sku', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (!empty($filters['menu_item_id'])) {
            $query->where('menu_item_id', $filters['menu_item_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        return $query->orderBy('sort_order')->orderBy('name')->paginate(15);
    }

    /**
     * Get variant by ID
     */
    public function getVariantById(string $id): MenuItemVariant
    {
        return MenuItemVariant::with(['menuItem.category'])->findOrFail($id);
    }

    /**
     * Create new variant
     */
    public function createVariant(string $menuItemId, array $data): MenuItemVariant
    {
        return $this->menuService->createVariant($menuItemId, $data);
    }

    /**
     * Update variant
     */
    public function updateVariant(string $id, array $data): MenuItemVariant
    {
        return $this->menuService->updateVariant($id, $data);
    }

    /**
     * Delete variant
     */
    public function deleteVariant(string $id): bool
    {
        return $this->menuService->deleteVariant($id);
    }

    // ==================== ADDON MANAGEMENT ====================

    /**
     * Get menu item addons
     */
    public function getMenuItemAddons(string $menuItemId): Collection
    {
        return $this->menuService->getMenuItemAddons($menuItemId);
    }

    /**
     * Get all addons with pagination and filters
     */
    public function getAllAddons(array $filters = []): LengthAwarePaginator
    {
        $query = MenuItemAddon::with(['menuItem.category']);

        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('addon_group_name', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (!empty($filters['menu_item_id'])) {
            $query->where('menu_item_id', $filters['menu_item_id']);
        }

        if (!empty($filters['addon_group_name'])) {
            $query->where('addon_group_name', $filters['addon_group_name']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['is_required'])) {
            $query->where('is_required', $filters['is_required']);
        }

        return $query->orderBy('addon_group_name')->orderBy('sort_order')->orderBy('name')->paginate(15);
    }

    /**
     * Get addon by ID
     */
    public function getAddonById(string $id): MenuItemAddon
    {
        return MenuItemAddon::with(['menuItem.category'])->findOrFail($id);
    }

    /**
     * Create new addon
     */
    public function createAddon(string $menuItemId, array $data): MenuItemAddon
    {
        return $this->menuService->createAddon($menuItemId, $data);
    }

    /**
     * Update addon
     */
    public function updateAddon(string $id, array $data): MenuItemAddon
    {
        return $this->menuService->updateAddon($id, $data);
    }

    /**
     * Delete addon
     */
    public function deleteAddon(string $id): bool
    {
        return $this->menuService->deleteAddon($id);
    }

    // ==================== DASHBOARD STATS ====================

    /**
     * Get dashboard statistics
     */
    public function getDashboardStats(): array
    {
        return [
            'total_menus' => Menu::count(),
            'active_menus' => Menu::where('is_active', true)->count(),
            'total_categories' => MenuCategory::count(),
            'active_categories' => MenuCategory::where('is_active', true)->count(),
            'total_items' => MenuItem::count(),
            'active_items' => MenuItem::where('is_active', true)->count(),
            'featured_items' => MenuItem::where('is_featured', true)->count(),
            'total_variants' => MenuItemVariant::count(),
            'active_variants' => MenuItemVariant::where('is_active', true)->count(),
            'total_addons' => MenuItemAddon::count(),
            'active_addons' => MenuItemAddon::where('is_active', true)->count(),
        ];
    }

    /**
     * Get menus for dropdown
     */
    public function getMenusForDropdown(): Collection
    {
        return Menu::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get(['id', 'name']);
    }

    /**
     * Get categories for dropdown
     */
    public function getCategoriesForDropdown(string $menuId = null): Collection
    {
        $query = MenuCategory::where('is_active', true);
        
        if ($menuId) {
            $query->where('menu_id', $menuId);
        }
        
        return $query->with('menu:id,name')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get(['id', 'name', 'menu_id']);
    }

    /**
     * Get menu items for dropdown
     */
    public function getMenuItemsForDropdown(string $categoryId = null): Collection
    {
        $query = MenuItem::where('is_active', true);
        
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }
        
        return $query->with('category:id,name')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get(['id', 'name', 'category_id']);
    }
}
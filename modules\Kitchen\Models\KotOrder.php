<?php

namespace Modules\Kitchen\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Tenant;
use App\Models\Branch;
use App\Models\Order;
use App\Models\User;

class KotOrder extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'kitchen_id',
        'order_id',
        'kot_number',
        'status',
        'priority',
        'estimated_prep_time_minutes',
        'actual_prep_time_minutes',
        'items_data',
        'special_instructions',
        'assigned_to',
        'started_at',
        'completed_at',
        'sent_to_kitchen_at',
        'notes',
        'created_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'items_data' => 'array',
            'estimated_prep_time_minutes' => 'integer',
            'actual_prep_time_minutes' => 'integer',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'sent_to_kitchen_at' => 'datetime',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function kitchen()
    {
        return $this->belongsTo(Kitchen::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function kotItems()
    {
        return $this->hasMany(KotOrderItem::class);
    }

    // Scopes
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeForKitchen($query, $kitchenId)
    {
        return $query->where('kitchen_id', $kitchenId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeHighPriority($query)
    {
        return $query->where('priority', 'high');
    }

    public function scopeOverdue($query)
    {
        return $query->where('estimated_prep_time_minutes', '<', now())
                    ->whereIn('status', ['pending', 'preparing']);
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'preparing']);
    }

    // Helper methods
    public function isOverdue()
    {
        if (!$this->started_at || $this->status === 'completed') {
            return false;
        }

        $expectedCompletionTime = $this->started_at->addMinutes($this->estimated_prep_time_minutes);
        return now()->gt($expectedCompletionTime);
    }

    public function getElapsedTime()
    {
        if (!$this->started_at) {
            return 0;
        }

        $endTime = $this->completed_at ?? now();
        return $this->started_at->diffInMinutes($endTime);
    }

    public function getRemainingTime()
    {
        if (!$this->started_at || $this->status === 'completed') {
            return 0;
        }

        $elapsedTime = $this->getElapsedTime();
        $remainingTime = $this->estimated_prep_time_minutes - $elapsedTime;
        return max(0, $remainingTime);
    }

    public function getStatusColor()
    {
        return match($this->status) {
            'pending' => 'yellow',
            'preparing' => 'blue',
            'ready' => 'green',
            'completed' => 'gray',
            'cancelled' => 'red',
            default => 'gray'
        };
    }

    public function getPriorityColor()
    {
        return match($this->priority) {
            'low' => 'green',
            'normal' => 'blue',
            'high' => 'orange',
            'urgent' => 'red',
            default => 'blue'
        };
    }

    public function markAsStarted($userId = null)
    {
        $this->update([
            'status' => 'preparing',
            'started_at' => now(),
            'assigned_to' => $userId
        ]);
    }

    public function markAsReady($userId = null)
    {
        $this->update([
            'status' => 'ready',
            'completed_at' => now(),
            'actual_prep_time_minutes' => $this->getElapsedTime()
        ]);
    }

    public function markAsCompleted($userId = null)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => $this->completed_at ?? now(),
            'actual_prep_time_minutes' => $this->actual_prep_time_minutes ?? $this->getElapsedTime()
        ]);
    }
}
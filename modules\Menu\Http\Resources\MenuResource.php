<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class MenuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'branch_id' => $this->branch_id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'menu_type' => $this->menu_type,
            'start_time' => $this->start_time?->format('H:i'),
            'end_time' => $this->end_time?->format('H:i'),
            'available_days' => $this->available_days,
            'is_active' => $this->is_active,
            'is_default' => $this->is_default,
            'sort_order' => $this->sort_order,
            'status' => $this->status,
            // 'created_at' => $this->created_at,
            // 'updated_at' => $this->updated_at,
            
            // Relationships
            // 'tenant' => $this->whenLoaded('tenant'),
            // 'branch' => $this->whenLoaded('branch'),
            'categories' => CategoryResource::collection($this->whenLoaded('categories')),
            'menu_items' => MenuItemResource::collection($this->whenLoaded('menuItems')),
            'active_categories' => CategoryResource::collection($this->whenLoaded('activeCategories')),
            'active_menu_items' => MenuItemResource::collection($this->whenLoaded('activeMenuItems')),
        ];
    }
}
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Payment\Http\Controllers\PaymentController;
use Modules\Payment\Http\Controllers\TransactionController;

/*
|--------------------------------------------------------------------------
| Payment API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Payment module. These routes are
| globally accessible and can be used by other modules for payment
| processing and transaction management.
|
*/

// Payment Management Routes
Route::prefix('api/payments')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    // Payment Methods
    Route::get('methods', [PaymentController::class, 'getPaymentMethods'])
        ->name('api.payments.methods');
    
    // Process Payment
    Route::post('process', [PaymentController::class, 'processPayment'])
        ->middleware(['payment.validate', 'payment.rate.limit'])
        ->name('api.payments.process');
    
    // Payment Details
    Route::get('{payment}', [PaymentController::class, 'show'])
        ->name('api.payments.show');
    
    // Payment Status
    Route::get('{payment}/status', [PaymentController::class, 'getStatus'])
        ->name('api.payments.status');
    
    // Update Payment
    Route::put('{payment}', [PaymentController::class, 'update'])
        ->middleware('payment.auth')
        ->name('api.payments.update');
    
    // Cancel Payment
    Route::post('{payment}/cancel', [PaymentController::class, 'cancel'])
        ->middleware('payment.auth')
        ->name('api.payments.cancel');
    
    // List Payments
    Route::get('/', [PaymentController::class, 'index'])
        ->name('api.payments.index');
    
    // Payment Analytics
    Route::get('analytics/summary', [PaymentController::class, 'getAnalytics'])
        ->middleware('payment.auth')
        ->name('api.payments.analytics');
    
    // Payment Reconciliation
    Route::post('reconcile', [PaymentController::class, 'reconcile'])
        ->middleware('payment.auth')
        ->name('api.payments.reconcile');
    
    // Refund Routes
    Route::prefix('{payment}/refunds')->group(function () {
        Route::post('/', [PaymentController::class, 'processRefund'])
            ->middleware('payment.auth')
            ->name('api.payments.refunds.process');
    });
});

// Transaction Management Routes
Route::prefix('api/transactions')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    // Create Transaction
    Route::post('/', [TransactionController::class, 'store'])
        ->middleware('payment.validate')
        ->name('api.transactions.store');
    
    // Get Transaction
    Route::get('{transaction}', [TransactionController::class, 'show'])
        ->name('api.transactions.show');
    
    // List Transactions
    Route::get('/', [TransactionController::class, 'index'])
        ->name('api.transactions.index');
    
    // Update Transaction
    Route::put('{transaction}', [TransactionController::class, 'update'])
        ->middleware('payment.auth')
        ->name('api.transactions.update');
    
    // Cancel Transaction
    Route::post('{transaction}/cancel', [TransactionController::class, 'cancel'])
        ->middleware('payment.auth')
        ->name('api.transactions.cancel');
    
    // Transaction Summary
    Route::get('summary/overview', [TransactionController::class, 'getSummary'])
        ->name('api.transactions.summary');
    
    // Transaction Analytics
    Route::get('analytics/detailed', [TransactionController::class, 'getAnalytics'])
        ->middleware('payment.auth')
        ->name('api.transactions.analytics');
    
    // Transactions by Entity
    Route::get('entity/{type}/{id}', [TransactionController::class, 'getByEntity'])
        ->name('api.transactions.by-entity');
    
    // Transaction Reconciliation
    Route::post('reconcile', [TransactionController::class, 'reconcile'])
        ->middleware('payment.auth')
        ->name('api.transactions.reconcile');
    
    // Export Transactions
    Route::post('export', [TransactionController::class, 'export'])
        ->middleware('payment.auth')
        ->name('api.transactions.export');
});

// Global Payment Helper Routes (for other modules)
Route::prefix('api/payment-helpers')->middleware(['api', 'auth:sanctum'])->group(function () {
    
    // Currency Formatting
    Route::post('format-currency', function (Request $request) {
        $amount = $request->input('amount');
        $currency = $request->input('currency', 'USD');
        $locale = $request->input('locale', 'en_US');
        
        return response()->json([
            'formatted' => payment_helper()->formatCurrency($amount, $currency, $locale),
            'amount' => $amount,
            'currency' => $currency,
            'locale' => $locale
        ]);
    })->name('api.payment-helpers.format-currency');
    
    // Payment Method Icons
    Route::get('payment-method-icon/{method}', function ($method) {
        return response()->json([
            'icon' => payment_helper()->getPaymentMethodIcon($method),
            'method' => $method
        ]);
    })->name('api.payment-helpers.method-icon');
    
    // Status Badge Classes
    Route::get('status-badge/{status}', function ($status) {
        return response()->json([
            'badge_class' => payment_helper()->getStatusBadgeClass($status),
            'status' => $status
        ]);
    })->name('api.payment-helpers.status-badge');
    
    // Calculate Fees
    Route::post('calculate-fees', function (Request $request) {
        $amount = $request->input('amount');
        $paymentMethod = $request->input('payment_method');
        
        return response()->json([
            'fees' => payment_helper()->calculateFees($amount, $paymentMethod),
            'amount' => $amount,
            'payment_method' => $paymentMethod
        ]);
    })->name('api.payment-helpers.calculate-fees');
    
    // Validate Amount
    Route::post('validate-amount', function (Request $request) {
        $amount = $request->input('amount');
        $currency = $request->input('currency', 'USD');
        
        return response()->json([
            'is_valid' => payment_helper()->validateAmount($amount, $currency),
            'amount' => $amount,
            'currency' => $currency
        ]);
    })->name('api.payment-helpers.validate-amount');
    
    // Generate Payment Summary
    Route::post('payment-summary', function (Request $request) {
        $payments = $request->input('payments', []);
        
        return response()->json([
            'summary' => payment_helper()->generatePaymentSummary($payments)
        ]);
    })->name('api.payment-helpers.payment-summary');
    
    // Calculate Change
    Route::post('calculate-change', function (Request $request) {
        $totalAmount = $request->input('total_amount');
        $paidAmount = $request->input('paid_amount');
        
        return response()->json([
            'change' => payment_helper()->calculateChange($totalAmount, $paidAmount),
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount
        ]);
    })->name('api.payment-helpers.calculate-change');
});

// Webhook Routes (for payment gateway callbacks)
Route::prefix('api/payment-webhooks')->group(function () {
    
    // Stripe Webhook
    Route::post('stripe', function (Request $request) {
        // Handle Stripe webhook
        $payload = $request->getContent();
        $signature = $request->header('Stripe-Signature');
        
        // Process webhook with payment service
        $result = payment_service()->handleStripeWebhook($payload, $signature);
        
        return response()->json($result);
    })->name('api.payment-webhooks.stripe');
    
    // PayPal Webhook
    Route::post('paypal', function (Request $request) {
        // Handle PayPal webhook
        $payload = $request->all();
        
        // Process webhook with payment service
        $result = payment_service()->handlePayPalWebhook($payload);
        
        return response()->json($result);
    })->name('api.payment-webhooks.paypal');
    
    // Square Webhook
    Route::post('square', function (Request $request) {
        // Handle Square webhook
        $payload = $request->all();
        $signature = $request->header('X-Square-Signature');
        
        // Process webhook with payment service
        $result = payment_service()->handleSquareWebhook($payload, $signature);
        
        return response()->json($result);
    })->name('api.payment-webhooks.square');
});

// Health Check Route
Route::get('api/payment-health', function () {
    return response()->json([
        'status' => 'healthy',
        'module' => 'Payment',
        'version' => '1.0.0',
        'timestamp' => now()->toISOString(),
        'services' => [
            'payment_service' => class_exists('Modules\\Payment\\Services\\PaymentService'),
            'transaction_service' => class_exists('Modules\\Payment\\Services\\TransactionService'),
            'refund_service' => class_exists('Modules\\Payment\\Services\\RefundService'),
            'payment_helper' => class_exists('Modules\\Payment\\Helpers\\PaymentHelper'),
        ]
    ]);
})->name('api.payment.health');

<?php

namespace Modules\Menu\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Services\PricingService;
use Modules\Menu\Services\AvailabilityService;

class MenuServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the PricingService
        $this->app->singleton(PricingService::class, function ($app) {
            return new PricingService();
        });
        
        // Register the AvailabilityService
        $this->app->singleton(AvailabilityService::class, function ($app) {
            return new AvailabilityService();
        });
        
        // Register the MenuService with dependencies
        $this->app->singleton(MenuService::class, function ($app) {
            return new MenuService(
                $app->make(PricingService::class),
                $app->make(AvailabilityService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'menu');
        
        // Load migrations from module
        $this->loadMigrationsFrom(__DIR__ . '/../Database/Migrations');
        
        // Publish config
        $this->publishes([
            __DIR__ . '/../Config/menu.php' => config_path('menu.php'),
        ], 'menu-config');
        
        // Merge config
        $this->mergeConfigFrom(
            __DIR__ . '/../Config/menu.php',
            'menu'
        );
    }

    /**
     * Load module routes
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            Route::group([
                'middleware' => 'api',
                'namespace' => 'Modules\\Menu\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/api.php';
            });
        }

        // Load Web routes
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            Route::group([
                'middleware' => 'web',
                'namespace' => 'Modules\\Menu\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/web.php';
            });
        }
    }
}
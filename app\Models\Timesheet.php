<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Timesheet extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'user_id',
        'shift_id',
        'shift_assignment_id',
        'date',
        'clock_in_time',
        'clock_out_time',
        'break_start_time',
        'break_end_time',
        'total_hours_worked',
        'regular_hours',
        'overtime_hours',
        'break_duration_minutes',
        'hourly_rate',
        'total_pay',
        'status',
        'notes',
        'approved_by',
        'approved_at',
        'clock_in_location',
        'clock_out_location',
        'clock_in_ip',
        'clock_out_ip',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'date' => 'date',
            'clock_in_time' => 'datetime',
            'clock_out_time' => 'datetime',
            'break_start_time' => 'datetime',
            'break_end_time' => 'datetime',
            'total_hours_worked' => 'decimal:2',
            'regular_hours' => 'decimal:2',
            'overtime_hours' => 'decimal:2',
            'hourly_rate' => 'decimal:2',
            'total_pay' => 'decimal:2',
            'approved_at' => 'datetime',
            'clock_in_location' => 'array',
            'clock_out_location' => 'array',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    public function shiftAssignment()
    {
        return $this->belongsTo(ShiftAssignment::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByDate($query, $date)
    {
        return $query->where('date', $date);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeActive($query)
    {
        return $query->whereNotNull('clock_in_time')
                    ->whereNull('clock_out_time');
    }

    // Methods
    public function clockIn($location = null, $ipAddress = null)
    {
        $this->update([
            'clock_in_time' => now(),
            'clock_in_location' => $location,
            'clock_in_ip' => $ipAddress,
            'status' => 'active',
        ]);
    }

    public function clockOut($location = null, $ipAddress = null)
    {
        $this->update([
            'clock_out_time' => now(),
            'clock_out_location' => $location,
            'clock_out_ip' => $ipAddress,
            'status' => 'completed',
        ]);

        $this->calculateHours();
    }

    public function startBreak()
    {
        $this->update([
            'break_start_time' => now(),
        ]);
    }

    public function endBreak()
    {
        $this->update([
            'break_end_time' => now(),
        ]);

        $this->calculateBreakDuration();
    }

    public function calculateHours()
    {
        if (!$this->clock_in_time || !$this->clock_out_time) {
            return;
        }

        $totalMinutes = $this->clock_out_time->diffInMinutes($this->clock_in_time);
        $workingMinutes = $totalMinutes - $this->break_duration_minutes;
        $totalHours = $workingMinutes / 60;

        $regularHours = min($totalHours, 8); // Assuming 8 hours is regular time
        $overtimeHours = max(0, $totalHours - 8);

        $this->update([
            'total_hours_worked' => $totalHours,
            'regular_hours' => $regularHours,
            'overtime_hours' => $overtimeHours,
        ]);

        $this->calculatePay();
    }

    public function calculateBreakDuration()
    {
        if (!$this->break_start_time || !$this->break_end_time) {
            return;
        }

        $breakMinutes = $this->break_end_time->diffInMinutes($this->break_start_time);
        
        $this->update([
            'break_duration_minutes' => $breakMinutes,
        ]);
    }

    public function calculatePay()
    {
        if (!$this->hourly_rate || !$this->total_hours_worked) {
            return;
        }

        $regularPay = $this->regular_hours * $this->hourly_rate;
        $overtimePay = $this->overtime_hours * $this->hourly_rate * 1.5; // 1.5x overtime rate
        
        $this->update([
            'total_pay' => $regularPay + $overtimePay,
        ]);
    }

    public function approve($approvedBy)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);
    }
}
<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreVariantRequest;
use Modules\Menu\Http\Requests\UpdateVariantRequest;
use Modules\Menu\Http\Resources\VariationResource;

class VariantController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Create menu item variant
     */
    public function store(StoreVariantRequest $request): JsonResponse
    {
        $variant = $this->menuService->createVariant($request->menu_item_id, $request->validated());
        return response()->json(new VariationResource($variant), 201);
    }

    /**
     * Update menu item variant
     */
    public function update(UpdateVariantRequest $request, string $variantId): JsonResponse
    {
        $variant = $this->menuService->updateVariant($variantId, $request->validated());
        return response()->json(new VariationResource($variant));
    }

    /**
     * Delete menu item variant
     */
    public function destroy(string $variantId): JsonResponse
    {
        $this->menuService->deleteVariant($variantId);
        return response()->json(['message' => 'Variant deleted successfully'], 200);
    }

    /**
     * Get variants for a menu item
     */
    public function index(string $menuItemId): JsonResponse
    {
        $variants = $this->menuService->getMenuItemVariants($menuItemId);
        return response()->json(VariationResource::collection($variants));
    }
} 
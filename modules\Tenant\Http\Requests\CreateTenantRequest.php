<?php

namespace Modules\Tenant\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateTenantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:tenants,code',
            'business_type' => 'nullable|string|max:100',
            'country_id' => 'nullable|exists:countries,id',
            'primary_contact_name' => 'required|string|max:255',
            'contact_email' => 'required|email|max:255|unique:tenants,contact_email',
            'contact_phone' => 'required|string|max:20',
            'business_address' => 'nullable|string|max:500',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'tax_number' => 'nullable|string|max:50',
            'business_license' => 'nullable|string|max:100',
            'logo_url' => 'nullable|url|max:500',
            'website_url' => 'nullable|url|max:500',
            'social_media' => 'nullable|array',
            'social_media.facebook' => 'nullable|url',
            'social_media.instagram' => 'nullable|url',
            'social_media.twitter' => 'nullable|url',
            'social_media.linkedin' => 'nullable|url',
            'business_hours' => 'nullable|array',
            'business_hours.*.day' => 'required_with:business_hours|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'business_hours.*.open_time' => 'required_with:business_hours|date_format:H:i',
            'business_hours.*.close_time' => 'required_with:business_hours|date_format:H:i|after:business_hours.*.open_time',
            'business_hours.*.is_closed' => 'nullable|boolean',
            'timezone' => 'nullable|string|max:50',
            'date_format' => 'nullable|string|max:20',
            'currency_code' => 'nullable|string|size:3',
            'language_code' => 'nullable|string|size:2',
            'status' => 'nullable|in:active,inactive,suspended',
            'trial_ends_at' => 'nullable|date|after:today',
            
            // Subscription related
            'plan_id' => 'nullable|exists:subscription_plans,id',
            'billing_cycle' => 'nullable|in:monthly,yearly,quarterly',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The tenant name is required.',
            'name.max' => 'The tenant name may not be greater than 255 characters.',
            'code.unique' => 'This tenant code is already taken.',
            'code.max' => 'The tenant code may not be greater than 50 characters.',
            'primary_contact_name.required' => 'The primary contact name is required.',
            'contact_email.required' => 'The contact email is required.',
            'contact_email.email' => 'Please provide a valid email address.',
            'contact_email.unique' => 'This email address is already registered.',
            'contact_phone.required' => 'The contact phone number is required.',
            'country_id.exists' => 'The selected country is invalid.',
            'latitude.between' => 'The latitude must be between -90 and 90.',
            'longitude.between' => 'The longitude must be between -180 and 180.',
            'logo_url.url' => 'The logo URL must be a valid URL.',
            'website_url.url' => 'The website URL must be a valid URL.',
            'social_media.facebook.url' => 'The Facebook URL must be a valid URL.',
            'social_media.instagram.url' => 'The Instagram URL must be a valid URL.',
            'social_media.twitter.url' => 'The Twitter URL must be a valid URL.',
            'social_media.linkedin.url' => 'The LinkedIn URL must be a valid URL.',
            'business_hours.*.day.in' => 'Invalid day specified in business hours.',
            'business_hours.*.open_time.date_format' => 'Open time must be in HH:MM format.',
            'business_hours.*.close_time.date_format' => 'Close time must be in HH:MM format.',
            'business_hours.*.close_time.after' => 'Close time must be after open time.',
            'currency_code.size' => 'Currency code must be exactly 3 characters.',
            'language_code.size' => 'Language code must be exactly 2 characters.',
            'status.in' => 'Status must be one of: active, inactive, suspended.',
            'trial_ends_at.after' => 'Trial end date must be in the future.',
            'plan_id.exists' => 'The selected subscription plan is invalid.',
            'billing_cycle.in' => 'Billing cycle must be one of: monthly, yearly, quarterly.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'tenant name',
            'code' => 'tenant code',
            'business_type' => 'business type',
            'country_id' => 'country',
            'primary_contact_name' => 'primary contact name',
            'contact_email' => 'contact email',
            'contact_phone' => 'contact phone',
            'business_address' => 'business address',
            'tax_number' => 'tax number',
            'business_license' => 'business license',
            'logo_url' => 'logo URL',
            'website_url' => 'website URL',
            'social_media' => 'social media',
            'business_hours' => 'business hours',
            'timezone' => 'timezone',
            'date_format' => 'date format',
            'currency_code' => 'currency code',
            'language_code' => 'language code',
            'trial_ends_at' => 'trial end date',
            'plan_id' => 'subscription plan',
            'billing_cycle' => 'billing cycle',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Normalize currency code to uppercase
        if ($this->has('currency_code')) {
            $this->merge([
                'currency_code' => strtoupper($this->currency_code)
            ]);
        }

        // Normalize language code to lowercase
        if ($this->has('language_code')) {
            $this->merge([
                'language_code' => strtolower($this->language_code)
            ]);
        }

        // Normalize tenant code
        if ($this->has('code')) {
            $this->merge([
                'code' => strtolower(str_replace(' ', '-', $this->code))
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation for business hours
            if ($this->has('business_hours')) {
                $this->validateBusinessHours($validator);
            }

            // Validate social media URLs format
            if ($this->has('social_media')) {
                $this->validateSocialMediaUrls($validator);
            }
        });
    }

    /**
     * Validate business hours logic
     */
    protected function validateBusinessHours($validator): void
    {
        $businessHours = $this->input('business_hours', []);
        $days = [];

        foreach ($businessHours as $index => $hours) {
            if (isset($hours['day'])) {
                if (in_array($hours['day'], $days)) {
                    $validator->errors()->add(
                        "business_hours.{$index}.day",
                        "Duplicate day '{$hours['day']}' in business hours."
                    );
                }
                $days[] = $hours['day'];
            }

            // Validate that closed days don't have open/close times
            if (isset($hours['is_closed']) && $hours['is_closed']) {
                if (isset($hours['open_time']) || isset($hours['close_time'])) {
                    $validator->errors()->add(
                        "business_hours.{$index}",
                        "Closed days should not have open or close times."
                    );
                }
            }
        }
    }

    /**
     * Validate social media URLs
     */
    protected function validateSocialMediaUrls($validator): void
    {
        $socialMedia = $this->input('social_media', []);
        $platformPatterns = [
            'facebook' => '/^https?:\/\/(www\.)?facebook\.com\/.+/',
            'instagram' => '/^https?:\/\/(www\.)?instagram\.com\/.+/',
            'twitter' => '/^https?:\/\/(www\.)?(twitter\.com|x\.com)\/.+/',
            'linkedin' => '/^https?:\/\/(www\.)?linkedin\.com\/.+/',
        ];

        foreach ($socialMedia as $platform => $url) {
            if (!empty($url) && isset($platformPatterns[$platform])) {
                if (!preg_match($platformPatterns[$platform], $url)) {
                    $validator->errors()->add(
                        "social_media.{$platform}",
                        "The {$platform} URL format is invalid."
                    );
                }
            }
        }
    }
}
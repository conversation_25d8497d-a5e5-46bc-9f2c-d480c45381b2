<?php

namespace Modules\Menu\Services;

use App\Models\MenuItem;
use App\Models\MenuItemBranch;
use App\Models\MenuItemVariant;
use App\Models\MenuItemAddon;
use Illuminate\Support\Collection;

class PricingService
{
    /**
     * Calculate total price for menu item with modifiers.
     */
    public function calculateTotalPrice(
        string $menuItemId, 
        array $variants = [], 
        array $addons = [], 
        ?string $branchId = null
    ): float {
        $basePrice = $this->getMenuItemPrice($menuItemId, $branchId);
        $variantAdjustment = $this->calculateVariantAdjustment($variants);
        $addonPrice = $this->calculateAddonPrice($addons);
        
        return $basePrice + $variantAdjustment + $addonPrice;
    }

    /**
     * Get menu item base price for specific branch.
     */
    public function getMenuItemPrice(string $menuItemId, ?string $branchId = null): float
    {
        if ($branchId) {
            $branchSettings = MenuItemBranch::where('menu_item_id', $menuItemId)
                ->where('branch_id', $branchId)
                ->first();
            
            if ($branchSettings && $branchSettings->price_override !== null) {
                return (float) $branchSettings->price_override;
            }
        }
        
        $menuItem = MenuItem::findOrFail($menuItemId);
        return (float) $menuItem->price;
    }

    /**
     * Calculate price adjustment from variants.
     */
    public function calculateVariantAdjustment(array $variantIds): float
    {
        if (empty($variantIds)) {
            return 0.0;
        }
        
        return MenuItemVariant::whereIn('id', $variantIds)
            ->sum('price_adjustment') ?? 0.0;
    }

    /**
     * Calculate total price from addons.
     */
    public function calculateAddonPrice(array $addons): float
    {
        if (empty($addons)) {
            return 0.0;
        }
        
        $totalPrice = 0.0;
        
        foreach ($addons as $addon) {
            $addonId = $addon['id'] ?? $addon['addon_id'] ?? null;
            $quantity = $addon['quantity'] ?? 1;
            
            if ($addonId) {
                $addonModel = MenuItemAddon::find($addonId);
                if ($addonModel) {
                    $totalPrice += ($addonModel->price * $quantity);
                }
            }
        }
        
        return $totalPrice;
    }

    /**
     * Get pricing breakdown for menu item.
     */
    public function getPricingBreakdown(
        string $menuItemId, 
        array $variants = [], 
        array $addons = [], 
        ?string $branchId = null
    ): array {
        $basePrice = $this->getMenuItemPrice($menuItemId, $branchId);
        $variantAdjustment = $this->calculateVariantAdjustment($variants);
        $addonPrice = $this->calculateAddonPrice($addons);
        $totalPrice = $basePrice + $variantAdjustment + $addonPrice;
        
        return [
            'base_price' => $basePrice,
            'variant_adjustment' => $variantAdjustment,
            'addon_price' => $addonPrice,
            'total_price' => $totalPrice,
            'breakdown' => [
                'variants' => $this->getVariantPriceBreakdown($variants),
                'addons' => $this->getAddonPriceBreakdown($addons)
            ]
        ];
    }

    /**
     * Get variant price breakdown.
     */
    protected function getVariantPriceBreakdown(array $variantIds): array
    {
        if (empty($variantIds)) {
            return [];
        }
        
        return MenuItemVariant::whereIn('id', $variantIds)
            ->get(['id', 'name', 'price_adjustment'])
            ->map(function ($variant) {
                return [
                    'id' => $variant->id,
                    'name' => $variant->name,
                    'price_adjustment' => (float) $variant->price_adjustment
                ];
            })
            ->toArray();
    }

    /**
     * Get addon price breakdown.
     */
    protected function getAddonPriceBreakdown(array $addons): array
    {
        if (empty($addons)) {
            return [];
        }
        
        $breakdown = [];
        
        foreach ($addons as $addon) {
            $addonId = $addon['id'] ?? $addon['addon_id'] ?? null;
            $quantity = $addon['quantity'] ?? 1;
            
            if ($addonId) {
                $addonModel = MenuItemAddon::find($addonId);
                if ($addonModel) {
                    $breakdown[] = [
                        'id' => $addonModel->id,
                        'name' => $addonModel->name,
                        'unit_price' => (float) $addonModel->price,
                        'quantity' => $quantity,
                        'total_price' => (float) ($addonModel->price * $quantity)
                    ];
                }
            }
        }
        
        return $breakdown;
    }

    /**
     * Update branch-specific pricing.
     */
    public function updateBranchPricing(string $menuItemId, string $branchId, ?float $priceOverride): void
    {
        MenuItemBranch::updateOrCreate(
            [
                'menu_item_id' => $menuItemId,
                'branch_id' => $branchId
            ],
            [
                'price_override' => $priceOverride
            ]
        );
    }

    /**
     * Get all branch-specific pricing for menu item.
     */
    public function getBranchPricing(string $menuItemId): Collection
    {
        return MenuItemBranch::where('menu_item_id', $menuItemId)
            ->whereNotNull('price_override')
            ->with('branch:id,name')
            ->get(['branch_id', 'price_override'])
            ->map(function ($item) {
                return [
                    'branch_id' => $item->branch_id,
                    'branch_name' => $item->branch->name ?? 'Unknown',
                    'price_override' => (float) $item->price_override
                ];
            });
    }

    /**
     * Calculate cost for menu item with modifiers.
     */
    public function calculateTotalCost(
        string $menuItemId, 
        array $variants = [], 
        array $addons = [], 
        ?string $branchId = null
    ): float {
        $baseCost = $this->getMenuItemCost($menuItemId, $branchId);
        $variantCostAdjustment = $this->calculateVariantCostAdjustment($variants);
        $addonCost = $this->calculateAddonCost($addons);
        
        return $baseCost + $variantCostAdjustment + $addonCost;
    }

    /**
     * Get menu item base cost for specific branch.
     */
    public function getMenuItemCost(string $menuItemId, ?string $branchId = null): float
    {
        if ($branchId) {
            $branchSettings = MenuItemBranch::where('menu_item_id', $menuItemId)
                ->where('branch_id', $branchId)
                ->first();
            
            if ($branchSettings && $branchSettings->cost_override !== null) {
                return (float) $branchSettings->cost_override;
            }
        }
        
        $menuItem = MenuItem::findOrFail($menuItemId);
        return (float) $menuItem->cost;
    }

    /**
     * Calculate cost adjustment from variants.
     */
    public function calculateVariantCostAdjustment(array $variantIds): float
    {
        if (empty($variantIds)) {
            return 0.0;
        }
        
        return MenuItemVariant::whereIn('id', $variantIds)
            ->sum('cost_adjustment') ?? 0.0;
    }

    /**
     * Calculate total cost from addons.
     */
    public function calculateAddonCost(array $addons): float
    {
        if (empty($addons)) {
            return 0.0;
        }
        
        $totalCost = 0.0;
        
        foreach ($addons as $addon) {
            $addonId = $addon['id'] ?? $addon['addon_id'] ?? null;
            $quantity = $addon['quantity'] ?? 1;
            
            if ($addonId) {
                $addonModel = MenuItemAddon::find($addonId);
                if ($addonModel) {
                    $totalCost += ($addonModel->cost * $quantity);
                }
            }
        }
        
        return $totalCost;
    }

    /**
     * Calculate profit margin for menu item.
     */
    public function calculateProfitMargin(
        string $menuItemId, 
        array $variants = [], 
        array $addons = [], 
        ?string $branchId = null
    ): array {
        $totalPrice = $this->calculateTotalPrice($menuItemId, $variants, $addons, $branchId);
        $totalCost = $this->calculateTotalCost($menuItemId, $variants, $addons, $branchId);
        
        $profit = $totalPrice - $totalCost;
        $marginPercentage = $totalPrice > 0 ? ($profit / $totalPrice) * 100 : 0;
        
        return [
            'total_price' => $totalPrice,
            'total_cost' => $totalCost,
            'profit' => $profit,
            'margin_percentage' => round($marginPercentage, 2)
        ];
    }

    /**
     * Get pricing statistics for menu items.
     */
    public function getPricingStatistics(?string $branchId = null): array
    {
        $query = MenuItem::query();
        
        if ($branchId) {
            $query->leftJoin('menu_item_branches', function ($join) use ($branchId) {
                $join->on('menu_items.id', '=', 'menu_item_branches.menu_item_id')
                     ->where('menu_item_branches.branch_id', $branchId);
            });
            
            $query->selectRaw('
                COUNT(*) as total_items,
                AVG(COALESCE(menu_item_branches.price_override, menu_items.price)) as avg_price,
                MIN(COALESCE(menu_item_branches.price_override, menu_items.price)) as min_price,
                MAX(COALESCE(menu_item_branches.price_override, menu_items.price)) as max_price,
                AVG(COALESCE(menu_item_branches.cost_override, menu_items.cost)) as avg_cost
            ');
        } else {
            $query->selectRaw('
                COUNT(*) as total_items,
                AVG(price) as avg_price,
                MIN(price) as min_price,
                MAX(price) as max_price,
                AVG(cost) as avg_cost
            ');
        }
        
        $stats = $query->first();
        
        return [
            'total_items' => (int) $stats->total_items,
            'average_price' => round((float) $stats->avg_price, 2),
            'minimum_price' => round((float) $stats->min_price, 2),
            'maximum_price' => round((float) $stats->max_price, 2),
            'average_cost' => round((float) $stats->avg_cost, 2),
            'average_margin' => $stats->avg_price > 0 ? 
                round((($stats->avg_price - $stats->avg_cost) / $stats->avg_price) * 100, 2) : 0
        ];
    }
}
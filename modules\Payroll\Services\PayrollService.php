<?php

namespace Modules\Payroll\Services;

use App\Models\PayPeriod;
use App\Models\Payslip;
use App\Models\User;
use App\Models\Timesheet;
use App\Models\ShiftAssignment;
use App\Models\Tenant;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class PayrollService
{
    /**
     * Create a new pay period.
     */
    public function createPayPeriod(array $data): PayPeriod
    {
        $data['name'] = $data['name'] ?? $this->generatePayPeriodName($data['period_type'], $data['start_date'], $data['end_date']);
        
        return PayPeriod::create($data);
    }

    /**
     * Get pay periods for a tenant.
     */
    public function getPayPeriods(int $tenantId, array $filters = []): Collection
    {
        $query = PayPeriod::forTenant($tenantId)
            ->with(['payslips', 'processedBy'])
            ->orderBy('start_date', 'desc');

        if (isset($filters['status'])) {
            $query->withStatus($filters['status']);
        }

        if (isset($filters['period_type'])) {
            $query->where('period_type', $filters['period_type']);
        }

        if (isset($filters['year'])) {
            $query->whereYear('start_date', $filters['year']);
        }

        return $query->get();
    }

    /**
     * Generate payroll for a pay period.
     */
    public function generatePayroll(int $payPeriodId, array $options = []): array
    {
        $payPeriod = PayPeriod::findOrFail($payPeriodId);
        
        if (!$payPeriod->canBeProcessed()) {
            throw new \Exception('Pay period cannot be processed in its current status.');
        }

        $payPeriod->update(['status' => 'processing']);

        try {
            // Get all active employees for the tenant
            $employees = User::where('tenant_id', $payPeriod->tenant_id)
                ->where('is_active', true)
                ->whereIn('role', ['staff', 'manager', 'admin'])
                ->get();

            $generatedPayslips = [];
            $errors = [];

            foreach ($employees as $employee) {
                try {
                    // Check if payslip already exists
                    $existingPayslip = Payslip::where('pay_period_id', $payPeriodId)
                        ->where('user_id', $employee->id)
                        ->first();

                    if ($existingPayslip && !($options['regenerate'] ?? false)) {
                        continue;
                    }

                    $payslipData = $this->calculateEmployeePayroll($employee, $payPeriod);
                    
                    if ($existingPayslip) {
                        $existingPayslip->update($payslipData);
                        $generatedPayslips[] = $existingPayslip;
                    } else {
                        $payslipData['payslip_number'] = Payslip::generatePayslipNumber($payPeriod->tenant_id, $payPeriodId);
                        $generatedPayslips[] = Payslip::create($payslipData);
                    }
                } catch (\Exception $e) {
                    $errors[] = [
                        'employee_id' => $employee->id,
                        'employee_name' => $employee->name,
                        'error' => $e->getMessage()
                    ];
                }
            }

            // Update pay period totals
            $payPeriod->calculateTotals();
            $payPeriod->update([
                'status' => 'completed',
                'processed_at' => Carbon::now(),
                'processed_by' => auth()->id()
            ]);

            return [
                'success' => true,
                'generated_payslips' => count($generatedPayslips),
                'errors' => $errors,
                'payslips' => $generatedPayslips
            ];

        } catch (\Exception $e) {
            $payPeriod->update(['status' => 'draft']);
            throw $e;
        }
    }

    /**
     * Calculate payroll for a specific employee.
     */
    public function calculateEmployeePayroll(User $employee, PayPeriod $payPeriod): array
    {
        // Get employee's timesheets for the pay period
        $timesheets = Timesheet::where('user_id', $employee->id)
            ->where('tenant_id', $payPeriod->tenant_id)
            ->whereBetween('clock_in_time', [$payPeriod->start_date, $payPeriod->end_date->endOfDay()])
            ->whereNotNull('clock_out_time')
            ->get();

        // Calculate hours
        $hoursData = $this->calculateHours($timesheets);
        
        // Get employee rates
        $hourlyRate = $employee->hourly_rate ?? 0;
        $salary = $employee->salary ?? 0;
        $overtimeRate = $hourlyRate * 1.5; // Standard overtime multiplier

        // Calculate earnings
        $regularPay = $hoursData['regular_hours'] * $hourlyRate;
        $overtimePay = $hoursData['overtime_hours'] * $overtimeRate;
        $holidayPay = $hoursData['holiday_hours'] * $hourlyRate;
        
        // Add salary if applicable
        if ($salary > 0) {
            $regularPay += $this->calculateSalaryPortion($salary, $payPeriod);
        }

        $grossPay = $regularPay + $overtimePay + $holidayPay;

        // Calculate deductions
        $deductions = $this->calculateDeductions($grossPay, $employee);
        
        // Calculate year-to-date amounts
        $ytdAmounts = $this->calculateYearToDateAmounts($employee, $payPeriod, $grossPay, $grossPay - $deductions['total']);

        return [
            'tenant_id' => $payPeriod->tenant_id,
            'pay_period_id' => $payPeriod->id,
            'user_id' => $employee->id,
            'branch_id' => $employee->branch_id,
            'employee_name' => $employee->name,
            'employee_email' => $employee->email,
            'employee_id' => $employee->employee_id,
            'position' => $employee->position,
            'department' => $employee->department,
            'hourly_rate' => $hourlyRate,
            'salary' => $salary,
            'regular_hours' => $hoursData['regular_hours'],
            'overtime_hours' => $hoursData['overtime_hours'],
            'overtime_rate' => $overtimeRate,
            'holiday_hours' => $hoursData['holiday_hours'],
            'sick_hours' => $hoursData['sick_hours'],
            'vacation_hours' => $hoursData['vacation_hours'],
            'regular_pay' => $regularPay,
            'overtime_pay' => $overtimePay,
            'holiday_pay' => $holidayPay,
            'bonus' => 0, // Can be added later
            'commission' => 0, // Can be added later
            'allowances' => 0, // Can be added later
            'gross_pay' => $grossPay,
            'tax_deduction' => $deductions['tax'],
            'social_security' => $deductions['social_security'],
            'health_insurance' => $deductions['health_insurance'],
            'retirement_contribution' => $deductions['retirement'],
            'other_deductions' => $deductions['other'],
            'total_deductions' => $deductions['total'],
            'net_pay' => $grossPay - $deductions['total'],
            'year_to_date_gross' => $ytdAmounts['gross'],
            'year_to_date_net' => $ytdAmounts['net'],
            'status' => 'draft',
        ];
    }

    /**
     * Calculate hours from timesheets.
     */
    private function calculateHours(Collection $timesheets): array
    {
        $regularHours = 0;
        $overtimeHours = 0;
        $holidayHours = 0;
        $sickHours = 0;
        $vacationHours = 0;

        foreach ($timesheets as $timesheet) {
            $hoursWorked = $timesheet->hours_worked ?? 0;
            
            // Determine if it's a holiday (this could be enhanced with a holidays table)
            $isHoliday = $this->isHoliday($timesheet->clock_in_time);
            
            if ($isHoliday) {
                $holidayHours += $hoursWorked;
            } elseif ($hoursWorked > 8) {
                // Standard 8-hour day, anything over is overtime
                $regularHours += 8;
                $overtimeHours += ($hoursWorked - 8);
            } else {
                $regularHours += $hoursWorked;
            }
        }

        return [
            'regular_hours' => $regularHours,
            'overtime_hours' => $overtimeHours,
            'holiday_hours' => $holidayHours,
            'sick_hours' => $sickHours,
            'vacation_hours' => $vacationHours,
        ];
    }

    /**
     * Calculate salary portion for the pay period.
     */
    private function calculateSalaryPortion(float $annualSalary, PayPeriod $payPeriod): float
    {
        $daysInPeriod = $payPeriod->getDurationInDays();
        $daysInYear = 365;
        
        return ($annualSalary / $daysInYear) * $daysInPeriod;
    }

    /**
     * Calculate deductions for an employee.
     */
    private function calculateDeductions(float $grossPay, User $employee): array
    {
        // Basic tax calculation (this should be enhanced with proper tax tables)
        $taxRate = 0.20; // 20% tax rate
        $socialSecurityRate = 0.062; // 6.2%
        $healthInsuranceRate = 0.05; // 5%
        $retirementRate = 0.03; // 3%

        $tax = $grossPay * $taxRate;
        $socialSecurity = $grossPay * $socialSecurityRate;
        $healthInsurance = $grossPay * $healthInsuranceRate;
        $retirement = $grossPay * $retirementRate;
        $other = 0;

        return [
            'tax' => $tax,
            'social_security' => $socialSecurity,
            'health_insurance' => $healthInsurance,
            'retirement' => $retirement,
            'other' => $other,
            'total' => $tax + $socialSecurity + $healthInsurance + $retirement + $other,
        ];
    }

    /**
     * Calculate year-to-date amounts.
     */
    private function calculateYearToDateAmounts(User $employee, PayPeriod $payPeriod, float $currentGross, float $currentNet): array
    {
        $yearStart = Carbon::create($payPeriod->start_date->year, 1, 1);
        
        $ytdPayslips = Payslip::where('user_id', $employee->id)
            ->whereHas('payPeriod', function ($query) use ($yearStart, $payPeriod) {
                $query->where('start_date', '>=', $yearStart)
                      ->where('id', '!=', $payPeriod->id);
            })
            ->get();

        $ytdGross = $ytdPayslips->sum('gross_pay') + $currentGross;
        $ytdNet = $ytdPayslips->sum('net_pay') + $currentNet;

        return [
            'gross' => $ytdGross,
            'net' => $ytdNet,
        ];
    }

    /**
     * Check if a date is a holiday.
     */
    private function isHoliday(Carbon $date): bool
    {
        // This is a basic implementation - should be enhanced with a holidays table
        $holidays = [
            '01-01', // New Year's Day
            '07-04', // Independence Day
            '12-25', // Christmas
        ];

        return in_array($date->format('m-d'), $holidays);
    }

    /**
     * Generate pay period name.
     */
    private function generatePayPeriodName(string $periodType, string $startDate, string $endDate): string
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        switch ($periodType) {
            case 'weekly':
                return 'Week of ' . $start->format('M j, Y');
            case 'bi_weekly':
                return 'Bi-weekly ' . $start->format('M j') . ' - ' . $end->format('M j, Y');
            case 'monthly':
                return $start->format('F Y');
            case 'quarterly':
                $quarter = ceil($start->month / 3);
                return 'Q' . $quarter . ' ' . $start->year;
            default:
                return $start->format('M j') . ' - ' . $end->format('M j, Y');
        }
    }

    /**
     * Get payslips for a user.
     */
    public function getUserPayslips(int $userId, array $filters = []): Collection
    {
        $query = Payslip::forUser($userId)
            ->with(['payPeriod', 'branch', 'approvedBy'])
            ->orderBy('created_at', 'desc');

        if (isset($filters['status'])) {
            $query->withStatus($filters['status']);
        }

        if (isset($filters['year'])) {
            $query->whereHas('payPeriod', function ($q) use ($filters) {
                $q->whereYear('start_date', $filters['year']);
            });
        }

        return $query->get();
    }

    /**
     * Approve a payslip.
     */
    public function approvePayslip(int $payslipId, int $approvedBy): bool
    {
        $payslip = Payslip::findOrFail($payslipId);
        return $payslip->approve($approvedBy);
    }

    /**
     * Mark payslip as paid.
     */
    public function markPayslipAsPaid(int $payslipId, string $paymentMethod = null, string $paymentReference = null): bool
    {
        $payslip = Payslip::findOrFail($payslipId);
        return $payslip->markAsPaid($paymentMethod, $paymentReference);
    }

    /**
     * Get payroll statistics for a tenant.
     */
    public function getPayrollStatistics(int $tenantId, array $filters = []): array
    {
        $query = Payslip::forTenant($tenantId);

        if (isset($filters['year'])) {
            $query->whereHas('payPeriod', function ($q) use ($filters) {
                $q->whereYear('start_date', $filters['year']);
            });
        }

        if (isset($filters['month'])) {
            $query->whereHas('payPeriod', function ($q) use ($filters) {
                $q->whereMonth('start_date', $filters['month']);
            });
        }

        $payslips = $query->get();

        return [
            'total_payslips' => $payslips->count(),
            'total_gross_pay' => $payslips->sum('gross_pay'),
            'total_deductions' => $payslips->sum('total_deductions'),
            'total_net_pay' => $payslips->sum('net_pay'),
            'average_gross_pay' => $payslips->avg('gross_pay'),
            'average_net_pay' => $payslips->avg('net_pay'),
            'status_breakdown' => $payslips->groupBy('status')->map->count(),
        ];
    }

    /**
     * Generate next pay period automatically.
     */
    public function generateNextPayPeriod(int $currentPayPeriodId): PayPeriod
    {
        $currentPeriod = PayPeriod::findOrFail($currentPayPeriodId);
        $nextPeriodData = $currentPeriod->getNextPayPeriod();
        
        $nextPeriodData['tenant_id'] = $currentPeriod->tenant_id;
        $nextPeriodData['name'] = $this->generatePayPeriodName(
            $nextPeriodData['period_type'],
            $nextPeriodData['start_date']->toDateString(),
            $nextPeriodData['end_date']->toDateString()
        );

        return PayPeriod::create($nextPeriodData);
    }
}
<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Delivery\Models\DeliveryAssignment;

class CreateDeliveryTipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if the assignment exists and belongs to the authenticated customer
        $assignment = DeliveryAssignment::with('order')
            ->where('id', $this->delivery_assignment_id)
            ->first();

        if (!$assignment) {
            return false;
        }

        // Only the customer who placed the order can tip
        return $assignment->order->customer_id === auth()->id() && 
               $assignment->status === 'delivered';
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'delivery_assignment_id' => [
                'required',
                'integer',
                'exists:delivery_assignments,id'
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.50',
                'max:100.00'
            ],
            'payment_method' => [
                'required',
                'string',
                Rule::in(['cash', 'card', 'digital_wallet', 'app_credit'])
            ],
            'transaction_reference' => [
                'nullable',
                'string',
                'max:100'
            ],
            'notes' => [
                'nullable',
                'string',
                'max:500'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'delivery_assignment_id.exists' => 'Invalid delivery assignment.',
            'amount.min' => 'Tip amount must be at least $0.50.',
            'amount.max' => 'Tip amount cannot exceed $100.00.',
            'payment_method.in' => 'Invalid payment method selected.',
            'notes.max' => 'Notes cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'delivery_assignment_id' => 'delivery',
            'payment_method' => 'payment method',
            'transaction_reference' => 'transaction reference',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set customer_id from authenticated user
        $this->merge([
            'customer_id' => auth()->id(),
            'status' => 'pending', // Default status
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation: Check if delivery is completed
            if ($this->delivery_assignment_id) {
                $assignment = DeliveryAssignment::find($this->delivery_assignment_id);
                
                if ($assignment && $assignment->status !== 'delivered') {
                    $validator->errors()->add(
                        'delivery_assignment_id',
                        'You can only tip for completed deliveries.'
                    );
                }

                // Check if tip window is still open (e.g., within 7 days)
                if ($assignment && $assignment->delivered_at) {
                    $tipDeadline = $assignment->delivered_at->addDays(7);
                    if (now()->isAfter($tipDeadline)) {
                        $validator->errors()->add(
                            'delivery_assignment_id',
                            'Tip period has expired. You can only tip within 7 days of delivery.'
                        );
                    }
                }

                // Check if customer has already tipped for this delivery
                $existingTip = \Modules\Delivery\Models\DeliveryTip::where('delivery_assignment_id', $this->delivery_assignment_id)
                    ->where('customer_id', auth()->id())
                    ->whereIn('status', ['pending', 'paid'])
                    ->exists();

                if ($existingTip) {
                    $validator->errors()->add(
                        'delivery_assignment_id',
                        'You have already tipped for this delivery.'
                    );
                }
            }

            // Validate transaction reference for non-cash payments
            if ($this->payment_method && $this->payment_method !== 'cash') {
                if (empty($this->transaction_reference)) {
                    $validator->errors()->add(
                        'transaction_reference',
                        'Transaction reference is required for non-cash payments.'
                    );
                }
            }
        });
    }
}
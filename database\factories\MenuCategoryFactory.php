<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Menu;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MenuCategory>
 */
class MenuCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->randomElement([
            'Appetizers', 'Main Courses', 'Desserts', 'Beverages', 'Salads',
            'Soups', 'Pasta', 'Pizza', 'Seafood', 'Meat', 'Vegetarian',
            'Sandwiches', 'Burgers', 'Sides', 'Specials'
        ]);
        
        return [
            'menu_id' => Menu::factory(),
            'name' => $name,
            'code' => Str::slug($name) . '-' . fake()->randomNumber(3),
            'description' => fake()->sentence(),
            'image_url' => fake()->imageUrl(400, 300, 'food'),
            'is_active' => true,
            'sort_order' => fake()->numberBetween(1, 20),
        ];
    }

    /**
     * Indicate that the category should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
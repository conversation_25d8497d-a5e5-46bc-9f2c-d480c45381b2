<?php

use Illuminate\Support\Facades\Route;
use Modules\Delivery\Http\Controllers\DeliveryController;

/*
|--------------------------------------------------------------------------
| Delivery API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Delivery module. These routes handle
| delivery personnel management, zones, assignments, tracking, reviews,
| and tips functionality.
|
*/

// Delivery Personnel Management
Route::prefix('delivery/personnel')->name('delivery.personnel.')->group(function () {
    Route::get('/', [DeliveryController::class, 'getDeliveryPersonnel'])->name('index');
    Route::post('/', [DeliveryController::class, 'createDeliveryPersonnel'])->name('store');
    Route::get('/{id}', [DeliveryController::class, 'getDeliveryPersonnel'])->name('show');
    Route::put('/{id}', [DeliveryController::class, 'updateDeliveryPersonnel'])->name('update');
    Route::post('/{id}/verify', [DeliveryController::class, 'verifyDeliveryPersonnel'])->name('verify');
    Route::get('/{id}/performance', [DeliveryController::class, 'getPersonnelPerformance'])->name('performance');
    Route::get('/available/list', [DeliveryController::class, 'getAvailablePersonnel'])->name('available');
});

// Delivery Zones Management
Route::prefix('delivery/zones')->name('delivery.zones.')->group(function () {
    Route::get('/', [DeliveryController::class, 'getDeliveryZones'])->name('index');
    Route::post('/', [DeliveryController::class, 'createDeliveryZone'])->name('store');
    Route::get('/{id}', [DeliveryController::class, 'getDeliveryZones'])->name('show');
    Route::put('/{id}', [DeliveryController::class, 'updateDeliveryZone'])->name('update');
    Route::post('/check', [DeliveryController::class, 'checkDeliveryZone'])->name('check');
});

// Delivery Assignments Management
Route::prefix('delivery/assignments')->name('delivery.assignments.')->group(function () {
    Route::get('/', [DeliveryController::class, 'getDeliveryAssignments'])->name('index');
    Route::get('/{id}', [DeliveryController::class, 'getDeliveryAssignments'])->name('show');
    Route::post('/assign', [DeliveryController::class, 'assignDelivery'])->name('assign');
    Route::post('/auto-assign', [DeliveryController::class, 'autoAssignDelivery'])->name('auto-assign');
    Route::put('/{id}/status', [DeliveryController::class, 'updateDeliveryStatus'])->name('update-status');
});

// Location Tracking
Route::prefix('delivery/tracking')->name('delivery.tracking.')->group(function () {
    Route::post('/{assignmentId}/location', [DeliveryController::class, 'updateLocation'])->name('update-location');
    Route::get('/{assignmentId}/tracking', [DeliveryController::class, 'getTrackingData'])->name('data');
    Route::get('/{assignmentId}/current-location', [DeliveryController::class, 'getCurrentLocation'])->name('current-location');
});

// Delivery Reviews
Route::prefix('delivery/reviews')->name('delivery.reviews.')->group(function () {
    Route::get('/', [DeliveryController::class, 'getDeliveryReviews'])->name('index');
    Route::post('/', [DeliveryController::class, 'createDeliveryReview'])->name('store');
    Route::get('/{id}', [DeliveryController::class, 'getDeliveryReviews'])->name('show');
    Route::post('/{id}/verify', [DeliveryController::class, 'verifyReview'])->name('verify');
    Route::get('/personnel/{personnelId}', [DeliveryController::class, 'getPersonnelReviews'])->name('personnel');
});

// Delivery Tips
Route::prefix('delivery/tips')->name('delivery.tips.')->group(function () {
    Route::get('/', [DeliveryController::class, 'getDeliveryTips'])->name('index');
    Route::post('/', [DeliveryController::class, 'createDeliveryTip'])->name('store');
    Route::get('/{id}', [DeliveryController::class, 'getDeliveryTips'])->name('show');
    Route::post('/{id}/process-payment', [DeliveryController::class, 'processPayment'])->name('process-payment');
    Route::get('/personnel/{personnelId}', [DeliveryController::class, 'getPersonnelTips'])->name('personnel');
});

// Analytics and Reports
Route::prefix('delivery/analytics')->name('delivery.analytics.')->group(function () {
    Route::get('/stats', [DeliveryController::class, 'getDeliveryStats'])->name('stats');
    Route::get('/personnel/{personnelId}/performance', [DeliveryController::class, 'getPersonnelPerformance'])->name('personnel-performance');
});

// Utility Routes
Route::prefix('delivery/utils')->name('delivery.utils.')->group(function () {
    Route::post('/calculate-fee', [DeliveryController::class, 'calculateDeliveryFee'])->name('calculate-fee');
    Route::get('/available-personnel', [DeliveryController::class, 'getAvailablePersonnel'])->name('available-personnel');
});

// Public Routes (no authentication required)
Route::prefix('delivery/public')->name('delivery.public.')->group(function () {
    Route::post('/check-zone', [DeliveryController::class, 'checkDeliveryZone'])->name('check-zone');
    Route::post('/calculate-fee', [DeliveryController::class, 'calculateDeliveryFee'])->name('calculate-fee');
});

// Customer-specific routes (authenticated customers only)
Route::prefix('delivery/customer')->name('delivery.customer.')->middleware(['auth:sanctum', 'customer'])->group(function () {
    Route::get('/my-deliveries', [DeliveryController::class, 'getCustomerDeliveries'])->name('my-deliveries');
    Route::get('/tracking/{assignmentId}', [DeliveryController::class, 'getTrackingData'])->name('tracking');
    Route::post('/reviews', [DeliveryController::class, 'createDeliveryReview'])->name('create-review');
    Route::post('/tips', [DeliveryController::class, 'createDeliveryTip'])->name('create-tip');
});

// Personnel-specific routes (authenticated delivery personnel only)
Route::prefix('delivery/personnel-app')->name('delivery.personnel-app.')->middleware(['auth:sanctum', 'delivery-personnel'])->group(function () {
    Route::get('/my-assignments', [DeliveryController::class, 'getPersonnelAssignments'])->name('my-assignments');
    Route::put('/assignments/{id}/status', [DeliveryController::class, 'updateDeliveryStatus'])->name('update-status');
    Route::post('/assignments/{assignmentId}/location', [DeliveryController::class, 'updateLocation'])->name('update-location');
    Route::get('/my-performance', [DeliveryController::class, 'getMyPerformance'])->name('my-performance');
    Route::get('/my-earnings', [DeliveryController::class, 'getMyEarnings'])->name('my-earnings');
});

// Admin routes (authenticated admin users only)
Route::prefix('delivery/admin')->name('delivery.admin.')->middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::get('/dashboard', [DeliveryController::class, 'getAdminDashboard'])->name('dashboard');
    Route::get('/reports/daily', [DeliveryController::class, 'getDailyReport'])->name('daily-report');
    Route::get('/reports/weekly', [DeliveryController::class, 'getWeeklyReport'])->name('weekly-report');
    Route::get('/reports/monthly', [DeliveryController::class, 'getMonthlyReport'])->name('monthly-report');
    Route::post('/personnel/{id}/suspend', [DeliveryController::class, 'suspendPersonnel'])->name('suspend-personnel');
    Route::post('/personnel/{id}/activate', [DeliveryController::class, 'activatePersonnel'])->name('activate-personnel');
});

// Webhook routes for external integrations
Route::prefix('delivery/webhooks')->name('delivery.webhooks.')->group(function () {
    Route::post('/payment-status', [DeliveryController::class, 'handlePaymentWebhook'])->name('payment-status');
    Route::post('/location-update', [DeliveryController::class, 'handleLocationWebhook'])->name('location-update');
});

<?php

namespace Modules\Kitchen\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Tenant;
use App\Models\Branch;
use App\Models\User;

class Kitchen extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'code',
        'description',
        'station_type',
        'max_concurrent_orders',
        'average_prep_time_minutes',
        'is_active',
        'display_order',
        'manager_id',
        'equipment_list',
        'operating_hours',
        'created_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'equipment_list' => 'array',
            'operating_hours' => 'array',
            'max_concurrent_orders' => 'integer',
            'average_prep_time_minutes' => 'integer',
            'display_order' => 'integer',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function menuItems()
    {
        return $this->hasMany(KitchenMenuItem::class);
    }

    public function kotOrders()
    {
        return $this->hasMany(KotOrder::class);
    }

    public function activeKotOrders()
    {
        return $this->kotOrders()->whereIn('status', ['pending', 'preparing']);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeByStationType($query, $stationType)
    {
        return $query->where('station_type', $stationType);
    }

    // Helper methods
    public function canAcceptNewOrder()
    {
        $currentOrders = $this->activeKotOrders()->count();
        return $currentOrders < $this->max_concurrent_orders;
    }

    public function getCurrentLoad()
    {
        return $this->activeKotOrders()->count();
    }

    public function getLoadPercentage()
    {
        if ($this->max_concurrent_orders == 0) {
            return 0;
        }
        return ($this->getCurrentLoad() / $this->max_concurrent_orders) * 100;
    }

    public function isOperatingNow()
    {
        if (!$this->operating_hours) {
            return true; // 24/7 operation if no hours specified
        }

        $currentTime = now()->format('H:i');
        $currentDay = strtolower(now()->format('l'));

        if (isset($this->operating_hours[$currentDay])) {
            $hours = $this->operating_hours[$currentDay];
            return $currentTime >= $hours['start'] && $currentTime <= $hours['end'];
        }

        return false;
    }
}
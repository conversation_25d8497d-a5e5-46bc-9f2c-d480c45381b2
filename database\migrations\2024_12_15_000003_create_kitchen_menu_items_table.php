<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kitchen_menu_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('kitchen_id')->constrained()->onDelete('cascade');
            $table->foreignId('menu_item_id')->constrained()->onDelete('cascade');
            $table->integer('prep_time_minutes')->nullable();
            $table->integer('priority_level')->default(5); // 1-10 scale
            $table->boolean('is_active')->default(true);
            $table->text('special_instructions')->nullable();
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('assigned_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['tenant_id', 'kitchen_id']);
            $table->index(['kitchen_id', 'is_active']);
            $table->index(['menu_item_id', 'is_active']);
            $table->index('priority_level');
            
            // Unique constraint: Each menu item can only be assigned to one kitchen per tenant
            $table->unique(['tenant_id', 'menu_item_id'], 'unique_menu_item_per_tenant');
            
            // Index for efficient lookups
            $table->index(['tenant_id', 'menu_item_id', 'kitchen_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kitchen_menu_items');
    }
};
<?php

use Illuminate\Support\Facades\Route;
use Modules\Payroll\Http\Controllers\PayrollController;

/*
|--------------------------------------------------------------------------
| Payroll API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Payroll module. These routes handle
| pay periods, payslips, payroll generation, and payroll statistics.
|
*/

// Pay Periods Management
Route::prefix('payroll')->middleware(['auth:sanctum'])->group(function () {
    
    // Pay Periods
    Route::prefix('pay-periods')->group(function () {
        Route::get('/', [PayrollController::class, 'getPayPeriods'])->name('payroll.pay-periods.index');
        Route::post('/', [PayrollController::class, 'createPayPeriod'])->name('payroll.pay-periods.create');
        Route::get('/{id}', [PayrollController::class, 'getPayPeriod'])->name('payroll.pay-periods.show');
        Route::post('/{id}/generate-payroll', [PayrollController::class, 'generatePayroll'])->name('payroll.pay-periods.generate-payroll');
        Route::post('/{id}/generate-next', [PayrollController::class, 'generateNextPayPeriod'])->name('payroll.pay-periods.generate-next');
        
        // Payslips for a specific pay period
        Route::get('/{payPeriodId}/payslips', [PayrollController::class, 'getPayslips'])->name('payroll.pay-periods.payslips');
    });
    
    // Payslips Management
    Route::prefix('payslips')->group(function () {
        // Employee's own payslips
        Route::get('/my-payslips', [PayrollController::class, 'getMyPayslips'])->name('payroll.payslips.my-payslips');
        
        // Individual payslip operations
        Route::get('/{id}', [PayrollController::class, 'getPayslip'])->name('payroll.payslips.show');
        Route::put('/{id}', [PayrollController::class, 'updatePayslip'])->name('payroll.payslips.update');
        Route::post('/{id}/approve', [PayrollController::class, 'approvePayslip'])->name('payroll.payslips.approve');
        Route::post('/{id}/mark-paid', [PayrollController::class, 'markPayslipAsPaid'])->name('payroll.payslips.mark-paid');
    });
    
    // Payroll Statistics and Reports
    Route::prefix('statistics')->group(function () {
        Route::get('/', [PayrollController::class, 'getPayrollStatistics'])->name('payroll.statistics.index');
    });
});

/*
|--------------------------------------------------------------------------
| Route Descriptions
|--------------------------------------------------------------------------
|
| Pay Periods:
| - GET /payroll/pay-periods - Get all pay periods with optional filters
| - POST /payroll/pay-periods - Create a new pay period
| - GET /payroll/pay-periods/{id} - Get specific pay period details
| - POST /payroll/pay-periods/{id}/generate-payroll - Generate payroll for pay period
| - POST /payroll/pay-periods/{id}/generate-next - Generate next pay period automatically
| - GET /payroll/pay-periods/{payPeriodId}/payslips - Get all payslips for a pay period
|
| Payslips:
| - GET /payroll/payslips/my-payslips - Get current user's payslips
| - GET /payroll/payslips/{id} - Get specific payslip details
| - PUT /payroll/payslips/{id} - Update payslip (admin/manager only)
| - POST /payroll/payslips/{id}/approve - Approve payslip (admin/manager only)
| - POST /payroll/payslips/{id}/mark-paid - Mark payslip as paid (admin only)
|
| Statistics:
| - GET /payroll/statistics - Get payroll statistics and reports
|
|--------------------------------------------------------------------------
| Route Parameters
|--------------------------------------------------------------------------
|
| Available query parameters:
|
| Pay Periods (GET /payroll/pay-periods):
| - status: draft, processing, completed, cancelled
| - period_type: weekly, bi_weekly, monthly, quarterly
| - year: filter by year
|
| My Payslips (GET /payroll/payslips/my-payslips):
| - status: draft, approved, paid, cancelled
| - year: filter by year
|
| Statistics (GET /payroll/statistics):
| - year: filter by year
| - month: filter by month
|
| Generate Payroll (POST /payroll/pay-periods/{id}/generate-payroll):
| - regenerate: boolean (whether to regenerate existing payslips)
|
| Mark Paid (POST /payroll/payslips/{id}/mark-paid):
| - payment_method: string (payment method used)
| - payment_reference: string (payment reference number)
|
*/
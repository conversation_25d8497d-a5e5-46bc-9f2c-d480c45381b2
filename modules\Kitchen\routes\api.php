<?php

use Illuminate\Support\Facades\Route;
use Modules\Kitchen\Http\Controllers\KitchenController;
use Modules\Kitchen\Http\Controllers\KotController;

/*
|--------------------------------------------------------------------------
| Kitchen API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Kitchen module. These routes handle
| kitchen management, menu item assignments, and KOT operations.
|
*/

Route::middleware(['auth:sanctum', 'tenant.check'])->prefix('kitchen')->group(function () {
    
    // Kitchen Management Routes
    Route::prefix('kitchens')->group(function () {
        Route::get('/', [KitchenController::class, 'index']); // Get all kitchens
        Route::post('/', [KitchenController::class, 'store']); // Create kitchen
        Route::get('/{kitchen}', [KitchenController::class, 'show']); // Get kitchen details
        Route::put('/{kitchen}', [KitchenController::class, 'update']); // Update kitchen
        Route::delete('/{kitchen}', [KitchenController::class, 'destroy']); // Delete kitchen
        
        // Kitchen Status Management
        Route::patch('/{kitchen}/toggle-status', [KitchenController::class, 'toggleStatus']); // Toggle kitchen active status
        
        // Kitchen Menu Item Management
        Route::get('/{kitchen}/menu-items', [KitchenController::class, 'getMenuItems']); // Get kitchen menu items
        Route::post('/{kitchen}/menu-items', [KitchenController::class, 'assignMenuItem']); // Assign menu item to kitchen
        Route::delete('/{kitchen}/menu-items/{menuItemId}', [KitchenController::class, 'removeMenuItem']); // Remove menu item from kitchen
        
        // Kitchen Dashboard and Operations
        Route::get('/{kitchen}/dashboard', [KitchenController::class, 'dashboard']); // Get kitchen dashboard data
        Route::get('/{kitchen}/active-kots', [KitchenController::class, 'getActiveKots']); // Get active KOTs for kitchen
    });
    
    // KOT (Kitchen Order Ticket) Management Routes
    Route::prefix('kots')->group(function () {
        Route::get('/', [KotController::class, 'index']); // Get KOTs with filters
        Route::post('/create-from-order', [KotController::class, 'createFromOrder']); // Create KOT from order
        Route::get('/statistics', [KotController::class, 'getStatistics']); // Get KOT statistics
        Route::get('/{kotOrder}', [KotController::class, 'show']); // Get KOT details
        
        // KOT Status Management
        Route::patch('/{kotOrder}/status', [KotController::class, 'updateStatus']); // Update KOT status
        Route::patch('/{kotOrder}/start-preparing', [KotController::class, 'startPreparing']); // Start preparing KOT
        Route::patch('/{kotOrder}/mark-ready', [KotController::class, 'markReady']); // Mark KOT as ready
        Route::patch('/{kotOrder}/complete', [KotController::class, 'complete']); // Complete KOT
        Route::patch('/{kotOrder}/cancel', [KotController::class, 'cancel']); // Cancel KOT
        
        // KOT Item Management
        Route::patch('/items/{kotOrderItem}/status', [KotController::class, 'updateItemStatus']); // Update KOT item status
    });
    
});

/*
|--------------------------------------------------------------------------
| Public Kitchen Routes (if needed)
|--------------------------------------------------------------------------
|
| These routes might be used for public kitchen displays or customer-facing
| kitchen status information.
|
*/

// Route::prefix('public/kitchen')->group(function () {
//     Route::get('/display/{kitchen}', [KitchenController::class, 'publicDisplay']); // Public kitchen display
//     Route::get('/status/{kitchen}', [KitchenController::class, 'publicStatus']); // Public kitchen status
// });
<?php

namespace Modules\Kitchen\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Kitchen\Services\KitchenService;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\KotOrderItem;
use Modules\Kitchen\Http\Requests\CreateKotRequest;
use Modules\Kitchen\Http\Requests\UpdateKotStatusRequest;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class KotController extends Controller
{
    protected $kitchenService;

    public function __construct(KitchenService $kitchenService)
    {
        $this->kitchenService = $kitchenService;
    }

    /**
     * Get KOTs with filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            $query = KotOrder::forTenant($tenantId)
                ->with(['kitchen', 'order', 'kotItems.menuItem', 'assignedTo']);

            // Apply filters
            if ($request->has('kitchen_id')) {
                $query->forKitchen($request->kitchen_id);
            }

            if ($request->has('branch_id')) {
                $query->forBranch($request->branch_id);
            }

            if ($request->has('status')) {
                $query->byStatus($request->status);
            }

            if ($request->has('priority')) {
                $query->byPriority($request->priority);
            }

            if ($request->boolean('active_only', false)) {
                $query->active();
            }

            if ($request->boolean('overdue_only', false)) {
                $query->overdue();
            }

            // Date range filter
            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $kots = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $kots,
                'message' => 'KOTs retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve KOTs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific KOT details
     */
    public function show(KotOrder $kotOrder): JsonResponse
    {
        try {
            $kotOrder->load([
                'kitchen',
                'order.customer',
                'kotItems.menuItem',
                'assignedTo',
                'createdBy'
            ]);

            return response()->json([
                'success' => true,
                'data' => $kotOrder,
                'message' => 'KOT details retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve KOT details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create KOT from order
     */
    public function createFromOrder(CreateKotRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $order = Order::findOrFail($data['order_id']);
            
            // Verify order belongs to user's tenant
            if ($order->tenant_id !== $request->user()->tenant_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            $options = [
                'priority' => $data['priority'] ?? 'normal',
                'special_instructions' => $data['special_instructions'] ?? null,
                'created_by' => $request->user()->id,
            ];

            $kotOrders = $this->kitchenService->createKotFromOrder($order, $options);

            return response()->json([
                'success' => true,
                'data' => $kotOrders,
                'message' => 'KOT orders created successfully'
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create KOT: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update KOT status
     */
    public function updateStatus(UpdateKotStatusRequest $request, KotOrder $kotOrder): JsonResponse
    {
        try {
            $data = $request->validated();
            $updatedKot = $this->kitchenService->updateKotStatus(
                $kotOrder,
                $data['status'],
                $request->user()->id
            );

            return response()->json([
                'success' => true,
                'data' => $updatedKot,
                'message' => 'KOT status updated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update KOT status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Start preparing KOT
     */
    public function startPreparing(KotOrder $kotOrder, Request $request): JsonResponse
    {
        try {
            if ($kotOrder->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'KOT is not in pending status'
                ], 422);
            }

            $updatedKot = $this->kitchenService->updateKotStatus(
                $kotOrder,
                'preparing',
                $request->user()->id
            );

            return response()->json([
                'success' => true,
                'data' => $updatedKot,
                'message' => 'KOT preparation started'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start KOT preparation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark KOT as ready
     */
    public function markReady(KotOrder $kotOrder, Request $request): JsonResponse
    {
        try {
            if ($kotOrder->status !== 'preparing') {
                return response()->json([
                    'success' => false,
                    'message' => 'KOT is not being prepared'
                ], 422);
            }

            $updatedKot = $this->kitchenService->updateKotStatus(
                $kotOrder,
                'ready',
                $request->user()->id
            );

            return response()->json([
                'success' => true,
                'data' => $updatedKot,
                'message' => 'KOT marked as ready'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark KOT as ready: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete KOT
     */
    public function complete(KotOrder $kotOrder, Request $request): JsonResponse
    {
        try {
            if (!in_array($kotOrder->status, ['ready', 'preparing'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'KOT cannot be completed from current status'
                ], 422);
            }

            $updatedKot = $this->kitchenService->updateKotStatus(
                $kotOrder,
                'completed',
                $request->user()->id
            );

            return response()->json([
                'success' => true,
                'data' => $updatedKot,
                'message' => 'KOT completed successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete KOT: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel KOT
     */
    public function cancel(KotOrder $kotOrder, Request $request): JsonResponse
    {
        try {
            if (in_array($kotOrder->status, ['completed', 'cancelled'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'KOT cannot be cancelled from current status'
                ], 422);
            }

            $kotOrder->update([
                'status' => 'cancelled',
                'notes' => $request->get('reason', 'Cancelled by user')
            ]);

            return response()->json([
                'success' => true,
                'data' => $kotOrder->fresh(),
                'message' => 'KOT cancelled successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel KOT: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update KOT item status
     */
    public function updateItemStatus(KotOrderItem $kotOrderItem, Request $request): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|in:pending,preparing,ready,completed'
            ]);

            $status = $request->status;

            switch ($status) {
                case 'preparing':
                    $kotOrderItem->markAsStarted();
                    break;
                case 'completed':
                    $kotOrderItem->markAsCompleted();
                    break;
                default:
                    $kotOrderItem->update(['status' => $status]);
            }

            return response()->json([
                'success' => true,
                'data' => $kotOrderItem->fresh(),
                'message' => 'KOT item status updated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update item status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get KOT statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            $branchId = $request->get('branch_id');
            $kitchenId = $request->get('kitchen_id');
            $dateFrom = $request->get('date_from', now()->startOfDay());
            $dateTo = $request->get('date_to', now()->endOfDay());

            $query = KotOrder::forTenant($tenantId)
                ->whereBetween('created_at', [$dateFrom, $dateTo]);

            if ($branchId) {
                $query->forBranch($branchId);
            }

            if ($kitchenId) {
                $query->forKitchen($kitchenId);
            }

            $statistics = [
                'total_kots' => $query->count(),
                'pending_kots' => $query->clone()->pending()->count(),
                'preparing_kots' => $query->clone()->preparing()->count(),
                'ready_kots' => $query->clone()->ready()->count(),
                'completed_kots' => $query->clone()->completed()->count(),
                'overdue_kots' => $query->clone()->overdue()->count(),
                'average_prep_time' => $query->clone()->completed()
                    ->whereNotNull('actual_prep_time_minutes')
                    ->avg('actual_prep_time_minutes'),
            ];

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'message' => 'KOT statistics retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics: ' . $e->getMessage()
            ], 500);
        }
    }
}
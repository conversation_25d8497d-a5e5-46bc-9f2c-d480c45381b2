<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateShiftTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'required|integer|exists:branches,id',
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'default_start_time' => 'required|date_format:H:i',
            'default_end_time' => 'required|date_format:H:i|after:default_start_time',
            'default_break_duration_minutes' => 'nullable|integer|min:0|max:480',
            'hourly_rate' => 'required|numeric|min:0|max:999.99',
            'overtime_rate_multiplier' => 'nullable|numeric|min:1|max:5',
            'color_code' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'requires_approval' => 'boolean',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'name.required' => 'Shift type name is required.',
            'name.max' => 'Shift type name cannot exceed 100 characters.',
            'description.max' => 'Description cannot exceed 500 characters.',
            'default_start_time.required' => 'Default start time is required.',
            'default_start_time.date_format' => 'Default start time must be in HH:MM format.',
            'default_end_time.required' => 'Default end time is required.',
            'default_end_time.date_format' => 'Default end time must be in HH:MM format.',
            'default_end_time.after' => 'Default end time must be after start time.',
            'default_break_duration_minutes.integer' => 'Break duration must be a number.',
            'default_break_duration_minutes.min' => 'Break duration cannot be negative.',
            'default_break_duration_minutes.max' => 'Break duration cannot exceed 8 hours.',
            'hourly_rate.required' => 'Hourly rate is required.',
            'hourly_rate.numeric' => 'Hourly rate must be a number.',
            'hourly_rate.min' => 'Hourly rate cannot be negative.',
            'hourly_rate.max' => 'Hourly rate cannot exceed 999.99.',
            'overtime_rate_multiplier.numeric' => 'Overtime rate multiplier must be a number.',
            'overtime_rate_multiplier.min' => 'Overtime rate multiplier must be at least 1.',
            'overtime_rate_multiplier.max' => 'Overtime rate multiplier cannot exceed 5.',
            'color_code.regex' => 'Color code must be a valid hex color (e.g., #FF0000).',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_active' => $this->boolean('is_active', true),
            'requires_approval' => $this->boolean('requires_approval', false),
            'default_break_duration_minutes' => $this->default_break_duration_minutes ?? 30,
            'overtime_rate_multiplier' => $this->overtime_rate_multiplier ?? 1.5,
        ]);
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add tenant_id
        $validated['tenant_id'] = Auth::user()->tenant_id;
        
        return $validated;
    }
}
<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'category',
        'key',
        'value',
        'data_type',
        'description',
        'is_public',
        'logo_url',
        'support_email',
        'support_phone',
        'address',
        'website_url',
        'social_links',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_public' => 'boolean',
            'social_links' => 'array',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    // Accessor to cast value based on data_type
    public function getValueAttribute($value)
    {
        switch ($this->data_type) {
            case 'boolean':
                return (bool) $value;
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    // Mutator to store value based on data_type
    public function setValueAttribute($value)
    {
        if ($this->data_type === 'json') {
            $this->attributes['value'] = json_encode($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }

    // Accessor for social_links
    public function getSocialLinksAttribute($value)
    {
        return $value ? json_decode($value, true) : null;
    }

    // Mutator for social_links
    public function setSocialLinksAttribute($value)
    {
        $this->attributes['social_links'] = is_array($value) ? json_encode($value) : $value;
    }
}
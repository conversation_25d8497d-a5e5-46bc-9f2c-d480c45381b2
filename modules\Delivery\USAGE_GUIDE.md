# Delivery Module Usage Guide

This guide provides comprehensive instructions on how to use the Delivery Module in the EPSIS Restaurant POS System.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Setting Up Delivery Personnel](#setting-up-delivery-personnel)
3. [Managing Delivery Zones](#managing-delivery-zones)
4. [Processing Delivery Orders](#processing-delivery-orders)
5. [Tracking Deliveries](#tracking-deliveries)
6. [Managing Reviews and Tips](#managing-reviews-and-tips)
7. [Analytics and Reporting](#analytics-and-reporting)
8. [API Reference](#api-reference)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Quick Start

### 1. Installation

The Delivery module is automatically registered when you install the EPSIS system. Ensure you have run the migrations:

```bash
php artisan migrate
```

### 2. Seed Sample Data (Optional)

```bash
php artisan db:seed --class="Modules\Delivery\Database\Seeders\DeliveryModuleSeeder"
```

### 3. Basic Configuration

Update the delivery configuration in `modules/Delivery/config/config.php`:

```php
'defaults' => [
    'delivery_fee' => 15.00,
    'minimum_order_amount' => 50.00,
    'estimated_delivery_time_minutes' => 30,
],
```

## Setting Up Delivery Personnel

### Creating Delivery Personnel

```php
// Via API
POST /api/delivery/personnel
{
    "user_id": 1,
    "branch_id": 1,
    "license_number": "DL123456",
    "license_expiry_date": "2026-12-31",
    "vehicle_type": "motorcycle",
    "vehicle_model": "Honda CB150R",
    "vehicle_plate_number": "B1234DEL",
    "phone_number": "+6281234567890",
    "emergency_contact_name": "John Doe",
    "emergency_contact_phone": "+6281234567891",
    "max_concurrent_deliveries": 3,
    "working_hours": [
        {"day": "monday", "start_time": "09:00", "end_time": "18:00"},
        {"day": "tuesday", "start_time": "09:00", "end_time": "18:00"}
    ]
}
```

```php
// Via Code
use Modules\Delivery\Models\DeliveryPersonnel;

$personnel = DeliveryPersonnel::create([
    'user_id' => 1,
    'branch_id' => 1,
    'license_number' => 'DL123456',
    'vehicle_type' => 'motorcycle',
    // ... other fields
]);
```

### Managing Personnel Status

```php
// Update availability
PUT /api/delivery/personnel/{id}
{
    "is_available": true,
    "status": "active"
}

// Update location
PUT /api/delivery/personnel/{id}/location
{
    "latitude": -6.2088,
    "longitude": 106.8456,
    "accuracy": 5.0
}
```

### Personnel Verification

```php
// Verify personnel
POST /api/delivery/personnel/{id}/verify
{
    "is_verified": true,
    "verification_notes": "All documents verified"
}
```

## Managing Delivery Zones

### Creating Delivery Zones

```php
// Define zone boundaries
POST /api/delivery/zones
{
    "branch_id": 1,
    "name": "Central Jakarta",
    "description": "Central business district",
    "polygon_coordinates": [
        [-6.1744, 106.8227],
        [-6.1744, 106.8427],
        [-6.1944, 106.8427],
        [-6.1944, 106.8227]
    ],
    "delivery_fee": 15.00,
    "minimum_order_amount": 50.00,
    "estimated_delivery_time_minutes": 30,
    "priority": 1
}
```

### Zone Management

```php
// Check if location is in delivery zone
GET /api/delivery/zones/check?latitude=-6.2&longitude=106.8&branch_id=1

// Get zones for branch
GET /api/delivery/zones?branch_id=1&active=true

// Update zone
PUT /api/delivery/zones/{id}
{
    "delivery_fee": 18.00,
    "is_active": true
}
```

## Processing Delivery Orders

### Creating Delivery Assignments

```php
// Manual assignment
POST /api/delivery/assignments
{
    "order_id": 123,
    "delivery_personnel_id": 5,
    "pickup_latitude": -6.2088,
    "pickup_longitude": 106.8456,
    "pickup_address": "Restaurant Branch Address",
    "delivery_latitude": -6.2188,
    "delivery_longitude": 106.8556,
    "delivery_address": "Customer Address",
    "customer_phone": "+6281234567890",
    "customer_name": "Jane Doe",
    "special_instructions": "Call when arrived"
}

// Auto assignment (finds best available personnel)
POST /api/delivery/assignments/auto-assign
{
    "order_id": 123,
    "pickup_latitude": -6.2088,
    "pickup_longitude": 106.8456,
    "pickup_address": "Restaurant Branch Address",
    "delivery_latitude": -6.2188,
    "delivery_longitude": 106.8556,
    "delivery_address": "Customer Address",
    "customer_phone": "+6281234567890",
    "customer_name": "Jane Doe"
}
```

### Updating Delivery Status

```php
// Update to picked up
PUT /api/delivery/assignments/{id}/status
{
    "status": "picked_up",
    "delivery_notes": "Package picked up from restaurant",
    "current_latitude": -6.2088,
    "current_longitude": 106.8456
}

// Update to delivered
PUT /api/delivery/assignments/{id}/status
{
    "status": "delivered",
    "delivery_notes": "Package delivered successfully",
    "delivery_proof": {
        "photo": "delivery_photo.jpg",
        "signature": "signature.png",
        "recipient_name": "Jane Doe",
        "notes": "Delivered to customer directly"
    },
    "current_latitude": -6.2188,
    "current_longitude": 106.8556
}

// Mark as failed
PUT /api/delivery/assignments/{id}/status
{
    "status": "failed",
    "failure_reason": "Customer not available",
    "delivery_notes": "Attempted delivery but customer was not home"
}
```

## Tracking Deliveries

### Real-time Location Updates

```php
// Update delivery location
POST /api/delivery/assignments/{id}/location
{
    "latitude": -6.2088,
    "longitude": 106.8456,
    "accuracy": 5.0,
    "speed": 25.5,
    "bearing": 45.0
}
```

### Getting Tracking Information

```php
// Get current location
GET /api/delivery/assignments/{id}/location

// Get tracking history
GET /api/delivery/assignments/{id}/tracking

// Get delivery route
GET /api/delivery/assignments/{id}/route
```

### Customer Tracking

```php
// Public tracking endpoint for customers
GET /api/delivery/track/{assignment_id}?token={customer_token}
```

## Managing Reviews and Tips

### Creating Reviews

```php
// Customer creates review
POST /api/delivery/reviews
{
    "delivery_assignment_id": 123,
    "rating": 5,
    "comment": "Excellent delivery service!",
    "review_categories": ["punctuality", "professionalism"],
    "is_anonymous": false
}
```

### Managing Tips

```php
// Customer adds tip
POST /api/delivery/tips
{
    "delivery_assignment_id": 123,
    "amount": 5.00,
    "payment_method": "card",
    "transaction_reference": "TXN123456",
    "notes": "Great service!"
}

// Process tip payment
POST /api/delivery/tips/{id}/process
{
    "status": "paid",
    "transaction_reference": "TXN123456"
}
```

## Analytics and Reporting

### Delivery Statistics

```php
// Get overall delivery stats
GET /api/delivery/analytics/stats?start_date=2024-01-01&end_date=2024-01-31

// Get branch-specific stats
GET /api/delivery/analytics/stats?branch_id=1&period=week
```

### Personnel Performance

```php
// Get personnel performance
GET /api/delivery/analytics/personnel/{id}/performance

// Get top performers
GET /api/delivery/analytics/personnel/top-performers?limit=10
```

### Revenue Analytics

```php
// Get delivery revenue
GET /api/delivery/analytics/revenue?period=month

// Get tip analytics
GET /api/delivery/analytics/tips?personnel_id=5
```

## API Reference

### Authentication

Most endpoints require authentication. Include the authorization header:

```
Authorization: Bearer {your-api-token}
```

### Response Format

All API responses follow this format:

```json
{
    "success": true,
    "data": {},
    "message": "Success message",
    "meta": {
        "pagination": {}
    }
}
```

### Error Handling

Error responses include detailed information:

```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "field_name": ["Error message"]
    }
}
```

### Rate Limiting

API endpoints are rate-limited:
- General endpoints: 60 requests per minute
- Location updates: 120 requests per minute
- Public tracking: 30 requests per minute

## Best Practices

### 1. Personnel Management

- Regularly verify personnel documents
- Monitor personnel performance metrics
- Ensure adequate personnel coverage during peak hours
- Implement fair assignment algorithms

### 2. Zone Configuration

- Define clear, non-overlapping zones
- Set realistic delivery fees and time estimates
- Regularly review and update zone boundaries
- Consider traffic patterns and peak hours

### 3. Order Processing

- Use auto-assignment for efficiency
- Provide clear pickup and delivery instructions
- Implement proper status tracking
- Handle failed deliveries promptly

### 4. Customer Experience

- Provide real-time tracking information
- Send status update notifications
- Encourage reviews and feedback
- Handle complaints professionally

### 5. Performance Monitoring

- Track key metrics (delivery time, success rate, customer satisfaction)
- Identify and address bottlenecks
- Reward high-performing personnel
- Continuously optimize routes and assignments

## Troubleshooting

### Common Issues

#### 1. Personnel Not Available for Assignment

**Problem**: Auto-assignment fails to find available personnel.

**Solutions**:
- Check personnel status and availability
- Verify personnel are within delivery radius
- Ensure personnel have capacity for more deliveries
- Check working hours configuration

#### 2. GPS Tracking Issues

**Problem**: Location updates are inaccurate or missing.

**Solutions**:
- Verify GPS permissions on delivery app
- Check network connectivity
- Validate coordinate ranges
- Implement fallback location methods

#### 3. Zone Detection Problems

**Problem**: Delivery address not matching any zone.

**Solutions**:
- Review zone polygon coordinates
- Check for overlapping zones
- Verify zone is active
- Implement default zone fallback

#### 4. Performance Issues

**Problem**: Slow API responses or timeouts.

**Solutions**:
- Optimize database queries
- Implement caching for frequently accessed data
- Use database indexes effectively
- Consider pagination for large datasets

### Debug Mode

Enable debug mode in configuration:

```php
'debug' => [
    'enabled' => true,
    'log_level' => 'debug',
    'track_queries' => true,
],
```

### Logging

Check application logs for delivery-related issues:

```bash
tail -f storage/logs/laravel.log | grep "delivery"
```

### Support

For additional support:

1. Check the module documentation
2. Review API response error messages
3. Consult the troubleshooting section
4. Contact technical support with detailed error information

---

*This guide covers the essential features of the Delivery Module. For advanced configurations and customizations, refer to the technical documentation.*
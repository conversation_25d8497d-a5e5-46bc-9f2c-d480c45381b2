<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ApplyPenaltyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin and manager roles can apply penalties
        return Auth::check() && in_array(Auth::user()->role, ['admin', 'manager']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                'integer',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    // Ensure the user belongs to the same tenant
                    $user = \App\Models\User::find($value);
                    if ($user && $user->tenant_id !== Auth::user()->tenant_id) {
                        $fail('The selected user does not belong to your organization.');
                    }
                },
            ],
            'type' => [
                'required',
                'string',
                Rule::in(['late', 'absence', 'misconduct', 'other']),
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
                'max:9999.99',
            ],
            'reason' => [
                'required',
                'string',
                'min:10',
                'max:500',
            ],
            'date' => [
                'required',
                'date',
                'before_or_equal:today',
                'after:' . now()->subMonths(3)->format('Y-m-d'), // Can't apply penalties older than 3 months
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'Please select a staff member.',
            'user_id.exists' => 'The selected staff member does not exist.',
            'type.required' => 'Please select a penalty type.',
            'type.in' => 'The penalty type must be one of: late, absence, misconduct, or other.',
            'amount.required' => 'Please enter the penalty amount.',
            'amount.numeric' => 'The penalty amount must be a valid number.',
            'amount.min' => 'The penalty amount must be at least $0.01.',
            'amount.max' => 'The penalty amount cannot exceed $9,999.99.',
            'reason.required' => 'Please provide a reason for the penalty.',
            'reason.min' => 'The reason must be at least 10 characters long.',
            'reason.max' => 'The reason cannot exceed 500 characters.',
            'date.required' => 'Please select the date when the penalty should be applied.',
            'date.date' => 'Please enter a valid date.',
            'date.before_or_equal' => 'The penalty date cannot be in the future.',
            'date.after' => 'Penalties cannot be applied for dates older than 3 months.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'staff member',
            'type' => 'penalty type',
            'amount' => 'penalty amount',
            'reason' => 'penalty reason',
            'date' => 'penalty date',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure amount is properly formatted
        if ($this->has('amount')) {
            $this->merge([
                'amount' => number_format((float)$this->amount, 2, '.', ''),
            ]);
        }

        // Ensure date is properly formatted
        if ($this->has('date') && $this->date) {
            try {
                $this->merge([
                    'date' => \Carbon\Carbon::parse($this->date)->format('Y-m-d'),
                ]);
            } catch (\Exception $e) {
                // Let validation handle invalid dates
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional business logic validation
            if ($this->user_id && $this->date) {
                // Check if there's already a penalty of the same type on the same date
                $existingPenalty = \App\Models\StaffPenalty::where('user_id', $this->user_id)
                    ->where('type', $this->type)
                    ->where('applied_date', $this->date)
                    ->where('status', 'active')
                    ->exists();

                if ($existingPenalty) {
                    $validator->errors()->add('date', 'A penalty of this type has already been applied for this date.');
                }
            }
        });
    }
}
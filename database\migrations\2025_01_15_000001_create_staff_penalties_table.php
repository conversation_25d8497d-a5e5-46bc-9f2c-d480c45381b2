<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('staff_penalties', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // 'late', 'absence', 'misconduct', 'other'
            $table->decimal('amount', 10, 2);
            $table->text('reason');
            $table->date('applied_date');
            $table->enum('status', ['active', 'waived', 'paid'])->default('active');
            $table->timestamp('waived_at')->nullable();
            $table->text('waived_reason')->nullable();
            $table->timestamps();

            $table->index(['tenant_id', 'user_id']);
            $table->index(['type', 'applied_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('staff_penalties');
    }
};
<?php

namespace Modules\Payroll\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Payroll\Services\PayrollService;
use App\Models\PayPeriod;
use App\Models\Payslip;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class PayrollController extends Controller
{
    protected PayrollService $payrollService;

    public function __construct(PayrollService $payrollService)
    {
        $this->payrollService = $payrollService;
    }

    /**
     * Get all pay periods for the tenant.
     */
    public function getPayPeriods(Request $request): JsonResponse
    {
        try {
            $tenantId = Auth::user()->tenant_id;
            $filters = $request->only(['status', 'period_type', 'year']);
            
            $payPeriods = $this->payrollService->getPayPeriods($tenantId, $filters);
            
            return response()->json([
                'success' => true,
                'data' => $payPeriods,
                'message' => 'Pay periods retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve pay periods: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new pay period.
     */
    public function createPayPeriod(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'nullable|string|max:100',
                'period_type' => 'required|in:weekly,bi_weekly,monthly,quarterly',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after:start_date',
                'notes' => 'nullable|string'
            ]);

            $validated['tenant_id'] = Auth::user()->tenant_id;
            
            $payPeriod = $this->payrollService->createPayPeriod($validated);
            
            return response()->json([
                'success' => true,
                'data' => $payPeriod->load(['payslips', 'processedBy']),
                'message' => 'Pay period created successfully'
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create pay period: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific pay period.
     */
    public function getPayPeriod(int $id): JsonResponse
    {
        try {
            $payPeriod = PayPeriod::with(['payslips.user', 'payslips.branch', 'processedBy'])
                ->where('tenant_id', Auth::user()->tenant_id)
                ->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $payPeriod,
                'message' => 'Pay period retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Pay period not found'
            ], 404);
        }
    }

    /**
     * Generate payroll for a pay period.
     */
    public function generatePayroll(Request $request, int $payPeriodId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'regenerate' => 'boolean'
            ]);

            $result = $this->payrollService->generatePayroll($payPeriodId, $validated);
            
            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Payroll generated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate payroll: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payslips for the authenticated user.
     */
    public function getMyPayslips(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $filters = $request->only(['status', 'year']);
            
            $payslips = $this->payrollService->getUserPayslips($userId, $filters);
            
            return response()->json([
                'success' => true,
                'data' => $payslips,
                'message' => 'Payslips retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payslips: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all payslips for a pay period (admin/manager only).
     */
    public function getPayslips(Request $request, int $payPeriodId): JsonResponse
    {
        try {
            // Check if user has permission to view all payslips
            if (!in_array(Auth::user()->role, ['admin', 'manager'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $payslips = Payslip::with(['user', 'branch', 'approvedBy'])
                ->where('pay_period_id', $payPeriodId)
                ->where('tenant_id', Auth::user()->tenant_id)
                ->get();
            
            return response()->json([
                'success' => true,
                'data' => $payslips,
                'message' => 'Payslips retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payslips: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific payslip.
     */
    public function getPayslip(int $id): JsonResponse
    {
        try {
            $payslip = Payslip::with(['payPeriod', 'user', 'branch', 'approvedBy'])
                ->where('tenant_id', Auth::user()->tenant_id)
                ->findOrFail($id);

            // Check if user can view this payslip
            if (!in_array(Auth::user()->role, ['admin', 'manager']) && $payslip->user_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }
            
            return response()->json([
                'success' => true,
                'data' => $payslip,
                'message' => 'Payslip retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payslip not found'
            ], 404);
        }
    }

    /**
     * Update a payslip (admin/manager only).
     */
    public function updatePayslip(Request $request, int $id): JsonResponse
    {
        try {
            // Check if user has permission to update payslips
            if (!in_array(Auth::user()->role, ['admin', 'manager'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $validated = $request->validate([
                'bonus' => 'nullable|numeric|min:0',
                'commission' => 'nullable|numeric|min:0',
                'allowances' => 'nullable|numeric|min:0',
                'other_deductions' => 'nullable|numeric|min:0',
                'notes' => 'nullable|string'
            ]);

            $payslip = Payslip::where('tenant_id', Auth::user()->tenant_id)
                ->findOrFail($id);

            if (!$payslip->canBeApproved()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payslip cannot be updated in its current status'
                ], 400);
            }

            $payslip->update($validated);
            $payslip->updateCalculatedFields();
            $payslip->save();
            
            return response()->json([
                'success' => true,
                'data' => $payslip->fresh(['payPeriod', 'user', 'branch']),
                'message' => 'Payslip updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payslip: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve a payslip (admin/manager only).
     */
    public function approvePayslip(int $id): JsonResponse
    {
        try {
            // Check if user has permission to approve payslips
            if (!in_array(Auth::user()->role, ['admin', 'manager'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $success = $this->payrollService->approvePayslip($id, Auth::id());
            
            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payslip cannot be approved in its current status'
                ], 400);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Payslip approved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve payslip: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark payslip as paid (admin only).
     */
    public function markPayslipAsPaid(Request $request, int $id): JsonResponse
    {
        try {
            // Check if user has permission to mark payslips as paid
            if (Auth::user()->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $validated = $request->validate([
                'payment_method' => 'nullable|string|max:50',
                'payment_reference' => 'nullable|string|max:100'
            ]);

            $success = $this->payrollService->markPayslipAsPaid(
                $id,
                $validated['payment_method'] ?? null,
                $validated['payment_reference'] ?? null
            );
            
            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payslip cannot be marked as paid in its current status'
                ], 400);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Payslip marked as paid successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark payslip as paid: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payroll statistics (admin/manager only).
     */
    public function getPayrollStatistics(Request $request): JsonResponse
    {
        try {
            // Check if user has permission to view statistics
            if (!in_array(Auth::user()->role, ['admin', 'manager'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $tenantId = Auth::user()->tenant_id;
            $filters = $request->only(['year', 'month']);
            
            $statistics = $this->payrollService->getPayrollStatistics($tenantId, $filters);
            
            return response()->json([
                'success' => true,
                'data' => $statistics,
                'message' => 'Payroll statistics retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payroll statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate next pay period automatically.
     */
    public function generateNextPayPeriod(int $currentPayPeriodId): JsonResponse
    {
        try {
            // Check if user has permission to create pay periods
            if (!in_array(Auth::user()->role, ['admin', 'manager'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            $nextPayPeriod = $this->payrollService->generateNextPayPeriod($currentPayPeriodId);
            
            return response()->json([
                'success' => true,
                'data' => $nextPayPeriod,
                'message' => 'Next pay period generated successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate next pay period: ' . $e->getMessage()
            ], 500);
        }
    }
}
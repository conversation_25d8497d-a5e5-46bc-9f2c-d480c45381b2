<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuItemBranch extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'menu_item_id',
        'branch_id',
        'branch_price',
        'is_available',
        'stock_quantity',
        'low_stock_threshold',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'branch_price' => 'decimal:2',
            'is_available' => 'boolean',
            'stock_quantity' => 'integer',
            'low_stock_threshold' => 'integer',
        ];
    }

    // Relationships
    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }
}
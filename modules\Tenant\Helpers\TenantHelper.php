<?php

namespace Modules\Tenant\Helpers;

use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TenantHelper
{
    /**
     * Generate a unique tenant code
     */
    public static function generateTenantCode(string $name, ?string $businessType = null): string
    {
        // Create base code from name
        $baseCode = Str::slug($name, '-');
        
        // Add business type if provided
        if ($businessType) {
            $typeCode = Str::slug($businessType, '-');
            $baseCode = $typeCode . '-' . $baseCode;
        }
        
        // Ensure uniqueness
        $code = $baseCode;
        $counter = 1;
        
        while (Tenant::where('code', $code)->exists()) {
            $code = $baseCode . '-' . $counter;
            $counter++;
        }
        
        return $code;
    }

    /**
     * Check if a tenant has access to a specific feature
     */
    public static function hasFeatureAccess(Tenant $tenant, string $feature): bool
    {
        $subscription = $tenant->activeSubscription;
        
        if (!$subscription || !$subscription->plan) {
            return false;
        }
        
        $features = $subscription->plan->features ?? [];
        
        return isset($features[$feature]) && $features[$feature] === true;
    }

    /**
     * Get feature limit for a tenant
     */
    public static function getFeatureLimit(Tenant $tenant, string $feature): ?int
    {
        $subscription = $tenant->activeSubscription;
        
        if (!$subscription || !$subscription->plan) {
            return null;
        }
        
        $limits = $subscription->plan->limits ?? [];
        
        return $limits[$feature] ?? null;
    }

    /**
     * Check if tenant has reached a usage limit
     */
    public static function hasReachedLimit(Tenant $tenant, string $feature, int $currentUsage): bool
    {
        $limit = self::getFeatureLimit($tenant, $feature);
        
        if ($limit === null) {
            return false; // No limit set
        }
        
        if ($limit === -1) {
            return false; // Unlimited
        }
        
        return $currentUsage >= $limit;
    }

    /**
     * Get tenant's current usage for a feature
     */
    public static function getCurrentUsage(Tenant $tenant, string $feature): int
    {
        $cacheKey = "tenant_usage_{$tenant->id}_{$feature}";
        
        return Cache::remember($cacheKey, 300, function () use ($tenant, $feature) {
            switch ($feature) {
                case 'branches':
                    return $tenant->branches()->count();
                    
                case 'users':
                    return $tenant->users()->count();
                    
                case 'menu_items':
                    return $tenant->menuItems()->count();
                    
                case 'orders_per_month':
                    return $tenant->orders()
                        ->whereMonth('created_at', now()->month)
                        ->whereYear('created_at', now()->year)
                        ->count();
                        
                case 'storage_mb':
                    // Calculate storage usage (simplified)
                    return $tenant->mediaFiles()->sum('size') / (1024 * 1024);
                    
                default:
                    return 0;
            }
        });
    }

    /**
     * Clear usage cache for a tenant
     */
    public static function clearUsageCache(Tenant $tenant, ?string $feature = null): void
    {
        if ($feature) {
            Cache::forget("tenant_usage_{$tenant->id}_{$feature}");
        } else {
            // Clear all usage cache for tenant
            $features = ['branches', 'users', 'menu_items', 'orders_per_month', 'storage_mb'];
            foreach ($features as $f) {
                Cache::forget("tenant_usage_{$tenant->id}_{$f}");
            }
        }
    }

    /**
     * Get tenant's subscription status
     */
    public static function getSubscriptionStatus(Tenant $tenant): array
    {
        $subscription = $tenant->activeSubscription;
        
        if (!$subscription) {
            return [
                'status' => 'no_subscription',
                'message' => 'No active subscription',
                'is_active' => false,
                'is_trial' => false,
                'days_remaining' => 0,
            ];
        }
        
        $now = now();
        $isActive = $subscription->status === 'active';
        $isTrial = $subscription->trial_ends_at && $now->lt($subscription->trial_ends_at);
        
        if ($isTrial) {
            $daysRemaining = $now->diffInDays($subscription->trial_ends_at, false);
            $status = 'trial';
            $message = "Trial period - {$daysRemaining} days remaining";
        } elseif ($isActive) {
            $daysRemaining = $subscription->ends_at ? $now->diffInDays($subscription->ends_at, false) : null;
            $status = 'active';
            $message = $daysRemaining ? "Active - {$daysRemaining} days remaining" : 'Active';
        } else {
            $daysRemaining = 0;
            $status = $subscription->status;
            $message = ucfirst($subscription->status);
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'is_active' => $isActive,
            'is_trial' => $isTrial,
            'days_remaining' => $daysRemaining,
            'subscription' => $subscription,
        ];
    }

    /**
     * Format business hours for display
     */
    public static function formatBusinessHours(array $businessHours): array
    {
        $formatted = [];
        $dayOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        foreach ($dayOrder as $day) {
            $dayHours = collect($businessHours)->firstWhere('day', $day);
            
            if ($dayHours) {
                if ($dayHours['is_closed'] ?? false) {
                    $formatted[$day] = 'Closed';
                } else {
                    $openTime = Carbon::createFromFormat('H:i', $dayHours['open_time'])->format('g:i A');
                    $closeTime = Carbon::createFromFormat('H:i', $dayHours['close_time'])->format('g:i A');
                    $formatted[$day] = "{$openTime} - {$closeTime}";
                }
            } else {
                $formatted[$day] = 'Not set';
            }
        }
        
        return $formatted;
    }

    /**
     * Check if tenant is currently open based on business hours
     */
    public static function isCurrentlyOpen(Tenant $tenant): bool
    {
        if (!$tenant->business_hours) {
            return true; // Assume open if no hours set
        }
        
        $now = now($tenant->timezone ?? config('app.timezone'));
        $currentDay = strtolower($now->format('l'));
        $currentTime = $now->format('H:i');
        
        $todayHours = collect($tenant->business_hours)->firstWhere('day', $currentDay);
        
        if (!$todayHours || ($todayHours['is_closed'] ?? false)) {
            return false;
        }
        
        $openTime = $todayHours['open_time'] ?? '00:00';
        $closeTime = $todayHours['close_time'] ?? '23:59';
        
        return $currentTime >= $openTime && $currentTime <= $closeTime;
    }

    /**
     * Get tenant statistics
     */
    public static function getTenantStats(Tenant $tenant): array
    {
        $cacheKey = "tenant_stats_{$tenant->id}";
        
        return Cache::remember($cacheKey, 600, function () use ($tenant) {
            return [
                'total_branches' => $tenant->branches()->count(),
                'total_users' => $tenant->users()->count(),
                'total_menu_items' => $tenant->menuItems()->count(),
                'total_orders' => $tenant->orders()->count(),
                'total_customers' => $tenant->customers()->count(),
                'monthly_orders' => $tenant->orders()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
                'monthly_revenue' => $tenant->orders()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->where('status', 'completed')
                    ->sum('total_amount'),
                'active_staff' => $tenant->staff()->where('status', 'active')->count(),
                'subscription_status' => self::getSubscriptionStatus($tenant),
            ];
        });
    }

    /**
     * Clear tenant statistics cache
     */
    public static function clearStatsCache(Tenant $tenant): void
    {
        Cache::forget("tenant_stats_{$tenant->id}");
    }

    /**
     * Validate tenant data isolation
     */
    public static function validateDataIsolation(Tenant $tenant, $model): bool
    {
        if (!method_exists($model, 'getTenantId')) {
            return true; // Skip validation if model doesn't support multi-tenancy
        }
        
        return $model->getTenantId() === $tenant->id;
    }

    /**
     * Get available subscription plans for tenant
     */
    public static function getAvailablePlans(Tenant $tenant): \Illuminate\Database\Eloquent\Collection
    {
        $currentPlan = $tenant->activeSubscription?->plan;
        
        return SubscriptionPlan::where('is_active', true)
            ->where('is_public', true)
            ->when($currentPlan, function ($query) use ($currentPlan) {
                // Show upgrade options
                return $query->where('price', '>=', $currentPlan->price);
            })
            ->orderBy('price')
            ->get();
    }

    /**
     * Calculate prorated amount for plan change
     */
    public static function calculateProratedAmount(
        Tenant $tenant,
        SubscriptionPlan $newPlan,
        string $billingCycle = 'monthly'
    ): array {
        $subscription = $tenant->activeSubscription;
        
        if (!$subscription) {
            return [
                'amount' => $newPlan->getPrice($billingCycle),
                'prorated_amount' => 0,
                'credit_amount' => 0,
                'total_amount' => $newPlan->getPrice($billingCycle),
            ];
        }
        
        $currentPlan = $subscription->plan;
        $now = now();
        $periodEnd = $subscription->ends_at;
        
        if (!$periodEnd) {
            return [
                'amount' => $newPlan->getPrice($billingCycle),
                'prorated_amount' => 0,
                'credit_amount' => 0,
                'total_amount' => $newPlan->getPrice($billingCycle),
            ];
        }
        
        $daysRemaining = $now->diffInDays($periodEnd, false);
        $totalDaysInPeriod = $subscription->starts_at->diffInDays($periodEnd);
        
        if ($daysRemaining <= 0 || $totalDaysInPeriod <= 0) {
            return [
                'amount' => $newPlan->getPrice($billingCycle),
                'prorated_amount' => 0,
                'credit_amount' => 0,
                'total_amount' => $newPlan->getPrice($billingCycle),
            ];
        }
        
        $currentPlanPrice = $currentPlan->getPrice($subscription->billing_cycle);
        $newPlanPrice = $newPlan->getPrice($billingCycle);
        
        // Calculate unused credit from current plan
        $creditAmount = ($currentPlanPrice / $totalDaysInPeriod) * $daysRemaining;
        
        // Calculate prorated amount for new plan
        $proratedAmount = ($newPlanPrice / $totalDaysInPeriod) * $daysRemaining;
        
        $totalAmount = max(0, $proratedAmount - $creditAmount);
        
        return [
            'amount' => $newPlanPrice,
            'prorated_amount' => round($proratedAmount, 2),
            'credit_amount' => round($creditAmount, 2),
            'total_amount' => round($totalAmount, 2),
            'days_remaining' => $daysRemaining,
            'total_days_in_period' => $totalDaysInPeriod,
        ];
    }

    /**
     * Generate tenant subdomain
     */
    public static function generateSubdomain(string $name): string
    {
        $subdomain = Str::slug($name, '-');
        
        // Ensure uniqueness
        $counter = 1;
        $originalSubdomain = $subdomain;
        
        while (Tenant::where('subdomain', $subdomain)->exists()) {
            $subdomain = $originalSubdomain . '-' . $counter;
            $counter++;
        }
        
        return $subdomain;
    }
}
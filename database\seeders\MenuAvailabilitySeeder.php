<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuAvailability;

class MenuAvailabilitySeeder extends Seeder
{
    public function run()
    {
        $menuItem = \App\Models\MenuItem::first();
        $branch = \App\Models\Branch::first();

        if (!$menuItem || !$branch) {
            $this->command->info('No menu item or branch found. Skipping MenuAvailabilitySeeder.');
            return;
        }

        MenuAvailability::create([
            'menu_item_id' => $menuItem->id,
            'branch_id' => $branch->id,
            'day_of_week' => 1, // Monday
            'start_time' => '09:00:00',
            'end_time' => '22:00:00',
            'is_available' => true,
        ]);
    }
}
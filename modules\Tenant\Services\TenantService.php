<?php

namespace Modules\Tenant\Services;

use App\Models\Tenant;
use App\Models\TenantSubscription;
use App\Models\SubscriptionPlan;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

class TenantService
{
    /**
     * Create a new tenant
     */
    public function createTenant(array $data): Tenant
    {
        // Generate unique tenant code if not provided
        if (!isset($data['code'])) {
            $data['code'] = $this->generateTenantCode($data['name']);
        }

        // Set default status if not provided
        if (!isset($data['status'])) {
            $data['status'] = 'active';
        }

        // Set trial end date if not provided
        if (!isset($data['trial_ends_at'])) {
            $data['trial_ends_at'] = Carbon::now()->addDays(14);
        }

        $tenant = Tenant::create($data);

        // Create default subscription if plan_id is provided
        if (isset($data['plan_id'])) {
            $this->createDefaultSubscription($tenant, $data['plan_id']);
        }

        // Create default branch
        $this->createDefaultBranch($tenant);

        return $tenant;
    }

    /**
     * Update tenant
     */
    public function updateTenant(Tenant $tenant, array $data): Tenant
    {
        $tenant->update($data);
        return $tenant->fresh();
    }

    /**
     * Delete tenant (soft delete)
     */
    public function deleteTenant(Tenant $tenant): bool
    {
        // Cancel active subscriptions
        $tenant->subscriptions()->where('status', 'active')->update([
            'status' => 'cancelled',
            'cancelled_at' => Carbon::now()
        ]);

        // Soft delete tenant
        return $tenant->delete();
    }

    /**
     * Activate tenant
     */
    public function activateTenant(Tenant $tenant): Tenant
    {
        $tenant->update(['status' => 'active']);
        return $tenant->fresh();
    }

    /**
     * Deactivate tenant
     */
    public function deactivateTenant(Tenant $tenant): Tenant
    {
        $tenant->update(['status' => 'inactive']);
        return $tenant->fresh();
    }

    /**
     * Get tenant statistics
     */
    public function getTenantStatistics(Tenant $tenant): array
    {
        $stats = [
            'total_branches' => $tenant->branches()->count(),
            'active_branches' => $tenant->branches()->where('status', 'active')->count(),
            'total_users' => $tenant->users()->count(),
            'active_users' => $tenant->users()->where('status', 'active')->count(),
            'total_menu_items' => $tenant->menuItems()->count(),
            'active_menu_items' => $tenant->menuItems()->where('is_active', true)->count(),
            'total_customers' => $tenant->customers()->count(),
            'subscription_status' => $this->getCurrentSubscriptionStatus($tenant),
            'trial_days_remaining' => $this->getTrialDaysRemaining($tenant),
        ];

        // Get monthly statistics
        $currentMonth = Carbon::now()->startOfMonth();
        $stats['monthly_orders'] = $tenant->orders()
            ->where('created_at', '>=', $currentMonth)
            ->count();

        $stats['monthly_revenue'] = $tenant->orders()
            ->where('created_at', '>=', $currentMonth)
            ->where('status', 'completed')
            ->sum('total_amount');

        return $stats;
    }

    /**
     * Check if tenant can access feature
     */
    public function canAccessFeature(Tenant $tenant, string $feature): bool
    {
        $subscription = $tenant->subscriptions()
            ->where('status', 'active')
            ->with('plan')
            ->first();

        if (!$subscription) {
            return false;
        }

        $features = $subscription->plan->features ?? [];
        return isset($features[$feature]) && $features[$feature] === true;
    }

    /**
     * Check if tenant has reached limit
     */
    public function hasReachedLimit(Tenant $tenant, string $limitType): bool
    {
        $subscription = $tenant->subscriptions()
            ->where('status', 'active')
            ->with('plan')
            ->first();

        if (!$subscription) {
            return true;
        }

        $plan = $subscription->plan;
        $currentUsage = $this->getCurrentUsage($tenant, $limitType);

        switch ($limitType) {
            case 'branches':
                return $currentUsage >= $plan->max_branches;
            case 'users':
                return $currentUsage >= $plan->max_users;
            case 'menu_items':
                return $currentUsage >= $plan->max_menu_items;
            case 'monthly_orders':
                return $currentUsage >= $plan->max_orders_per_month;
            default:
                return false;
        }
    }

    /**
     * Get current usage for a specific limit type
     */
    protected function getCurrentUsage(Tenant $tenant, string $limitType): int
    {
        switch ($limitType) {
            case 'branches':
                return $tenant->branches()->count();
            case 'users':
                return $tenant->users()->count();
            case 'menu_items':
                return $tenant->menuItems()->count();
            case 'monthly_orders':
                $currentMonth = Carbon::now()->startOfMonth();
                return $tenant->orders()
                    ->where('created_at', '>=', $currentMonth)
                    ->count();
            default:
                return 0;
        }
    }

    /**
     * Generate unique tenant code
     */
    protected function generateTenantCode(string $name): string
    {
        $baseCode = Str::slug(Str::limit($name, 20, ''));
        $code = $baseCode;
        $counter = 1;

        while (Tenant::where('code', $code)->exists()) {
            $code = $baseCode . '-' . $counter;
            $counter++;
        }

        return $code;
    }

    /**
     * Create default subscription for tenant
     */
    protected function createDefaultSubscription(Tenant $tenant, int $planId): TenantSubscription
    {
        $plan = SubscriptionPlan::findOrFail($planId);
        
        return TenantSubscription::create([
            'tenant_id' => $tenant->id,
            'plan_id' => $plan->id,
            'status' => 'trial',
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addDays(14),
            'trial_days' => 14,
            'billing_cycle' => 'monthly',
            'next_billing_date' => Carbon::now()->addDays(14),
        ]);
    }

    /**
     * Create default branch for tenant
     */
    protected function createDefaultBranch(Tenant $tenant): Branch
    {
        return Branch::create([
            'tenant_id' => $tenant->id,
            'name' => $tenant->name . ' - Main Branch',
            'code' => 'MAIN',
            'address' => $tenant->business_address ?? '',
            'phone' => $tenant->contact_phone ?? '',
            'email' => $tenant->contact_email ?? '',
            'manager_name' => 'Manager',
            'operating_hours' => [
                'monday' => ['open' => '09:00', 'close' => '22:00', 'is_closed' => false],
                'tuesday' => ['open' => '09:00', 'close' => '22:00', 'is_closed' => false],
                'wednesday' => ['open' => '09:00', 'close' => '22:00', 'is_closed' => false],
                'thursday' => ['open' => '09:00', 'close' => '22:00', 'is_closed' => false],
                'friday' => ['open' => '09:00', 'close' => '22:00', 'is_closed' => false],
                'saturday' => ['open' => '09:00', 'close' => '22:00', 'is_closed' => false],
                'sunday' => ['open' => '09:00', 'close' => '22:00', 'is_closed' => false],
            ],
            'timezone' => 'UTC',
            'status' => 'active',
            'is_delivery_enabled' => true,
            'is_takeaway_enabled' => true,
            'is_dine_in_enabled' => true,
            'is_online_ordering_enabled' => true,
        ]);
    }

    /**
     * Get current subscription status
     */
    protected function getCurrentSubscriptionStatus(Tenant $tenant): ?string
    {
        $subscription = $tenant->subscriptions()
            ->where('status', 'active')
            ->orWhere('status', 'trial')
            ->latest()
            ->first();

        return $subscription ? $subscription->status : null;
    }

    /**
     * Get trial days remaining
     */
    protected function getTrialDaysRemaining(Tenant $tenant): ?int
    {
        if (!$tenant->trial_ends_at) {
            return null;
        }

        $daysRemaining = Carbon::now()->diffInDays($tenant->trial_ends_at, false);
        return max(0, $daysRemaining);
    }

    /**
     * Validate tenant data isolation
     */
    public function validateDataIsolation(Tenant $tenant, $model): bool
    {
        if (method_exists($model, 'tenant')) {
            return $model->tenant_id === $tenant->id;
        }

        return false;
    }

    /**
     * Get tenant by code
     */
    public function getTenantByCode(string $code): ?Tenant
    {
        return Tenant::where('code', $code)->first();
    }

    /**
     * Check if tenant code is available
     */
    public function isCodeAvailable(string $code, ?int $excludeTenantId = null): bool
    {
        $query = Tenant::where('code', $code);
        
        if ($excludeTenantId) {
            $query->where('id', '!=', $excludeTenantId);
        }
        
        return !$query->exists();
    }
}
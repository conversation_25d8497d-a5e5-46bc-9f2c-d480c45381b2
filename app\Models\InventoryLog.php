<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_inventory_id',
        'action',
        'quantity_before',
        'quantity_after',
        'quantity_changed',
        'reason',
        'notes',
        'user_id',
        'ip_address',
        'user_agent',
        'resolved',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'quantity_before' => 'decimal:6',
            'quantity_after' => 'decimal:6',
            'quantity_changed' => 'decimal:6',
            'resolved' => 'boolean',
        ];
    }

    // Relationships
    public function branchInventory()
    {
        return $this->belongsTo(BranchInventory::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeUnresolved($query)
    {
        return $query->where('resolved', false);
    }
}
<?php

namespace Modules\Kitchen\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateKitchenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $kitchen = $this->route('kitchen');
        
        return [
            'branch_id' => 'sometimes|exists:branches,id',
            'name' => 'sometimes|string|max:255',
            'code' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('kitchens')->where(function ($query) {
                    return $query->where('tenant_id', $this->user()->tenant_id);
                })->ignore($kitchen->id ?? null)
            ],
            'description' => 'nullable|string|max:1000',
            'station_type' => 'sometimes|in:hot,cold,grill,fryer,salad,dessert,beverage,prep,main,other',
            'max_concurrent_orders' => 'sometimes|integer|min:1|max:100',
            'average_prep_time_minutes' => 'sometimes|integer|min:1|max:300',
            'is_active' => 'boolean',
            'display_order' => 'integer|min:0',
            'manager_id' => 'nullable|exists:users,id',
            'equipment_list' => 'nullable|array',
            'equipment_list.*' => 'string|max:255',
            'operating_hours' => 'nullable|array',
            'operating_hours.monday' => 'nullable|array',
            'operating_hours.monday.start' => 'nullable|date_format:H:i',
            'operating_hours.monday.end' => 'nullable|date_format:H:i|after:operating_hours.monday.start',
            'operating_hours.tuesday' => 'nullable|array',
            'operating_hours.tuesday.start' => 'nullable|date_format:H:i',
            'operating_hours.tuesday.end' => 'nullable|date_format:H:i|after:operating_hours.tuesday.start',
            'operating_hours.wednesday' => 'nullable|array',
            'operating_hours.wednesday.start' => 'nullable|date_format:H:i',
            'operating_hours.wednesday.end' => 'nullable|date_format:H:i|after:operating_hours.wednesday.start',
            'operating_hours.thursday' => 'nullable|array',
            'operating_hours.thursday.start' => 'nullable|date_format:H:i',
            'operating_hours.thursday.end' => 'nullable|date_format:H:i|after:operating_hours.thursday.start',
            'operating_hours.friday' => 'nullable|array',
            'operating_hours.friday.start' => 'nullable|date_format:H:i',
            'operating_hours.friday.end' => 'nullable|date_format:H:i|after:operating_hours.friday.start',
            'operating_hours.saturday' => 'nullable|array',
            'operating_hours.saturday.start' => 'nullable|date_format:H:i',
            'operating_hours.saturday.end' => 'nullable|date_format:H:i|after:operating_hours.saturday.start',
            'operating_hours.sunday' => 'nullable|array',
            'operating_hours.sunday.start' => 'nullable|date_format:H:i',
            'operating_hours.sunday.end' => 'nullable|date_format:H:i|after:operating_hours.sunday.start',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'branch_id.exists' => 'Selected branch does not exist.',
            'name.max' => 'Kitchen name cannot exceed 255 characters.',
            'code.unique' => 'Kitchen code must be unique within your tenant.',
            'code.max' => 'Kitchen code cannot exceed 20 characters.',
            'station_type.in' => 'Invalid station type selected.',
            'max_concurrent_orders.min' => 'Maximum concurrent orders must be at least 1.',
            'max_concurrent_orders.max' => 'Maximum concurrent orders cannot exceed 100.',
            'average_prep_time_minutes.min' => 'Average preparation time must be at least 1 minute.',
            'average_prep_time_minutes.max' => 'Average preparation time cannot exceed 300 minutes.',
            'manager_id.exists' => 'Selected manager does not exist.',
            'equipment_list.array' => 'Equipment list must be an array.',
            'operating_hours.array' => 'Operating hours must be an array.',
            '*.start.date_format' => 'Start time must be in HH:MM format.',
            '*.end.date_format' => 'End time must be in HH:MM format.',
            '*.end.after' => 'End time must be after start time.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'branch_id' => 'branch',
            'max_concurrent_orders' => 'maximum concurrent orders',
            'average_prep_time_minutes' => 'average preparation time',
            'manager_id' => 'manager',
            'equipment_list' => 'equipment list',
            'operating_hours' => 'operating hours',
        ];
    }
}
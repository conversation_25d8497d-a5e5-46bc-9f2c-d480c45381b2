# Staff Module

This module manages all staff-related functionalities, including staff profiles, attendance, shift scheduling, and leave requests.

## Features

- **Staff Management**: CRUD operations for staff profiles (users with staff roles).
- **Attendance Tracking**: Check-in/check-out, attendance records, and statistics.
- **Shift Management**: Create, update, and manage shifts.
- **Shift Type Management**: Define and manage different types of shifts.
- **Shift Assignment**: Assign staff to shifts, handle acceptance/declination, and replacement requests.
- **Leave Management**: Submit and manage leave requests.
- **Timesheet Management**: Track staff working hours and generate timesheets.
- **Payroll Integration**: Calculate working hours for payroll purposes.

## Directory Structure

```
modules/Staff/
├── Helpers/                  # Helper functions (if any)
├── Http/
│   ├── Controllers/          # API controllers for staff operations
│   │   └── StaffController.php
│   ├── Middleware/           # Middleware for access control (if any)
│   └── Requests/             # Form requests for validation
│       ├── AssignShiftRequest.php
│       ├── CheckInRequest.php
│       ├── CheckOutRequest.php
│       ├── CreateShiftRequest.php
│       ├── CreateShiftTypeRequest.php
│       ├── UpdateShiftRequest.php
│       └── UpdateShiftTypeRequest.php
├── Providers/                # Service providers for module registration
│   └── HRServiceProvider.php
├── Services/                 # Business logic and data manipulation
│   └── HRService.php
├── resources/                # Frontend resources (views, assets - if any)
│   └── views/
└── routes/                   # API and web routes
    ├── api.php
    └── web.php
```

## API Endpoints

All API endpoints are prefixed with `/api/staff` and require `auth:sanctum` and `tenant.context` middleware unless specified.

### Staff Management

- `GET /api/staff`
  - Description: Get a paginated list of staff members.
  - Query Parameters: `search`, `role`, `branch_id`, `per_page`.
- `GET /api/staff/{id}`
  - Description: Get details of a specific staff member.
- `GET /api/staff/{userId}/working-hours`
  - Description: Get working hours for a staff member within a date range.
  - Query Parameters: `start_date`, `end_date`.

### Attendance Management

- `POST /api/attendance/check-in`
  - Description: Record staff check-in.
  - Request Body: `CheckInRequest` fields.
- `POST /api/attendance/check-out`
  - Description: Record staff check-out.
  - Request Body: `CheckOutRequest` fields.
- `GET /api/attendance`
  - Description: Get attendance records with filters.
  - Query Parameters: `user_id`, `branch_id`, `date_from`, `date_to`, `status`, `per_page`.
- `GET /api/attendance/stats`
  - Description: Get attendance statistics for a period.
  - Query Parameters: `start_date`, `end_date`, `branch_id`.

### Schedule Management

- `GET /api/schedule`
  - Description: Get staff schedule.

### Shift Management

- `GET /api/shifts`
  - Description: Get a list of shifts.
- `POST /api/shifts`
  - Description: Create a new shift.
  - Request Body: `CreateShiftRequest` fields.
- `PUT /api/shifts/{id}`
  - Description: Update an existing shift.
  - Request Body: `UpdateShiftRequest` fields.
- `GET /api/shifts/understaffed`
  - Description: Get understaffed shifts.
- `GET /api/shifts/{shiftId}/available-staff`
  - Description: Get available staff for a specific shift.

### Shift Assignment

- `POST /api/shifts/assign`
  - Description: Assign staff to a shift.
  - Request Body: `AssignShiftRequest` fields.
- `POST /api/shifts/assignments/{assignmentId}/accept`
  - Description: Accept a shift assignment.
- `POST /api/shifts/assignments/{assignmentId}/decline`
  - Description: Decline a shift assignment.
- `POST /api/shifts/assignments/{assignmentId}/request-replacement`
  - Description: Request a replacement for a shift assignment.

### Shift Types Management

- `GET /api/shift-types`
  - Description: Get a list of shift types.
- `POST /api/shift-types`
  - Description: Create a new shift type.
  - Request Body: `CreateShiftTypeRequest` fields.
- `PUT /api/shift-types/{id}`
  - Description: Update an existing shift type.
  - Request Body: `UpdateShiftTypeRequest` fields.

### Public Routes

- `POST /api/public/staff`
  - Description: Public routes for staff-related information (e.g., for external systems).

## Configuration

This module does not currently have a dedicated configuration file. All settings are managed within the code or via environment variables.

## Usage

To use this module, ensure it is registered in your application's `config/app.php` (if not auto-discovered) and run migrations to create necessary tables.

Example of using the `HRService`:

```php
use Modules\HR\Services\HRService;
use App\Models\User;

class SomeClass
{
    protected HRService $hrService;

public function __construct(HRService $hrService)
{
    $this->hrService = $hrService;
    }

    public function exampleUsage(int $tenantId, User $staffMember)
    {
        // Get all staff members
        $staff = $this->hrService->getStaff($tenantId);

        // Check in a staff member
        $this->hrService->checkIn($tenantId, $staffMember->id, [
            'branch_id' => 1,
            'location_latitude' => 34.0522,
            'location_longitude' => -118.2437,
        ]);

        // Create a new shift
        $shift = $this->hrService->createShift($tenantId, [
            'branch_id' => 1,
            'shift_type_id' => 1,
            'date' => '2024-07-20',
            'start_time' => '09:00',
            'end_time' => '17:00',
            'required_staff_count' => 5,
        ]);

        // Assign staff to a shift
        $this->hrService->assignShift($tenantId, [
            'shift_id' => $shift->id,
            'shift_type_id' => $shift->shift_type_id,
            'user_id' => $staffMember->id,
            'role' => 'Cashier',
        ]);
    }
}
```
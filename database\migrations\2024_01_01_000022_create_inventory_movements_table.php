<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->enum('movement_type', ['in', 'out', 'adjustment', 'transfer', 'waste', 'production']);
            $table->string('reference_type', 50)->nullable()->comment('purchase_order, order, adjustment, etc.');
            $table->unsignedBigInteger('reference_id')->nullable()->comment('ID of related record');
            $table->decimal('quantity', 15, 3);
            $table->decimal('unit_cost', 10, 4)->nullable();
            $table->decimal('total_cost', 12, 2)->nullable();
            $table->decimal('balance_after', 15, 3)->comment('Stock balance after this movement');
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->timestamp('movement_date')->default(now());
            $table->timestamps();
            
            $table->index(['branch_id', 'product_id', 'movement_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_movements');
    }
};
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class PayPeriod extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'name',
        'period_type',
        'start_date',
        'end_date',
        'status',
        'total_gross_pay',
        'total_deductions',
        'total_net_pay',
        'total_employees',
        'processed_at',
        'processed_by',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'processed_at' => 'datetime',
        'total_gross_pay' => 'decimal:2',
        'total_deductions' => 'decimal:2',
        'total_net_pay' => 'decimal:2',
    ];

    /**
     * Get the tenant that owns the pay period.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user who processed this pay period.
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Get the payslips for this pay period.
     */
    public function payslips(): HasMany
    {
        return $this->hasMany(Payslip::class);
    }

    /**
     * Scope a query to only include pay periods for a specific tenant.
     */
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Scope a query to only include pay periods with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include pay periods within a date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate]);
    }

    /**
     * Check if the pay period is currently active.
     */
    public function isActive(): bool
    {
        $now = Carbon::now()->toDateString();
        return $this->start_date <= $now && $this->end_date >= $now;
    }

    /**
     * Check if the pay period can be processed.
     */
    public function canBeProcessed(): bool
    {
        return in_array($this->status, ['draft', 'processing']);
    }

    /**
     * Check if the pay period is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Get the duration of the pay period in days.
     */
    public function getDurationInDays(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Generate a default name for the pay period based on type and dates.
     */
    public function generateName(): string
    {
        switch ($this->period_type) {
            case 'weekly':
                return 'Week of ' . $this->start_date->format('M j, Y');
            case 'bi_weekly':
                return 'Bi-weekly ' . $this->start_date->format('M j') . ' - ' . $this->end_date->format('M j, Y');
            case 'monthly':
                return $this->start_date->format('F Y');
            case 'quarterly':
                $quarter = ceil($this->start_date->month / 3);
                return 'Q' . $quarter . ' ' . $this->start_date->year;
            default:
                return $this->start_date->format('M j') . ' - ' . $this->end_date->format('M j, Y');
        }
    }

    /**
     * Calculate totals from associated payslips.
     */
    public function calculateTotals(): void
    {
        $payslips = $this->payslips;
        
        $this->total_gross_pay = $payslips->sum('gross_pay');
        $this->total_deductions = $payslips->sum('total_deductions');
        $this->total_net_pay = $payslips->sum('net_pay');
        $this->total_employees = $payslips->count();
        
        $this->save();
    }

    /**
     * Get the next pay period based on the current period type.
     */
    public function getNextPayPeriod(): array
    {
        $nextStart = null;
        $nextEnd = null;

        switch ($this->period_type) {
            case 'weekly':
                $nextStart = $this->end_date->copy()->addDay();
                $nextEnd = $nextStart->copy()->addDays(6);
                break;
            case 'bi_weekly':
                $nextStart = $this->end_date->copy()->addDay();
                $nextEnd = $nextStart->copy()->addDays(13);
                break;
            case 'monthly':
                $nextStart = $this->start_date->copy()->addMonth()->startOfMonth();
                $nextEnd = $nextStart->copy()->endOfMonth();
                break;
            case 'quarterly':
                $nextStart = $this->start_date->copy()->addQuarter()->startOfQuarter();
                $nextEnd = $nextStart->copy()->endOfQuarter();
                break;
        }

        return [
            'start_date' => $nextStart,
            'end_date' => $nextEnd,
            'period_type' => $this->period_type,
        ];
    }
}
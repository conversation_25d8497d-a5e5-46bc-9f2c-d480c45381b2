<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\PayPeriod;
use App\Models\Payslip;

class GeneratePayslipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin and manager roles can generate payslips
        return Auth::check() && in_array(Auth::user()->role, ['admin', 'manager']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                'integer',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    // Ensure the user belongs to the same tenant
                    $user = \App\Models\User::find($value);
                    if ($user && $user->tenant_id !== Auth::user()->tenant_id) {
                        $fail('The selected user does not belong to your organization.');
                    }
                },
            ],
            'pay_period_id' => [
                'required',
                'integer',
                'exists:pay_periods,id',
                function ($attribute, $value, $fail) {
                    // Ensure the pay period belongs to the same tenant
                    $payPeriod = PayPeriod::find($value);
                    if ($payPeriod && $payPeriod->tenant_id !== Auth::user()->tenant_id) {
                        $fail('The selected pay period does not belong to your organization.');
                    }
                },
            ],
            'force_regenerate' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'Please select a staff member.',
            'user_id.exists' => 'The selected staff member does not exist.',
            'pay_period_id.required' => 'Please select a pay period.',
            'pay_period_id.exists' => 'The selected pay period does not exist.',
            'force_regenerate.boolean' => 'Force regenerate must be true or false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'staff member',
            'pay_period_id' => 'pay period',
            'force_regenerate' => 'force regenerate option',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($this->user_id && $this->pay_period_id) {
                // Check if pay period is valid for payslip generation
                $payPeriod = PayPeriod::find($this->pay_period_id);
                if ($payPeriod) {
                    // Check if pay period is completed
                    if ($payPeriod->status !== 'completed' && $payPeriod->end_date->isFuture()) {
                        $validator->errors()->add('pay_period_id', 'Payslips can only be generated for completed pay periods.');
                    }

                    // Check if payslip already exists (unless force regenerate is true)
                    if (!$this->boolean('force_regenerate')) {
                        $existingPayslip = Payslip::where('user_id', $this->user_id)
                            ->where('pay_period_id', $this->pay_period_id)
                            ->exists();

                        if ($existingPayslip) {
                            $validator->errors()->add('pay_period_id', 'A payslip already exists for this staff member and pay period. Use force_regenerate=true to regenerate.');
                        }
                    }
                }

                // Check if user has required salary information
                $user = \App\Models\User::find($this->user_id);
                if ($user) {
                    if (!$user->base_salary && !$user->hourly_rate) {
                        $validator->errors()->add('user_id', 'The selected staff member does not have salary information configured.');
                    }
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default value for force_regenerate if not provided
        if (!$this->has('force_regenerate')) {
            $this->merge([
                'force_regenerate' => false,
            ]);
        }
    }

    /**
     * Get validated data with additional processing.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Ensure force_regenerate is boolean
        if (isset($validated['force_regenerate'])) {
            $validated['force_regenerate'] = $this->boolean('force_regenerate');
        }
        
        return $validated;
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('delivery_assignment_id')->constrained('delivery_assignments')->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained('customers');
            $table->integer('rating')->comment('1-5 star rating');
            $table->text('comment')->nullable();
            $table->json('review_categories')->nullable()->comment('Speed, politeness, food condition, etc.');
            $table->boolean('is_anonymous')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();
            
            $table->index(['delivery_assignment_id']);
            $table->index(['customer_id', 'rating']);
            $table->index(['rating', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_reviews');
    }
};
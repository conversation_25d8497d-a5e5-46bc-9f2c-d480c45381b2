# Customer Module

This module manages customer-related functionalities, including customer profiles, loyalty points, and customer statistics.

## Features

- **Customer Management**: CRUD operations for customer profiles.
- **Loyalty Program Integration**: Add and redeem loyalty points, view loyalty history.
- **Customer Analytics**: Retrieve customer statistics and top customers.
- **Customer Search**: Find customers by contact information (phone or email).
- **Customer Status**: Activate, deactivate, and update customer visit information.

## Directory Structure

- `Http/Controllers`: Handles incoming HTTP requests and returns responses.
  - `CustomerController.php`: Manages customer-related API endpoints.
- `Http/Requests`: Contains form request classes for validation.
  - `StoreCustomerRequest.php`: Validates requests for creating new customers.
  - `UpdateCustomerRequest.php`: Validates requests for updating existing customers.
  - `LoyaltyPointsRequest.php`: Validates requests for adding/redeeming loyalty points.
- `Services`: Contains the business logic for customer operations.
  - `CustomerService.php`: Provides methods for customer CRUD, loyalty management, and statistics.
- `Providers`: Service providers for module registration.
  - `CustomerServiceProvider.php`: Registers module services and loads routes.
- `routes`: Defines API and web routes for the module.
  - `api.php`: API routes for customer management.
  - `web.php`: Web routes (currently empty, can be used for future web-based features).

## API Endpoints

All API endpoints are prefixed with `/api/customers` and require `auth:api` middleware unless specified.

### Customer CRUD

- `GET /api/customers`: Get a paginated list of customers.
- `POST /api/customers`: Create a new customer.
- `GET /api/customers/{id}`: Get a specific customer by ID.
- `PUT /api/customers/{id}`: Update an existing customer.
- `DELETE /api/customers/{id}`: Delete a customer (soft delete).

### Customer Search and Utilities

- `POST /api/customers/find-by-contact`: Find a customer by phone or email (public route).
- `GET /api/customers/stats/overview`: Get overall customer statistics.
- `GET /api/customers/top-customers`: Get top customers by loyalty points.

### Customer Status Management

- `PATCH /api/customers/{id}/activate`: Activate a customer.
- `PATCH /api/customers/{id}/deactivate`: Deactivate a customer.
- `PATCH /api/customers/{id}/update-visit`: Update a customer's last visit timestamp.

### Loyalty Points Management

- `POST /api/customers/{id}/loyalty/add-points`: Add loyalty points to a customer.
- `POST /api/customers/{id}/loyalty/redeem-points`: Redeem loyalty points from a customer.
- `GET /api/customers/{id}/loyalty/history`: Get a customer's loyalty transaction history.

## Usage

To interact with the Customer module's API, ensure you have an authenticated user and make requests to the defined endpoints. For example, to get all customers:

```bash
curl -X GET "http://your-app-url/api/customers" \
     -H "Accept: application/json" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

To create a new customer:

```bash
curl -X POST "http://your-app-url/api/customers" \
     -H "Accept: application/json" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
           "first_name": "John",
           "last_name": "Doe",
           "email": "<EMAIL>",
           "phone": "+1234567890"
         }'
```

## Dependencies

This module relies on the `Customer` model and `LoyaltyTransaction` model, as well as standard Laravel facades like `Auth` and `DB`.
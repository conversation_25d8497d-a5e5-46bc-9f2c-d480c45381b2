@extends('menu::layouts.app')

@section('title', 'Menu Items')

@section('header-actions')
    <a href="{{ route('menu.web.menu-items.create') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i>
        Add Menu Item
    </a>
@endsection

@section('content')
<div class="row">
    <!-- Filters -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('menu.web.menu-items.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Search menu items...">
                    </div>
                    <div class="col-md-2">
                        <label for="menu_id" class="form-label">Menu</label>
                        <select class="form-select" id="menu_id" name="menu_id">
                            <option value="">All Menus</option>
                            @if(isset($menus))
                                @foreach($menus as $menu)
                                    <option value="{{ $menu->id }}" {{ request('menu_id') == $menu->id ? 'selected' : '' }}>
                                        {{ $menu->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="category_id" class="form-label">Category</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">All Categories</option>
                            @if(isset($categories))
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="availability" class="form-label">Availability</label>
                        <select class="form-select" id="availability" name="availability">
                            <option value="">All</option>
                            <option value="available" {{ request('availability') === 'available' ? 'selected' : '' }}>Available</option>
                            <option value="unavailable" {{ request('availability') === 'unavailable' ? 'selected' : '' }}>Unavailable</option>
                        </select>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="bi bi-search"></i>
                        </button>
                        <a href="{{ route('menu.web.menu-items.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- View Toggle -->
    <div class="col-12 mb-3">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                @if(isset($menuItems))
                    <span class="text-muted">{{ $menuItems->total() ?? 0 }} items found</span>
                @endif
            </div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleView('grid')" id="gridViewBtn">
                    <i class="bi bi-grid-3x3-gap me-1"></i> Grid
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary active" onclick="toggleView('list')" id="listViewBtn">
                    <i class="bi bi-list me-1"></i> List
                </button>
            </div>
        </div>
    </div>
    
    <!-- Menu Items Content -->
    <div class="col-12">
        @if(isset($menuItems) && $menuItems->count() > 0)
            <!-- List View -->
            <div id="listView">
                <div class="card">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>Menu</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Availability</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($menuItems as $item)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($item->image_url)
                                                    <img src="{{ $item->image_url }}" alt="{{ $item->name }}" 
                                                         class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 50px; height: 50px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <div class="fw-medium">{{ $item->name }}</div>
                                                    @if($item->description)
                                                        <small class="text-muted">{{ Str::limit($item->description, 60) }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($item->category)
                                                <a href="{{ route('menu.web.categories.show', $item->category->id) }}" class="text-decoration-none">
                                                    {{ $item->category->name }}
                                                </a>
                                            @else
                                                <span class="text-muted">No Category</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($item->menu)
                                                <a href="{{ route('menu.web.menus.show', $item->menu->id) }}" class="text-decoration-none">
                                                    {{ $item->menu->name }}
                                                </a>
                                                <br><small class="text-muted">{{ ucfirst(str_replace('_', ' ', $item->menu->menu_type)) }}</small>
                                            @else
                                                <span class="text-muted">No Menu</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="fw-medium">${{ number_format($item->base_price, 2) }}</span>
                                            @if($item->variants && $item->variants->count() > 0)
                                                <br><small class="text-muted">{{ $item->variants->count() }} variants</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($item->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" 
                                                       {{ $item->is_available ? 'checked' : '' }}
                                                       onchange="toggleAvailability({{ $item->id }}, this.checked)">
                                                <label class="form-check-label small">
                                                    {{ $item->is_available ? 'Available' : 'Unavailable' }}
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('menu.web.menu-items.show', $item->id) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="View">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('menu.web.menu-items.edit', $item->id) }}" 
                                                   class="btn btn-sm btn-outline-secondary" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                            data-bs-toggle="dropdown" title="More">
                                                        <i class="bi bi-three-dots"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="{{ route('menu.web.menu-items.menu-items.addons.index', $item->id) }}">
                                                            <i class="bi bi-plus-square me-1"></i> Manage Addons
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="{{ route('menu.web.menu-items.menu-items.variants.index', $item->id) }}">
                                                            <i class="bi bi-collection me-1"></i> Manage Variants
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteItem({{ $item->id }})">
                                                            <i class="bi bi-trash me-1"></i> Delete
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Grid View -->
            <div id="gridView" style="display: none;">
                <div class="row">
                    @foreach($menuItems as $item)
                        <div class="col-md-6 col-lg-4 col-xl-3 mb-4">
                            <div class="card h-100">
                                @if($item->image_url)
                                    <img src="{{ $item->image_url }}" class="card-img-top" alt="{{ $item->name }}" 
                                         style="height: 200px; object-fit: cover;">
                                @else
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                         style="height: 200px;">
                                        <i class="bi bi-image display-4 text-muted"></i>
                                    </div>
                                @endif
                                
                                <div class="card-body">
                                    <h6 class="card-title">{{ $item->name }}</h6>
                                    @if($item->description)
                                        <p class="card-text text-muted small">{{ Str::limit($item->description, 80) }}</p>
                                    @endif
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="h6 text-primary mb-0">${{ number_format($item->base_price, 2) }}</span>
                                        <div>
                                            @if($item->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <div class="small text-muted mb-2">
                                        <div><strong>Category:</strong> {{ $item->category->name ?? 'No Category' }}</div>
                                        <div><strong>Menu:</strong> {{ $item->menu->name ?? 'No Menu' }}</div>
                                    </div>
                                    
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" 
                                               {{ $item->is_available ? 'checked' : '' }}
                                               onchange="toggleAvailability({{ $item->id }}, this.checked)">
                                        <label class="form-check-label small">
                                            {{ $item->is_available ? 'Available' : 'Unavailable' }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100" role="group">
                                        <a href="{{ route('menu.web.menu-items.show', $item->id) }}" class="btn btn-outline-primary btn-sm">
                                            View
                                        </a>
                                        <a href="{{ route('menu.web.menu-items.edit', $item->id) }}" class="btn btn-outline-secondary btn-sm">
                                            Edit
                                        </a>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                                    data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ route('menu.web.menu-items.menu-items.addons.index', $item->id) }}">
                                                    Addons
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ route('menu.web.menu-items.menu-items.variants.index', $item->id) }}">
                                                    Variants
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteItem({{ $item->id }})">
                                                    Delete
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            
            <!-- Pagination -->
            @if(method_exists($menuItems, 'links'))
                <div class="d-flex justify-content-center mt-4">
                    {{ $menuItems->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-card-list display-1 text-muted"></i>
                    <h4 class="mt-3">No Menu Items Found</h4>
                    <p class="text-muted">Start building your menu by adding delicious items.</p>
                    <a href="{{ route('menu.web.menu-items.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>
                        Create Your First Menu Item
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Delete Item Modal -->
<div class="modal fade" id="deleteItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Warning!</strong> This will permanently delete the menu item and all its variants and addons.
                </div>
                <p>Are you sure you want to delete this menu item? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteItemForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>
                        Delete Item
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleView(viewType) {
    const listView = document.getElementById('listView');
    const gridView = document.getElementById('gridView');
    const listBtn = document.getElementById('listViewBtn');
    const gridBtn = document.getElementById('gridViewBtn');
    
    if (viewType === 'grid') {
        listView.style.display = 'none';
        gridView.style.display = 'block';
        listBtn.classList.remove('active');
        gridBtn.classList.add('active');
    } else {
        listView.style.display = 'block';
        gridView.style.display = 'none';
        listBtn.classList.add('active');
        gridBtn.classList.remove('active');
    }
}

function toggleAvailability(itemId, isAvailable) {
    fetch(`/menu/menu-items/${itemId}/toggle-availability`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ is_available: isAvailable })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the label text
            const labels = document.querySelectorAll(`input[onchange*="${itemId}"] + label`);
            labels.forEach(label => {
                label.textContent = isAvailable ? 'Available' : 'Unavailable';
            });
        } else {
            alert('Failed to update availability');
            // Revert the checkbox
            const checkboxes = document.querySelectorAll(`input[onchange*="${itemId}"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = !isAvailable;
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
        // Revert the checkbox
        const checkboxes = document.querySelectorAll(`input[onchange*="${itemId}"]`);
        checkboxes.forEach(checkbox => {
            checkbox.checked = !isAvailable;
        });
    });
}

function deleteItem(itemId) {
    const deleteForm = document.getElementById('deleteItemForm');
    deleteForm.action = `/menu/menu-items/${itemId}`;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteItemModal'));
    deleteModal.show();
}

// Auto-submit form on filter change
document.getElementById('menu_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('category_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('availability').addEventListener('change', function() {
    this.form.submit();
});
</script>
@endpush
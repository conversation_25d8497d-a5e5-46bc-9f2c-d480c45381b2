<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Inventory\Services\InventoryLogService;

class InventoryLogController extends Controller
{
    protected $inventoryLogService;

    public function __construct(InventoryLogService $inventoryLogService)
    {
        $this->inventoryLogService = $inventoryLogService;
    }

    /**
     * Display a listing of inventory logs.
     */
    public function index(Request $request)
    {
        $logs = $this->inventoryLogService->getAllLogs($request->all());
        return response()->json($logs);
    }

    /**
     * Display logs for a specific inventory item.
     */
    public function getItemLogs(Request $request, string $inventoryId)
    {
        $logs = $this->inventoryLogService->getItemLogs($inventoryId, $request->all());
        return response()->json($logs);
    }

    /**
     * Display logs for a specific user.
     */
    public function getUserLogs(Request $request, string $userId)
    {
        $logs = $this->inventoryLogService->getUserLogs($userId, $request->all());
        return response()->json($logs);
    }

    /**
     * Get inventory movements summary.
     */
    public function getMovementsSummary(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'product_id' => 'nullable|exists:products,id',
            'movement_type' => 'nullable|in:add,subtract,set,transfer,adjustment,waste,return'
        ]);

        $summary = $this->inventoryLogService->getMovementsSummary($request->all());
        return response()->json($summary);
    }

    /**
     * Get stock level history for an item.
     */
    public function getStockHistory(Request $request, string $inventoryId)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'interval' => 'nullable|in:hour,day,week,month'
        ]);

        $history = $this->inventoryLogService->getStockHistory($inventoryId, $request->all());
        return response()->json($history);
    }

    /**
     * Get inventory valuation history.
     */
    public function getValuationHistory(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'interval' => 'nullable|in:day,week,month'
        ]);

        $history = $this->inventoryLogService->getValuationHistory($request->all());
        return response()->json($history);
    }

    /**
     * Get low stock alerts history.
     */
    public function getLowStockAlerts(Request $request)
    {
        $alerts = $this->inventoryLogService->getLowStockAlerts($request->all());
        return response()->json($alerts);
    }

    /**
     * Get waste tracking report.
     */
    public function getWasteReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'category' => 'nullable|string',
            'reason' => 'nullable|string'
        ]);

        $report = $this->inventoryLogService->getWasteReport($request->all());
        return response()->json($report);
    }

    /**
     * Export inventory logs to CSV.
     */
    public function exportLogs(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'format' => 'nullable|in:csv,excel,pdf'
        ]);

        $export = $this->inventoryLogService->exportLogs($request->all());
        
        $format = $request->input('format', 'csv');
        $filename = 'inventory-logs-' . now()->format('Y-m-d') . '.' . $format;
        
        return response($export['content'], 200, [
            'Content-Type' => $export['mime_type'],
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    /**
     * Get audit trail for specific inventory changes.
     */
    public function getAuditTrail(Request $request, string $inventoryId)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'action' => 'nullable|string'
        ]);

        $trail = $this->inventoryLogService->getAuditTrail($inventoryId, $request->all());
        return response()->json($trail);
    }

    /**
     * Get inventory discrepancy report.
     */
    public function getDiscrepancyReport(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'threshold' => 'nullable|numeric|min:0'
        ]);

        $report = $this->inventoryLogService->getDiscrepancyReport($request->all());
        return response()->json($report);
    }
}
<?php

namespace Modules\Tenant\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Tenant;
use Modules\Tenant\Helpers\TenantHelper;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$guards): BaseResponse
    {
        $tenant = $this->resolveTenant($request);
        
        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant not found',
                'message' => 'Invalid tenant identifier provided'
            ], 404);
        }
        
        // Check if tenant is active
        if ($tenant->status !== 'active') {
            return response()->json([
                'error' => 'Tenant inactive',
                'message' => 'This tenant account is currently inactive',
                'status' => $tenant->status
            ], 403);
        }
        
        // Set tenant context
        $this->setTenantContext($tenant);
        
        // Add tenant to request
        $request->merge(['tenant' => $tenant]);
        
        return $next($request);
    }
    
    /**
     * Resolve tenant from request
     */
    protected function resolveTenant(Request $request): ?Tenant
    {
        // Try to get tenant from route parameter
        $tenantId = $request->route('tenant');
        if ($tenantId) {
            return Tenant::find($tenantId);
        }
        
        // Try to get tenant from header
        $tenantCode = $request->header('X-Tenant-Code');
        if ($tenantCode) {
            return Tenant::where('code', $tenantCode)->first();
        }
        
        // Try to get tenant from subdomain
        $subdomain = $this->extractSubdomain($request);
        if ($subdomain) {
            return Tenant::where('subdomain', $subdomain)->first();
        }
        
        // Try to get tenant from query parameter
        $tenantParam = $request->query('tenant');
        if ($tenantParam) {
            // Check if it's an ID or code
            if (is_numeric($tenantParam)) {
                return Tenant::find($tenantParam);
            } else {
                return Tenant::where('code', $tenantParam)->first();
            }
        }
        
        return null;
    }
    
    /**
     * Extract subdomain from request
     */
    protected function extractSubdomain(Request $request): ?string
    {
        $host = $request->getHost();
        $parts = explode('.', $host);
        
        // If we have at least 3 parts (subdomain.domain.tld)
        if (count($parts) >= 3) {
            return $parts[0];
        }
        
        return null;
    }
    
    /**
     * Set tenant context for the application
     */
    protected function setTenantContext(Tenant $tenant): void
    {
        // Store tenant in app container
        app()->instance('tenant', $tenant);
        
        // Set tenant ID in config for database queries
        config(['tenant.current_id' => $tenant->id]);
        
        // Set timezone if tenant has one
        if ($tenant->timezone) {
            config(['app.timezone' => $tenant->timezone]);
            date_default_timezone_set($tenant->timezone);
        }
        
        // Set locale if tenant has one
        if ($tenant->language_code) {
            app()->setLocale($tenant->language_code);
        }
        
        // Set currency context
        if ($tenant->currency_code) {
            config(['tenant.currency' => $tenant->currency_code]);
        }
    }
}
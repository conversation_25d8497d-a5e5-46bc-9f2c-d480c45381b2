<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */


    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Sample data for dashboard
        $stats = [
            'todaySales' => 15420.50,
            'totalOrders' => 156,
            'totalCustomers' => 1234,
            'staffCount' => 25,
            'todayExpenses' => 2340.75,
            'pendingOrders' => 12
        ];

        // Sample recent orders data
        $recentOrders = [
            [
                'id' => '#ORD-001',
                'customer' => '<PERSON>',
                'items' => 3,
                'total' => 45.50,
                'status' => 'completed',
                'time' => '2 mins ago'
            ],
            [
                'id' => '#ORD-002',
                'customer' => 'Jane Smith',
                'items' => 2,
                'total' => 32.00,
                'status' => 'preparing',
                'time' => '5 mins ago'
            ],
            [
                'id' => '#ORD-003',
                'customer' => '<PERSON>',
                'items' => 1,
                'total' => 18.75,
                'status' => 'pending',
                'time' => '8 mins ago'
            ],
            [
                'id' => '#ORD-004',
                'customer' => 'Sarah <PERSON>',
                'items' => 4,
                'total' => 67.25,
                'status' => 'completed',
                'time' => '12 mins ago'
            ],
            [
                'id' => '#ORD-005',
                'customer' => 'David Brown',
                'items' => 2,
                'total' => 29.50,
                'status' => 'cancelled',
                'time' => '15 mins ago'
            ]
        ];

        // Sample chart data
        $salesTrendData = [
            'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            'data' => [1200, 1900, 3000, 5000, 2000, 3000, 4500]
        ];

        $categorySalesData = [
            'labels' => ['Main Dishes', 'Appetizers', 'Desserts', 'Beverages', 'Specials'],
            'data' => [35, 25, 15, 20, 5]
        ];

        return view('test-dashboard', compact('stats', 'recentOrders', 'salesTrendData', 'categorySalesData'));
    }

    /**
     * Toggle theme preference
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleTheme(Request $request)
    {
        $theme = $request->input('theme', 'light');
        
        // You can store theme preference in user settings or session
        session(['theme' => $theme]);
        
        return response()->json([
            'success' => true,
            'theme' => $theme,
            'message' => __('messages.theme_updated')
        ]);
    }

    /**
     * Mark notification as read
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markNotificationRead(Request $request)
    {
        $notificationId = $request->input('notification_id');
        
        // Here you would typically update the notification in the database
        // For now, we'll just return a success response
        
        return response()->json([
            'success' => true,
            'message' => __('messages.notification_marked_read')
        ]);
    }

    /**
     * Get dashboard statistics via AJAX
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        // This would typically fetch real data from your models
        $stats = [
            'todaySales' => number_format(15420.50, 2),
            'totalOrders' => 156,
            'totalCustomers' => 1234,
            'staffCount' => 25,
            'todayExpenses' => number_format(2340.75, 2),
            'pendingOrders' => 12,
            'salesGrowth' => '+12.5%',
            'ordersGrowth' => '+8.3%',
            'customersGrowth' => '+15.2%',
            'expensesGrowth' => '-5.1%'
        ];

        return response()->json($stats);
    }

    /**
     * Get chart data via AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChartData(Request $request)
    {
        $chartType = $request->input('type', 'sales');
        
        switch ($chartType) {
            case 'sales':
                return response()->json([
                    'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    'datasets' => [[
                        'label' => __('messages.sales'),
                        'data' => [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                        'borderColor' => '#3b82f6',
                        'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                        'tension' => 0.4
                    ]]
                ]);
                
            case 'category':
                return response()->json([
                    'labels' => [
                        __('messages.main_dishes'),
                        __('messages.appetizers'),
                        __('messages.desserts'),
                        __('messages.beverages'),
                        __('messages.specials')
                    ],
                    'datasets' => [[
                        'data' => [35, 25, 15, 20, 5],
                        'backgroundColor' => [
                            '#ef4444',
                            '#f97316',
                            '#eab308',
                            '#22c55e',
                            '#3b82f6'
                        ]
                    ]]
                ]);
                
            default:
                return response()->json(['error' => 'Invalid chart type'], 400);
        }
    }
}
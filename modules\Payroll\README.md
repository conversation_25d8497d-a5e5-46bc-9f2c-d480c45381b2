# Payroll Module

A comprehensive payroll management system for the EPSIS application that handles employee payroll processing, pay periods, and payslip generation.

## Features

### Pay Period Management
- Create and manage pay periods (weekly, bi-weekly, monthly, quarterly)
- Automatic pay period generation
- Pay period status tracking (draft, processing, completed, cancelled)
- Prevent overlapping pay periods

### Payroll Processing
- Automatic payroll calculation based on timesheets
- Support for hourly and salaried employees
- Overtime calculation (1.5x rate for hours > 40/week)
- Holiday pay calculation (2x rate)
- Multiple deduction types (tax, social security, health insurance, retirement)
- Year-to-date tracking

### Payslip Management
- Individual payslip generation
- Detailed earnings and deductions breakdown
- Payslip approval workflow
- Payment tracking and status updates
- Unique payslip numbering system

### Multi-tenant Support
- Full tenant isolation
- Branch-specific payroll processing
- Tenant-aware data filtering

## Database Structure

### Pay Periods Table
- Manages payroll periods with start/end dates
- Tracks total amounts and employee counts
- Supports different period types

### Payslips Table
- Individual employee payroll records
- Comprehensive earnings and deductions tracking
- Approval and payment workflow
- Year-to-date calculations

## API Endpoints

### Pay Periods
- `GET /api/payroll/pay-periods` - List pay periods
- `POST /api/payroll/pay-periods` - Create pay period
- `GET /api/payroll/pay-periods/{id}` - Get specific pay period
- `POST /api/payroll/pay-periods/{id}/generate` - Generate payroll
- `POST /api/payroll/pay-periods/generate-next` - Auto-generate next period
- `GET /api/payroll/pay-periods/{id}/payslips` - Get period payslips

### Payslips
- `GET /api/payroll/payslips/user/{userId}` - Get user's payslips
- `GET /api/payroll/payslips/{id}` - Get specific payslip
- `PUT /api/payroll/payslips/{id}` - Update payslip
- `POST /api/payroll/payslips/{id}/approve` - Approve payslip
- `POST /api/payroll/payslips/{id}/mark-paid` - Mark as paid

### Statistics
- `GET /api/payroll/statistics` - Get payroll statistics

## Usage Examples

### Creating a Pay Period
```json
POST /api/payroll/pay-periods
{
    "name": "January 2025 - Week 1",
    "period_type": "weekly",
    "start_date": "2025-01-01",
    "end_date": "2025-01-07",
    "notes": "First week of January"
}
```

### Generating Payroll
```json
POST /api/payroll/pay-periods/1/generate
```

### Approving a Payslip
```json
POST /api/payroll/payslips/1/approve
```

## Configuration

The module uses configurable rates for deductions:
- Tax rates: Progressive brackets
- Social Security: 6.2%
- Medicare: 1.45%
- Health Insurance: 5%
- Retirement: 3%

These can be customized in the PayrollHelper class.

## Dependencies

- Laravel Framework
- Existing User, Tenant, Branch models
- Timesheet system for hour tracking
- Multi-tenant architecture

## Installation

1. Run migrations to create payroll tables
2. Register PayrollServiceProvider in bootstrap/providers.php
3. Ensure User model has hourly_rate and salary fields
4. Configure API routes

## Security

- Role-based access control (admin/manager required)
- Tenant isolation enforced
- Payslip approval workflow
- Audit trail for all changes
<?php

namespace Modules\Inventory\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Inventory\Helpers\InventoryHelper;

class InventoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'branch_id' => $this->branch_id,
            'product_id' => $this->product_id,
            'current_stock' => (float) $this->current_stock,
            'available_stock' => (float) $this->available_stock,
            'reserved_stock' => (float) $this->reserved_stock,
            'minimum_stock_level' => (float) $this->minimum_stock_level,
            'maximum_stock_level' => (float) $this->maximum_stock_level,
            'reorder_point' => (float) $this->reorder_point,
            'unit_cost' => (float) $this->unit_cost,
            'total_value' => (float) $this->total_value,
            'location' => $this->location,
            'batch_number' => $this->batch_number,
            'expiry_date' => $this->expiry_date?->format('Y-m-d'),
            'last_counted_at' => $this->last_counted_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            
            // Relationships
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                    'code' => $this->branch->code,
                ];
            }),
            
            'product' => $this->whenLoaded('product', function () {
                return [
                    'id' => $this->product->id,
                    'name' => $this->product->name,
                    'sku' => $this->product->sku,
                    'barcode' => $this->product->barcode,
                    'unit' => $this->product->unit,
                    'category' => $this->product->category,
                    'description' => $this->product->description,
                ];
            }),
            
            // Computed fields
            'stock_status' => [
                'status' => InventoryHelper::getStockStatusText($this->resource),
                'color' => InventoryHelper::getStockStatusColor($this->resource),
                'is_low_stock' => InventoryHelper::isLowStock($this->resource),
                'is_out_of_stock' => InventoryHelper::isOutOfStock($this->resource),
                'is_overstocked' => InventoryHelper::isOverstocked($this->resource),
                'is_expiring_soon' => InventoryHelper::isExpiringSoon($this->resource),
            ],
            
            'analytics' => $this->when($request->get('include_analytics'), function () {
                return [
                    'turnover_rate' => InventoryHelper::calculateTurnoverRate($this->id),
                    'days_of_supply' => InventoryHelper::calculateDaysOfSupply($this->id),
                    'suggested_reorder_quantity' => InventoryHelper::calculateReorderQuantity($this->id),
                    'optimal_reorder_point' => InventoryHelper::calculateOptimalReorderPoint($this->id),
                ];
            }),
            
            'formatted' => [
                'current_stock' => InventoryHelper::formatQuantity($this->current_stock, $this->product->unit ?? 'units'),
                'unit_cost' => InventoryHelper::formatCurrency($this->unit_cost),
                'total_value' => InventoryHelper::formatCurrency($this->total_value),
                'minimum_level' => InventoryHelper::formatQuantity($this->minimum_stock_level, $this->product->unit ?? 'units'),
                'maximum_level' => InventoryHelper::formatQuantity($this->maximum_stock_level, $this->product->unit ?? 'units'),
            ],
            
            // Recent movements (if requested)
            'recent_movements' => $this->whenLoaded('inventoryMovements', function () {
                return $this->inventoryMovements->take(5)->map(function ($movement) {
                    return [
                        'id' => $movement->id,
                        'type' => $movement->type,
                        'quantity' => (float) $movement->quantity,
                        'unit_cost' => (float) $movement->unit_cost,
                        'reason' => $movement->reason,
                        'performed_at' => $movement->performed_at->format('Y-m-d H:i:s'),
                        'user' => $movement->user ? [
                            'id' => $movement->user->id,
                            'name' => $movement->user->name,
                        ] : null,
                    ];
                });
            }),
            
            // Recent logs (if requested)
            'recent_logs' => $this->whenLoaded('inventoryLogs', function () {
                return $this->inventoryLogs->take(5)->map(function ($log) {
                    return [
                        'id' => $log->id,
                        'action' => $log->action,
                        'quantity_before' => (float) $log->quantity_before,
                        'quantity_after' => (float) $log->quantity_after,
                        'quantity_changed' => (float) $log->quantity_changed,
                        'reason' => $log->reason,
                        'notes' => $log->notes,
                        'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                        'user' => $log->user ? [
                            'id' => $log->user->id,
                            'name' => $log->user->name,
                        ] : null,
                    ];
                });
            }),
        ];
    }
    
    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'timestamp' => now()->toISOString(),
                'timezone' => config('app.timezone'),
            ],
        ];
    }
}
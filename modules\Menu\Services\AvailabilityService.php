<?php

namespace Modules\Menu\Services;

use App\Models\MenuItem;
use App\Models\MenuAvailability;
use App\Models\MenuItemBranch;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class AvailabilityService
{
    /**
     * Check if menu item is available at specific date and time.
     */
    public function isAvailableAt(string $menuItemId, string $date, ?string $time = null, ?string $branchId = null): bool
    {
        // Check if menu item exists and is active
        $menuItem = MenuItem::find($menuItemId);
        if (!$menuItem || !$menuItem->is_active) {
            return false;
        }

        // Check branch-specific availability if branch is specified
        if ($branchId && !$this->isBranchAvailable($menuItemId, $branchId)) {
            return false;
        }

        $checkDate = Carbon::parse($date);
        $checkTime = $time ? Carbon::parse($time)->format('H:i:s') : Carbon::now()->format('H:i:s');
        $dayOfWeek = $checkDate->dayOfWeek; // 0 = Sunday, 1 = Monday, etc.

        // Check date-specific availability first
        $dateSpecificAvailability = $this->getDateSpecificAvailability($menuItemId, $checkDate);
        if ($dateSpecificAvailability !== null) {
            return $this->checkTimeAvailability($dateSpecificAvailability, $checkTime);
        }

        // Check regular weekly schedule
        $weeklyAvailability = $this->getWeeklyAvailability($menuItemId, $dayOfWeek);
        if ($weeklyAvailability->isEmpty()) {
            return true; // No restrictions means available
        }

        return $weeklyAvailability->contains(function ($schedule) use ($checkTime) {
            return $this->checkTimeAvailability($schedule, $checkTime);
        });
    }

    /**
     * Check branch-specific availability.
     */
    protected function isBranchAvailable(string $menuItemId, string $branchId): bool
    {
        $branchSettings = MenuItemBranch::where('menu_item_id', $menuItemId)
            ->where('branch_id', $branchId)
            ->first();

        return $branchSettings ? $branchSettings->is_available : true;
    }

    /**
     * Get date-specific availability.
     */
    protected function getDateSpecificAvailability(string $menuItemId, Carbon $date)
    {
        return MenuAvailability::where('menu_item_id', $menuItemId)
            ->where('date_specific', true)
            ->where('start_date', '<=', $date->format('Y-m-d'))
            ->where('end_date', '>=', $date->format('Y-m-d'))
            ->first();
    }

    /**
     * Get weekly availability schedule.
     */
    protected function getWeeklyAvailability(string $menuItemId, int $dayOfWeek): Collection
    {
        return MenuAvailability::where('menu_item_id', $menuItemId)
            ->where('date_specific', false)
            ->where('day_of_week', $dayOfWeek)
            ->get();
    }

    /**
     * Check if current time falls within availability window.
     */
    protected function checkTimeAvailability($schedule, string $checkTime): bool
    {
        if (!$schedule->is_available) {
            return false;
        }

        if (!$schedule->start_time || !$schedule->end_time) {
            return true; // No time restrictions
        }

        $startTime = Carbon::parse($schedule->start_time)->format('H:i:s');
        $endTime = Carbon::parse($schedule->end_time)->format('H:i:s');

        // Handle overnight schedules (e.g., 22:00 - 06:00)
        if ($startTime > $endTime) {
            return $checkTime >= $startTime || $checkTime <= $endTime;
        }

        return $checkTime >= $startTime && $checkTime <= $endTime;
    }

    /**
     * Set availability schedule for menu item.
     */
    public function setAvailabilitySchedule(string $menuItemId, array $scheduleData): MenuAvailability
    {
        return MenuAvailability::create(array_merge($scheduleData, [
            'menu_item_id' => $menuItemId
        ]));
    }

    /**
     * Update availability schedule.
     */
    public function updateAvailabilitySchedule(string $scheduleId, array $scheduleData): MenuAvailability
    {
        $schedule = MenuAvailability::findOrFail($scheduleId);
        $schedule->update($scheduleData);
        return $schedule;
    }

    /**
     * Delete availability schedule.
     */
    public function deleteAvailabilitySchedule(string $scheduleId): bool
    {
        return MenuAvailability::destroy($scheduleId) > 0;
    }

    /**
     * Get all availability schedules for menu item.
     */
    public function getMenuItemSchedules(string $menuItemId): Collection
    {
        return MenuAvailability::where('menu_item_id', $menuItemId)
            ->orderBy('date_specific', 'desc')
            ->orderBy('day_of_week')
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Get available menu items for specific date and time.
     */
    public function getAvailableMenuItems(?string $date = null, ?string $time = null, ?string $branchId = null): Collection
    {
        $checkDate = $date ? Carbon::parse($date) : Carbon::now();
        $checkTime = $time ?: Carbon::now()->format('H:i:s');

        $menuItems = MenuItem::where('is_active', true)->get();

        return $menuItems->filter(function ($menuItem) use ($checkDate, $checkTime, $branchId) {
            return $this->isAvailableAt(
                $menuItem->id, 
                $checkDate->format('Y-m-d'), 
                $checkTime, 
                $branchId
            );
        });
    }

    /**
     * Get menu item availability for the next 7 days.
     */
    public function getWeeklyAvailabilityForecast(string $menuItemId): array
    {
        $forecast = [];
        $startDate = Carbon::now()->startOfDay();

        for ($i = 0; $i < 7; $i++) {
            $date = $startDate->copy()->addDays($i);
            $daySchedules = $this->getDaySchedules($menuItemId, $date);
            
            $forecast[] = [
                'date' => $date->format('Y-m-d'),
                'day_name' => $date->format('l'),
                'is_available' => !$daySchedules->isEmpty(),
                'schedules' => $daySchedules->map(function ($schedule) {
                    return [
                        'start_time' => $schedule->start_time,
                        'end_time' => $schedule->end_time,
                        'is_available' => $schedule->is_available
                    ];
                })->toArray()
            ];
        }

        return $forecast;
    }

    /**
     * Get schedules for specific day.
     */
    protected function getDaySchedules(string $menuItemId, Carbon $date): Collection
    {
        // Check for date-specific schedules first
        $dateSpecific = MenuAvailability::where('menu_item_id', $menuItemId)
            ->where('date_specific', true)
            ->where('start_date', '<=', $date->format('Y-m-d'))
            ->where('end_date', '>=', $date->format('Y-m-d'))
            ->get();

        if ($dateSpecific->isNotEmpty()) {
            return $dateSpecific;
        }

        // Fall back to weekly schedule
        return MenuAvailability::where('menu_item_id', $menuItemId)
            ->where('date_specific', false)
            ->where('day_of_week', $date->dayOfWeek)
            ->get();
    }

    /**
     * Set branch-specific availability.
     */
    public function setBranchAvailability(string $menuItemId, string $branchId, bool $isAvailable): void
    {
        MenuItemBranch::updateOrCreate(
            [
                'menu_item_id' => $menuItemId,
                'branch_id' => $branchId
            ],
            [
                'is_available' => $isAvailable
            ]
        );
    }

    /**
     * Get branch availability status for menu item.
     */
    public function getBranchAvailability(string $menuItemId): Collection
    {
        return MenuItemBranch::where('menu_item_id', $menuItemId)
            ->with('branch:id,name')
            ->get(['branch_id', 'is_available'])
            ->map(function ($item) {
                return [
                    'branch_id' => $item->branch_id,
                    'branch_name' => $item->branch->name ?? 'Unknown',
                    'is_available' => $item->is_available
                ];
            });
    }

    /**
     * Bulk update availability for multiple menu items.
     */
    public function bulkUpdateAvailability(array $menuItemIds, bool $isAvailable): int
    {
        return MenuItem::whereIn('id', $menuItemIds)
            ->update(['is_active' => $isAvailable]);
    }

    /**
     * Get availability conflicts for menu item.
     */
    public function getAvailabilityConflicts(string $menuItemId): array
    {
        $schedules = MenuAvailability::where('menu_item_id', $menuItemId)
            ->orderBy('date_specific', 'desc')
            ->orderBy('day_of_week')
            ->orderBy('start_time')
            ->get();

        $conflicts = [];
        
        // Check for overlapping time slots on same day
        $groupedSchedules = $schedules->groupBy(function ($schedule) {
            return $schedule->date_specific ? 
                $schedule->start_date . '-' . $schedule->end_date : 
                'weekly-' . $schedule->day_of_week;
        });

        foreach ($groupedSchedules as $group => $daySchedules) {
            if ($daySchedules->count() > 1) {
                for ($i = 0; $i < $daySchedules->count() - 1; $i++) {
                    for ($j = $i + 1; $j < $daySchedules->count(); $j++) {
                        $schedule1 = $daySchedules[$i];
                        $schedule2 = $daySchedules[$j];
                        
                        if ($this->hasTimeOverlap($schedule1, $schedule2)) {
                            $conflicts[] = [
                                'type' => 'time_overlap',
                                'schedule_1' => $schedule1->id,
                                'schedule_2' => $schedule2->id,
                                'description' => 'Overlapping time slots detected'
                            ];
                        }
                    }
                }
            }
        }

        return $conflicts;
    }

    /**
     * Check if two schedules have overlapping times.
     */
    protected function hasTimeOverlap($schedule1, $schedule2): bool
    {
        if (!$schedule1->start_time || !$schedule1->end_time || 
            !$schedule2->start_time || !$schedule2->end_time) {
            return false;
        }

        $start1 = Carbon::parse($schedule1->start_time);
        $end1 = Carbon::parse($schedule1->end_time);
        $start2 = Carbon::parse($schedule2->start_time);
        $end2 = Carbon::parse($schedule2->end_time);

        return $start1->lt($end2) && $start2->lt($end1);
    }

    /**
     * Forecast weekly availability for menu item (alias for getWeeklyAvailabilityForecast).
     */
    public function forecastWeeklyAvailability(string $menuItemId, ?string $branchId = null): array
    {
        return $this->getWeeklyAvailabilityForecast($menuItemId);
    }

    /**
     * Check for conflicts in availability schedule.
     */
    public function checkAvailabilityConflicts(string $menuItemId, array $schedule, ?string $branchId = null): array
    {
        // Get existing conflicts
        $existingConflicts = $this->getAvailabilityConflicts($menuItemId);
        
        // Check new schedule against existing schedules
        $newConflicts = [];
        $existingSchedules = MenuAvailability::where('menu_item_id', $menuItemId)->get();
        
        foreach ($schedule as $newScheduleData) {
            foreach ($existingSchedules as $existingSchedule) {
                if ($this->scheduleConflictsWith($newScheduleData, $existingSchedule)) {
                    $newConflicts[] = [
                        'type' => 'schedule_conflict',
                        'existing_schedule_id' => $existingSchedule->id,
                        'new_schedule' => $newScheduleData,
                        'description' => 'New schedule conflicts with existing schedule'
                    ];
                }
            }
        }
        
        return array_merge($existingConflicts, $newConflicts);
    }

    /**
     * Check if new schedule conflicts with existing schedule.
     */
    protected function scheduleConflictsWith(array $newSchedule, $existingSchedule): bool
    {
        // Check if they're for the same day/date
        $sameDay = false;
        
        if (isset($newSchedule['date_specific']) && $newSchedule['date_specific']) {
            // Date-specific schedule
            if ($existingSchedule->date_specific) {
                $sameDay = $newSchedule['start_date'] === $existingSchedule->start_date &&
                          $newSchedule['end_date'] === $existingSchedule->end_date;
            }
        } else {
            // Weekly recurring schedule
            if (!$existingSchedule->date_specific) {
                $sameDay = $newSchedule['day_of_week'] === $existingSchedule->day_of_week;
            }
        }
        
        if (!$sameDay) {
            return false;
        }
        
        // Check time overlap
        if (!isset($newSchedule['start_time']) || !isset($newSchedule['end_time']) ||
            !$existingSchedule->start_time || !$existingSchedule->end_time) {
            return false;
        }
        
        $newStart = Carbon::parse($newSchedule['start_time']);
        $newEnd = Carbon::parse($newSchedule['end_time']);
        $existingStart = Carbon::parse($existingSchedule->start_time);
        $existingEnd = Carbon::parse($existingSchedule->end_time);
        
        return $newStart->lt($existingEnd) && $existingStart->lt($newEnd);
    }

    /**
     * Get availability statistics.
     */
    public function getAvailabilityStatistics(): array
    {
        $totalItems = MenuItem::count();
        $activeItems = MenuItem::where('is_active', true)->count();
        $itemsWithSchedules = MenuAvailability::distinct('menu_item_id')->count('menu_item_id');
        $totalSchedules = MenuAvailability::count();
        $dateSpecificSchedules = MenuAvailability::where('date_specific', true)->count();
        
        return [
            'total_menu_items' => $totalItems,
            'active_menu_items' => $activeItems,
            'items_with_schedules' => $itemsWithSchedules,
            'total_schedules' => $totalSchedules,
            'date_specific_schedules' => $dateSpecificSchedules,
            'weekly_schedules' => $totalSchedules - $dateSpecificSchedules,
            'availability_coverage' => $totalItems > 0 ? round(($itemsWithSchedules / $totalItems) * 100, 2) : 0
        ];
    }
}
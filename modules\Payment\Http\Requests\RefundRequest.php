<?php

namespace Modules\Payment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Payment;

class RefundRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Implement your authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'amount' => 'required|numeric|min:0.01|max:999999.99',
            'refund_type' => [
                'required',
                'string',
                Rule::in(['full', 'partial', 'processing_fee', 'adjustment'])
            ],
            'refund_reason' => [
                'required',
                'string',
                Rule::in([
                    'customer_request',
                    'order_cancelled',
                    'product_defective',
                    'service_not_provided',
                    'duplicate_payment',
                    'processing_error',
                    'fraud_prevention',
                    'policy_violation',
                    'system_error',
                    'merchant_error',
                    'chargeback',
                    'other'
                ])
            ],
            'reason_description' => 'sometimes|string|max:1000',
            'reference_number' => 'sometimes|string|max:100',
            'notes' => 'sometimes|string|max:1000',
            'metadata' => 'sometimes|array',
            
            // Refund method preferences
            'refund_method' => [
                'sometimes',
                'string',
                Rule::in(['original_method', 'cash', 'store_credit', 'bank_transfer', 'check'])
            ],
            
            // Alternative refund details
            'refund_account_number' => 'sometimes|string|max:50',
            'refund_routing_number' => 'sometimes|string|max:20',
            'refund_wallet_address' => 'sometimes|string|max:255',
            
            // Processing options
            'expedited_processing' => 'sometimes|boolean',
            'notify_customer' => 'sometimes|boolean',
            'send_receipt' => 'sometimes|boolean',
            
            // Authorization
            'authorized_by' => 'sometimes|uuid|exists:users,id',
            'authorization_code' => 'sometimes|string|max:50',
            
            // Customer communication
            'customer_email' => 'sometimes|email|max:255',
            'customer_phone' => 'sometimes|string|max:20',
            'communication_preference' => 'sometimes|string|in:email,sms,phone,none'
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'Refund amount is required.',
            'amount.min' => 'Refund amount must be at least $0.01.',
            'amount.max' => 'Refund amount cannot exceed $999,999.99.',
            'refund_type.required' => 'Refund type is required.',
            'refund_type.in' => 'Invalid refund type selected.',
            'refund_reason.required' => 'Refund reason is required.',
            'refund_reason.in' => 'Invalid refund reason selected.',
            'reason_description.max' => 'Reason description cannot exceed 1000 characters.',
            'reference_number.max' => 'Reference number cannot exceed 100 characters.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'refund_method.in' => 'Invalid refund method selected.',
            'refund_account_number.max' => 'Account number cannot exceed 50 characters.',
            'refund_routing_number.max' => 'Routing number cannot exceed 20 characters.',
            'refund_wallet_address.max' => 'Wallet address cannot exceed 255 characters.',
            'authorized_by.exists' => 'Authorizing user does not exist.',
            'authorization_code.max' => 'Authorization code cannot exceed 50 characters.',
            'customer_email.email' => 'Customer email must be a valid email address.',
            'customer_phone.max' => 'Customer phone cannot exceed 20 characters.',
            'communication_preference.in' => 'Invalid communication preference selected.'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Get the payment from route parameter
            $payment = $this->route('payment');
            
            if ($payment) {
                // Validate refund eligibility
                $this->validateRefundEligibility($validator, $payment);
                
                // Validate refund amount
                $this->validateRefundAmount($validator, $payment);
                
                // Validate refund method compatibility
                $this->validateRefundMethod($validator, $payment);
                
                // Validate authorization requirements
                $this->validateAuthorizationRequirements($validator, $payment);
            }
        });
    }

    /**
     * Validate refund eligibility
     */
    protected function validateRefundEligibility($validator, Payment $payment): void
    {
        // Check if payment is completed
        if ($payment->status !== 'completed') {
            $validator->errors()->add('payment', 'Only completed payments can be refunded.');
            return;
        }
        
        // Check if payment is already fully refunded
        if ($payment->status === 'refunded') {
            $validator->errors()->add('payment', 'Payment has already been fully refunded.');
            return;
        }
        
        // Check refund time limit
        $refundTimeLimit = config('payment.refund_time_limit_days', 30);
        if ($refundTimeLimit && $payment->created_at->diffInDays(now()) > $refundTimeLimit) {
            $validator->errors()->add('payment', 'Refund time limit of ' . $refundTimeLimit . ' days has been exceeded.');
        }
        
        // Check if payment method supports refunds
        $paymentMethod = $payment->paymentMethod;
        if ($paymentMethod) {
            $nonRefundableMethods = ['gift_card', 'store_credit'];
            if (in_array($paymentMethod->type, $nonRefundableMethods)) {
                $validator->errors()->add('payment', 'Refunds are not supported for ' . $paymentMethod->type . ' payments.');
            }
        }
    }

    /**
     * Validate refund amount
     */
    protected function validateRefundAmount($validator, Payment $payment): void
    {
        $refundAmount = $this->amount;
        
        // Check if refund amount exceeds payment amount
        if ($refundAmount > $payment->amount) {
            $validator->errors()->add('amount', 'Refund amount cannot exceed the original payment amount of $' . number_format($payment->amount, 2) . '.');
            return;
        }
        
        // Calculate existing refunds
        $existingRefunds = Payment::where('parent_payment_id', $payment->id)
            ->where('status', 'completed')
            ->sum('amount'); // This will be negative for refunds
        
        $totalRefunded = abs($existingRefunds);
        $remainingRefundable = $payment->amount - $totalRefunded;
        
        if ($refundAmount > $remainingRefundable) {
            $validator->errors()->add('amount', 'Refund amount cannot exceed the remaining refundable amount of $' . number_format($remainingRefundable, 2) . '.');
        }
        
        // Validate full refund type
        if ($this->refund_type === 'full' && abs($refundAmount - $remainingRefundable) > 0.01) {
            $validator->errors()->add('amount', 'Full refund amount must equal the remaining refundable amount of $' . number_format($remainingRefundable, 2) . '.');
        }
    }

    /**
     * Validate refund method compatibility
     */
    protected function validateRefundMethod($validator, Payment $payment): void
    {
        $refundMethod = $this->refund_method ?? 'original_method';
        $originalMethod = $payment->paymentMethod->type;
        
        // Define compatible refund methods for each payment type
        $compatibleMethods = [
            'cash' => ['original_method', 'cash'],
            'card' => ['original_method', 'bank_transfer', 'check'],
            'digital_wallet' => ['original_method', 'bank_transfer'],
            'bank_transfer' => ['original_method', 'bank_transfer'],
            'crypto' => ['original_method'],
            'check' => ['original_method', 'cash', 'bank_transfer']
        ];
        
        $allowedMethods = $compatibleMethods[$originalMethod] ?? ['original_method'];
        
        if (!in_array($refundMethod, $allowedMethods)) {
            $validator->errors()->add('refund_method', 'Selected refund method is not compatible with the original payment method.');
        }
        
        // Validate required fields for specific refund methods
        if ($refundMethod === 'bank_transfer') {
            if (!$this->has('refund_account_number')) {
                $validator->errors()->add('refund_account_number', 'Account number is required for bank transfer refunds.');
            }
            if (!$this->has('refund_routing_number')) {
                $validator->errors()->add('refund_routing_number', 'Routing number is required for bank transfer refunds.');
            }
        }
        
        if ($originalMethod === 'crypto' && $refundMethod === 'original_method') {
            if (!$this->has('refund_wallet_address')) {
                $validator->errors()->add('refund_wallet_address', 'Wallet address is required for cryptocurrency refunds.');
            }
        }
    }

    /**
     * Validate authorization requirements
     */
    protected function validateAuthorizationRequirements($validator, Payment $payment): void
    {
        $refundAmount = $this->amount;
        
        // Check if refund amount requires authorization
        $authorizationThreshold = config('payment.refund_authorization_threshold', 500);
        
        if ($refundAmount >= $authorizationThreshold) {
            if (!$this->has('authorized_by')) {
                $validator->errors()->add('authorized_by', 'Refunds of $' . number_format($authorizationThreshold, 2) . ' or more require authorization.');
            } else {
                // Validate authorizer permissions
                $authorizer = \App\Models\User::find($this->authorized_by);
                if ($authorizer && !$authorizer->can('authorize_refunds')) {
                    $validator->errors()->add('authorized_by', 'Selected user does not have permission to authorize refunds.');
                }
            }
        }
        
        // Check for suspicious refund patterns
        $recentRefunds = Payment::where('processed_by', auth()->id())
            ->where('amount', '<', 0)
            ->where('created_at', '>=', now()->subHours(24))
            ->count();
        
        if ($recentRefunds >= 5 && !$this->has('authorization_code')) {
            $validator->errors()->add('authorization_code', 'Multiple refunds in 24 hours require an authorization code.');
        }
    }

    /**
     * Get validated data with defaults
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();
        
        // Set default values
        $validated['refund_method'] = $validated['refund_method'] ?? 'original_method';
        $validated['notify_customer'] = $validated['notify_customer'] ?? true;
        $validated['send_receipt'] = $validated['send_receipt'] ?? true;
        $validated['communication_preference'] = $validated['communication_preference'] ?? 'email';
        $validated['metadata'] = $validated['metadata'] ?? [];
        
        // Add system metadata
        $validated['metadata']['refund_initiated_via'] = 'api';
        $validated['metadata']['request_ip'] = $this->ip();
        $validated['metadata']['user_agent'] = $this->userAgent();
        $validated['metadata']['timestamp'] = now()->toISOString();
        $validated['metadata']['initiated_by'] = auth()->id();
        
        return $validated;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure amount is properly formatted
        if ($this->has('amount')) {
            $this->merge([
                'amount' => round((float) $this->amount, 2)
            ]);
        }
        
        // Clean phone number
        if ($this->has('customer_phone')) {
            $this->merge([
                'customer_phone' => preg_replace('/[^0-9+\-\(\)\s]/', '', $this->customer_phone)
            ]);
        }
        
        // Ensure boolean fields are properly cast
        foreach (['expedited_processing', 'notify_customer', 'send_receipt'] as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->$field, FILTER_VALIDATE_BOOLEAN)
                ]);
            }
        }
        
        // Trim string fields
        foreach (['reason_description', 'notes', 'reference_number'] as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => trim($this->$field)
                ]);
            }
        }
    }

    /**
     * Get the refund reason display name
     */
    public function getRefundReasonDisplayName(): string
    {
        $reasons = [
            'customer_request' => 'Customer Request',
            'order_cancelled' => 'Order Cancelled',
            'product_defective' => 'Product Defective',
            'service_not_provided' => 'Service Not Provided',
            'duplicate_payment' => 'Duplicate Payment',
            'processing_error' => 'Processing Error',
            'fraud_prevention' => 'Fraud Prevention',
            'policy_violation' => 'Policy Violation',
            'system_error' => 'System Error',
            'merchant_error' => 'Merchant Error',
            'chargeback' => 'Chargeback',
            'other' => 'Other'
        ];
        
        return $reasons[$this->refund_reason] ?? 'Unknown';
    }
}
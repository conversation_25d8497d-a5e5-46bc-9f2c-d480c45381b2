<?php

use Illuminate\Support\Facades\Route;
use Modules\Settings\Http\Controllers\SettingController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->group(function () {
    Route::prefix('settings')->group(function () {
        // General settings endpoints
        Route::get('/', [SettingController::class, 'index']);
        Route::get('/{key}', [SettingController::class, 'show']);
        Route::post('/', [SettingController::class, 'store']);
        Route::put('/{key}', [SettingController::class, 'update']);
        Route::delete('/{key}', [SettingController::class, 'destroy']);
        
        // Specialized category endpoints
        Route::get('/categories/available', [SettingController::class, 'getAvailableCategories']);
        Route::post('/categories/{category}/bulk-update', [SettingController::class, 'bulkUpdateCategory']);
        
        // KOT settings
        Route::get('/kot', [SettingController::class, 'getKotSettings']);
        Route::post('/kot', [SettingController::class, 'setKotSettings']);
        Route::delete('/kot/{key}', [SettingController::class, 'deleteKotSetting']);
        
        // Language settings
        Route::get('/language', [SettingController::class, 'getLanguageSettings']);
        Route::post('/language', [SettingController::class, 'setLanguageSettings']);
        Route::delete('/language/{key}', [SettingController::class, 'deleteLanguageSetting']);
        
        // Notification settings
        Route::get('/notification', [SettingController::class, 'getNotificationSettings']);
        Route::post('/notification', [SettingController::class, 'setNotificationSettings']);
        Route::delete('/notification/{key}', [SettingController::class, 'deleteNotificationSetting']);
        
        // Pusher settings
        Route::get('/pusher', [SettingController::class, 'getPusherSettings']);
        Route::post('/pusher', [SettingController::class, 'setPusherSettings']);
        Route::delete('/pusher/{key}', [SettingController::class, 'deletePusherSetting']);
        
        // Receipt settings
        Route::get('/receipt', [SettingController::class, 'getReceiptSettings']);
        Route::post('/receipt', [SettingController::class, 'setReceiptSettings']);
        Route::delete('/receipt/{key}', [SettingController::class, 'deleteReceiptSetting']);
        
        // Reservation settings
        Route::get('/reservation', [SettingController::class, 'getReservationSettings']);
        Route::post('/reservation', [SettingController::class, 'setReservationSettings']);
        Route::delete('/reservation/{key}', [SettingController::class, 'deleteReservationSetting']);
        
        // Branch delivery settings
        Route::get('/branch-delivery', [SettingController::class, 'getBranchDeliverySettings']);
        Route::post('/branch-delivery', [SettingController::class, 'setBranchDeliverySettings']);
        Route::delete('/branch-delivery/{key}', [SettingController::class, 'deleteBranchDeliverySetting']);
        
        // Email settings
        Route::get('/email', [SettingController::class, 'getEmailSettings']);
        Route::post('/email', [SettingController::class, 'setEmailSettings']);
        Route::delete('/email/{key}', [SettingController::class, 'deleteEmailSetting']);
        
        // File storage settings
        Route::get('/file-storage', [SettingController::class, 'getFileStorageSettings']);
        Route::post('/file-storage', [SettingController::class, 'setFileStorageSettings']);
        Route::delete('/file-storage/{key}', [SettingController::class, 'deleteFileStorageSetting']);
        
        // Front FAQ settings
        Route::get('/front-faq', [SettingController::class, 'getFrontFaqSettings']);
        Route::post('/front-faq', [SettingController::class, 'setFrontFaqSettings']);
        Route::delete('/front-faq/{key}', [SettingController::class, 'deleteFrontFaqSetting']);
        
        // Front review settings
        Route::get('/front-review', [SettingController::class, 'getFrontReviewSettings']);
        Route::post('/front-review', [SettingController::class, 'setFrontReviewSettings']);
        Route::delete('/front-review/{key}', [SettingController::class, 'deleteFrontReviewSetting']);
    });
});

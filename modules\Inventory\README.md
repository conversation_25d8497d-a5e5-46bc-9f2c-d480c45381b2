# Inventory Module

This module manages all inventory-related functionalities within the EPSIS system. It provides a comprehensive set of features for tracking stock levels, managing suppliers, handling purchase orders, and generating alerts and reports.

## Features

- **Stock Management**: Track current stock levels, minimum/maximum stock levels, reorder points, and unit costs for all inventory items across different branches.
- **Supplier Management**: Maintain a database of suppliers, including contact information, payment terms, and performance metrics.
- **Purchase Order Management**: Create, track, and manage purchase orders from creation to receipt, including detailed item information, financial summaries, and status updates.
- **Inventory Movements & Logging**: Record all inventory movements (additions, subtractions, transfers) and maintain a detailed log of all inventory-related actions for auditing and analysis.
- **Alerts & Notifications**: Automatically trigger alerts for low stock, out-of-stock, overstocked, and expiring/expired items, with notifications sent via email and database.
- **Inventory Analytics**: Generate reports and insights on inventory turnover, days of supply, ABC classification, and valuation.
- **Security & Access Control**: Implement robust middleware for authentication, general module access, specific permissions, and branch-specific access for inventory operations.
- **API Endpoints**: Expose a rich set of RESTful API endpoints for seamless integration with other systems and front-end applications.
- **Console Commands**: Provide console commands for background processing of inventory alerts and other scheduled tasks.

## Directory Structure

- `Console/Commands`: Contains Artisan console commands, such as `ProcessInventoryAlertsCommand`.
- `Helpers`: Houses helper classes like `InventoryHelper` for common inventory-related calculations and utility functions.
- `Http/Controllers`: Defines the API controllers for managing inventory items, suppliers, purchase orders, and inventory logs.
- `Http/Middleware`: Includes middleware for access control and security, such as `InventoryAccessMiddleware`.
- `Http/Requests`: Contains form request classes for validating incoming API requests.
- `Http/Resources`: Provides API resource classes (`InventoryResource`, `SupplierResource`, `PurchaseOrderResource`) for transforming models into JSON responses.
- `Jobs`: Holds background jobs, like `ProcessInventoryAlerts`, for asynchronous processing.
- `Listeners`: Contains event listeners, such as `InventoryUpdatedListener`, to react to inventory-related events.
- `Notifications`: Defines notification classes (`LowStockAlert`, `PurchaseOrderStatusChanged`) for various alerts and updates.
- `Providers`: Includes the `InventoryServiceProvider` for registering module services, commands, and middleware.
- `Services`: Contains service classes (`InventoryService`, `SupplierService`, `PurchaseOrderService`, `InventoryLogService`) that encapsulate business logic.
- `config`: Stores module-specific configuration files, e.g., `inventory.php`.
- `routes`: Defines API routes for the module.

## API Endpoints

The module exposes a comprehensive set of API endpoints under the `/api/inventory` prefix. Key endpoints include:

- `/api/inventory/items`: CRUD operations for inventory items, including stock updates, movements, and analytics.
- `/api/inventory/suppliers`: CRUD operations for supplier management.
- `/api/inventory/purchase-orders`: CRUD operations for purchase orders, including item receipt and status updates.
- `/api/inventory/logs`: Retrieve inventory log and movement history.

For detailed API documentation, please refer to the Postman collection or the route list (`php artisan route:list --name=inventory`).

## Configuration

The `inventory.php` configuration file allows customization of various module settings, including alert thresholds and notification preferences.

## Usage

To interact with the Inventory module, use the provided API endpoints. Ensure proper authentication and authorization as defined by the `InventoryAccessMiddleware` and associated permissions.
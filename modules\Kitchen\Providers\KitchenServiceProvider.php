<?php

namespace Modules\Kitchen\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Kitchen\Services\KitchenService;

class KitchenServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Kitchen Service as singleton
        $this->app->singleton(KitchenService::class, function ($app) {
            return new KitchenService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views if they exist
        $this->loadViews();
        
        // Load migrations
        $this->loadMigrations();
        
        // Publish and merge config if needed
        $this->publishConfig();
    }

    /**
     * Load module routes
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        Route::middleware('api')
            ->prefix('api')
            ->group(__DIR__ . '/../routes/api.php');

        // Load web routes if they exist
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            Route::middleware('web')
                ->group(__DIR__ . '/../routes/web.php');
        }
    }

    /**
     * Load module views
     */
    protected function loadViews(): void
    {
        $viewPath = __DIR__ . '/../views';
        
        if (is_dir($viewPath)) {
            $this->loadViewsFrom($viewPath, 'kitchen');
        }
    }

    /**
     * Load module migrations
     */
    protected function loadMigrations(): void
    {
        $migrationPaths = [
            database_path('migrations'),
        ];
        
        $this->loadMigrationsFrom($migrationPaths);
    }

    /**
     * Publish and merge configuration
     */
    protected function publishConfig(): void
    {
        $configPath = __DIR__ . '/../Config/kitchen.php';
        
        if (file_exists($configPath)) {
            // Publish config file
            $this->publishes([
                $configPath => config_path('kitchen.php'),
            ], 'kitchen-config');
            
            // Merge config
            $this->mergeConfigFrom($configPath, 'kitchen');
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array<int, string>
     */
    public function provides(): array
    {
        return [
            KitchenService::class,
        ];
    }
}
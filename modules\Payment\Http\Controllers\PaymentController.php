<?php

namespace Modules\Payment\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Payment\Services\PaymentService;
use Modules\Payment\Services\RefundService;
use Modules\Payment\Http\Requests\ProcessPaymentRequest;
use Modules\Payment\Http\Requests\RefundRequest;
use App\Models\Payment;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class PaymentController extends Controller
{
    protected PaymentService $paymentService;
    protected RefundService $refundService;

    public function __construct(
        PaymentService $paymentService,
        RefundService $refundService
    ) {
        $this->paymentService = $paymentService;
        $this->refundService = $refundService;
    }

    /**
     * Get all active payment methods
     */
    public function getPaymentMethods(): JsonResponse
    {
        try {
            $paymentMethods = PaymentMethod::where('is_active', true)
                ->select(['id', 'name', 'type', 'provider', 'is_active'])
                ->get();

            return response()->json([
                'success' => true,
                'data' => $paymentMethods
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment methods',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process a new payment
     */
    public function processPayment(ProcessPaymentRequest $request): JsonResponse
    {
        try {
            $payment = $this->paymentService->processPayment($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'data' => $payment->load(['paymentMethod', 'transaction', 'processedBy'])
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get payment details
     */
    public function show(Payment $payment): JsonResponse
    {
        try {
            $payment->load(['paymentMethod', 'transaction', 'processedBy']);

            return response()->json([
                'success' => true,
                'data' => $payment
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(Payment $payment): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $payment->id,
                    'payment_number' => $payment->payment_number,
                    'status' => $payment->status,
                    'amount' => $payment->amount,
                    'processed_at' => $payment->processed_at
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update payment
     */
    public function update(Request $request, Payment $payment): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'status' => 'sometimes|in:pending,processing,completed,failed,cancelled,refunded',
                'notes' => 'sometimes|string|max:1000',
                'reference_number' => 'sometimes|string|max:100'
            ]);

            $payment->update($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Payment updated successfully',
                'data' => $payment->fresh()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Cancel payment
     */
    public function cancel(Payment $payment): JsonResponse
    {
        try {
            $cancelledPayment = $this->paymentService->cancelPayment($payment);

            return response()->json([
                'success' => true,
                'message' => 'Payment cancelled successfully',
                'data' => $cancelledPayment
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel payment',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * List payments with filtering
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Payment::with(['paymentMethod', 'transaction', 'processedBy']);

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('payment_method_id')) {
                $query->where('payment_method_uuid', $request->payment_method_id);
            }

            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            if ($request->has('search')) {
                $query->where(function ($q) use ($request) {
                    $q->where('payment_number', 'like', '%' . $request->search . '%')
                      ->orWhere('reference_number', 'like', '%' . $request->search . '%');
                });
            }

            $perPage = $request->get('per_page', 10);
            $payments = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $payments->items(),
                'meta' => [
                    'current_page' => $payments->currentPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                    'last_page' => $payments->lastPage()
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payments',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process refund
     */
    public function processRefund(RefundRequest $request, Payment $payment): JsonResponse
    {
        try {
            $refund = $this->refundService->processRefund($payment, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Refund processed successfully',
                'data' => $refund
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Refund processing failed',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get payment analytics
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $analytics = $this->paymentService->getPaymentAnalytics($request->all());

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reconcile payments
     */
    public function reconcile(Request $request): JsonResponse
    {
        try {
            $result = $this->paymentService->reconcilePayments($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Payment reconciliation completed',
                'data' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment reconciliation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\Table;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class TableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all branches
        $branches = Branch::all();

        foreach ($branches as $branch) {
            // Check if branch already has tables
            $existingTablesCount = Table::where('branch_id', $branch->id)->count();
            
            if ($existingTablesCount === 0) {
                // Create default tables for the branch
                $this->createDefaultTables($branch);
            } else {
                // Update existing tables to ensure they have QR codes
                $this->updateTablesWithQRCodes($branch);
            }
        }
    }

    /**
     * Create default tables for a branch
     */
    private function createDefaultTables(Branch $branch): void
    {
        $defaultTables = [
            ['table_number' => '1', 'table_name' => 'Table 1', 'seating_capacity' => 4, 'section' => 'Indoor'],
            ['table_number' => '2', 'table_name' => 'Table 2', 'seating_capacity' => 4, 'section' => 'Indoor'],
            ['table_number' => '3', 'table_name' => 'Table 3', 'seating_capacity' => 6, 'section' => 'Indoor'],
            ['table_number' => '4', 'table_name' => 'Table 4', 'seating_capacity' => 2, 'section' => 'Indoor'],
            ['table_number' => '5', 'table_name' => 'Table 5', 'seating_capacity' => 8, 'section' => 'Indoor'],
            ['table_number' => 'VIP1', 'table_name' => 'VIP Table 1', 'seating_capacity' => 6, 'section' => 'VIP'],
            ['table_number' => 'VIP2', 'table_name' => 'VIP Table 2', 'seating_capacity' => 8, 'section' => 'VIP'],
            ['table_number' => 'OUT1', 'table_name' => 'Outdoor Table 1', 'seating_capacity' => 4, 'section' => 'Outdoor'],
            ['table_number' => 'OUT2', 'table_name' => 'Outdoor Table 2', 'seating_capacity' => 6, 'section' => 'Outdoor'],
            ['table_number' => 'BAR1', 'table_name' => 'Bar Table 1', 'seating_capacity' => 2, 'section' => 'Bar'],
        ];

        foreach ($defaultTables as $tableData) {
            $table = Table::create([
                'branch_id' => $branch->id,
                'table_number' => $tableData['table_number'],
                'table_name' => $tableData['table_name'],
                'seating_capacity' => $tableData['seating_capacity'],
                'section' => $tableData['section'],
                'status' => 'available',
                'qr_code' => $this->generateQRCode($branch, $tableData['table_number']),
                'position_coordinates' => $this->getDefaultPosition($tableData['table_number']),
                'notes' => 'Default table created by seeder',
                'is_active' => true,
            ]);
        }
    }

    /**
     * Update existing tables to ensure they have QR codes
     */
    private function updateTablesWithQRCodes(Branch $branch): void
    {
        $tables = Table::where('branch_id', $branch->id)
                      ->where(function($query) {
                          $query->whereNull('qr_code')
                                ->orWhere('qr_code', '');
                      })
                      ->get();

        foreach ($tables as $table) {
            $table->update([
                'qr_code' => $this->generateQRCode($branch, $table->table_number)
            ]);
        }
    }

    /**
     * Generate QR code data for a table
     */
    private function generateQRCode(Branch $branch, string $tableNumber): string
    {
        // Generate a unique QR code that includes branch and table information
        $qrData = [
            'branch_id' => $branch->id,
            'table_number' => $tableNumber,
            'url' => url("/menu/{$branch->id}/{$tableNumber}"),
            'timestamp' => now()->timestamp,
            'hash' => Str::random(8)
        ];
        
        return base64_encode(json_encode($qrData));
    }

    /**
     * Get default position coordinates for a table
     */
    private function getDefaultPosition(string $tableNumber): array
    {
        // Simple grid layout for default positioning
        $positions = [
            '1' => ['x' => 100, 'y' => 100],
            '2' => ['x' => 300, 'y' => 100],
            '3' => ['x' => 500, 'y' => 100],
            '4' => ['x' => 100, 'y' => 300],
            '5' => ['x' => 300, 'y' => 300],
            'VIP1' => ['x' => 500, 'y' => 300],
            'VIP2' => ['x' => 700, 'y' => 300],
            'OUT1' => ['x' => 100, 'y' => 500],
            'OUT2' => ['x' => 300, 'y' => 500],
            'BAR1' => ['x' => 500, 'y' => 500],
        ];

        return $positions[$tableNumber] ?? ['x' => 0, 'y' => 0];
    }
}
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    public function run()
    {
        if (class_exists(\Database\Factories\SubscriptionPlanFactory::class)) {
            SubscriptionPlan::factory()->count(3)->create();
        } else {
            SubscriptionPlan::create([
                'name' => 'Basic Plan',
                'code' => 'BASIC',
                'description' => 'Basic plan for small businesses',
                'price' => 9.99,
                'billing_cycle' => 'monthly',
                'max_branches' => 1,
                'max_users' => 1,
                'max_products' => 50,
                'max_orders_per_month' => 500,
            ]);
        }
    }
}
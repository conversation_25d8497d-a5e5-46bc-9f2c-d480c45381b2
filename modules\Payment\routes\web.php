<?php

use Illuminate\Support\Facades\Route;
use Modules\Payment\Http\Controllers\PaymentController;
use Modules\Payment\Http\Controllers\TransactionController;

/*
|--------------------------------------------------------------------------
| Payment Web Routes
|--------------------------------------------------------------------------
|
| Here are the web routes for the Payment module. These routes provide
| web interfaces for payment management and can be used by other modules.
|
*/

// Payment Dashboard Routes
Route::prefix('payments')->middleware(['web', 'auth'])->group(function () {
    
    // Payment Dashboard
    Route::get('/', function () {
        return view('payment::dashboard');
    })->name('payments.dashboard');
    
    // Payment Methods Management
    Route::get('methods', function () {
        return view('payment::methods.index');
    })->name('payments.methods.index');
    
    // Payment Processing Interface
    Route::get('process', function () {
        return view('payment::process');
    })->name('payments.process');
    
    // Payment History
    Route::get('history', function () {
        return view('payment::history');
    })->name('payments.history');
    
    // Payment Analytics
    Route::get('analytics', function () {
        return view('payment::analytics');
    })->name('payments.analytics');
    
    // Payment Settings
    Route::get('settings', function () {
        return view('payment::settings');
    })->name('payments.settings');
    
    // Individual Payment Details
    Route::get('{payment}', function ($payment) {
        return view('payment::show', compact('payment'));
    })->name('payments.show');
    
    // Payment Receipt
    Route::get('{payment}/receipt', function ($payment) {
        return view('payment::receipt', compact('payment'));
    })->name('payments.receipt');
    
    // Print Receipt
    Route::get('{payment}/receipt/print', function ($payment) {
        return view('payment::receipt-print', compact('payment'));
    })->name('payments.receipt.print');
});

// Transaction Management Routes
Route::prefix('transactions')->middleware(['web', 'auth'])->group(function () {
    
    // Transaction Dashboard
    Route::get('/', function () {
        return view('payment::transactions.index');
    })->name('transactions.index');
    
    // Create Transaction Form
    Route::get('create', function () {
        return view('payment::transactions.create');
    })->name('transactions.create');
    
    // Transaction Details
    Route::get('{transaction}', function ($transaction) {
        return view('payment::transactions.show', compact('transaction'));
    })->name('transactions.show');
    
    // Edit Transaction
    Route::get('{transaction}/edit', function ($transaction) {
        return view('payment::transactions.edit', compact('transaction'));
    })->name('transactions.edit');
    
    // Transaction Analytics
    Route::get('analytics/overview', function () {
        return view('payment::transactions.analytics');
    })->name('transactions.analytics');
    
    // Transaction Reports
    Route::get('reports', function () {
        return view('payment::transactions.reports');
    })->name('transactions.reports');
});

// Payment Widget Routes (for embedding in other modules)
Route::prefix('payment-widgets')->middleware(['web', 'auth'])->group(function () {
    
    // Payment Form Widget
    Route::get('payment-form', function () {
        return view('payment::widgets.payment-form');
    })->name('payment-widgets.payment-form');
    
    // Transaction Summary Widget
    Route::get('transaction-summary', function () {
        return view('payment::widgets.transaction-summary');
    })->name('payment-widgets.transaction-summary');
    
    // Payment Status Widget
    Route::get('payment-status/{payment}', function ($payment) {
        return view('payment::widgets.payment-status', compact('payment'));
    })->name('payment-widgets.payment-status');
    
    // Quick Payment Widget
    Route::get('quick-payment', function () {
        return view('payment::widgets.quick-payment');
    })->name('payment-widgets.quick-payment');
    
    // Payment Analytics Widget
    Route::get('analytics-widget', function () {
        return view('payment::widgets.analytics');
    })->name('payment-widgets.analytics');
});

// Public Payment Routes (for customer-facing interfaces)
Route::prefix('pay')->group(function () {
    
    // Public Payment Form
    Route::get('{token}', function ($token) {
        return view('payment::public.payment-form', compact('token'));
    })->name('public.payment.form');
    
    // Payment Success Page
    Route::get('success/{payment}', function ($payment) {
        return view('payment::public.success', compact('payment'));
    })->name('public.payment.success');
    
    // Payment Failed Page
    Route::get('failed/{payment}', function ($payment) {
        return view('payment::public.failed', compact('payment'));
    })->name('public.payment.failed');
    
    // Payment Cancelled Page
    Route::get('cancelled/{payment}', function ($payment) {
        return view('payment::public.cancelled', compact('payment'));
    })->name('public.payment.cancelled');
    
    // Payment Receipt (Public)
    Route::get('receipt/{payment}', function ($payment) {
        return view('payment::public.receipt', compact('payment'));
    })->name('public.payment.receipt');
});

// Payment Documentation Routes
Route::prefix('payment-docs')->middleware(['web', 'auth'])->group(function () {
    
    // Payment Module Documentation
    Route::get('/', function () {
        return view('payment::docs.index');
    })->name('payment-docs.index');
    
    // API Documentation
    Route::get('api', function () {
        return view('payment::docs.api');
    })->name('payment-docs.api');
    
    // Integration Guide
    Route::get('integration', function () {
        return view('payment::docs.integration');
    })->name('payment-docs.integration');
    
    // Configuration Guide
    Route::get('configuration', function () {
        return view('payment::docs.configuration');
    })->name('payment-docs.configuration');
    
    // Troubleshooting Guide
    Route::get('troubleshooting', function () {
        return view('payment::docs.troubleshooting');
    })->name('payment-docs.troubleshooting');
});

<?php

namespace Modules\Tenant\Helpers;

use App\Models\TenantSubscription;
use App\Models\SubscriptionPlan;
use App\Models\Tenant;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class SubscriptionHelper
{
    /**
     * Calculate next billing date based on billing cycle
     */
    public static function calculateNextBillingDate(Carbon $startDate, string $billingCycle): Carbon
    {
        switch ($billingCycle) {
            case 'monthly':
                return $startDate->copy()->addMonth();
            case 'quarterly':
                return $startDate->copy()->addMonths(3);
            case 'yearly':
                return $startDate->copy()->addYear();
            default:
                return $startDate->copy()->addMonth();
        }
    }

    /**
     * Calculate subscription price with discounts
     */
    public static function calculateSubscriptionPrice(
        SubscriptionPlan $plan,
        string $billingCycle,
        ?float $discountAmount = null,
        ?string $discountType = null
    ): array {
        $basePrice = $plan->getPrice($billingCycle);
        $discountValue = 0;
        
        if ($discountAmount && $discountType) {
            if ($discountType === 'percentage') {
                $discountValue = ($basePrice * $discountAmount) / 100;
            } else {
                $discountValue = $discountAmount;
            }
        }
        
        $finalPrice = max(0, $basePrice - $discountValue);
        
        return [
            'base_price' => $basePrice,
            'discount_amount' => $discountValue,
            'final_price' => $finalPrice,
            'savings' => $discountValue,
            'discount_percentage' => $basePrice > 0 ? ($discountValue / $basePrice) * 100 : 0,
        ];
    }

    /**
     * Check if subscription is in trial period
     */
    public static function isInTrial(TenantSubscription $subscription): bool
    {
        if (!$subscription->trial_ends_at) {
            return false;
        }
        
        return now()->lt($subscription->trial_ends_at);
    }

    /**
     * Get trial days remaining
     */
    public static function getTrialDaysRemaining(TenantSubscription $subscription): int
    {
        if (!$subscription->trial_ends_at) {
            return 0;
        }
        
        $daysRemaining = now()->diffInDays($subscription->trial_ends_at, false);
        return max(0, $daysRemaining);
    }

    /**
     * Check if subscription is expiring soon
     */
    public static function isExpiringSoon(TenantSubscription $subscription, int $days = 7): bool
    {
        if (!$subscription->ends_at) {
            return false;
        }
        
        return now()->diffInDays($subscription->ends_at, false) <= $days;
    }

    /**
     * Get subscription usage statistics
     */
    public static function getUsageStats(TenantSubscription $subscription): array
    {
        $tenant = $subscription->tenant;
        $plan = $subscription->plan;
        
        if (!$plan || !$plan->limits) {
            return [];
        }
        
        $stats = [];
        
        foreach ($plan->limits as $feature => $limit) {
            $currentUsage = TenantHelper::getCurrentUsage($tenant, $feature);
            $usagePercentage = $limit > 0 ? ($currentUsage / $limit) * 100 : 0;
            
            $stats[$feature] = [
                'current' => $currentUsage,
                'limit' => $limit,
                'percentage' => min(100, $usagePercentage),
                'is_unlimited' => $limit === -1,
                'is_over_limit' => $limit > 0 && $currentUsage > $limit,
                'remaining' => $limit > 0 ? max(0, $limit - $currentUsage) : null,
            ];
        }
        
        return $stats;
    }

    /**
     * Get billing history for subscription
     */
    public static function getBillingHistory(TenantSubscription $subscription, int $limit = 12): Collection
    {
        return $subscription->billingRecords()
            ->with(['subscription.plan'])
            ->orderBy('billing_date', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($record) {
                return [
                    'id' => $record->id,
                    'billing_date' => $record->billing_date,
                    'amount' => $record->amount,
                    'currency' => $record->currency,
                    'status' => $record->status,
                    'payment_method' => $record->payment_method,
                    'invoice_number' => $record->invoice_number,
                    'description' => $record->description,
                    'plan_name' => $record->subscription->plan->name ?? 'Unknown Plan',
                ];
            });
    }

    /**
     * Calculate upgrade/downgrade pricing
     */
    public static function calculatePlanChangePrice(
        TenantSubscription $currentSubscription,
        SubscriptionPlan $newPlan,
        string $newBillingCycle
    ): array {
        $currentPlan = $currentSubscription->plan;
        $now = now();
        
        if (!$currentPlan || !$currentSubscription->ends_at) {
            return [
                'type' => 'new',
                'immediate_charge' => $newPlan->getPrice($newBillingCycle),
                'prorated_credit' => 0,
                'total_due' => $newPlan->getPrice($newBillingCycle),
            ];
        }
        
        $daysRemaining = $now->diffInDays($currentSubscription->ends_at, false);
        $totalDaysInPeriod = $currentSubscription->starts_at->diffInDays($currentSubscription->ends_at);
        
        if ($daysRemaining <= 0) {
            return [
                'type' => 'expired',
                'immediate_charge' => $newPlan->getPrice($newBillingCycle),
                'prorated_credit' => 0,
                'total_due' => $newPlan->getPrice($newBillingCycle),
            ];
        }
        
        // Calculate prorated amounts
        $currentPlanPrice = $currentPlan->getPrice($currentSubscription->billing_cycle);
        $newPlanPrice = $newPlan->getPrice($newBillingCycle);
        
        $dailyCurrentRate = $currentPlanPrice / $totalDaysInPeriod;
        $dailyNewRate = $newPlanPrice / $totalDaysInPeriod;
        
        $proratedCredit = $dailyCurrentRate * $daysRemaining;
        $proratedCharge = $dailyNewRate * $daysRemaining;
        
        $totalDue = max(0, $proratedCharge - $proratedCredit);
        
        $isUpgrade = $newPlanPrice > $currentPlanPrice;
        
        return [
            'type' => $isUpgrade ? 'upgrade' : 'downgrade',
            'immediate_charge' => round($proratedCharge, 2),
            'prorated_credit' => round($proratedCredit, 2),
            'total_due' => round($totalDue, 2),
            'days_remaining' => $daysRemaining,
            'current_plan_price' => $currentPlanPrice,
            'new_plan_price' => $newPlanPrice,
            'is_upgrade' => $isUpgrade,
        ];
    }

    /**
     * Get subscription renewal options
     */
    public static function getRenewalOptions(TenantSubscription $subscription): array
    {
        $plan = $subscription->plan;
        
        if (!$plan) {
            return [];
        }
        
        $options = [];
        $billingCycles = ['monthly', 'quarterly', 'yearly'];
        
        foreach ($billingCycles as $cycle) {
            $price = $plan->getPrice($cycle);
            
            if ($price > 0) {
                $savings = 0;
                $monthlyEquivalent = $price;
                
                if ($cycle === 'quarterly') {
                    $monthlyEquivalent = $price / 3;
                    $savings = ($plan->getPrice('monthly') * 3) - $price;
                } elseif ($cycle === 'yearly') {
                    $monthlyEquivalent = $price / 12;
                    $savings = ($plan->getPrice('monthly') * 12) - $price;
                }
                
                $options[] = [
                    'cycle' => $cycle,
                    'price' => $price,
                    'monthly_equivalent' => round($monthlyEquivalent, 2),
                    'savings' => round($savings, 2),
                    'savings_percentage' => $savings > 0 ? round(($savings / ($price + $savings)) * 100, 1) : 0,
                    'is_current' => $cycle === $subscription->billing_cycle,
                ];
            }
        }
        
        return $options;
    }

    /**
     * Check if subscription can be cancelled
     */
    public static function canBeCancelled(TenantSubscription $subscription): array
    {
        $canCancel = true;
        $reasons = [];
        
        if ($subscription->status === 'cancelled') {
            $canCancel = false;
            $reasons[] = 'Subscription is already cancelled';
        }
        
        if ($subscription->status === 'expired') {
            $canCancel = false;
            $reasons[] = 'Subscription has already expired';
        }
        
        // Check if there are any pending payments
        $pendingPayments = $subscription->billingRecords()
            ->where('status', 'pending')
            ->exists();
            
        if ($pendingPayments) {
            $reasons[] = 'There are pending payments that need to be resolved';
        }
        
        return [
            'can_cancel' => $canCancel,
            'reasons' => $reasons,
            'immediate_cancellation' => $canCancel && !$pendingPayments,
        ];
    }

    /**
     * Get subscription health score
     */
    public static function getHealthScore(TenantSubscription $subscription): array
    {
        $score = 100;
        $factors = [];
        
        // Check payment history
        $failedPayments = $subscription->billingRecords()
            ->where('status', 'failed')
            ->where('created_at', '>=', now()->subMonths(6))
            ->count();
            
        if ($failedPayments > 0) {
            $deduction = min(30, $failedPayments * 10);
            $score -= $deduction;
            $factors[] = "Failed payments in last 6 months: -{$deduction} points";
        }
        
        // Check usage patterns
        $usageStats = self::getUsageStats($subscription);
        $overLimitFeatures = collect($usageStats)->where('is_over_limit', true)->count();
        
        if ($overLimitFeatures > 0) {
            $deduction = min(20, $overLimitFeatures * 10);
            $score -= $deduction;
            $factors[] = "Over limit features: -{$deduction} points";
        }
        
        // Check if subscription is expiring soon
        if (self::isExpiringSoon($subscription, 7)) {
            $score -= 15;
            $factors[] = "Expiring soon: -15 points";
        }
        
        // Check auto-renewal status
        if (!$subscription->auto_renew) {
            $score -= 10;
            $factors[] = "Auto-renewal disabled: -10 points";
        }
        
        $score = max(0, $score);
        
        $healthLevel = 'excellent';
        if ($score < 70) {
            $healthLevel = 'poor';
        } elseif ($score < 85) {
            $healthLevel = 'fair';
        } elseif ($score < 95) {
            $healthLevel = 'good';
        }
        
        return [
            'score' => $score,
            'level' => $healthLevel,
            'factors' => $factors,
            'recommendations' => self::getHealthRecommendations($subscription, $score, $factors),
        ];
    }

    /**
     * Get health recommendations
     */
    private static function getHealthRecommendations(TenantSubscription $subscription, int $score, array $factors): array
    {
        $recommendations = [];
        
        if ($score < 70) {
            $recommendations[] = 'Consider upgrading to a higher plan to avoid usage limits';
            $recommendations[] = 'Review payment method and update if necessary';
        }
        
        if (!$subscription->auto_renew) {
            $recommendations[] = 'Enable auto-renewal to avoid service interruption';
        }
        
        if (self::isExpiringSoon($subscription, 7)) {
            $recommendations[] = 'Renew subscription to continue service';
        }
        
        if (empty($recommendations)) {
            $recommendations[] = 'Subscription is healthy - no action required';
        }
        
        return $recommendations;
    }

    /**
     * Format subscription for API response
     */
    public static function formatForApi(TenantSubscription $subscription): array
    {
        return [
            'id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'plan' => [
                'id' => $subscription->plan->id,
                'name' => $subscription->plan->name,
                'description' => $subscription->plan->description,
                'features' => $subscription->plan->features,
                'limits' => $subscription->plan->limits,
            ],
            'status' => $subscription->status,
            'billing_cycle' => $subscription->billing_cycle,
            'starts_at' => $subscription->starts_at,
            'ends_at' => $subscription->ends_at,
            'trial_ends_at' => $subscription->trial_ends_at,
            'auto_renew' => $subscription->auto_renew,
            'is_trial' => self::isInTrial($subscription),
            'trial_days_remaining' => self::getTrialDaysRemaining($subscription),
            'is_expiring_soon' => self::isExpiringSoon($subscription),
            'usage_stats' => self::getUsageStats($subscription),
            'health_score' => self::getHealthScore($subscription),
            'created_at' => $subscription->created_at,
            'updated_at' => $subscription->updated_at,
        ];
    }
}
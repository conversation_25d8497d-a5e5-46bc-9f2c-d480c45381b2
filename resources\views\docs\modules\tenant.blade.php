@extends('docs.layout')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <div class="p-4 rounded-full bg-purple-100 mr-4">
                <i class="fas fa-building text-purple-600 text-2xl"></i>
            </div>
            <div>
                <h1 class="text-4xl font-bold text-gray-900">Tenant Module</h1>
                <p class="text-xl text-gray-600 mt-2">Multi-tenancy support with subscription management, billing, and usage tracking</p>
            </div>
        </div>
        
        <!-- Module Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-purple-600">32</div>
                <div class="text-gray-600 text-sm">Total Endpoints</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-green-600">2</div>
                <div class="text-gray-600 text-sm">Public Routes</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-orange-600">30</div>
                <div class="text-gray-600 text-sm">Protected Routes</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-blue-600">3</div>
                <div class="text-gray-600 text-sm">Middleware</div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="relative mb-6">
            <input type="text" id="endpointSearch" placeholder="Search tenant endpoints..." 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
            <i class="fas fa-search absolute right-4 top-4 text-gray-400"></i>
        </div>
    </div>

    <!-- Public Routes Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-globe text-green-600 mr-3"></i>
            Public Routes
            <span class="ml-3 bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded">No Auth Required</span>
        </h2>

        <!-- List Tenants Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants</h3>
                        <span class="ml-3 text-gray-600">List all tenants</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Query Parameters</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>per_page: 15 (optional)
search: "restaurant" (optional)
status: "active" (optional)</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "Pizza Palace",
        "code": "PP001",
        "contact_email": "<EMAIL>",
        "status": "active",
        "created_at": "2024-01-16T14:30:00Z",
        "country": {
          "id": 1,
          "name": "United States",
          "code": "US"
        },
        "subscriptions": [
          {
            "id": 1,
            "status": "active",
            "plan": {
              "name": "Professional",
              "price": 99.99
            }
          }
        ]
      }
    ],
    "current_page": 1,
    "total": 25
  },
  "message": "Tenants retrieved successfully"
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Retrieves a paginated list of all tenants with their basic information, country details, and subscription status.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Tenant Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants</h3>
                        <span class="ml-3 text-gray-600">Create a new tenant</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Pizza Palace",
  "contact_email": "<EMAIL>",
  "contact_phone": "+1234567890",
  "business_type": "restaurant",
  "country_id": 1,
  "currency_code": "USD",
  "timezone": "America/New_York",
  "address": "123 Main St, City, State",
  "subscription": {
    "plan_id": 1,
    "billing_cycle": "monthly"
  }
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Pizza Palace",
    "code": "PP001",
    "contact_email": "<EMAIL>",
    "contact_phone": "+1234567890",
    "business_type": "restaurant",
    "status": "active",
    "currency_code": "USD",
    "timezone": "America/New_York",
    "created_at": "2024-01-16T14:30:00Z",
    "country": {
      "id": 1,
      "name": "United States",
      "code": "US"
    },
    "subscriptions": [
      {
        "id": 1,
        "status": "active",
        "plan": {
          "name": "Professional",
          "price": 99.99
        }
      }
    ]
  },
  "message": "Tenant created successfully"
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Creates a new tenant with the provided information and optionally sets up a subscription plan.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Protected Routes Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-shield-alt text-orange-600 mr-3"></i>
            Protected Routes
            <span class="ml-3 bg-orange-100 text-orange-800 text-sm font-medium px-2.5 py-0.5 rounded">Auth Required</span>
        </h2>

        <!-- Tenant Management Section -->
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Tenant Management</h3>

        <!-- Get Tenant Statistics Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants/statistics</h3>
                        <span class="ml-3 text-gray-600">Get tenant statistics</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "total_tenants": 150,
    "active_tenants": 142,
    "inactive_tenants": 8,
    "total_subscriptions": 135,
    "revenue_this_month": 15750.00,
    "new_tenants_this_month": 12
  },
  "message": "Tenant statistics retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Get Tenant Details Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants/{tenant}</h3>
                        <span class="ml-3 text-gray-600">Get tenant details</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>tenant: 1 (Tenant ID)</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Pizza Palace",
    "code": "PP001",
    "contact_email": "<EMAIL>",
    "contact_phone": "+1234567890",
    "business_type": "restaurant",
    "status": "active",
    "currency_code": "USD",
    "timezone": "America/New_York",
    "address": "123 Main St, City, State",
    "created_at": "2024-01-16T14:30:00Z",
    "country": {
      "id": 1,
      "name": "United States",
      "code": "US"
    },
    "subscriptions": [
      {
        "id": 1,
        "status": "active",
        "plan": {
          "name": "Professional",
          "price": 99.99
        }
      }
    ],
    "branches": [
      {
        "id": 1,
        "name": "Downtown Branch",
        "address": "456 Downtown Ave"
      }
    ]
  },
  "message": "Tenant retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Update Tenant Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants/{tenant}</h3>
                        <span class="ml-3 text-gray-600">Update tenant</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Pizza Palace Updated",
  "contact_email": "<EMAIL>",
  "contact_phone": "+1987654321",
  "address": "789 New Address St",
  "timezone": "America/Los_Angeles"
}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Pizza Palace Updated",
    "code": "PP001",
    "contact_email": "<EMAIL>",
    "contact_phone": "+1987654321",
    "address": "789 New Address St",
    "timezone": "America/Los_Angeles",
    "updated_at": "2024-01-16T15:30:00Z"
  },
  "message": "Tenant updated successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Tenant Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants/{tenant}</h3>
                        <span class="ml-3 text-gray-600">Delete tenant</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>tenant: 1 (Tenant ID)</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Tenant deleted successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activate Tenant Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants/{tenant}/activate</h3>
                        <span class="ml-3 text-gray-600">Activate tenant</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Pizza Palace",
    "status": "active",
    "activated_at": "2024-01-16T15:30:00Z"
  },
  "message": "Tenant activated successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deactivate Tenant Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenants/{tenant}/deactivate</h3>
                        <span class="ml-3 text-gray-600">Deactivate tenant</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Pizza Palace",
    "status": "inactive",
    "deactivated_at": "2024-01-16T15:30:00Z"
  },
  "message": "Tenant deactivated successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Management Section -->
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Subscription Management</h3>

        <!-- List Subscriptions Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/subscriptions</h3>
                        <span class="ml-3 text-gray-600">List all subscriptions</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>per_page: 15 (optional)
status: "active" (optional)
tenant_id: 1 (optional)</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "tenant_id": 1,
        "plan_id": 1,
        "status": "active",
        "billing_cycle": "monthly",
        "starts_at": "2024-01-01T00:00:00Z",
        "ends_at": "2024-02-01T00:00:00Z",
        "auto_renew": true,
        "plan": {
          "name": "Professional",
          "price": 99.99,
          "features": ["multi_store", "advanced_reports"]
        },
        "tenant": {
          "name": "Pizza Palace",
          "code": "PP001"
        }
      }
    ],
    "current_page": 1,
    "total": 50
  },
  "message": "Subscriptions retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Get Subscription Plans Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/subscriptions/plans</h3>
                        <span class="ml-3 text-gray-600">Get available subscription plans</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Basic",
      "description": "Perfect for small restaurants",
      "price": 29.99,
      "billing_cycles": ["monthly", "yearly"],
      "max_branches": 1,
      "max_users": 5,
      "max_menu_items": 100,
      "max_orders_per_month": 1000,
      "features": ["basic_pos", "inventory", "reports"]
    },
    {
      "id": 2,
      "name": "Professional",
      "description": "For growing restaurant chains",
      "price": 99.99,
      "billing_cycles": ["monthly", "yearly"],
      "max_branches": 5,
      "max_users": 25,
      "max_menu_items": 500,
      "max_orders_per_month": 10000,
      "features": ["advanced_pos", "multi_store", "advanced_reports"]
    }
  ],
  "message": "Subscription plans retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Subscription Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/subscriptions</h3>
                        <span class="ml-3 text-gray-600">Create new subscription</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "tenant_id": 1,
  "plan_id": 2,
  "billing_cycle": "monthly",
  "starts_at": "2024-01-16T00:00:00Z",
  "auto_renew": true
}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "plan_id": 2,
    "status": "active",
    "billing_cycle": "monthly",
    "starts_at": "2024-01-16T00:00:00Z",
    "ends_at": "2024-02-16T00:00:00Z",
    "auto_renew": true,
    "created_at": "2024-01-16T14:30:00Z"
  },
  "message": "Subscription created successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Get Subscription Details Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/subscriptions/{subscription}</h3>
                        <span class="ml-3 text-gray-600">Get subscription details</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>subscription: 1 (Subscription ID)</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "plan_id": 2,
    "status": "active",
    "billing_cycle": "monthly",
    "starts_at": "2024-01-16T00:00:00Z",
    "ends_at": "2024-02-16T00:00:00Z",
    "auto_renew": true,
    "plan": {
      "name": "Professional",
      "price": 99.99,
      "features": ["advanced_pos", "multi_store"]
    },
    "tenant": {
      "name": "Pizza Palace",
      "code": "PP001"
    },
    "usage_stats": {
      "branches_used": 2,
      "users_used": 8,
      "menu_items_used": 45,
      "orders_this_month": 1250
    }
  },
  "message": "Subscription retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cancel Subscription Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/subscriptions/{subscription}/cancel</h3>
                        <span class="ml-3 text-gray-600">Cancel subscription</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "reason": "Customer requested cancellation",
  "cancel_at_period_end": true
}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "status": "cancelled",
    "cancelled_at": "2024-01-16T15:30:00Z",
    "cancellation_reason": "Customer requested cancellation",
    "ends_at": "2024-02-16T00:00:00Z"
  },
  "message": "Subscription cancelled successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upgrade Subscription Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/subscriptions/{subscription}/upgrade</h3>
                        <span class="ml-3 text-gray-600">Upgrade subscription</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "new_plan_id": 3,
  "billing_cycle": "monthly",
  "prorate": true
}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "plan_id": 3,
    "status": "active",
    "upgraded_at": "2024-01-16T15:30:00Z",
    "prorated_amount": 45.50,
    "new_plan": {
      "name": "Enterprise",
      "price": 199.99
    }
  },
  "message": "Subscription upgraded successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Get Subscription Usage Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/subscriptions/{subscription}/usage</h3>
                        <span class="ml-3 text-gray-600">Get subscription usage statistics</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "subscription_id": 1,
    "plan_limits": {
      "max_branches": 5,
      "max_users": 25,
      "max_menu_items": 500,
      "max_orders_per_month": 10000
    },
    "current_usage": {
      "branches_used": 2,
      "users_used": 8,
      "menu_items_used": 45,
      "orders_this_month": 1250
    },
    "usage_percentages": {
      "branches": 40,
      "users": 32,
      "menu_items": 9,
      "orders": 12.5
    },
    "health_score": 85,
    "warnings": []
  },
  "message": "Usage statistics retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Management Section -->
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Branch Management</h3>

        <!-- List Branches Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches</h3>
                        <span class="ml-3 text-gray-600">List all branches</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>tenant_id: 1 (optional)
per_page: 15 (optional)
search: "downtown" (optional)
status: "active" (optional)</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "tenant_id": 1,
        "name": "Downtown Branch",
        "code": "DT001",
        "address": "123 Main St, Downtown",
        "phone": "+1234567890",
        "email": "<EMAIL>",
        "manager_name": "John Doe",
        "status": "active",
        "is_delivery_enabled": true,
        "is_takeaway_enabled": true,
        "is_dine_in_enabled": true,
        "created_at": "2024-01-16T14:30:00Z"
      }
    ],
    "current_page": 1,
    "total": 5
  },
  "message": "Branches retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Branch Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches</h3>
                        <span class="ml-3 text-gray-600">Create a new branch</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "tenant_id": 1,
  "name": "Downtown Branch",
  "code": "DT001",
  "address": "123 Main St, Downtown",
  "phone": "+1234567890",
  "email": "<EMAIL>",
  "manager_name": "John Doe",
  "opening_hours": {
    "monday": {"open": "09:00", "close": "22:00", "is_closed": false},
    "tuesday": {"open": "09:00", "close": "22:00", "is_closed": false}
  },
  "timezone": "America/New_York",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "is_active": true,
  "is_delivery_enabled": true,
  "is_takeaway_enabled": true,
  "is_dine_in_enabled": true
}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Downtown Branch",
    "code": "DT001",
    "address": "123 Main St, Downtown",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "manager_name": "John Doe",
    "status": "active",
    "operating_hours": {
      "monday": {"open": "09:00", "close": "22:00", "is_closed": false}
    },
    "timezone": "America/New_York",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "is_delivery_enabled": true,
    "is_takeaway_enabled": true,
    "is_dine_in_enabled": true,
    "created_at": "2024-01-16T14:30:00Z"
  },
  "message": "Branch created successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Get Branch Details Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches/{branch}</h3>
                        <span class="ml-3 text-gray-600">Get branch details</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branch: 1 (Branch ID)</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "tenant_id": 1,
    "name": "Downtown Branch",
    "code": "DT001",
    "address": "123 Main St, Downtown",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "manager_name": "John Doe",
    "status": "active",
    "operating_hours": {
      "monday": {"open": "09:00", "close": "22:00", "is_closed": false}
    },
    "timezone": "America/New_York",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "is_delivery_enabled": true,
    "is_takeaway_enabled": true,
    "is_dine_in_enabled": true,
    "tenant": {
      "id": 1,
      "name": "Pizza Palace",
      "code": "PP001"
    },
    "created_at": "2024-01-16T14:30:00Z"
  },
  "message": "Branch retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Update Branch Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches/{branch}</h3>
                        <span class="ml-3 text-gray-600">Update branch</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Downtown Branch Updated",
  "address": "456 New Address St",
  "phone": "+1987654321",
  "manager_name": "Jane Smith",
  "is_delivery_enabled": false
}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Downtown Branch Updated",
    "address": "456 New Address St",
    "phone": "+1987654321",
    "manager_name": "Jane Smith",
    "is_delivery_enabled": false,
    "updated_at": "2024-01-16T15:30:00Z"
  },
  "message": "Branch updated successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Branch Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches/{branch}</h3>
                        <span class="ml-3 text-gray-600">Delete branch</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                        <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>branch: 1 (Branch ID)</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Branch deleted successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activate Branch Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches/{branch}/activate</h3>
                        <span class="ml-3 text-gray-600">Activate branch</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "status": "active",
    "activated_at": "2024-01-16T15:30:00Z"
  },
  "message": "Branch activated successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Deactivate Branch Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches/{branch}/deactivate</h3>
                        <span class="ml-3 text-gray-600">Deactivate branch</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "status": "inactive",
    "deactivated_at": "2024-01-16T15:30:00Z"
  },
  "message": "Branch deactivated successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Get Branch Statistics Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/branches/{branch}/statistics</h3>
                        <span class="ml-3 text-gray-600">Get branch statistics</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "branch_id": 1,
    "menu_items_count": 45,
    "categories_count": 8,
    "orders_today": 25,
    "orders_this_month": 750,
    "revenue_today": 1250.75,
    "revenue_this_month": 35750.25,
    "average_order_value": 47.67,
    "top_selling_items": [
      {
        "item_name": "Margherita Pizza",
        "orders_count": 125
      }
    ]
  },
  "message": "Branch statistics retrieved successfully"
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tenant-Specific Routes Section -->
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Tenant-Specific Routes</h3>

        <!-- Tenant Dashboard Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenant/dashboard</h3>
                        <span class="ml-3 text-gray-600">Get tenant dashboard data</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
X-Tenant-ID: 1</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "tenant": {
    "id": 1,
    "name": "Pizza Palace",
    "code": "PP001",
    "status": "active"
  },
  "subscription_status": "active",
  "statistics": {
    "total_branches": 2,
    "total_users": 8,
    "total_menu_items": 45,
    "orders_today": 25,
    "revenue_today": 1250.75,
    "orders_this_month": 1250,
    "revenue_this_month": 45750.25
  }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tenant Usage Statistics Endpoint -->
        <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
            <div class="cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/tenant/usage</h3>
                        <span class="ml-3 text-gray-600">Get tenant usage statistics</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                        <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
X-Tenant-ID: 1</code></pre>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                        <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "usage_stats": {
    "branches": {
      "used": 2,
      "limit": 5,
      "percentage": 40
    },
    "users": {
      "used": 8,
      "limit": 25,
      "percentage": 32
    },
    "menu_items": {
      "used": 45,
      "limit": 500,
      "percentage": 9
    },
    "orders_this_month": {
      "used": 1250,
      "limit": 10000,
      "percentage": 12.5
    }
  },
  "health_score": 85
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-star text-yellow-600 mr-3"></i>
            Key Features
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full bg-purple-100 mr-4">
                        <i class="fas fa-users text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Multi-Tenancy</h3>
                </div>
                <p class="text-gray-600">Complete tenant isolation with secure data separation and customizable configurations per tenant.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full bg-green-100 mr-4">
                        <i class="fas fa-credit-card text-green-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Subscription Management</h3>
                </div>
                <p class="text-gray-600">Flexible subscription plans with automated billing, upgrades, and usage tracking.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full bg-blue-100 mr-4">
                        <i class="fas fa-chart-line text-blue-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Usage Monitoring</h3>
                </div>
                <p class="text-gray-600">Real-time usage tracking with limits enforcement and detailed analytics.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full bg-orange-100 mr-4">
                        <i class="fas fa-store text-orange-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Multi-Store Support</h3>
                </div>
                <p class="text-gray-600">Manage multiple restaurant branches with centralized control and data synchronization.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full bg-red-100 mr-4">
                        <i class="fas fa-shield-alt text-red-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Security & Isolation</h3>
                </div>
                <p class="text-gray-600">Enterprise-grade security with complete data isolation between tenants.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <div class="flex items-center mb-4">
                    <div class="p-3 rounded-full bg-indigo-100 mr-4">
                        <i class="fas fa-cogs text-indigo-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Automated Billing</h3>
                </div>
                <p class="text-gray-600">Automated invoicing, payment processing, and subscription lifecycle management.</p>
            </div>
        </div>
    </div>

    <!-- Middleware Information -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-filter text-blue-600 mr-3"></i>
            Middleware
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">TenantMiddleware</h3>
                <p class="text-gray-600 mb-3">Resolves and sets the tenant context for incoming requests.</p>
                <div class="bg-gray-50 p-3 rounded">
                    <code class="text-sm">middleware(['tenant'])</code>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">SubscriptionMiddleware</h3>
                <p class="text-gray-600 mb-3">Validates subscription status and access permissions.</p>
                <div class="bg-gray-50 p-3 rounded">
                    <code class="text-sm">middleware(['subscription'])</code>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">UsageLimitMiddleware</h3>
                <p class="text-gray-600 mb-3">Enforces feature usage limits based on subscription plan.</p>
                <div class="bg-gray-50 p-3 rounded">
                    <code class="text-sm">middleware(['usage_limit'])</code>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Codes Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
            Common Error Codes
        </h2>
        
        <div class="bg-white rounded-lg shadow border border-gray-100 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">400</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Invalid tenant data</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Required fields are missing or invalid</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">401</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Unauthorized</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Authentication token is missing or invalid</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">403</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Subscription expired</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Tenant subscription has expired or is inactive</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">404</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Tenant not found</td>
                            <td class="px-6 py-4 text-sm text-gray-500">The specified tenant does not exist</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">429</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Usage limit exceeded</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Tenant has exceeded their subscription limits</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">500</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Internal server error</td>
                            <td class="px-6 py-4 text-sm text-gray-500">An unexpected error occurred on the server</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Usage Examples Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-code text-green-600 mr-3"></i>
            Usage Examples
        </h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">JavaScript (Fetch API)</h3>
                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>// Create a new tenant
fetch('/api/tenants', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    name: 'Pizza Palace',
    contact_email: '<EMAIL>',
    business_type: 'restaurant',
    country_id: 1
  })
})
.then(response => response.json())
.then(data => console.log(data));</code></pre>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">PHP (Laravel)</h3>
                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>// Using the Tenant service
use Modules\Tenant\Services\TenantService;

$tenantService = new TenantService();

$tenant = $tenantService->createTenant([
    'name' => 'Pizza Palace',
    'contact_email' => '<EMAIL>',
    'business_type' => 'restaurant',
    'country_id' => 1
]);

echo $tenant->name; // Pizza Palace</code></pre>
            </div>
        </div>
    </div>
</div>


@endsection
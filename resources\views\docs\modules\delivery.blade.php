@extends('docs.layout')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <div class="p-4 rounded-full bg-teal-100 mr-4">
                <i class="fas fa-truck text-teal-600 text-2xl"></i>
            </div>
            <div>
                <h1 class="text-4xl font-bold text-gray-900">Delivery Module</h1>
                <p class="text-xl text-gray-600 mt-2">Comprehensive delivery management system for restaurant operations</p>
            </div>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-2xl font-bold mb-4 text-gray-800">Quick Navigation</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="#overview" class="bg-blue-50 hover:bg-blue-100 p-3 rounded-lg text-center transition-colors">
                <i class="fas fa-info-circle text-blue-600 text-xl mb-2"></i>
                <div class="text-sm font-medium">Overview</div>
            </a>
            <a href="#personnel" class="bg-green-50 hover:bg-green-100 p-3 rounded-lg text-center transition-colors">
                <i class="fas fa-users text-green-600 text-xl mb-2"></i>
                <div class="text-sm font-medium">Personnel</div>
            </a>
            <a href="#orders" class="bg-orange-50 hover:bg-orange-100 p-3 rounded-lg text-center transition-colors">
                <i class="fas fa-shopping-bag text-orange-600 text-xl mb-2"></i>
                <div class="text-sm font-medium">Order Management</div>
            </a>
            <a href="#cash-custody" class="bg-red-50 hover:bg-red-100 p-3 rounded-lg text-center transition-colors">
                <i class="fas fa-money-bill-wave text-red-600 text-xl mb-2"></i>
                <div class="text-sm font-medium">Cash Custody</div>
            </a>
        </div>
     </div>

        <!-- Database Schema -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-database mr-3 text-indigo-600"></i>
                Database Schema
            </h2>
            
            <div class="space-y-8">
                <!-- Delivery Personnel Table -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-700 flex items-center">
                        <i class="fas fa-table mr-2 text-blue-600"></i>
                        delivery_personnel
                    </h3>
                    <p class="text-gray-600 mb-4">Stores information about delivery personnel including their details, vehicle information, and performance metrics.</p>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                </tr>
                            </thead>
                            <tbody class="text-sm">
                                <tr><td class="px-4 py-2 border-b font-mono">id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Primary key</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">user_id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Foreign key to users table</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">branch_id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Foreign key to branches table</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">license_number</td><td class="px-4 py-2 border-b">varchar(100)</td><td class="px-4 py-2 border-b">Driver's license number</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">vehicle_type</td><td class="px-4 py-2 border-b">enum</td><td class="px-4 py-2 border-b">motorcycle, car, bicycle, scooter</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">vehicle_plate_number</td><td class="px-4 py-2 border-b">varchar(20)</td><td class="px-4 py-2 border-b">Vehicle plate number</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">status</td><td class="px-4 py-2 border-b">enum</td><td class="px-4 py-2 border-b">active, inactive, suspended</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">current_latitude</td><td class="px-4 py-2 border-b">decimal(10,8)</td><td class="px-4 py-2 border-b">Current GPS latitude</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">current_longitude</td><td class="px-4 py-2 border-b">decimal(11,8)</td><td class="px-4 py-2 border-b">Current GPS longitude</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">max_concurrent_deliveries</td><td class="px-4 py-2 border-b">int</td><td class="px-4 py-2 border-b">Maximum concurrent deliveries</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivery_radius_km</td><td class="px-4 py-2 border-b">decimal(5,2)</td><td class="px-4 py-2 border-b">Delivery radius in kilometers</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">rating</td><td class="px-4 py-2 border-b">decimal(3,2)</td><td class="px-4 py-2 border-b">Average rating (0.00-5.00)</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">total_deliveries</td><td class="px-4 py-2 border-b">int</td><td class="px-4 py-2 border-b">Total completed deliveries</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">total_earnings</td><td class="px-4 py-2 border-b">decimal(10,2)</td><td class="px-4 py-2 border-b">Total earnings amount</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">working_hours</td><td class="px-4 py-2 border-b">json</td><td class="px-4 py-2 border-b">Working hours schedule</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">is_verified</td><td class="px-4 py-2 border-b">boolean</td><td class="px-4 py-2 border-b">Verification status</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Delivery Zones Table -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-700 flex items-center">
                        <i class="fas fa-table mr-2 text-green-600"></i>
                        delivery_zones
                    </h3>
                    <p class="text-gray-600 mb-4">Defines delivery zones with geographical boundaries and service parameters.</p>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                </tr>
                            </thead>
                            <tbody class="text-sm">
                                <tr><td class="px-4 py-2 border-b font-mono">id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Primary key</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">branch_id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Foreign key to branches table</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">name</td><td class="px-4 py-2 border-b">varchar(100)</td><td class="px-4 py-2 border-b">Zone name</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">description</td><td class="px-4 py-2 border-b">text</td><td class="px-4 py-2 border-b">Zone description</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">polygon_coordinates</td><td class="px-4 py-2 border-b">json</td><td class="px-4 py-2 border-b">Geographical boundary coordinates</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivery_fee</td><td class="px-4 py-2 border-b">decimal(8,2)</td><td class="px-4 py-2 border-b">Base delivery fee for zone</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">minimum_order_amount</td><td class="px-4 py-2 border-b">decimal(8,2)</td><td class="px-4 py-2 border-b">Minimum order amount</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">estimated_delivery_time_minutes</td><td class="px-4 py-2 border-b">int</td><td class="px-4 py-2 border-b">Estimated delivery time</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">is_active</td><td class="px-4 py-2 border-b">boolean</td><td class="px-4 py-2 border-b">Zone active status</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">priority</td><td class="px-4 py-2 border-b">int</td><td class="px-4 py-2 border-b">Zone priority level</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Delivery Assignments Table -->
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-700 flex items-center">
                        <i class="fas fa-table mr-2 text-purple-600"></i>
                        delivery_assignments
                    </h3>
                    <p class="text-gray-600 mb-4">Tracks delivery assignments and their status throughout the delivery process.</p>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-4 py-2 border-b text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                </tr>
                            </thead>
                            <tbody class="text-sm">
                                <tr><td class="px-4 py-2 border-b font-mono">id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Primary key</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">order_id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Foreign key to orders table</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivery_personnel_id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Foreign key to delivery_personnel</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivery_zone_id</td><td class="px-4 py-2 border-b">bigint</td><td class="px-4 py-2 border-b">Foreign key to delivery_zones</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">status</td><td class="px-4 py-2 border-b">enum</td><td class="px-4 py-2 border-b">assigned, picked_up, in_transit, delivered, failed</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">assigned_at</td><td class="px-4 py-2 border-b">timestamp</td><td class="px-4 py-2 border-b">Assignment timestamp</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">picked_up_at</td><td class="px-4 py-2 border-b">timestamp</td><td class="px-4 py-2 border-b">Pickup timestamp</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivered_at</td><td class="px-4 py-2 border-b">timestamp</td><td class="px-4 py-2 border-b">Delivery completion timestamp</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivery_notes</td><td class="px-4 py-2 border-b">text</td><td class="px-4 py-2 border-b">Delivery notes</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">failure_reason</td><td class="px-4 py-2 border-b">text</td><td class="px-4 py-2 border-b">Reason for delivery failure</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivery_proof</td><td class="px-4 py-2 border-b">json</td><td class="px-4 py-2 border-b">Delivery proof (signature, photo)</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">distance_km</td><td class="px-4 py-2 border-b">decimal(8,2)</td><td class="px-4 py-2 border-b">Delivery distance</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">estimated_duration_minutes</td><td class="px-4 py-2 border-b">int</td><td class="px-4 py-2 border-b">Estimated delivery duration</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">actual_duration_minutes</td><td class="px-4 py-2 border-b">int</td><td class="px-4 py-2 border-b">Actual delivery duration</td></tr>
                                <tr><td class="px-4 py-2 border-b font-mono">delivery_fee_earned</td><td class="px-4 py-2 border-b">decimal(8,2)</td><td class="px-4 py-2 border-b">Fee earned for delivery</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Additional Tables Summary -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-star mr-2 text-yellow-600"></i>
                            delivery_reviews
                        </h4>
                        <p class="text-sm text-gray-600">Customer reviews and ratings for delivery assignments including rating, comment, and review categories.</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-dollar-sign mr-2 text-green-600"></i>
                            delivery_tips
                        </h4>
                        <p class="text-sm text-gray-600">Tips given to delivery personnel including amount, payment method, and transaction details.</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-red-600"></i>
                            delivery_tracking
                        </h4>
                        <p class="text-sm text-gray-600">Real-time GPS tracking data for delivery assignments including coordinates, speed, and bearing.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Module Overview -->
        <div id="overview" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-info-circle mr-3 text-blue-600"></i>
                Module Overview
            </h2>
            <div class="prose max-w-none">
                <p class="text-lg text-gray-700 mb-6">
                    The Delivery Module is a comprehensive system designed to manage all aspects of restaurant delivery operations. 
                    It handles delivery personnel management, order assignments, real-time tracking, cash custody, and customer satisfaction.
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div class="bg-blue-50 rounded-lg p-6">
                        <h3 class="font-semibold text-lg mb-3 text-blue-800">
                            <i class="fas fa-motorcycle mr-2"></i>
                            Personnel Management
                        </h3>
                        <p class="text-blue-700">Manage delivery staff, track performance, handle multiple concurrent deliveries, and monitor real-time locations.</p>
                    </div>
                    
                    <div class="bg-green-50 rounded-lg p-6">
                        <h3 class="font-semibold text-lg mb-3 text-green-800">
                            <i class="fas fa-route mr-2"></i>
                            Smart Assignment
                        </h3>
                        <p class="text-green-700">Intelligent order assignment based on proximity, capacity, and performance ratings for optimal delivery efficiency.</p>
                    </div>
                    
                    <div class="bg-orange-50 rounded-lg p-6">
                        <h3 class="font-semibold text-lg mb-3 text-orange-800">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            Real-time Tracking
                        </h3>
                        <p class="text-orange-700">GPS-based tracking system for monitoring delivery progress and providing accurate ETAs to customers.</p>
                    </div>
                    
                    <div class="bg-red-50 rounded-lg p-6">
                        <h3 class="font-semibold text-lg mb-3 text-red-800">
                            <i class="fas fa-money-check-alt mr-2"></i>
                            Cash Management
                        </h3>
                        <p class="text-red-700">Secure cash custody system for COD orders with detailed tracking and reconciliation features.</p>
                    </div>
                    
                    <div class="bg-purple-50 rounded-lg p-6">
                        <h3 class="font-semibold text-lg mb-3 text-purple-800">
                            <i class="fas fa-star mr-2"></i>
                            Customer Experience
                        </h3>
                        <p class="text-purple-700">Review system, tip management, and delivery notifications for enhanced customer satisfaction.</p>
                    </div>
                    
                    <div class="bg-indigo-50 rounded-lg p-6">
                        <h3 class="font-semibold text-lg mb-3 text-indigo-800">
                            <i class="fas fa-chart-line mr-2"></i>
                            Analytics & Reports
                        </h3>
                        <p class="text-indigo-700">Comprehensive reporting on delivery performance, personnel efficiency, and customer satisfaction metrics.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Personnel Management -->
        <div id="personnel" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-users mr-3 text-green-600"></i>
                Delivery Personnel Management
            </h2>
            
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Multi-Order Capacity</h3>
                <div class="bg-green-50 border-l-4 border-green-400 p-6 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-green-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-lg font-medium text-green-800">Key Feature: Multiple Order Handling</h4>
                            <p class="text-green-700 mt-2">
                                Each delivery person can handle multiple orders simultaneously based on their capacity and vehicle type. 
                                The system intelligently assigns orders considering proximity, delivery time windows, and personnel availability.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Personnel Attributes</h4>
                        <div class="space-y-4">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">Capacity Management</h5>
                                <ul class="text-gray-600 space-y-1">
                                    <li>• <strong>Max Concurrent Deliveries:</strong> Configurable per personnel (1-5 orders)</li>
                                    <li>• <strong>Vehicle Type:</strong> Motorcycle, bicycle, car, or on-foot</li>
                                    <li>• <strong>Delivery Radius:</strong> Maximum distance coverage (km)</li>
                                    <li>• <strong>Working Hours:</strong> Flexible scheduling system</li>
                                </ul>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">Performance Tracking</h5>
                                <ul class="text-gray-600 space-y-1">
                                    <li>• <strong>Rating System:</strong> Customer feedback (1-5 stars)</li>
                                    <li>• <strong>Total Deliveries:</strong> Lifetime delivery count</li>
                                    <li>• <strong>Total Earnings:</strong> Commission and tips tracking</li>
                                    <li>• <strong>Success Rate:</strong> Completed vs. failed deliveries</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Status Management</h4>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-green-100 rounded-lg">
                                <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-green-800">Active</span>
                                    <p class="text-green-600 text-sm">Available for new assignments</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-blue-100 rounded-lg">
                                <span class="w-3 h-3 bg-blue-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-blue-800">On Delivery</span>
                                    <p class="text-blue-600 text-sm">Currently handling deliveries</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-yellow-100 rounded-lg">
                                <span class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-yellow-800">On Break</span>
                                    <p class="text-yellow-600 text-sm">Temporarily unavailable</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-red-100 rounded-lg">
                                <span class="w-3 h-3 bg-red-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-red-800">Offline</span>
                                    <p class="text-red-600 text-sm">Not available for assignments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Management -->
        <div id="orders" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-shopping-bag mr-3 text-orange-600"></i>
                Order Management & Assignment
            </h2>
            
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Individual Order Destinations</h3>
                <div class="bg-orange-50 border-l-4 border-orange-400 p-6 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-map-marked-alt text-orange-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-lg font-medium text-orange-800">Unique Delivery Addresses</h4>
                            <p class="text-orange-700 mt-2">
                                Each order has its own specific destination address and coordinates. The system optimizes routes 
                                when assigning multiple orders to a single delivery person, considering proximity and delivery time windows.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Order Assignment Process</h4>
                        <div class="space-y-4">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">1. Automatic Assignment</h5>
                                <p class="text-gray-600 text-sm mb-2">System automatically finds the best delivery person based on:</p>
                                <ul class="text-gray-600 text-sm space-y-1 ml-4">
                                    <li>• Distance from restaurant/current location</li>
                                    <li>• Current workload and capacity</li>
                                    <li>• Performance rating and history</li>
                                    <li>• Vehicle type and delivery zone</li>
                                </ul>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">2. Manual Assignment</h5>
                                <p class="text-gray-600 text-sm">Managers can manually assign orders to specific personnel when needed, with system validation for capacity and availability.</p>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">3. Route Optimization</h5>
                                <p class="text-gray-600 text-sm">When multiple orders are assigned, the system suggests optimal delivery sequence to minimize travel time and distance.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Order Status Tracking</h4>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-gray-100 rounded-lg">
                                <span class="w-3 h-3 bg-gray-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-gray-800">Assigned</span>
                                    <p class="text-gray-600 text-sm">Order assigned to delivery person</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-blue-100 rounded-lg">
                                <span class="w-3 h-3 bg-blue-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-blue-800">Picked Up</span>
                                    <p class="text-blue-600 text-sm">Order collected from restaurant</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-yellow-100 rounded-lg">
                                <span class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-yellow-800">In Transit</span>
                                    <p class="text-yellow-600 text-sm">On the way to customer</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-green-100 rounded-lg">
                                <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-green-800">Delivered</span>
                                    <p class="text-green-600 text-sm">Successfully delivered to customer</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-red-100 rounded-lg">
                                <span class="w-3 h-3 bg-red-500 rounded-full mr-3"></span>
                                <div>
                                    <span class="font-semibold text-red-800">Failed</span>
                                    <p class="text-red-600 text-sm">Delivery unsuccessful</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cash Custody Management -->
        <div id="cash-custody" class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-money-bill-wave mr-3 text-red-600"></i>
                Cash Custody & Payment Collection (عهدة نقود)
            </h2>
            
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Payment Collection System</h3>
                <div class="bg-red-50 border-l-4 border-red-400 p-6 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-shield-alt text-red-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-lg font-medium text-red-800">Secure Cash Management</h4>
                            <p class="text-red-700 mt-2">
                                Delivery personnel collect order payments and maintain cash custody (عهدة نقود) with full tracking and accountability. 
                                The system ensures secure handling of cash transactions and provides detailed reconciliation reports.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Payment Methods</h4>
                        <div class="space-y-4">
                            <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                                <h5 class="font-semibold text-green-800 mb-2">
                                    <i class="fas fa-money-bill mr-2"></i>
                                    Cash on Delivery (COD)
                                </h5>
                                <ul class="text-green-700 text-sm space-y-1">
                                    <li>• Delivery person collects exact order amount</li>
                                    <li>• Change calculation and management</li>
                                    <li>• Receipt generation and customer copy</li>
                                    <li>• Real-time cash tracking in system</li>
                                </ul>
                            </div>
                            
                            <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                <h5 class="font-semibold text-blue-800 mb-2">
                                    <i class="fas fa-credit-card mr-2"></i>
                                    Digital Payments
                                </h5>
                                <ul class="text-blue-700 text-sm space-y-1">
                                    <li>• Mobile payment apps integration</li>
                                    <li>• Card payment terminals (if available)</li>
                                    <li>• QR code payment options</li>
                                    <li>• Automatic payment confirmation</li>
                                </ul>
                            </div>
                            
                            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                                <h5 class="font-semibold text-purple-800 mb-2">
                                    <i class="fas fa-gift mr-2"></i>
                                    Tips & Gratuity
                                </h5>
                                <ul class="text-purple-700 text-sm space-y-1">
                                    <li>• Customer tip collection</li>
                                    <li>• Digital tip options</li>
                                    <li>• Tip distribution tracking</li>
                                    <li>• Performance-based incentives</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-xl font-semibold mb-4 text-gray-700">Cash Custody Features</h4>
                        <div class="space-y-4">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">Daily Cash Management</h5>
                                <ul class="text-gray-600 text-sm space-y-1">
                                    <li>• Opening cash balance assignment</li>
                                    <li>• Real-time collection tracking</li>
                                    <li>• End-of-day reconciliation</li>
                                    <li>• Cash deposit and handover</li>
                                </ul>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">Security & Accountability</h5>
                                <ul class="text-gray-600 text-sm space-y-1">
                                    <li>• Digital signatures for cash handover</li>
                                    <li>• Photo documentation of receipts</li>
                                    <li>• GPS-stamped collection records</li>
                                    <li>• Discrepancy reporting system</li>
                                </ul>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="font-semibold text-gray-800 mb-2">Reporting & Analytics</h5>
                                <ul class="text-gray-600 text-sm space-y-1">
                                    <li>• Daily cash collection reports</li>
                                    <li>• Personnel performance metrics</li>
                                    <li>• Payment method analytics</li>
                                    <li>• Audit trail maintenance</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cash Flow Process -->
            <div class="mt-8">
                <h4 class="text-xl font-semibold mb-4 text-gray-700">Cash Flow Process</h4>
                <div class="bg-gradient-to-r from-red-50 to-orange-50 rounded-lg p-6">
                    <div class="flex flex-wrap justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white font-bold mb-2">1</div>
                            <h5 class="font-semibold text-gray-800">Cash Assignment</h5>
                            <p class="text-gray-600 text-sm">Initial cash for change</p>
                        </div>
                        
                        <div class="hidden md:block text-gray-400">
                            <i class="fas fa-arrow-right text-2xl"></i>
                        </div>
                        
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mb-2">2</div>
                            <h5 class="font-semibold text-gray-800">Order Collection</h5>
                            <p class="text-gray-600 text-sm">Payment from customers</p>
                        </div>
                        
                        <div class="hidden md:block text-gray-400">
                            <i class="fas fa-arrow-right text-2xl"></i>
                        </div>
                        
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold mb-2">3</div>
                            <h5 class="font-semibold text-gray-800">Real-time Tracking</h5>
                            <p class="text-gray-600 text-sm">System updates</p>
                        </div>
                        
                        <div class="hidden md:block text-gray-400">
                            <i class="fas fa-arrow-right text-2xl"></i>
                        </div>
                        
                        <div class="flex flex-col items-center text-center">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold mb-2">4</div>
                            <h5 class="font-semibold text-gray-800">End-of-Day</h5>
                            <p class="text-gray-600 text-sm">Cash reconciliation</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Reference -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-code mr-3 text-indigo-600"></i>
                Complete API Reference
            </h2>
            
            <!-- Personnel Management Endpoints -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Personnel Management</h3>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/personnel</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Get all delivery personnel with availability status</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": [
    {
      "id": 1,
      "user_id": 15,
      "branch_id": 1,
      "license_number": "DL123456",
      "vehicle_type": "motorcycle",
      "vehicle_plate_number": "ABC-123",
      "status": "active",
      "current_latitude": 24.7136,
      "current_longitude": 46.6753,
      "max_concurrent_deliveries": 3,
      "delivery_radius_km": 10.00,
      "rating": 4.85,
      "total_deliveries": 245,
      "total_earnings": 1250.50,
      "is_verified": true,
      "user": {
        "name": "Ahmed Ali",
        "phone": "+966501234567"
      }
    }
  ]
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/personnel</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Create new delivery personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "user_id": 15,
  "branch_id": 1,
  "license_number": "DL123456",
  "vehicle_type": "motorcycle",
  "vehicle_plate_number": "ABC-123",
  "vehicle_model": "Honda CB150",
  "max_concurrent_deliveries": 3,
  "delivery_radius_km": 10.00,
  "working_hours": {
    "monday": {"start": "09:00", "end": "22:00"},
    "tuesday": {"start": "09:00", "end": "22:00"}
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">PUT</span>
                            <code class="text-sm">/api/delivery/personnel/{id}</code>
                        </div>
                        <p class="text-gray-600 text-sm">Update delivery personnel information</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "license_number": "DL987654",
  "vehicle_type": "car",
  "vehicle_plate_number": "XYZ-789",
  "max_concurrent_deliveries": 2,
  "delivery_radius_km": 15.00,
  "status": "inactive"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 1,
    "user_id": 15,
    "branch_id": 1,
    "license_number": "DL987654",
    "vehicle_type": "car",
    "vehicle_plate_number": "XYZ-789",
    "status": "inactive",
    "current_latitude": 24.7136,
    "current_longitude": 46.6753,
    "max_concurrent_deliveries": 2,
    "delivery_radius_km": 15.00,
    "rating": 4.85,
    "total_deliveries": 245,
    "total_earnings": 1250.50,
    "is_verified": true
  }
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
            <!-- Assignment Endpoints -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Delivery Assignments</h3>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/assignments/assign</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Manually assign order to delivery personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "order_id": 456,
  "personnel_id": 123,
  "delivery_zone_id": 5
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 789,
    "order_id": 456,
    "delivery_personnel_id": 123,
    "delivery_zone_id": 5,
    "status": "assigned",
    "assigned_at": "2024-01-15T10:30:00Z",
    "distance_km": 3.5,
    "estimated_duration_minutes": 25,
    "delivery_fee_earned": 7.00
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/assignments/auto-assign</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Automatically assign order to best available personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "order_id": 456
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 790,
    "order_id": 456,
    "delivery_personnel_id": 124,
    "delivery_zone_id": 5,
    "status": "assigned",
    "assigned_at": "2024-01-15T10:35:00Z",
    "distance_km": 4.2,
    "estimated_duration_minutes": 30,
    "delivery_fee_earned": 8.50
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">PUT</span>
                            <code class="text-sm">/api/delivery/assignments/{id}/status</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Update delivery status</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "status": "delivered",
  "delivery_proof": {
    "signature": "base64_signature_image",
    "photo": "base64_delivery_photo"
  },
  "delivery_notes": "Delivered to customer at door"
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
            <!-- Tracking Endpoints -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Location Tracking</h3>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/tracking/{assignmentId}/location</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Update delivery personnel location</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "latitude": 24.7136,
  "longitude": 46.6753,
  "accuracy": 5.0,
  "speed": 45.5,
  "bearing": 180
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/tracking/{assignmentId}/tracking</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Get delivery tracking data</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "assignment_id": 789,
    "current_location": {
      "latitude": 24.7136,
      "longitude": 46.6753,
      "recorded_at": "2024-01-15T10:45:00Z"
    },
    "tracking_history": [
      {
        "latitude": 24.7100,
        "longitude": 46.6700,
        "recorded_at": "2024-01-15T10:30:00Z"
      }
    ],
    "estimated_arrival": "2024-01-15T11:00:00Z"
  }
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
            <!-- Zones Management -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Zones Management</h3>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/zones</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get all delivery zones</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{ 
  "data": [
    {
      "id": 1,
      "branch_id": 1,
      "name": "Downtown",
      "description": "Central business district delivery zone",
      "polygon_coordinates": {"type": "Polygon", "coordinates": [[[10,20],[10,30],[20,30],[20,20],[10,20]]]}, 
      "delivery_fee": 5.00,
      "minimum_order_amount": 20.00,
      "estimated_delivery_time_minutes": 30,
      "is_active": true,
      "priority": 1
    },
    {
      "id": 2,
      "branch_id": 1,
      "name": "Suburbia",
      "description": "Residential delivery zone",
      "polygon_coordinates": {"type": "Polygon", "coordinates": [[[30,40],[30,50],[40,50],[40,40],[30,40]]]}, 
      "delivery_fee": 7.50,
      "minimum_order_amount": 25.00,
      "estimated_delivery_time_minutes": 45,
      "is_active": true,
      "priority": 2
    }
  ]
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/zones</code>
                        </div>
                        <p class="text-gray-600 text-sm">Create new delivery zone</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "branch_id": 1,
  "name": "New Zone",
  "description": "Description for new zone",
  "polygon_coordinates": {"type": "Polygon", "coordinates": [[[10,20],[10,30],[20,30],[20,20],[10,20]]]}, 
  "delivery_fee": 6.00,
  "minimum_order_amount": 15.00,
  "estimated_delivery_time_minutes": 35,
  "is_active": true,
  "priority": 3
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 3,
    "branch_id": 1,
    "name": "New Zone",
    "description": "Description for new zone",
    "polygon_coordinates": {"type": "Polygon", "coordinates": [[[10,20],[10,30],[20,30],[20,20],[10,20]]]}, 
    "delivery_fee": 6.00,
    "minimum_order_amount": 15.00,
    "estimated_delivery_time_minutes": 35,
    "is_active": true,
    "priority": 3
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/zones/check</code>
                        </div>
                        <p class="text-gray-600 text-sm">Check if address is within delivery zone</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "latitude": 24.7136,
  "longitude": 46.6753
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "is_within_zone": true,
    "zone_id": 1,
    "zone_name": "Downtown",
    "delivery_fee": 5.00,
    "estimated_delivery_time_minutes": 30
  }
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
            <!-- Reviews & Tips -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Reviews & Tips</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold text-gray-700">Reviews</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                                <code class="text-sm">/api/delivery/reviews</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get delivery reviews</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": [
    {
      "id": 1,
      "assignment_id": 789,
      "customer_id": 101,
      "rating": 5,
      "comment": "Excellent service!",
      "review_categories": ["politeness", "speed"],
      "is_anonymous": false,
      "is_verified": true,
      "created_at": "2024-01-15T11:05:00Z"
    }
  ]
}</pre>
                            </div>
                        </details>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                                <code class="text-sm">/api/delivery/reviews</code>
                        </div>
                        <p class="text-gray-600 text-sm">Create delivery review</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "assignment_id": 789,
  "rating": 4,
  "comment": "Good service, a bit slow.",
  "review_categories": ["speed"],
  "is_anonymous": false
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 2,
    "assignment_id": 789,
    "customer_id": 101, 
    "rating": 4,
    "comment": "Good service, a bit slow.",
    "review_categories": ["speed"],
    "is_anonymous": false,
    "is_verified": false,
    "created_at": "2024-01-15T11:10:00Z"
  }
}</pre>
                            </div>
                        </details>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                                <code class="text-sm">/api/delivery/reviews/personnel/{personnelId}</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get reviews for specific personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": [
    {
      "id": 1,
      "assignment_id": 789,
      "customer_id": 101,
      "rating": 5,
      "comment": "Excellent service!",
      "review_categories": ["politeness", "speed"],
      "is_anonymous": false,
      "is_verified": true,
      "created_at": "2024-01-15T11:05:00Z"
    },
    {
      "id": 3,
      "assignment_id": 790,
      "customer_id": 102,
      "rating": 4,
      "comment": "Fast delivery.",
      "review_categories": ["speed"],
      "is_anonymous": true,
      "is_verified": true,
      "created_at": "2024-01-15T12:00:00Z"
    }
  ]
}</pre>
                            </div>
                        </details>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold text-gray-700">Tips</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                                <code class="text-sm">/api/delivery/tips</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get delivery tips</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": [
    {
      "id": 1,
      "assignment_id": 789,
      "customer_id": 101,
      "amount": 5.00,
      "payment_method": "credit_card",
      "transaction_reference": "txn_12345",
      "status": "completed",
      "note": "Great service!",
      "created_at": "2024-01-15T11:05:00Z"
    }
  ]
}</pre>
                            </div>
                        </details>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                                <code class="text-sm">/api/delivery/tips</code>
                        </div>
                        <p class="text-gray-600 text-sm">Create delivery tip</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "assignment_id": 789,
  "amount": 5.00,
  "payment_method": "credit_card",
  "note": "Thanks for the quick delivery!"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 2,
    "assignment_id": 789,
    "customer_id": 101, 
    "amount": 5.00,
    "payment_method": "credit_card",
    "transaction_reference": null,
    "status": "pending",
    "note": "Thanks for the quick delivery!",
    "created_at": "2024-01-15T11:15:00Z"
  }
}</pre>
                            </div>
                        </details>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                                <code class="text-sm">/api/delivery/tips/{id}/process-payment</code>
                        </div>
                        <p class="text-gray-600 text-sm">Process tip payment</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "transaction_reference": "txn_67890",
  "status": "completed"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 2,
    "assignment_id": 789,
    "customer_id": 101, 
    "amount": 5.00,
    "payment_method": "credit_card",
    "transaction_reference": "txn_67890",
    "status": "completed",
    "note": "Thanks for the quick delivery!",
    "created_at": "2024-01-15T11:15:00Z"
  }
}</pre>
                            </div>
                        </details>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analytics & Reports -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Analytics & Reports</h3>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/analytics/stats</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get delivery statistics</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "total_deliveries": 1250,
    "completed_deliveries": 1200,
    "failed_deliveries": 50,
    "average_delivery_time_minutes": 35.5,
    "average_rating": 4.7,
    "total_earnings": 15000.00,
    "total_tips": 1200.00
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/analytics/personnel/{personnelId}/performance</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get personnel performance analytics</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "personnel_id": 123,
    "total_deliveries": 245,
    "completed_deliveries": 240,
    "failed_deliveries": 5,
    "average_delivery_time_minutes": 30.2,
    "average_rating": 4.85,
    "total_earnings": 1250.50,
    "total_tips": 120.00,
    "performance_score": 92.5
  }
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
            <!-- Customer Routes -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Customer Routes</h3>
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                    <p class="text-blue-700 text-sm"><strong>Authentication:</strong> Requires customer authentication via Sanctum</p>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/customer/my-deliveries</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get customer's delivery history</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": [
    {
      "id": 789,
      "order_id": 456,
      "status": "delivered",
      "delivered_at": "2024-01-15T11:00:00Z",
      "delivery_personnel": {
        "id": 123,
        "name": "Ahmed Ali",
        "rating": 4.85
      }
    }
  ]
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/customer/tracking/{assignmentId}</code>
                        </div>
                        <p class="text-gray-600 text-sm">Track a specific delivery</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "assignment_id": 789,
    "status": "in_transit",
    "current_location": {
      "latitude": 34.052235,
      "longitude": -118.243683
    },
    "estimated_delivery_time": "2024-01-15T11:30:00Z",
    "delivery_personnel": {
      "id": 123,
      "name": "Ahmed Ali",
      "phone": "+966501234567"
    }
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/customer/reviews</code>
                        </div>
                        <p class="text-gray-600 text-sm">Create delivery review</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "assignment_id": 789,
  "rating": 5,
  "comment": "Excellent service! Fast and friendly."
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 3,
    "assignment_id": 789,
    "customer_id": 101,
    "rating": 5,
    "comment": "Excellent service! Fast and friendly.",
    "created_at": "2024-01-15T11:05:00Z"
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/customer/tips</code>
                        </div>
                        <p class="text-gray-600 text-sm">Create delivery tip</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "assignment_id": 789,
  "amount": 5.00,
  "payment_method": "credit_card",
  "note": "Thanks for the quick delivery!"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 2,
    "assignment_id": 789,
    "customer_id": 101,
    "amount": 5.00,
    "payment_method": "credit_card",
    "transaction_reference": null,
    "status": "pending",
    "note": "Thanks for the quick delivery!",
    "created_at": "2024-01-15T11:10:00Z"
  }
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
            <!-- Personnel App Routes -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Personnel App Routes</h3>
                <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
                    <p class="text-green-700 text-sm"><strong>Authentication:</strong> Requires delivery personnel authentication</p>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/personnel-app/my-assignments</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get personnel's current assignments</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": [
    {
      "id": 789,
      "order_id": 456,
      "status": "assigned",
      "pickup_location": {
        "address": "123 Main St, City, Country",
        "latitude": 34.052235,
        "longitude": -118.243683
      },
      "delivery_location": {
        "address": "456 Oak Ave, City, Country",
        "latitude": 34.052235,
        "longitude": -118.243683
      },
      "customer_details": {
        "name": "Jane Doe",
        "phone": "+966509876543"
      },
      "estimated_pickup_time": "2024-01-15T10:00:00Z",
      "estimated_delivery_time": "2024-01-15T10:45:00Z"
    }
  ]
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">PUT</span>
                            <code class="text-sm">/api/delivery/personnel-app/assignments/{id}/status</code>
                        </div>
                        <p class="text-gray-600 text-sm">Update assignment status</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "status": "picked_up"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 789,
    "order_id": 456,
    "status": "picked_up",
    "pickup_location": {
      "address": "123 Main St, City, Country",
      "latitude": 34.052235,
      "longitude": -118.243683
    },
    "delivery_location": {
      "address": "456 Oak Ave, City, Country",
      "latitude": 34.052235,
      "longitude": -118.243683
    },
    "customer_details": {
      "name": "Jane Doe",
      "phone": "+966509876543"
    },
    "estimated_pickup_time": "2024-01-15T10:00:00Z",
    "estimated_delivery_time": "2024-01-15T10:45:00Z"
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/personnel-app/assignments/{assignmentId}/location</code>
                        </div>
                        <p class="text-gray-600 text-sm">Update current location</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "latitude": 34.052235,
  "longitude": -118.243683
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "message": "Location updated successfully."
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/personnel-app/my-performance</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get performance metrics</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "total_deliveries": 245,
    "completed_deliveries": 240,
    "failed_deliveries": 5,
    "average_delivery_time_minutes": 30.2,
    "average_rating": 4.85,
    "performance_score": 92.5
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/personnel-app/my-earnings</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get earnings summary</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "total_earnings": 1250.50,
    "total_tips": 120.00,
    "weekly_earnings": [
      {
        "week": "2024-W01",
        "earnings": 300.00,
        "tips": 30.00
      },
      {
        "week": "2024-W02",
        "earnings": 450.50,
        "tips": 45.00
      }
    ]
  }
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
            <!-- Admin Routes -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Admin Routes</h3>
                <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                    <p class="text-red-700 text-sm"><strong>Authentication:</strong> Requires admin authentication</p>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/admin/dashboard</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get admin dashboard data</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "total_deliveries_today": 50,
    "pending_assignments": 10,
    "active_personnel": 25,
    "revenue_today": 500.00,
    "average_delivery_time_today": 32.5,
    "delivery_status_distribution": {
      "completed": 40,
      "in_transit": 8,
      "failed": 2
    }
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/admin/reports/{period}</code>
                        </div>
                        <p class="text-gray-600 text-sm">Get delivery reports (daily, weekly, monthly)</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response (daily example):</strong></div>
                                <pre>{
  "data": {
    "period": "daily",
    "date": "2024-01-15",
    "total_deliveries": 150,
    "completed_deliveries": 145,
    "failed_deliveries": 5,
    "total_revenue": 1500.00,
    "average_delivery_time_minutes": 30.0
  }
}</pre>
                                <div class="mb-2 mt-3"><strong>Response (weekly example):</strong></div>
                                <pre>{
  "data": {
    "period": "weekly",
    "week": "2024-W02",
    "total_deliveries": 1000,
    "completed_deliveries": 980,
    "failed_deliveries": 20,
    "total_revenue": 10000.00,
    "average_delivery_time_minutes": 33.5
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/admin/personnel</code>
                        </div>
                        <p class="text-gray-600 text-sm">Retrieve a list of all delivery personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>[
  {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "status": "active",
    "current_location": {
      "latitude": 34.052235,
      "longitude": -118.243683
    },
    "vehicle_type": "motorcycle",
    "created_at": "2023-01-01T10:00:00Z",
    "updated_at": "2024-01-15T14:30:00Z"
  },
  {
    "id": 2,
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+1987654321",
    "status": "inactive",
    "current_location": null,
    "vehicle_type": "car",
    "created_at": "2023-02-01T11:00:00Z",
    "updated_at": "2024-01-10T09:00:00Z"
  }
]</pre>
                            </div>
                        </details>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/admin/personnel/{id}/suspend</code>
                        </div>
                        <p class="text-gray-600 text-sm">Retrieve details of a specific delivery personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "status": "active",
  "current_location": {
    "latitude": 34.052235,
    "longitude": -118.243683
  },
  "vehicle_type": "motorcycle",
  "created_at": "2023-01-01T10:00:00Z",
  "updated_at": "2024-01-15T14:30:00Z"
}</pre>
                            </div>
                        </details>
                        <p class="text-gray-600 text-sm">Suspend delivery personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "message": "Personnel suspended successfully."
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/admin/personnel/{id}/activate</code>
                        </div>
                        <p class="text-gray-600 text-sm">Update details of a specific delivery personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "name": "John Doe Updated",
  "email": "<EMAIL>",
  "phone": "+1122334455",
  "status": "active",
  "vehicle_type": "car"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "id": 1,
  "name": "John Doe Updated",
  "email": "<EMAIL>",
  "phone": "+1122334455",
  "status": "active",
  "current_location": {
    "latitude": 34.052235,
    "longitude": -118.243683
  },
  "vehicle_type": "car",
  "created_at": "2023-01-01T10:00:00Z",
  "updated_at": "2024-01-15T15:00:00Z"
}</pre>
                            </div>
                        </details>
                        <p class="text-gray-600 text-sm">Activate delivery personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "message": "Personnel activated successfully."
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">DELETE</span>
                            <code class="text-sm">/api/delivery/admin/personnel/{id}</code>
                        </div>
                        <p class="text-gray-600 text-sm">Delete a delivery personnel record</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "message": "Personnel deleted successfully."
}</pre>
                            </div>
                        </details>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/admin/assignments</code>
                        </div>
                        <p class="text-gray-600 text-sm">Retrieve a list of all delivery assignments</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>[
  {
    "id": 1,
    "order_id": 101,
    "personnel_id": 1,
    "status": "assigned",
    "pickup_address": "123 Main St, City, Country",
    "delivery_address": "456 Oak Ave, City, Country",
    "assigned_at": "2024-01-15T10:00:00Z",
    "picked_up_at": null,
    "delivered_at": null
  },
  {
    "id": 2,
    "order_id": 102,
    "personnel_id": 2,
    "status": "in_progress",
    "pickup_address": "789 Pine St, City, Country",
    "delivery_address": "101 Elm St, City, Country",
    "assigned_at": "2024-01-15T11:00:00Z",
    "picked_up_at": "2024-01-15T11:15:00Z",
    "delivered_at": null
  }
]</pre>
                            </div>
                        </details>
                    </div>
            <!-- Utility & Public Routes -->

            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Utility & Public Routes</h3>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/utils/calculate-fee</code>
                        </div>
                        <p class="text-gray-600 text-sm">Retrieve details of a specific delivery assignment</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "id": 1,
  "order_id": 101,
  "personnel_id": 1,
  "status": "assigned",
  "pickup_address": "123 Main St, City, Country",
  "delivery_address": "456 Oak Ave, City, Country",
  "assigned_at": "2024-01-15T10:00:00Z",
  "picked_up_at": null,
  "delivered_at": null
}</pre>
                            </div>
                        </details>
                        <p class="text-gray-600 text-sm">Calculate delivery fee</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "pickup_latitude": 34.052235,
  "pickup_longitude": -118.243683,
  "delivery_latitude": 34.052235,
  "delivery_longitude": -118.243683,
  "distance_unit": "km"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "fee": 15.00,
    "currency": "SAR",
    "distance": 10.5,
    "distance_unit": "km"
  }
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/utils/available-personnel</code>
                        </div>
                        <p class="text-gray-600 text-sm">Reassign a delivery assignment to a different personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "new_personnel_id": 2
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "id": 1,
  "order_id": 101,
  "personnel_id": 2,
  "status": "assigned",
  "pickup_address": "123 Main St, City, Country",
  "delivery_address": "456 Oak Ave, City, Country",
  "assigned_at": "2024-01-15T10:00:00Z",
  "picked_up_at": null,
  "delivered_at": null
}</pre>
                            </div>
                        </details>
                        <p class="text-gray-600 text-sm">Get available personnel</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": [
    {
      "id": 123,
      "name": "Ahmed Ali",
      "current_location": {
        "latitude": 34.052235,
        "longitude": -118.243683
      },
      "status": "available",
      "last_active": "2024-01-15T11:30:00Z"
    },
    {
      "id": 124,
      "name": "Fatima Zahra",
      "current_location": {
        "latitude": 34.052235,
        "longitude": -118.243683
      },
      "status": "available",
      "last_active": "2024-01-15T11:25:00Z"
    }
  ]
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/admin/assignments/{id}/cancel</code>
                        </div>
                        <p class="text-gray-600 text-sm">Cancel a delivery assignment</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "message": "Assignment cancelled successfully."
}</pre>
                            </div>
                        </details>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/public/check-zone</code>
                        </div>
                        <p class="text-gray-600 text-sm">Check delivery zone (public)</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "latitude": 34.052235,
  "longitude": -118.243683
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "in_zone": true,
    "zone_name": "Downtown Delivery Zone"
  }
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/admin/settings</code>
                        </div>
                        <p class="text-gray-600 text-sm">Retrieve delivery module settings</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "base_delivery_fee": 5.00,
    "fee_per_km": 1.50,
    "max_delivery_distance_km": 50,
    "auto_assign_enabled": true,
    "personnel_tracking_interval_seconds": 30
  }
}</pre>
                            </div>
                        </details>
                    </div>
            <!-- Webhook Routes -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Webhook Routes</h3>
                <div class="bg-purple-50 border-l-4 border-purple-400 p-4 mb-4">
                    <p class="text-purple-700 text-sm"><strong>Note:</strong> These routes are for external integrations and webhooks</p>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/webhooks/payment-status</code>
                        </div>
                        <p class="text-gray-600 text-sm">Update delivery module settings</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "base_delivery_fee": 6.00,
  "fee_per_km": 1.75,
  "auto_assign_enabled": false
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "base_delivery_fee": 6.00,
    "fee_per_km": 1.75,
    "max_delivery_distance_km": 50,
    "auto_assign_enabled": false,
    "personnel_tracking_interval_seconds": 30
  }
}</pre>
                            </div>
                        </details>
                        <p class="text-gray-600 text-sm">Handle payment status webhooks</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "event": "payment_updated",
  "data": {
    "transaction_id": "txn_12345",
    "order_id": 678,
    "status": "completed",
    "amount": 25.50,
    "currency": "SAR"
  }
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "message": "Payment status webhook received and processed."
}</pre>
                            </div>
                        </details>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">POST</span>
                            <code class="text-sm">/api/delivery/webhooks/location-update</code>
                        </div>
                        <p class="text-gray-600 text-sm">Handle location update webhooks</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "event": "location_updated",
  "data": {
    "assignment_id": 789,
    "personnel_id": 123,
    "latitude": 34.052235,
    "longitude": -118.243683,
    "timestamp": "2024-01-15T11:45:00Z"
  }
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "message": "Location update webhook received and processed."
}</pre>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
            
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">GET</span>
                            <code class="text-sm">/api/delivery/admin/logs</code>
                        </div>
                        <p class="text-gray-600 text-sm">Retrieve delivery module logs</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Response:</strong></div>
                                <pre>[
  {
    "timestamp": "2024-01-15T10:00:00Z",
    "level": "INFO",
    "message": "Delivery assignment created for Order #101",
    "context": {
      "order_id": 101,
      "personnel_id": 1
    }
  },
  {
    "timestamp": "2024-01-15T10:05:00Z",
    "level": "WARNING",
    "message": "Personnel #2 is offline, reassigning Order #102",
    "context": {
      "order_id": 102,
      "personnel_id": 2
    }
  }
]</pre>
                            </div>
                        </details>
                    </div>
            <!-- Order Assignment from Orders Module -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Order Assignment (Orders Module Integration)</h3>
                <div class="bg-blue-50 border-l-4 border-blue-400 p-6 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-lg font-medium text-blue-800">Integration with Orders Module</h4>
                            <p class="text-blue-700 mt-2">
                                The delivery assignment process is integrated with the Orders module. When an order is created with type 'delivery', 
                                it can be automatically or manually assigned to delivery personnel through the Delivery module's assignment system.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold mr-2">PATCH</span>
                            <code class="text-sm">/api/orders/{id}/status</code>
                        </div>
                        <p class="text-gray-600 text-sm mb-2">Update order status (triggers delivery assignment when status = 'confirmed')</p>
                        <details class="mt-2">
                            <summary class="cursor-pointer text-blue-600 text-sm">View Request/Response</summary>
                            <div class="mt-2 bg-gray-900 rounded p-3 text-white text-xs">
                                <div class="mb-2"><strong>Request:</strong></div>
                                <pre>{
  "status": "confirmed"
}</pre>
                                <div class="mb-2 mt-3"><strong>Response:</strong></div>
                                <pre>{
  "data": {
    "id": 456,
    "customer_id": 101,
    "status": "confirmed",
    "order_type": "delivery",
    "delivery_address": "456 Oak Ave, City, Country",
    "total_amount": 75.00,
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:15:00Z"
  }
}</pre>
                            </div>
                        </details>

                    </div>
                </div>
            </div>
        </div>

        <!-- Best Practices -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-lightbulb mr-3 text-yellow-600"></i>
                Best Practices & Tips
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-700">Operational Excellence</h3>
                    <div class="space-y-4">
                        <div class="bg-green-50 border-l-4 border-green-400 p-4">
                            <h4 class="font-semibold text-green-800 mb-2">Capacity Planning</h4>
                            <p class="text-green-700 text-sm">Set realistic concurrent delivery limits based on vehicle type and area coverage. Monitor performance and adjust accordingly.</p>
                        </div>
                        
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                            <h4 class="font-semibold text-blue-800 mb-2">Route Optimization</h4>
                            <p class="text-blue-700 text-sm">Use the system's route suggestions to minimize delivery time and fuel costs. Group nearby orders when possible.</p>
                        </div>
                        
                        <div class="bg-purple-50 border-l-4 border-purple-400 p-4">
                            <h4 class="font-semibold text-purple-800 mb-2">Customer Communication</h4>
                            <p class="text-purple-700 text-sm">Keep customers informed with real-time updates and accurate ETAs. Use the tracking system for transparency.</p>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-700">Security & Compliance</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 border-l-4 border-red-400 p-4">
                            <h4 class="font-semibold text-red-800 mb-2">Cash Security</h4>
                            <p class="text-red-700 text-sm">Implement daily cash limits, secure handover procedures, and regular reconciliation to minimize risks.</p>
                        </div>
                        
                        <div class="bg-orange-50 border-l-4 border-orange-400 p-4">
                            <h4 class="font-semibold text-orange-800 mb-2">Documentation</h4>
                            <p class="text-orange-700 text-sm">Maintain proper records of all transactions, delivery proofs, and customer interactions for audit purposes.</p>
                        </div>
                        
                        <div class="bg-indigo-50 border-l-4 border-indigo-400 p-4">
                            <h4 class="font-semibold text-indigo-800 mb-2">Performance Monitoring</h4>
                            <p class="text-indigo-700 text-sm">Regularly review delivery metrics, customer feedback, and personnel performance to identify improvement opportunities.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Services and Helpers -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 text-gray-800">
                <i class="fas fa-cogs mr-3 text-indigo-600"></i>
                Services and Helpers
            </h2>
            
            <!-- Delivery Service -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">DeliveryService</h3>
                <div class="bg-gray-50 rounded-lg p-6 mb-4">
                    <p class="text-gray-600 mb-4">
                        The <code class="bg-gray-200 px-2 py-1 rounded">DeliveryService</code> class provides core functionality for managing delivery operations, 
                        including assignment logic, status updates, and performance calculations.
                    </p>
                    <p class="text-sm text-gray-500 mb-4">
                        <strong>Location:</strong> <code>modules/Delivery/Services/DeliveryService.php</code>
                    </p>
                </div>
                
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-function mr-2 text-blue-600"></i>
                            assignDelivery()
                        </h4>
                        <p class="text-gray-600 text-sm mb-2">Manually assigns a delivery order to a specific personnel.</p>
                        <div class="bg-gray-900 rounded p-3 text-white text-xs">
                            <pre>public function assignDelivery($orderId, $personnelId, $zoneId = null)
{
    // Validates personnel availability
    // Creates delivery assignment record
    // Updates order status
    // Sends notifications
    // Returns assignment details
}</pre>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-function mr-2 text-green-600"></i>
                            autoAssignDelivery()
                        </h4>
                        <p class="text-gray-600 text-sm mb-2">Automatically assigns delivery to the best available personnel based on location, capacity, and performance.</p>
                        <div class="bg-gray-900 rounded p-3 text-white text-xs">
                            <pre>public function autoAssignDelivery($orderId)
{
    // Finds best available personnel
    // Considers distance, capacity, rating
    // Creates assignment automatically
    // Returns assignment or null if no personnel available
}</pre>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-function mr-2 text-yellow-600"></i>
                            updateDeliveryStatus()
                        </h4>
                        <p class="text-gray-600 text-sm mb-2">Updates the status of a delivery assignment with optional proof and notes.</p>
                        <div class="bg-gray-900 rounded p-3 text-white text-xs">
                            <pre>public function updateDeliveryStatus($assignmentId, $status, $data = [])
{
    // Validates status transition
    // Updates assignment record
    // Handles delivery proof
    // Triggers status-specific actions
    // Updates personnel metrics
}</pre>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-function mr-2 text-purple-600"></i>
                            findBestPersonnel()
                        </h4>
                        <p class="text-gray-600 text-sm mb-2">Finds the best available delivery personnel for a given order based on multiple criteria.</p>
                        <div class="bg-gray-900 rounded p-3 text-white text-xs">
                            <pre>public function findBestPersonnel($order)
{
    // Filters available personnel
    // Calculates distance scores
    // Considers capacity and rating
    // Returns best match or null
}</pre>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-function mr-2 text-red-600"></i>
                            calculateDeliveryDistance()
                        </h4>
                        <p class="text-gray-600 text-sm mb-2">Calculates the distance between two geographical points using the Haversine formula.</p>
                        <div class="bg-gray-900 rounded p-3 text-white text-xs">
                            <pre>public function calculateDeliveryDistance($lat1, $lng1, $lat2, $lng2)
{
    // Uses Haversine formula
    // Returns distance in kilometers
    // Accounts for Earth's curvature
}</pre>
                        </div>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold mb-2 text-gray-700 flex items-center">
                            <i class="fas fa-function mr-2 text-teal-600"></i>
                            estimateDeliveryDuration()
                        </h4>
                        <p class="text-gray-600 text-sm mb-2">Estimates delivery duration based on distance, traffic conditions, and vehicle type.</p>
                        <div class="bg-gray-900 rounded p-3 text-white text-xs">
                            <pre>public function estimateDeliveryDuration($distance, $vehicleType = 'motorcycle')
{
    // Considers vehicle speed
    // Adds buffer time
    // Accounts for traffic patterns
    // Returns estimated minutes
}</pre>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Middleware Information -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Middleware</h3>
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-lg font-medium text-yellow-800">No Custom Middleware</h4>
                            <p class="text-yellow-700 mt-2">
                                The Delivery module currently does not implement any custom middleware. 
                                It relies on Laravel's built-in middleware and any global middleware defined in the application.
                                Authentication and authorization are handled through standard Laravel middleware.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Integration Notes -->
            <div class="mb-8">
                <h3 class="text-2xl font-semibold mb-4 text-gray-700">Integration & Usage</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h4 class="text-lg font-semibold mb-3 text-blue-800 flex items-center">
                            <i class="fas fa-plug mr-2"></i>
                            Service Integration
                        </h4>
                        <ul class="text-blue-700 text-sm space-y-2">
                            <li>• Inject <code>DeliveryService</code> into controllers</li>
                            <li>• Use dependency injection for clean architecture</li>
                            <li>• Service handles all business logic</li>
                            <li>• Controllers remain thin and focused</li>
                        </ul>
                    </div>
                    
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <h4 class="text-lg font-semibold mb-3 text-green-800 flex items-center">
                            <i class="fas fa-code mr-2"></i>
                            Usage Example
                        </h4>
                        <div class="bg-gray-900 rounded p-3 text-white text-xs">
                            <pre>// In Controller
public function __construct(DeliveryService $deliveryService)
{
    $this->deliveryService = $deliveryService;
}

// Auto-assign delivery
$assignment = $this->deliveryService
    ->autoAssignDelivery($orderId);</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
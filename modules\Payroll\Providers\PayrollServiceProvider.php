<?php

namespace Modules\Payroll\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Payroll\Services\PayrollService;

class PayrollServiceProvider extends ServiceProvider
{
    /**
     * The module namespace to assume when generating URLs to actions.
     *
     * @var string
     */
    protected $moduleNamespace = 'Modules\\Payroll\\Http\\Controllers';

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->loadMigrationsFrom(base_path('modules/Payroll/database/migrations'));
        $this->registerRoutes();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        // Register the PayrollService
        $this->app->singleton(PayrollService::class, function ($app) {
            return new PayrollService();
        });
    }



    /**
     * Register routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        Route::group([
            'middleware' => ['api'],
            'prefix' => 'api',
            'namespace' => $this->moduleNamespace
        ], function () {
            require base_path('modules/Payroll/routes/api.php');
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [
            PayrollService::class,
        ];
    }
}
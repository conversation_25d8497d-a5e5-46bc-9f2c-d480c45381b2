<?php

use Illuminate\Support\Facades\Route;
use Modules\HR\Http\Controllers\HRController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Protected routes requiring authentication
Route::middleware(['auth:sanctum', 'tenant.context'])->group(function () {
    
    // Staff Management
    Route::prefix('hr/staff')->group(function () {
        Route::get('/', [HRController::class, 'index'])->name('hr.staff.index');
        Route::get('/{id}', [HRController::class, 'show'])->name('hr.staff.show');
        Route::get('/{userId}/working-hours', [HRController::class, 'workingHours'])->name('hr.staff.working-hours');
        Route::get('/{id}/calculate-salary', [HRController::class, 'calculateSalary'])->name('hr.staff.calculate-salary');
        Route::get('/{id}/payslips', [HRController::class, 'payslips'])->name('hr.staff.payslips');
        Route::get('/{id}/penalties', [HRController::class, 'penalties'])->name('hr.staff.penalties');
        Route::get('/{id}/performance-metrics', [HRController::class, 'performanceMetrics'])->name('hr.staff.performance-metrics');
    });
    
    // Attendance Management
    Route::prefix('hr')->group(function () {
        Route::post('/check-in', [HRController::class, 'checkIn'])->name('hr.check-in');
        Route::post('/check-out', [HRController::class, 'checkOut'])->name('hr.check-out');
        Route::get('/attendance', [HRController::class, 'attendance'])->name('hr.attendance');
        Route::get('/attendance-stats', [HRController::class, 'attendanceStats'])->name('hr.attendance-stats');
        Route::post('/bulk-attendance', [HRController::class, 'bulkAttendance'])->name('hr.bulk-attendance');
    });
    
    // Schedule Management
    Route::prefix('hr/schedule')->group(function () {
        Route::get('/', [HRController::class, 'schedule'])->name('hr.schedule.index');
    });
    
    // Shift Management
    Route::prefix('hr/shifts')->group(function () {
        Route::get('/', [HRController::class, 'shifts'])->name('hr.shifts.index');
        Route::post('/', [HRController::class, 'createShift'])->name('hr.shifts.store');
        Route::put('/{id}', [HRController::class, 'updateShift'])->name('hr.shifts.update');
        Route::get('/understaffed', [HRController::class, 'understaffedShifts'])->name('hr.shifts.understaffed');
        Route::get('/{shiftId}/available-staff', [HRController::class, 'availableStaff'])->name('hr.shifts.available-staff');
        
        // Shift Assignment
        Route::post('/assign', [HRController::class, 'assignShift'])->name('hr.shifts.assign');
        Route::post('/assignments/{assignmentId}/accept', [HRController::class, 'acceptShift'])->name('hr.shift-assignments.accept');
        Route::post('/assignments/{assignmentId}/decline', [HRController::class, 'declineShift'])->name('hr.shift-assignments.decline');
        Route::post('/assignments/{assignmentId}/request-replacement', [HRController::class, 'requestReplacement'])->name('hr.shift-assignments.request-replacement');
    });
    
    // Shift Types Management
    Route::prefix('hr/shift-types')->group(function () {
        Route::get('/', [HRController::class, 'shiftTypes'])->name('hr.shift-types.index');
        Route::post('/', [HRController::class, 'createShiftType'])->name('hr.shift-types.store');
        Route::put('/{id}', [HRController::class, 'updateShiftType'])->name('hr.shift-types.update');
    });
    
    // Payroll Management
    Route::prefix('hr')->group(function () {
        Route::post('/generate-payslip', [HRController::class, 'generatePayslip'])->name('hr.generate-payslip');
        Route::post('/apply-penalty', [HRController::class, 'applyPenalty'])->name('hr.apply-penalty');
        Route::post('/penalties/{penaltyId}/waive', [HRController::class, 'waivePenalty'])->name('hr.penalties.waive');
        Route::get('/penalty-stats', [HRController::class, 'penaltyStats'])->name('hr.penalty-stats');
    });
    
    // Leave Management
    Route::prefix('hr/leave')->group(function () {
        Route::get('/requests', [HRController::class, 'leaveRequests'])->name('hr.leave.requests');
        Route::post('/requests', [HRController::class, 'submitLeaveRequest'])->name('hr.leave.submit');
        Route::put('/requests/{id}', [HRController::class, 'updateLeaveRequest'])->name('hr.leave.update');
        Route::post('/requests/{id}/cancel', [HRController::class, 'cancelLeaveRequest'])->name('hr.leave.cancel');
    });
});

// Public routes (if any)
Route::prefix('public/staff')->group(function () {
    // Add any public staff-related routes here if needed
    // For example, public shift information for customers
});

<?php

namespace Modules\Tenant\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\TenantSubscription;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Modules\Tenant\Services\SubscriptionService;
use Modules\Tenant\Http\Requests\CreateSubscriptionRequest;
use Modules\Tenant\Http\Requests\UpdateSubscriptionRequest;

class SubscriptionController extends Controller
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Display a listing of subscriptions
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $planId = $request->get('plan_id');
            
            $query = TenantSubscription::with(['tenant', 'plan']);
            
            if ($status) {
                $query->where('status', $status);
            }
            
            if ($planId) {
                $query->where('plan_id', $planId);
            }
            
            $subscriptions = $query->orderBy('created_at', 'desc')->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => $subscriptions,
                'message' => 'Subscriptions retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve subscriptions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created subscription
     */
    public function store(CreateSubscriptionRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $subscription = $this->subscriptionService->createSubscription($request->validated());
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $subscription->load(['tenant', 'plan']),
                'message' => 'Subscription created successfully'
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified subscription
     */
    public function show(TenantSubscription $subscription): JsonResponse
    {
        try {
            $subscription->load(['tenant', 'plan']);
            
            return response()->json([
                'success' => true,
                'data' => $subscription,
                'message' => 'Subscription retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified subscription
     */
    public function update(UpdateSubscriptionRequest $request, TenantSubscription $subscription): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $updatedSubscription = $this->subscriptionService->updateSubscription($subscription, $request->validated());
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $updatedSubscription->load(['tenant', 'plan']),
                'message' => 'Subscription updated successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancel(TenantSubscription $subscription): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $cancelledSubscription = $this->subscriptionService->cancelSubscription($subscription);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $cancelledSubscription,
                'message' => 'Subscription cancelled successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Suspend subscription
     */
    public function suspend(TenantSubscription $subscription): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $suspendedSubscription = $this->subscriptionService->suspendSubscription($subscription);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $suspendedSubscription,
                'message' => 'Subscription suspended successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to suspend subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reactivate subscription
     */
    public function reactivate(TenantSubscription $subscription): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $reactivatedSubscription = $this->subscriptionService->reactivateSubscription($subscription);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $reactivatedSubscription,
                'message' => 'Subscription reactivated successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to reactivate subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upgrade subscription plan
     */
    public function upgrade(Request $request, TenantSubscription $subscription): JsonResponse
    {
        try {
            $request->validate([
                'new_plan_id' => 'required|exists:subscription_plans,id'
            ]);
            
            DB::beginTransaction();
            
            $upgradedSubscription = $this->subscriptionService->upgradeSubscription(
                $subscription, 
                $request->new_plan_id
            );
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $upgradedSubscription,
                'message' => 'Subscription upgraded successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to upgrade subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get subscription usage statistics
     */
    public function usage(TenantSubscription $subscription): JsonResponse
    {
        try {
            $usage = $this->subscriptionService->getSubscriptionUsage($subscription);
            
            return response()->json([
                'success' => true,
                'data' => $usage,
                'message' => 'Subscription usage retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve subscription usage: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available subscription plans
     */
    public function plans(): JsonResponse
    {
        try {
            $plans = SubscriptionPlan::where('is_active', true)
                ->orderBy('monthly_price')
                ->get();
            
            return response()->json([
                'success' => true,
                'data' => $plans,
                'message' => 'Subscription plans retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve subscription plans: ' . $e->getMessage()
            ], 500);
        }
    }
}
<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateDeliveryPersonnelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasPermission('manage_delivery_personnel');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                'integer',
                'exists:users,id',
                Rule::unique('delivery_personnel', 'user_id')
            ],
            'branch_id' => [
                'required',
                'integer',
                'exists:branches,id'
            ],
            'license_number' => [
                'required',
                'string',
                'max:50',
                Rule::unique('delivery_personnel', 'license_number')
            ],
            'license_expiry_date' => [
                'required',
                'date',
                'after:today'
            ],
            'vehicle_type' => [
                'required',
                'string',
                Rule::in(['motorcycle', 'bicycle', 'car', 'scooter'])
            ],
            'vehicle_model' => [
                'required',
                'string',
                'max:100'
            ],
            'vehicle_plate_number' => [
                'required',
                'string',
                'max:20',
                Rule::unique('delivery_personnel', 'vehicle_plate_number')
            ],
            'phone_number' => [
                'required',
                'string',
                'max:20'
            ],
            'emergency_contact_name' => [
                'required',
                'string',
                'max:100'
            ],
            'emergency_contact_phone' => [
                'required',
                'string',
                'max:20'
            ],
            'max_concurrent_deliveries' => [
                'required',
                'integer',
                'min:1',
                'max:10'
            ],
            'working_hours' => [
                'required',
                'array'
            ],
            'working_hours.*.day' => [
                'required',
                'string',
                Rule::in(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
            ],
            'working_hours.*.start_time' => [
                'required',
                'date_format:H:i'
            ],
            'working_hours.*.end_time' => [
                'required',
                'date_format:H:i',
                'after:working_hours.*.start_time'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.unique' => 'This user is already registered as delivery personnel.',
            'license_number.unique' => 'This license number is already registered.',
            'license_expiry_date.after' => 'License expiry date must be in the future.',
            'vehicle_plate_number.unique' => 'This vehicle plate number is already registered.',
            'max_concurrent_deliveries.max' => 'Maximum concurrent deliveries cannot exceed 10.',
            'working_hours.*.end_time.after' => 'End time must be after start time.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'user',
            'branch_id' => 'branch',
            'license_number' => 'license number',
            'license_expiry_date' => 'license expiry date',
            'vehicle_type' => 'vehicle type',
            'vehicle_model' => 'vehicle model',
            'vehicle_plate_number' => 'vehicle plate number',
            'phone_number' => 'phone number',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'max_concurrent_deliveries' => 'maximum concurrent deliveries',
        ];
    }
}
<?php

namespace Modules\Reservation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateReservationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $reservationId = $this->route('reservation')->id ?? null;

        return [
            'table_id' => 'nullable|exists:tables,id',
            'area_id' => 'nullable|exists:areas,id',
            'reservation_status_id' => 'nullable|exists:reservation_statuses,id',
            'reservation_number' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                Rule::unique('reservations', 'reservation_number')->ignore($reservationId)
            ],
            'customer_name' => 'sometimes|required|string|max:255',
            'customer_phone' => 'sometimes|required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'party_size' => 'sometimes|required|integer|min:1|max:50',
            'reservation_datetime' => 'nullable|date',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'special_requests' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'seated_at' => 'nullable|date',
            'completed_at' => 'nullable|date|after_or_equal:seated_at',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'table_id.exists' => 'Selected table does not exist.',
            'area_id.exists' => 'Selected area does not exist.',
            'reservation_status_id.exists' => 'Selected reservation status does not exist.',
            'reservation_number.required' => 'Reservation number is required.',
            'reservation_number.unique' => 'This reservation number already exists.',
            'customer_name.required' => 'Customer name is required.',
            'customer_phone.required' => 'Customer phone is required.',
            'customer_email.email' => 'Please provide a valid email address.',
            'party_size.required' => 'Party size is required.',
            'party_size.min' => 'Party size must be at least 1.',
            'party_size.max' => 'Party size cannot exceed 50.',
            'duration_minutes.min' => 'Duration must be at least 30 minutes.',
            'duration_minutes.max' => 'Duration cannot exceed 8 hours.',
            'completed_at.after_or_equal' => 'Completion time must be after or equal to seated time.',
        ];
    }
}
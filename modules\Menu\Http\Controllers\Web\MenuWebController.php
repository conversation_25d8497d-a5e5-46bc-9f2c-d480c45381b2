<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreMenuRequest;
use Modules\Menu\Http\Requests\UpdateMenuRequest;

class MenuWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of menus
     */
    public function index(Request $request): View
    {
        $menus = $this->menuService->getAllMenus($request->all());
        return view('menu::menus.index', compact('menus'));
    }

    /**
     * Show the form for creating a new menu
     */
    public function create(): View
    {
        return view('menu::menus.create');
    }

    /**
     * Store a newly created menu
     */
    public function store(StoreMenuRequest $request): RedirectResponse
    {
        try {
            $menu = $this->menuService->createMenu($request->validated());
            return redirect()->route('menu.web.menus.index')
                ->with('success', 'Menu created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to create menu: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified menu
     */
    public function show(string $id): View
    {
        $menu = $this->menuService->getMenuById($id);
        return view('menu::menus.show', compact('menu'));
    }

    /**
     * Show the form for editing the specified menu
     */
    public function edit(string $id): View
    {
        $menu = $this->menuService->getMenuById($id);
        return view('menu::menus.edit', compact('menu'));
    }

    /**
     * Update the specified menu
     */
    public function update(UpdateMenuRequest $request, string $id): RedirectResponse
    {
        try {
            $menu = $this->menuService->updateMenu($id, $request->validated());
            return redirect()->route('menu.web.menus.index')
                ->with('success', 'Menu updated successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to update menu: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified menu
     */
    public function destroy(string $id): RedirectResponse
    {
        try {
            $this->menuService->deleteMenu($id);
            return redirect()->route('menu.web.menus.index')
                ->with('success', 'Menu deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete menu: ' . $e->getMessage());
        }
    }
}
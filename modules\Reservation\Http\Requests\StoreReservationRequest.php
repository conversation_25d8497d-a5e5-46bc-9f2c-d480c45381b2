<?php

namespace Modules\Reservation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreReservationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'required|exists:branches,id',
            'customer_id' => 'nullable|exists:customers,id',
            'table_id' => 'nullable|exists:tables,id',
            'area_id' => 'nullable|exists:areas,id',
            'reservation_status_id' => 'required|exists:reservation_statuses,id',
            'reservation_number' => 'required|string|max:50|unique:reservations,reservation_number',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'party_size' => 'required|integer|min:1|max:50',
            'reservation_datetime' => 'nullable|date|after:now',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'special_requests' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'created_by' => 'nullable|exists:users,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'table_id.exists' => 'Selected table does not exist.',
            'area_id.exists' => 'Selected area does not exist.',
            'reservation_status_id.required' => 'Reservation status is required.',
            'reservation_status_id.exists' => 'Selected reservation status does not exist.',
            'reservation_number.required' => 'Reservation number is required.',
            'reservation_number.unique' => 'This reservation number already exists.',
            'customer_name.required' => 'Customer name is required.',
            'customer_phone.required' => 'Customer phone is required.',
            'customer_email.email' => 'Please provide a valid email address.',
            'party_size.required' => 'Party size is required.',
            'party_size.min' => 'Party size must be at least 1.',
            'party_size.max' => 'Party size cannot exceed 50.',
            'reservation_datetime.after' => 'Reservation date must be in the future.',
            'duration_minutes.min' => 'Duration must be at least 30 minutes.',
            'duration_minutes.max' => 'Duration cannot exceed 8 hours.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if (!$this->has('duration_minutes')) {
            $this->merge([
                'duration_minutes' => 120, // Default 2 hours
            ]);
        }

        if (!$this->has('created_by') && auth()->check()) {
            $this->merge([
                'created_by' => auth()->id(),
            ]);
        }
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained('customers');
            $table->foreignId('table_id')->nullable()->constrained('tables');
            $table->string('order_number', 50);
            $table->enum('order_type', ['dine_in', 'takeaway', 'delivery', 'online']);
            $table->enum('status', ['pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled'])->default('pending');
            $table->decimal('subtotal', 10, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('service_charge', 10, 2)->default(0);
            $table->decimal('delivery_fee', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2)->default(0);

            $table->json('customer_info')->nullable()->comment('Name, phone for guest orders');
            $table->text('delivery_address')->nullable();
            $table->json('delivery_coordinates')->nullable()->comment('Lat, lng for delivery');
            $table->text('special_instructions')->nullable();
            $table->foreignId('server_id')->nullable()->constrained('users');
            $table->foreignId('cashier_id')->nullable()->constrained('users');
            $table->foreignId('delivery_man_id')->nullable()->constrained('users');
            $table->timestamp('order_time')->default(now());
            $table->timestamp('estimated_ready_time')->nullable();
            $table->timestamp('actual_ready_time')->nullable();
            $table->timestamp('served_time')->nullable();
            $table->timestamps();
            
            $table->unique(['branch_id', 'order_number']);
            $table->index(['branch_id', 'status', 'order_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
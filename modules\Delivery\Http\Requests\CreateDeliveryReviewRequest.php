<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Delivery\Models\DeliveryAssignment;

class CreateDeliveryReviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if the assignment exists and belongs to the authenticated customer
        $assignment = DeliveryAssignment::with('order')
            ->where('id', $this->delivery_assignment_id)
            ->first();

        if (!$assignment) {
            return false;
        }

        // Only the customer who placed the order can review
        return $assignment->order->customer_id === auth()->id() && 
               $assignment->status === 'delivered';
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'delivery_assignment_id' => [
                'required',
                'integer',
                'exists:delivery_assignments,id',
                Rule::unique('delivery_reviews', 'delivery_assignment_id')
            ],
            'rating' => [
                'required',
                'integer',
                'min:1',
                'max:5'
            ],
            'comment' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'review_categories' => [
                'sometimes',
                'array'
            ],
            'review_categories.*' => [
                'string',
                Rule::in([
                    'punctuality',
                    'professionalism',
                    'food_condition',
                    'communication',
                    'politeness',
                    'vehicle_cleanliness'
                ])
            ],
            'is_anonymous' => [
                'sometimes',
                'boolean'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'delivery_assignment_id.unique' => 'You have already reviewed this delivery.',
            'delivery_assignment_id.exists' => 'Invalid delivery assignment.',
            'rating.min' => 'Rating must be at least 1 star.',
            'rating.max' => 'Rating cannot exceed 5 stars.',
            'comment.max' => 'Comment cannot exceed 1000 characters.',
            'review_categories.*.in' => 'Invalid review category selected.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'delivery_assignment_id' => 'delivery',
            'review_categories' => 'review categories',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set customer_id from authenticated user
        $this->merge([
            'customer_id' => auth()->id(),
        ]);

        // Ensure is_anonymous defaults to false if not provided
        if (!$this->has('is_anonymous')) {
            $this->merge(['is_anonymous' => false]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation: Check if delivery is completed
            if ($this->delivery_assignment_id) {
                $assignment = DeliveryAssignment::find($this->delivery_assignment_id);
                
                if ($assignment && $assignment->status !== 'delivered') {
                    $validator->errors()->add(
                        'delivery_assignment_id',
                        'You can only review completed deliveries.'
                    );
                }

                // Check if review window is still open (e.g., within 30 days)
                if ($assignment && $assignment->delivered_at) {
                    $reviewDeadline = $assignment->delivered_at->addDays(30);
                    if (now()->isAfter($reviewDeadline)) {
                        $validator->errors()->add(
                            'delivery_assignment_id',
                            'Review period has expired. You can only review deliveries within 30 days.'
                        );
                    }
                }
            }
        });
    }
}
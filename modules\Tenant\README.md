# Tenant Module

The Tenant Module provides comprehensive multi-tenancy support for the POS system, enabling management of multiple restaurants (tenants), their branches, subscriptions, and billing.

## Features

### Core Functionality
- **Tenant Management**: Create, update, activate/deactivate tenants
- **Subscription Management**: Handle subscription plans, billing cycles, and renewals
- **Multi-Store Support**: Manage multiple branches per tenant
- **Billing System**: Automated billing, invoicing, and payment processing
- **Usage Tracking**: Monitor feature usage and enforce limits
- **Data Isolation**: Ensure tenant data separation and security

### Key Components

#### Controllers
- `TenantController`: Manages tenant CRUD operations and statistics
- `SubscriptionController`: Handles subscription lifecycle management

#### Services
- `TenantService`: Core tenant business logic
- `SubscriptionService`: Subscription management and lifecycle
- `BillingService`: Billing, invoicing, and payment processing
- `MultiStoreService`: Multi-branch management and data synchronization

#### Middleware
- `TenantMiddleware`: Resolves and sets tenant context
- `SubscriptionMiddleware`: Validates subscription status and access
- `UsageLimitMiddleware`: Enforces feature usage limits

#### Helpers
- `TenantHelper`: Utility functions for tenant operations
- `SubscriptionHelper`: Subscription-related utilities

## Installation

1. **Register the Service Provider**
   Add to `config/app.php`:
   ```php
   'providers' => [
       // ...
       Modules\Tenant\Providers\TenantServiceProvider::class,
   ],
   ```

2. **Publish Configuration**
   ```bash
   php artisan vendor:publish --tag=tenant-config
   ```

3. **Publish Views** (optional)
   ```bash
   php artisan vendor:publish --tag=tenant-views
   ```

## Configuration

The module configuration is located in `config/tenant.php`. Key settings include:

- **Tenant Code Generation**: Prefix, length, and character set
- **Subdomain Support**: Domain configuration and reserved names
- **Subscription Settings**: Trial periods, billing cycles, grace periods
- **Usage Limits**: Cache TTL, warning thresholds
- **Billing Configuration**: Tax rates, payment methods, retry settings
- **Multi-Store Settings**: Branch limits, synchronization options

## Usage

### Creating a Tenant

```php
use Modules\Tenant\Services\TenantService;

$tenantService = app(TenantService::class);

$tenant = $tenantService->createTenant([
    'name' => 'Restaurant Name',
    'email' => '<EMAIL>',
    'phone' => '+**********',
    'business_type' => 'restaurant',
    'currency_code' => 'USD',
    'timezone' => 'America/New_York',
    'subscription' => [
        'plan_id' => 1,
        'billing_cycle' => 'monthly'
    ]
]);
```

### Managing Subscriptions

```php
use Modules\Tenant\Services\SubscriptionService;

$subscriptionService = app(SubscriptionService::class);

// Create subscription
$subscription = $subscriptionService->createSubscription($tenantId, [
    'plan_id' => 1,
    'billing_cycle' => 'monthly',
    'starts_at' => now(),
    'auto_renew' => true
]);

// Upgrade subscription
$subscriptionService->upgradeSubscription($subscriptionId, $newPlanId);

// Cancel subscription
$subscriptionService->cancelSubscription($subscriptionId, 'User requested cancellation');
```

### Using Middleware

#### API Routes
```php
Route::middleware(['tenant.api'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index']);
});
```

#### Web Routes
```php
Route::middleware(['tenant.web'])->group(function () {
    Route::get('/tenant/dashboard', [TenantController::class, 'dashboard']);
});
```

#### Usage Limits
```php
Route::middleware(['usage.limit:branches,10'])->group(function () {
    Route::post('/branches', [BranchController::class, 'store']);
});
```

### Checking Feature Access

```php
use Modules\Tenant\Helpers\TenantHelper;

// Check if tenant has access to a feature
if (TenantHelper::hasFeatureAccess($tenantId, 'multi_store')) {
    // Allow multi-store functionality
}

// Check usage limits
if (TenantHelper::isWithinUsageLimit($tenantId, 'branches', 5)) {
    // Allow creating 5 more branches
}
```

### Multi-Store Operations

```php
use Modules\Tenant\Services\MultiStoreService;

$multiStoreService = app(MultiStoreService::class);

// Create branch
$branch = $multiStoreService->createBranch($tenantId, [
    'name' => 'Downtown Branch',
    'address' => '123 Main St',
    'phone' => '+**********'
]);

// Sync menu across branches
$multiStoreService->syncMenuAcrossBranches($tenantId);

// Generate consolidated report
$report = $multiStoreService->generateConsolidatedReport($tenantId, 'sales', [
    'start_date' => '2024-01-01',
    'end_date' => '2024-01-31'
]);
```

## API Endpoints

### Tenant Management
- `GET /api/tenants` - List tenants
- `POST /api/tenants` - Create tenant
- `GET /api/tenants/{id}` - Get tenant details
- `PUT /api/tenants/{id}` - Update tenant
- `DELETE /api/tenants/{id}` - Delete tenant
- `POST /api/tenants/{id}/activate` - Activate tenant
- `POST /api/tenants/{id}/deactivate` - Deactivate tenant
- `GET /api/tenants/{id}/statistics` - Get tenant statistics

### Subscription Management
- `GET /api/subscriptions` - List subscriptions
- `POST /api/subscriptions` - Create subscription
- `GET /api/subscriptions/{id}` - Get subscription details
- `PUT /api/subscriptions/{id}` - Update subscription
- `POST /api/subscriptions/{id}/cancel` - Cancel subscription
- `POST /api/subscriptions/{id}/suspend` - Suspend subscription
- `POST /api/subscriptions/{id}/reactivate` - Reactivate subscription
- `POST /api/subscriptions/{id}/upgrade` - Upgrade subscription
- `GET /api/subscriptions/{id}/usage` - Get usage statistics
- `GET /api/subscription-plans` - List available plans

### Tenant-Specific Routes
- `GET /api/tenant/dashboard` - Tenant dashboard data
- `GET /api/tenant/usage/branches` - Branch usage limits
- `GET /api/tenant/usage/users` - User usage limits
- `GET /api/tenant/usage/menu-items` - Menu item usage limits
- `GET /api/tenant/usage/statistics` - Overall usage statistics

## Web Routes

### Admin Routes
- `/tenants` - Tenant management interface
- `/tenants/create` - Create new tenant
- `/tenants/{id}` - View tenant details
- `/tenants/{id}/edit` - Edit tenant
- `/subscriptions` - Subscription management
- `/subscriptions/{id}` - View subscription details

### Tenant-Specific Routes
- `/tenant/dashboard` - Tenant dashboard
- `/tenant/subscription` - Subscription management
- `/tenant/usage` - Usage statistics
- `/tenant/billing` - Billing history

### Public Tenant Routes (Subdomain)
- `{tenant}.domain.com/` - Tenant home page
- `{tenant}.domain.com/menu` - Public menu
- `{tenant}.domain.com/contact` - Contact information

## Database Schema

The module works with the following database tables:

- `tenants` - Tenant information
- `branches` - Tenant branches
- `subscription_plans` - Available subscription plans
- `tenant_subscriptions` - Tenant subscription records
- `billing_records` - Billing and payment history

## Security Features

- **Data Isolation**: Ensures tenant data separation
- **Context Validation**: Prevents cross-tenant data access
- **Usage Enforcement**: Enforces subscription limits
- **Audit Trail**: Logs important tenant operations
- **Secure Billing**: Handles payment processing securely

## Caching

The module implements intelligent caching for:
- Tenant data and settings
- Subscription information
- Usage statistics
- Billing records

Cache keys are prefixed with `tenant:` and include TTL settings configurable in the config file.

## Error Handling

The module provides comprehensive error handling for:
- Invalid tenant context
- Subscription validation failures
- Usage limit violations
- Billing processing errors
- Data isolation violations

## Testing

To test the module functionality:

1. Create test tenants with different subscription plans
2. Test usage limit enforcement
3. Verify data isolation between tenants
4. Test subscription lifecycle (create, upgrade, cancel)
5. Validate billing calculations and processing

## Support

For issues or questions regarding the Tenant module, please refer to the system documentation or contact the development team.
<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Delivery\Models\DeliveryPersonnel;
use Modules\Delivery\Models\DeliveryZone;
use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryReview;
use Modules\Delivery\Models\DeliveryTip;
use Modules\Delivery\Models\DeliveryTracking;
use Modules\Delivery\Services\DeliveryService;
use Modules\Delivery\Http\Requests\CreateDeliveryPersonnelRequest;
use Modules\Delivery\Http\Requests\CreateDeliveryZoneRequest;
use Modules\Delivery\Http\Requests\AssignDeliveryRequest;
use Modules\Delivery\Http\Requests\UpdateDeliveryStatusRequest;
use Modules\Delivery\Http\Requests\CreateDeliveryReviewRequest;
use Modules\Delivery\Http\Requests\CreateDeliveryTipRequest;
use Modules\Delivery\Http\Requests\UpdateLocationRequest;

class DeliveryController extends Controller
{
    protected DeliveryService $deliveryService;

    public function __construct(DeliveryService $deliveryService)
    {
        $this->deliveryService = $deliveryService;
    }

    // Delivery Personnel Management
    public function getDeliveryPersonnel(Request $request): JsonResponse
    {
        $query = DeliveryPersonnel::with(['user', 'branch'])
            ->when($request->branch_id, fn($q) => $q->where('branch_id', $request->branch_id))
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->is_verified !== null, fn($q) => $q->where('is_verified', $request->boolean('is_verified')))
            ->orderBy('created_at', 'desc');

        $personnel = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $personnel,
        ]);
    }

    public function createDeliveryPersonnel(CreateDeliveryPersonnelRequest $request): JsonResponse
    {
        $personnel = DeliveryPersonnel::create($request->validated());
        $personnel->load(['user', 'branch']);

        return response()->json([
            'success' => true,
            'message' => 'Delivery personnel created successfully',
            'data' => $personnel,
        ], 201);
    }

    public function updateDeliveryPersonnel(Request $request, DeliveryPersonnel $personnel): JsonResponse
    {
        $personnel->update($request->only([
            'license_number', 'vehicle_type', 'vehicle_plate_number', 'vehicle_model',
            'status', 'max_concurrent_deliveries', 'delivery_radius_km', 'working_hours'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Delivery personnel updated successfully',
            'data' => $personnel->fresh(['user', 'branch']),
        ]);
    }

    public function verifyDeliveryPersonnel(DeliveryPersonnel $personnel): JsonResponse
    {
        $personnel->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Delivery personnel verified successfully',
            'data' => $personnel->fresh(['user', 'branch']),
        ]);
    }

    // Delivery Zones Management
    public function getDeliveryZones(Request $request): JsonResponse
    {
        $query = DeliveryZone::with('branch')
            ->when($request->branch_id, fn($q) => $q->where('branch_id', $request->branch_id))
            ->when($request->is_active !== null, fn($q) => $q->where('is_active', $request->boolean('is_active')))
            ->orderBy('priority', 'desc');

        $zones = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $zones,
        ]);
    }

    public function createDeliveryZone(CreateDeliveryZoneRequest $request): JsonResponse
    {
        $zone = DeliveryZone::create($request->validated());
        $zone->load('branch');

        return response()->json([
            'success' => true,
            'message' => 'Delivery zone created successfully',
            'data' => $zone,
        ], 201);
    }

    public function updateDeliveryZone(Request $request, DeliveryZone $zone): JsonResponse
    {
        $zone->update($request->only([
            'name', 'description', 'coordinates', 'delivery_fee',
            'minimum_order_amount', 'estimated_delivery_time_minutes',
            'is_active', 'priority'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'Delivery zone updated successfully',
            'data' => $zone->fresh('branch'),
        ]);
    }

    // Delivery Assignment Management
    public function getDeliveryAssignments(Request $request): JsonResponse
    {
        $query = DeliveryAssignment::with(['order', 'deliveryPersonnel.user', 'deliveryZone'])
            ->when($request->personnel_id, fn($q) => $q->where('delivery_personnel_id', $request->personnel_id))
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->date, fn($q) => $q->whereDate('assigned_at', $request->date))
            ->orderBy('assigned_at', 'desc');

        $assignments = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $assignments,
        ]);
    }

    public function assignDelivery(AssignDeliveryRequest $request): JsonResponse
    {
        $assignment = $this->deliveryService->assignDelivery(
            $request->order_id,
            $request->delivery_personnel_id,
            $request->delivery_zone_id
        );

        return response()->json([
            'success' => true,
            'message' => 'Delivery assigned successfully',
            'data' => $assignment->load(['order', 'deliveryPersonnel.user', 'deliveryZone']),
        ], 201);
    }

    public function autoAssignDelivery(Request $request): JsonResponse
    {
        $request->validate(['order_id' => 'required|exists:orders,id']);
        
        $assignment = $this->deliveryService->autoAssignDelivery($request->order_id);

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'No available delivery personnel found',
            ], 422);
        }

        return response()->json([
            'success' => true,
            'message' => 'Delivery auto-assigned successfully',
            'data' => $assignment->load(['order', 'deliveryPersonnel.user', 'deliveryZone']),
        ]);
    }

    public function updateDeliveryStatus(UpdateDeliveryStatusRequest $request, DeliveryAssignment $assignment): JsonResponse
    {
        $result = $this->deliveryService->updateDeliveryStatus(
            $assignment,
            $request->status,
            $request->only(['delivery_notes', 'failure_reason', 'delivery_proof'])
        );

        return response()->json([
            'success' => true,
            'message' => 'Delivery status updated successfully',
            'data' => $assignment->fresh(['order', 'deliveryPersonnel.user', 'deliveryZone']),
        ]);
    }

    // Location Tracking
    public function updateLocation(UpdateLocationRequest $request): JsonResponse
    {
        $personnelId = $request->delivery_personnel_id;
        $personnel = DeliveryPersonnel::findOrFail($personnelId);

        // Update personnel location
        $personnel->updateLocation($request->latitude, $request->longitude);

        // Record tracking for active deliveries
        $activeAssignments = $personnel->activeDeliveries;
        foreach ($activeAssignments as $assignment) {
            DeliveryTracking::create([
                'delivery_assignment_id' => $assignment->id,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'accuracy' => $request->accuracy,
                'speed' => $request->speed,
                'bearing' => $request->bearing,
                'recorded_at' => now(),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Location updated successfully',
        ]);
    }

    public function getDeliveryTracking(DeliveryAssignment $assignment): JsonResponse
    {
        $tracking = $assignment->trackingRecords()
            ->orderBy('recorded_at', 'desc')
            ->paginate(50);

        return response()->json([
            'success' => true,
            'data' => $tracking,
        ]);
    }

    public function getCurrentLocation(DeliveryAssignment $assignment): JsonResponse
    {
        $location = $assignment->getCurrentLocation();

        return response()->json([
            'success' => true,
            'data' => $location,
        ]);
    }

    // Reviews Management
    public function getDeliveryReviews(Request $request): JsonResponse
    {
        $query = DeliveryReview::with(['deliveryAssignment.order', 'customer'])
            ->when($request->personnel_id, function ($q) use ($request) {
                $q->whereHas('deliveryAssignment', fn($qa) => $qa->where('delivery_personnel_id', $request->personnel_id));
            })
            ->when($request->rating, fn($q) => $q->where('rating', $request->rating))
            ->when($request->is_verified !== null, fn($q) => $q->where('is_verified', $request->boolean('is_verified')))
            ->orderBy('created_at', 'desc');

        $reviews = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $reviews,
        ]);
    }

    public function createDeliveryReview(CreateDeliveryReviewRequest $request): JsonResponse
    {
        $review = DeliveryReview::create($request->validated());
        $review->load(['deliveryAssignment.order', 'customer']);

        return response()->json([
            'success' => true,
            'message' => 'Review created successfully',
            'data' => $review,
        ], 201);
    }

    public function verifyReview(DeliveryReview $review): JsonResponse
    {
        $review->verify();

        return response()->json([
            'success' => true,
            'message' => 'Review verified successfully',
            'data' => $review->fresh(['deliveryAssignment.order', 'customer']),
        ]);
    }

    // Tips Management
    public function getDeliveryTips(Request $request): JsonResponse
    {
        $query = DeliveryTip::with(['deliveryAssignment.order', 'customer'])
            ->when($request->personnel_id, function ($q) use ($request) {
                $q->whereHas('deliveryAssignment', fn($qa) => $qa->where('delivery_personnel_id', $request->personnel_id));
            })
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->payment_method, fn($q) => $q->where('payment_method', $request->payment_method))
            ->orderBy('created_at', 'desc');

        $tips = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $tips,
        ]);
    }

    public function createDeliveryTip(CreateDeliveryTipRequest $request): JsonResponse
    {
        $tip = DeliveryTip::create($request->validated());
        $tip->load(['deliveryAssignment.order', 'customer']);

        return response()->json([
            'success' => true,
            'message' => 'Tip created successfully',
            'data' => $tip,
        ], 201);
    }

    public function processTipPayment(DeliveryTip $tip, Request $request): JsonResponse
    {
        $request->validate(['transaction_reference' => 'nullable|string']);
        
        $tip->markAsPaid($request->transaction_reference);

        return response()->json([
            'success' => true,
            'message' => 'Tip payment processed successfully',
            'data' => $tip->fresh(['deliveryAssignment.order', 'customer']),
        ]);
    }

    // Analytics and Reports
    public function getDeliveryStats(Request $request): JsonResponse
    {
        $stats = $this->deliveryService->getDeliveryStats(
            $request->branch_id,
            $request->start_date,
            $request->end_date
        );

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    public function getPersonnelPerformance(DeliveryPersonnel $personnel, Request $request): JsonResponse
    {
        $performance = $this->deliveryService->getPersonnelPerformance(
            $personnel->id,
            $request->start_date,
            $request->end_date
        );

        return response()->json([
            'success' => true,
            'data' => $performance,
        ]);
    }

    // Utility Methods
    public function checkDeliveryZone(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $zone = DeliveryZone::findZoneForLocation(
            $request->branch_id,
            $request->latitude,
            $request->longitude
        );

        return response()->json([
            'success' => true,
            'data' => $zone,
        ]);
    }

    public function getAvailablePersonnel(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'radius_km' => 'nullable|numeric|min:1|max:50',
        ]);

        $query = DeliveryPersonnel::where('branch_id', $request->branch_id)
            ->available()
            ->with('user');

        if ($request->latitude && $request->longitude) {
            $query->withinRadius(
                $request->latitude,
                $request->longitude,
                $request->get('radius_km', 10)
            );
        }

        $personnel = $query->get();

        return response()->json([
            'success' => true,
            'data' => $personnel,
        ]);
    }
}
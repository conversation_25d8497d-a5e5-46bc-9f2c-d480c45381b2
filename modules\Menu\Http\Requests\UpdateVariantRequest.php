<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|required|string|max:255',
            'code' => 'sometimes|required|string|max:100',
            'price_modifier' => 'sometimes|required|numeric',
            'cost_modifier' => 'nullable|numeric',
            'is_default' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Variant name is required.',
            'name.max' => 'Variant name cannot exceed 255 characters.',
            'code.required' => 'Variant code is required.',
            'code.max' => 'Variant code cannot exceed 100 characters.',
            'price_modifier.required' => 'Price modifier is required.',
            'price_modifier.numeric' => 'Price modifier must be a valid number.',
            'cost_modifier.numeric' => 'Cost modifier must be a valid number.',
            'sort_order.integer' => 'Sort order must be a valid number.',
            'sort_order.min' => 'Sort order must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'price_modifier' => 'price modifier',
            'cost_modifier' => 'cost modifier',
            'is_default' => 'default status',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_default')) {
            $this->merge([
                'is_default' => filter_var($this->is_default, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
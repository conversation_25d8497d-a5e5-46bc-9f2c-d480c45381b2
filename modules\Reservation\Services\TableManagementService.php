<?php

namespace Modules\Reservation\Services;

use App\Models\Table;
use Illuminate\Support\Carbon;

class TableManagementService
{
    public function isTableAvailable(int $tableId, string $datetime, int $duration): bool
    {
        // Check if the table is available for the given datetime and duration
        $table = Table::findOrFail($tableId);
        $start = Carbon::parse($datetime);
        $end = $start->copy()->addMinutes($duration);
        $overlap = $table->reservations()
            ->where(function ($query) use ($start, $end) {
                $query->whereBetween('reservation_datetime', [$start, $end])
                      ->orWhereRaw('? BETWEEN reservation_datetime AND DATE_ADD(reservation_datetime, INTERVAL duration_minutes MINUTE)', [$start]);
            })
            ->exists();
        return !$overlap;
    }

    // Add more methods as needed (e.g., assignTable, getLayout, etc.)
} 
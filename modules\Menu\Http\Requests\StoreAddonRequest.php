<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAddonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'menu_item_id' => 'required|exists:menu_items,id',
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'cost' => 'nullable|numeric|min:0',
            'category' => 'nullable|string|max:100',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'max_quantity' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_item_id.required' => 'Menu item is required.',
            'menu_item_id.exists' => 'Selected menu item does not exist.',
            'name.required' => 'Addon name is required.',
            'name.max' => 'Addon name cannot exceed 255 characters.',
            'price.required' => 'Price is required.',
            'price.numeric' => 'Price must be a valid number.',
            'price.min' => 'Price must be greater than or equal to 0.',
            'cost.numeric' => 'Cost must be a valid number.',
            'cost.min' => 'Cost must be greater than or equal to 0.',
            'category.max' => 'Category cannot exceed 100 characters.',
            'max_quantity.integer' => 'Max quantity must be a valid number.',
            'max_quantity.min' => 'Max quantity must be at least 1.',
            'sort_order.integer' => 'Sort order must be a valid number.',
            'sort_order.min' => 'Sort order must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'menu_item_id' => 'menu item',
            'is_required' => 'required status',
            'is_active' => 'active status',
            'max_quantity' => 'maximum quantity',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_required')) {
            $this->merge([
                'is_required' => filter_var($this->is_required, FILTER_VALIDATE_BOOLEAN)
            ]);
        }

        if ($this->has('is_active')) {
            $this->merge([
                'is_active' => filter_var($this->is_active, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
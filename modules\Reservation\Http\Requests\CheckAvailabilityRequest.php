<?php

namespace Modules\Reservation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CheckAvailabilityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'required|exists:branches,id',
            'area_id' => 'nullable|exists:areas,id',
            'table_id' => 'nullable|exists:tables,id',
            'party_size' => 'required|integer|min:1|max:50',
            'reservation_datetime' => 'required|date|after:now',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'area_id.exists' => 'Selected area does not exist.',
            'table_id.exists' => 'Selected table does not exist.',
            'party_size.required' => 'Party size is required.',
            'party_size.min' => 'Party size must be at least 1.',
            'party_size.max' => 'Party size cannot exceed 50.',
            'reservation_datetime.required' => 'Reservation date and time is required.',
            'reservation_datetime.after' => 'Reservation date must be in the future.',
            'duration_minutes.min' => 'Duration must be at least 30 minutes.',
            'duration_minutes.max' => 'Duration cannot exceed 8 hours.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if (!$this->has('duration_minutes')) {
            $this->merge([
                'duration_minutes' => 120, // Default 2 hours
            ]);
        }
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches');
            $table->string('name');
            $table->string('type', 50)->comment('sales, inventory, staff, etc.');
            $table->json('parameters')->nullable()->comment('Report filters and parameters');
            $table->json('data')->nullable()->comment('Report data/results');
            $table->string('file_path')->nullable()->comment('Path to generated file');
            $table->enum('format', ['pdf', 'excel', 'csv', 'json'])->default('pdf');
            $table->enum('status', ['generating', 'completed', 'failed'])->default('generating');
            $table->date('report_date_from');
            $table->date('report_date_to');
            $table->foreignId('generated_by')->constrained('users');
            $table->timestamp('generated_at')->default(now());
            $table->timestamps();
            
            $table->index(['tenant_id', 'type', 'generated_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
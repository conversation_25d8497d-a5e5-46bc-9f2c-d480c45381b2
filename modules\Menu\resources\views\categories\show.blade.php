@extends('menu::layouts.app')

@section('title', $category->name)

@section('header-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('menu.web.categories.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Categories
        </a>
        <a href="{{ route('menu.web.categories.edit', $category->id) }}" class="btn btn-primary">
            <i class="bi bi-pencil me-1"></i>
            Edit Category
        </a>
        <a href="{{ route('menu.web.menu-items.create') }}?category_id={{ $category->id }}" class="btn btn-success">
            <i class="bi bi-plus-circle me-1"></i>
            Add Menu Item
        </a>
    </div>
@endsection

@section('content')
<div class="row">
    <!-- Category Details -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Category Details</h5>
            </div>
            <div class="card-body">
                <!-- Category Image -->
                @if($category->image_url)
                    <div class="text-center mb-3">
                        <img src="{{ $category->image_url }}" alt="{{ $category->name }}" 
                             class="img-fluid rounded" style="max-height: 200px; object-fit: cover;">
                    </div>
                @endif
                
                <dl class="row">
                    <dt class="col-sm-4">Name:</dt>
                    <dd class="col-sm-8">{{ $category->name }}</dd>
                    
                    <dt class="col-sm-4">Menu:</dt>
                    <dd class="col-sm-8">
                        <a href="{{ route('menu.web.menus.show', $category->menu->id) }}" class="text-decoration-none">
                            {{ $category->menu->name ?? 'N/A' }}
                        </a>
                        @if($category->menu)
                            <br><small class="text-muted">{{ ucfirst(str_replace('_', ' ', $category->menu->menu_type)) }}</small>
                        @endif
                    </dd>
                    
                    <dt class="col-sm-4">Status:</dt>
                    <dd class="col-sm-8">
                        @if($category->is_active)
                            <span class="badge bg-success">Active</span>
                        @else
                            <span class="badge bg-secondary">Inactive</span>
                        @endif
                    </dd>
                    
                    <dt class="col-sm-4">Sort Order:</dt>
                    <dd class="col-sm-8">{{ $category->sort_order ?? 0 }}</dd>
                    
                    <dt class="col-sm-4">Items Count:</dt>
                    <dd class="col-sm-8">
                        <span class="badge bg-info">{{ $category->menuItems->count() ?? 0 }} items</span>
                    </dd>
                    
                    <dt class="col-sm-4">Created:</dt>
                    <dd class="col-sm-8">
                        {{ $category->created_at->format('M d, Y') }}
                        <br><small class="text-muted">{{ $category->created_at->diffForHumans() }}</small>
                    </dd>
                    
                    <dt class="col-sm-4">Updated:</dt>
                    <dd class="col-sm-8">
                        {{ $category->updated_at->format('M d, Y') }}
                        <br><small class="text-muted">{{ $category->updated_at->diffForHumans() }}</small>
                    </dd>
                </dl>
                
                @if($category->description)
                    <hr>
                    <h6>Description</h6>
                    <p class="text-muted">{{ $category->description }}</p>
                @endif
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">Quick Stats</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 text-primary mb-1">{{ $category->menuItems->where('is_active', true)->count() }}</div>
                            <small class="text-muted">Active Items</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-secondary mb-1">{{ $category->menuItems->where('is_active', false)->count() }}</div>
                        <small class="text-muted">Inactive Items</small>
                    </div>
                </div>
                
                @if($category->menuItems->count() > 0)
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <div class="h6 text-success mb-1">${{ number_format($category->menuItems->min('base_price') ?? 0, 2) }}</div>
                                <small class="text-muted">Min Price</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h6 text-info mb-1">${{ number_format($category->menuItems->max('base_price') ?? 0, 2) }}</div>
                            <small class="text-muted">Max Price</small>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Menu Items -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Menu Items</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleView('grid')" id="gridViewBtn">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary active" onclick="toggleView('list')" id="listViewBtn">
                        <i class="bi bi-list"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                @if($category->menuItems && $category->menuItems->count() > 0)
                    <!-- List View -->
                    <div id="listView">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Available</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($category->menuItems as $item)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($item->image_url)
                                                        <img src="{{ $item->image_url }}" alt="{{ $item->name }}" 
                                                             class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="bi bi-image text-muted"></i>
                                                        </div>
                                                    @endif
                                                    <div>
                                                        <div class="fw-medium">{{ $item->name }}</div>
                                                        @if($item->description)
                                                            <small class="text-muted">{{ Str::limit($item->description, 50) }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-medium">${{ number_format($item->base_price, 2) }}</span>
                                            </td>
                                            <td>
                                                @if($item->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($item->is_available)
                                                    <span class="badge bg-success">Available</span>
                                                @else
                                                    <span class="badge bg-warning">Unavailable</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('menu.web.menu-items.show', $item->id) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="{{ route('menu.web.menu-items.edit', $item->id) }}" 
                                                       class="btn btn-sm btn-outline-secondary">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-{{ $item->is_available ? 'warning' : 'success' }}" 
                                                            onclick="toggleAvailability({{ $item->id }}, {{ $item->is_available ? 'false' : 'true' }})">
                                                        <i class="bi bi-{{ $item->is_available ? 'eye-slash' : 'eye' }}"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Grid View -->
                    <div id="gridView" style="display: none;">
                        <div class="row">
                            @foreach($category->menuItems as $item)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100">
                                        @if($item->image_url)
                                            <img src="{{ $item->image_url }}" class="card-img-top" alt="{{ $item->name }}" 
                                                 style="height: 150px; object-fit: cover;">
                                        @endif
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $item->name }}</h6>
                                            @if($item->description)
                                                <p class="card-text text-muted small">{{ Str::limit($item->description, 80) }}</p>
                                            @endif
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="h6 text-primary mb-0">${{ number_format($item->base_price, 2) }}</span>
                                                <div>
                                                    @if($item->is_active)
                                                        <span class="badge bg-success">Active</span>
                                                    @else
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <div class="btn-group w-100" role="group">
                                                <a href="{{ route('menu.web.menu-items.show', $item->id) }}" class="btn btn-outline-primary btn-sm">
                                                    View
                                                </a>
                                                <a href="{{ route('menu.web.menu-items.edit', $item->id) }}" class="btn btn-outline-secondary btn-sm">
                                                    Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="bi bi-card-list display-1 text-muted"></i>
                        <h4 class="mt-3">No Menu Items</h4>
                        <p class="text-muted">This category doesn't have any menu items yet.</p>
                        <a href="{{ route('menu.web.menu-items.create') }}?category_id={{ $category->id }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Add First Menu Item
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleView(viewType) {
    const listView = document.getElementById('listView');
    const gridView = document.getElementById('gridView');
    const listBtn = document.getElementById('listViewBtn');
    const gridBtn = document.getElementById('gridViewBtn');
    
    if (viewType === 'grid') {
        listView.style.display = 'none';
        gridView.style.display = 'block';
        listBtn.classList.remove('active');
        gridBtn.classList.add('active');
    } else {
        listView.style.display = 'block';
        gridView.style.display = 'none';
        listBtn.classList.add('active');
        gridBtn.classList.remove('active');
    }
}

function toggleAvailability(itemId, isAvailable) {
    if (confirm(`Are you sure you want to ${isAvailable ? 'make this item available' : 'make this item unavailable'}?`)) {
        // In a real application, this would make an AJAX request
        fetch(`/menu/menu-items/${itemId}/toggle-availability`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ is_available: isAvailable })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to update availability');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}
</script>
@endpush
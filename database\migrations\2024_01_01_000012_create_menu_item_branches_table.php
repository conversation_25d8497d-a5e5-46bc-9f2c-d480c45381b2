<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_branches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('menu_item_id')->constrained('menu_items')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->decimal('branch_price', 10, 2)->nullable()->comment('Override base price');
            $table->boolean('is_available')->default(true);
            $table->integer('stock_quantity')->nullable()->comment('If tracking inventory');
            $table->integer('low_stock_threshold')->default(0);
            $table->timestamps();
            
            $table->unique(['menu_item_id', 'branch_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_branches');
    }
};
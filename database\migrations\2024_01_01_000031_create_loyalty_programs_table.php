<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_programs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('name');
            $table->string('code', 50);
            $table->text('description')->nullable();
            $table->enum('type', ['points', 'visits', 'spending']);
            $table->decimal('points_per_currency', 8, 2)->default(1)->comment('Points earned per currency unit spent');
            $table->decimal('currency_per_point', 8, 4)->default(0.01)->comment('Currency value per point');
            $table->integer('minimum_points_redemption')->default(100);
            $table->decimal('minimum_spend_amount', 10, 2)->nullable();
            $table->integer('visits_required')->nullable()->comment('For visit-based programs');
            $table->json('tier_benefits')->nullable()->comment('Different tier benefits');
            $table->date('valid_from');
            $table->date('valid_until')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['tenant_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_programs');
    }
};
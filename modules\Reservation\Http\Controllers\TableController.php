<?php

namespace Modules\Reservation\Http\Controllers;

use App\Models\Table;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class TableController extends Controller
{
    public function index()
    {
        return response()->json(Table::with(['branch', 'area'])->get());
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'area_id' => 'required|exists:areas,id',
            'table_number' => 'required|string|max:20',
            'table_name' => 'nullable|string',
            'seating_capacity' => 'required|integer|min:1',
            'section' => 'nullable|string|max:50',
            'status' => 'required|string',
            'qr_code' => 'nullable|string',
            'position_coordinates' => 'nullable|json',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);
        $table = Table::create($data);
        return response()->json($table, 201);
    }

    public function show(Table $table)
    {
        return response()->json($table->load(['branch', 'area']));
    }

    public function update(Request $request, Table $table)
    {
        $data = $request->validate([
            'area_id' => 'sometimes|required|exists:areas,id',
            'table_number' => 'sometimes|required|string|max:20',
            'table_name' => 'nullable|string',
            'seating_capacity' => 'sometimes|required|integer|min:1',
            'section' => 'nullable|string|max:50',
            'status' => 'sometimes|required|string',
            'qr_code' => 'nullable|string',
            'position_coordinates' => 'nullable|json',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);
        $table->update($data);
        return response()->json($table);
    }

    public function destroy(Table $table)
    {
        $table->delete();
        return response()->json(null, 204);
    }
} 
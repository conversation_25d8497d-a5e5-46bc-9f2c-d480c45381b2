<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Inventory\Http\Requests\StorePurchaseOrderRequest;
use Modules\Inventory\Http\Requests\UpdatePurchaseOrderRequest;
use Modules\Inventory\Services\PurchaseOrderService;

class PurchaseOrderController extends Controller
{
    protected $purchaseOrderService;

    public function __construct(PurchaseOrderService $purchaseOrderService)
    {
        $this->purchaseOrderService = $purchaseOrderService;
    }

    /**
     * Display a listing of purchase orders.
     */
    public function index(Request $request)
    {
        $orders = $this->purchaseOrderService->getAllPurchaseOrders($request->all());
        return response()->json($orders);
    }

    /**
     * Store a newly created purchase order.
     */
    public function store(StorePurchaseOrderRequest $request)
    {
        $order = $this->purchaseOrderService->createPurchaseOrder($request->validated());
        return response()->json($order, 201);
    }

    /**
     * Display the specified purchase order.
     */
    public function show(string $id)
    {
        $order = $this->purchaseOrderService->getPurchaseOrderById($id);
        return response()->json($order);
    }

    /**
     * Update the specified purchase order.
     */
    public function update(UpdatePurchaseOrderRequest $request, string $id)
    {
        $order = $this->purchaseOrderService->updatePurchaseOrder($id, $request->validated());
        return response()->json($order);
    }

    /**
     * Remove the specified purchase order.
     */
    public function destroy(string $id)
    {
        $this->purchaseOrderService->deletePurchaseOrder($id);
        return response()->json(null, 204);
    }

    /**
     * Approve purchase order
     */
    public function approve(string $id)
    {
        $order = $this->purchaseOrderService->approvePurchaseOrder($id);
        return response()->json($order);
    }

    /**
     * Reject purchase order
     */
    public function reject(Request $request, string $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $order = $this->purchaseOrderService->rejectPurchaseOrder($id, $request->reason);
        return response()->json($order);
    }

    /**
     * Mark purchase order as received
     */
    public function receive(Request $request, string $id)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:purchase_order_items,id',
            'items.*.received_quantity' => 'required|numeric|min:0',
            'items.*.unit_cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000'
        ]);

        $order = $this->purchaseOrderService->receivePurchaseOrder($id, $request->validated());
        return response()->json($order);
    }

    /**
     * Cancel purchase order
     */
    public function cancel(Request $request, string $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $order = $this->purchaseOrderService->cancelPurchaseOrder($id, $request->reason);
        return response()->json($order);
    }

    /**
     * Get purchase order items
     */
    public function getItems(string $id)
    {
        $items = $this->purchaseOrderService->getPurchaseOrderItems($id);
        return response()->json($items);
    }

    /**
     * Add item to purchase order
     */
    public function addItem(Request $request, string $id)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit_cost' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:255'
        ]);

        $item = $this->purchaseOrderService->addItemToPurchaseOrder($id, $request->validated());
        return response()->json($item, 201);
    }

    /**
     * Update purchase order item
     */
    public function updateItem(Request $request, string $orderId, string $itemId)
    {
        $request->validate([
            'quantity' => 'sometimes|required|numeric|min:0.01',
            'unit_cost' => 'sometimes|required|numeric|min:0',
            'notes' => 'nullable|string|max:255'
        ]);

        $item = $this->purchaseOrderService->updatePurchaseOrderItem($itemId, $request->validated());
        return response()->json($item);
    }

    /**
     * Remove item from purchase order
     */
    public function removeItem(string $orderId, string $itemId)
    {
        $this->purchaseOrderService->removeItemFromPurchaseOrder($itemId);
        return response()->json(null, 204);
    }

    /**
     * Generate purchase order PDF
     */
    public function generatePDF(string $id)
    {
        $pdf = $this->purchaseOrderService->generatePurchaseOrderPDF($id);
        return response($pdf, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="purchase-order-' . $id . '.pdf"'
        ]);
    }
}
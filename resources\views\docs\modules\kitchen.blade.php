@extends('docs.layout')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <div class="p-4 rounded-full bg-orange-100 mr-4">
                <i class="fas fa-fire text-orange-600 text-2xl"></i>
            </div>
            <div>
                <h1 class="text-4xl font-bold text-gray-900">Kitchen Module</h1>
                <p class="text-xl text-gray-600 mt-2">Kitchen operations, order preparation, and cooking workflow management</p>
            </div>
        </div>
    </div>

    <!-- Module Overview -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Module Overview</h2>
        <p class="text-gray-600 mb-6">The Kitchen Module is a comprehensive solution for managing kitchen operations in a restaurant POS system. It handles kitchen stations, menu item assignments, KOT (Kitchen Order Ticket) processing, and real-time order preparation workflow.</p>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="text-center p-4 bg-orange-50 rounded-lg">
                <i class="fas fa-utensils text-orange-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">Kitchen Stations</h3>
                <p class="text-sm text-gray-600">Manage multiple kitchen stations per branch</p>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <i class="fas fa-clipboard-list text-blue-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">KOT Processing</h3>
                <p class="text-sm text-gray-600">Automated order ticket generation</p>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <i class="fas fa-clock text-green-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">Time Tracking</h3>
                <p class="text-sm text-gray-600">Monitor preparation times and efficiency</p>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <i class="fas fa-chart-line text-purple-600 text-2xl mb-2"></i>
                <h3 class="font-semibold text-gray-900">Analytics</h3>
                <p class="text-sm text-gray-600">Performance metrics and reporting</p>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>
        
        <div class="grid md:grid-cols-2 gap-8">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-cogs text-orange-600 mr-2"></i>
                    Kitchen Management
                </h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Create and manage multiple kitchen stations per branch</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Support for different station types (grill, fryer, salad, beverage, dessert)</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Configure operating hours, equipment lists, and capacity settings</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Assign managers and staff to specific kitchen stations</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-link text-blue-600 mr-2"></i>
                    Menu Item Assignment
                </h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Assign menu items to specific kitchen stations</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Set preparation times and priority levels for each assignment</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Ensure unique assignment (one menu item per kitchen per tenant)</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Manage special instructions and preparation notes</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-receipt text-green-600 mr-2"></i>
                    KOT Processing
                </h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Automatic KOT generation from orders</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Intelligent routing of order items to appropriate kitchens</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Real-time status tracking (pending → preparing → ready → completed)</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Priority management with visual indicators</li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-tachometer-alt text-purple-600 mr-2"></i>
                    Dashboard & Analytics
                </h3>
                <ul class="space-y-2 text-gray-600">
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Real-time kitchen dashboard with active orders</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Performance metrics and preparation time analysis</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Order queue management and status overview</li>
                    <li class="flex items-start"><i class="fas fa-check text-green-500 mr-2 mt-1"></i> Kitchen efficiency tracking and reporting</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- API Endpoints Section -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">API Endpoints</h2>
        
        <div class="space-y-8">
            <!-- Kitchen Management -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-server text-orange-600 mr-2"></i>
                    Kitchen Management
                </h3>
                
                <!-- List Kitchens -->
                <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4 mb-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kitchens</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">List All Kitchens</h4>
                    <p class="text-gray-600 mb-3">Retrieve a paginated list of all kitchen stations for the current branch.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Query Parameters:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>?page=1&per_page=10&station_type=grill&is_active=true</code></pre>
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Main Grill Station",
      "code": "GRILL-01",
      "station_type": "grill",
      "is_active": true,
      "max_concurrent_orders": 5,
      "operating_hours": {
        "start": "06:00",
        "end": "23:00"
      },
      "current_orders_count": 3,
      "assigned_menu_items_count": 12
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 10,
    "total": 4
  }
}</code></pre>
                    </div>
                </div>

                <!-- Create Kitchen -->
                <div class="endpoint-item border-l-4 border-green-500 pl-6 py-4 mb-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">POST</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kitchens</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">Create New Kitchen</h4>
                    <p class="text-gray-600 mb-3">Create a new kitchen station with configuration settings.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Request Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "name": "Dessert Station",
  "code": "DESSERT-01",
  "station_type": "dessert",
  "description": "Dedicated station for desserts and cold preparations",
  "max_concurrent_orders": 3,
  "operating_hours": {
    "start": "10:00",
    "end": "22:00"
  },
  "equipment_list": ["Ice cream machine", "Mixer", "Refrigerator"],
  "manager_id": 5,
  "is_active": true
}</code></pre>
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Kitchen created successfully",
  "data": {
    "id": 5,
    "name": "Dessert Station",
    "code": "DESSERT-01",
    "station_type": "dessert",
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z"
  }
}</code></pre>
                    </div>
                </div>

                <!-- Get Kitchen Details -->
                <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4 mb-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kitchens/{id}</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">Get Kitchen Details</h4>
                    <p class="text-gray-600 mb-3">Retrieve detailed information about a specific kitchen station.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "Main Grill Station",
    "code": "GRILL-01",
    "station_type": "grill",
    "description": "Primary grilling station for meats and vegetables",
    "max_concurrent_orders": 5,
    "operating_hours": {
      "start": "06:00",
      "end": "23:00"
    },
    "equipment_list": ["Gas grill", "Char grill", "Salamander"],
    "is_active": true,
    "manager": {
      "id": 3,
      "name": "John Smith",
      "email": "<EMAIL>"
    },
    "assigned_menu_items": [
      {
        "id": 1,
        "name": "Grilled Chicken",
        "prep_time_minutes": 15,
        "priority_level": "medium"
      }
    ],
    "current_orders_count": 3,
    "created_at": "2024-01-01T00:00:00Z"
  }
}</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Menu Item Assignment -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-link text-blue-600 mr-2"></i>
                    Menu Item Assignment
                </h3>
                
                <!-- Assign Menu Item -->
                <div class="endpoint-item border-l-4 border-green-500 pl-6 py-4 mb-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">POST</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kitchens/{id}/menu-items</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">Assign Menu Item to Kitchen</h4>
                    <p class="text-gray-600 mb-3">Assign a menu item to a specific kitchen station with preparation settings.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Request Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "menu_item_id": 15,
  "prep_time_minutes": 12,
  "priority_level": "high",
  "special_instructions": "Cook to medium-rare, season with house blend",
  "is_active": true
}</code></pre>
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Menu item assigned to kitchen successfully",
  "data": {
    "id": 25,
    "kitchen_id": 1,
    "menu_item_id": 15,
    "prep_time_minutes": 12,
    "priority_level": "high",
    "menu_item": {
      "id": 15,
      "name": "Ribeye Steak",
      "category": "Main Course"
    }
  }
}</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- KOT Operations -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-receipt text-green-600 mr-2"></i>
                    KOT Operations
                </h3>
                
                <!-- Create KOT from Order -->
                <div class="endpoint-item border-l-4 border-green-500 pl-6 py-4 mb-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">POST</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kots/create-from-order</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">Create KOT from Order</h4>
                    <p class="text-gray-600 mb-3">Generate Kitchen Order Tickets from a restaurant order, automatically routing items to appropriate kitchens.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Request Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "order_id": 123,
  "priority": "normal",
  "special_instructions": "Customer has nut allergy",
  "estimated_completion_time": "2024-01-15T12:45:00Z"
}</code></pre>
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "KOT created successfully",
  "data": [
    {
      "id": 45,
      "kot_number": "KOT-2024-001",
      "kitchen_id": 1,
      "kitchen_name": "Main Grill Station",
      "order_id": 123,
      "status": "pending",
      "priority": "normal",
      "items": [
        {
          "id": 67,
          "menu_item_name": "Grilled Chicken",
          "quantity": 2,
          "special_instructions": "No sauce",
          "status": "pending"
        }
      ],
      "estimated_completion_time": "2024-01-15T12:45:00Z",
      "created_at": "2024-01-15T12:30:00Z"
    }
  ]
}</code></pre>
                    </div>
                </div>

                <!-- Update KOT Status -->
                <div class="endpoint-item border-l-4 border-yellow-500 pl-6 py-4 mb-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">PATCH</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kots/{id}/status</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">Update KOT Status</h4>
                    <p class="text-gray-600 mb-3">Update the preparation status of a Kitchen Order Ticket.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Request Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>{
  "status": "preparing",
  "notes": "Started preparation at 12:35 PM"
}</code></pre>
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "KOT status updated successfully",
  "data": {
    "id": 45,
    "kot_number": "KOT-2024-001",
    "status": "preparing",
    "started_at": "2024-01-15T12:35:00Z",
    "estimated_completion_time": "2024-01-15T12:45:00Z"
  }
}</code></pre>
                    </div>
                </div>

                <!-- Get KOT Statistics -->
                <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kots/statistics</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">Get KOT Statistics</h4>
                    <p class="text-gray-600 mb-3">Retrieve kitchen performance statistics and metrics.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Query Parameters:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto mb-4"><code>?date_from=2024-01-01&date_to=2024-01-31&kitchen_id=1</code></pre>
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "total_kots": 156,
    "completed_kots": 142,
    "pending_kots": 8,
    "preparing_kots": 6,
    "average_prep_time_minutes": 18.5,
    "efficiency_percentage": 91.0,
    "busiest_hours": [
      {"hour": "12:00", "kot_count": 25},
      {"hour": "19:00", "kot_count": 22}
    ],
    "kitchen_performance": [
      {
        "kitchen_id": 1,
        "kitchen_name": "Main Grill Station",
        "total_kots": 89,
        "average_prep_time": 16.2,
        "efficiency": 94.5
      }
    ]
  }
}</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Dashboard & Monitoring -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-tachometer-alt text-purple-600 mr-2"></i>
                    Dashboard & Monitoring
                </h3>
                
                <!-- Kitchen Dashboard -->
                <div class="endpoint-item border-l-4 border-blue-500 pl-6 py-4">
                    <div class="flex items-center mb-3">
                        <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">GET</span>
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">/api/kitchen/kitchens/{id}/dashboard</code>
                    </div>
                    <h4 class="font-semibold text-lg mb-2">Kitchen Dashboard Data</h4>
                    <p class="text-gray-600 mb-3">Get real-time dashboard data for a specific kitchen station.</p>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold mb-2">Response Example:</h5>
                        <pre class="bg-gray-800 text-green-400 p-3 rounded text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "kitchen": {
      "id": 1,
      "name": "Main Grill Station",
      "status": "active",
      "capacity_utilization": 60
    },
    "active_kots": [
      {
        "id": 45,
        "kot_number": "KOT-2024-001",
        "order_number": "ORD-2024-123",
        "status": "preparing",
        "priority": "high",
        "items_count": 3,
        "estimated_completion": "2024-01-15T12:45:00Z",
        "elapsed_time_minutes": 8
      }
    ],
    "today_stats": {
      "completed_kots": 12,
      "pending_kots": 3,
      "average_prep_time": 16.5,
      "efficiency_percentage": 92.0
    },
    "queue_summary": {
      "high_priority": 1,
      "normal_priority": 2,
      "low_priority": 0
    }
  }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Models Section -->
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Data Models</h2>
        
        <div class="grid md:grid-cols-2 gap-6">
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <i class="fas fa-database text-orange-600 mr-2"></i>
                    Kitchen
                </h3>
                <p class="text-gray-600 mb-3">Represents a kitchen station with configuration and capacity settings.</p>
                <div class="text-sm text-gray-500">
                    <strong>Key Fields:</strong> name, code, station_type, max_concurrent_orders, operating_hours
                </div>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <i class="fas fa-database text-blue-600 mr-2"></i>
                    KitchenMenuItem
                </h3>
                <p class="text-gray-600 mb-3">Manages the assignment of menu items to specific kitchens.</p>
                <div class="text-sm text-gray-500">
                    <strong>Key Fields:</strong> kitchen_id, menu_item_id, prep_time_minutes, priority_level
                </div>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <i class="fas fa-database text-green-600 mr-2"></i>
                    KotOrder
                </h3>
                <p class="text-gray-600 mb-3">Represents a Kitchen Order Ticket with status tracking.</p>
                <div class="text-sm text-gray-500">
                    <strong>Key Fields:</strong> order_id, kitchen_id, status, priority, estimated_completion_time
                </div>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <i class="fas fa-database text-purple-600 mr-2"></i>
                    KotOrderItem
                </h3>
                <p class="text-gray-600 mb-3">Individual items within a KOT order.</p>
                <div class="text-sm text-gray-500">
                    <strong>Key Fields:</strong> kot_order_id, order_item_id, menu_item_id, status, prep_time_minutes
                </div>
            </div>
        </div>
    </div>

    <!-- Integration Section -->
    <div class="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Integration</h2>
        
        <div class="grid md:grid-cols-3 gap-6">
            <div class="text-center p-6 bg-blue-50 rounded-lg">
                <i class="fas fa-shopping-cart text-blue-600 text-3xl mb-4"></i>
                <h3 class="font-semibold text-gray-900 mb-2">Orders Module</h3>
                <p class="text-sm text-gray-600">Automatic KOT creation when orders are placed. Order status updates reflect kitchen preparation progress.</p>
            </div>
            
            <div class="text-center p-6 bg-green-50 rounded-lg">
                <i class="fas fa-utensils text-green-600 text-3xl mb-4"></i>
                <h3 class="font-semibold text-gray-900 mb-2">Menu Module</h3>
                <p class="text-sm text-gray-600">Menu items are assigned to kitchens with preparation times and special instructions.</p>
            </div>
            
            <div class="text-center p-6 bg-purple-50 rounded-lg">
                <i class="fas fa-users text-purple-600 text-3xl mb-4"></i>
                <h3 class="font-semibold text-gray-900 mb-2">Staff Module</h3>
                <p class="text-sm text-gray-600">Kitchen managers and staff are assigned to specific kitchen stations for workflow management.</p>
            </div>
        </div>
    </div>
</div>
@endsection
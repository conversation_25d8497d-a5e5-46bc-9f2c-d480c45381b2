<?php

use Illuminate\Support\Facades\Route;
use Modules\Auth\Http\Controllers\AuthController;
use Modules\Auth\Http\Controllers\RoleController;
use Modules\Auth\Http\Controllers\PermissionController;

/*
|--------------------------------------------------------------------------
| Auth API Routes
|--------------------------------------------------------------------------
|
| Here are the authentication API routes for the Auth module.
| These routes are loaded by the AuthServiceProvider within a group which
| contains the "api" middleware group.
|
*/

// Public routes (no authentication required)
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
});

// Protected routes (authentication required)
Route::middleware(['auth:api'])->prefix('auth')->group(function () {
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('logout-all', [AuthController::class, 'logoutAll']);
    Route::post('refresh-token', [AuthController::class, 'refreshToken']);
    Route::get('profile', [AuthController::class, 'profile']);
    Route::put('profile', [AuthController::class, 'updateProfile']);
    Route::post('change-password', [AuthController::class, 'changePassword']);
    Route::post('verify-email', [AuthController::class, 'verifyEmail']);
    Route::post('resend-verification', [AuthController::class, 'resendEmailVerification']);
});

// Admin routes (require admin role)
Route::middleware(['auth:api'])->prefix('auth/admin')->group(function () {
// Route::middleware(['auth:api', 'role:admin'])->prefix('auth/admin')->group(function () {
    // Role management
    Route::apiResource('roles', RoleController::class);
    Route::post('roles/{role}/permissions', [RoleController::class, 'assignPermissions']);
    Route::delete('roles/{role}/permissions', [RoleController::class, 'revokePermissions']);
    
    // Permission management
    Route::apiResource('permissions', PermissionController::class);
    Route::get('permissions/grouped', [PermissionController::class, 'grouped']);
    Route::post('permissions/bulk', [PermissionController::class, 'bulkStore']);
    
    // User management
    Route::get('users', [AuthController::class, 'getUsers']);
    Route::post('users/{user}/roles', [AuthController::class, 'assignUserRoles']);
    Route::delete('users/{user}/roles', [AuthController::class, 'revokeUserRoles']);
    Route::post('users/{user}/permissions', [AuthController::class, 'assignUserPermissions']);
    Route::delete('users/{user}/permissions', [AuthController::class, 'revokeUserPermissions']);
});


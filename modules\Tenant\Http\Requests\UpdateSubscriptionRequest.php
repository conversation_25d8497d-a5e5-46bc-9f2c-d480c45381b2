<?php

namespace Modules\Tenant\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'plan_id' => 'sometimes|required|exists:subscription_plans,id',
            'billing_cycle' => 'sometimes|required|in:monthly,yearly,quarterly',
            'auto_renew' => 'sometimes|nullable|boolean',
            'payment_method' => 'sometimes|nullable|string|in:credit_card,bank_transfer,paypal,stripe,manual',
            'payment_details' => 'sometimes|nullable|array',
            'payment_details.card_last_four' => 'nullable|string|size:4',
            'payment_details.card_brand' => 'nullable|string|max:50',
            'payment_details.payment_method_id' => 'nullable|string|max:255',
            'discount_code' => 'sometimes|nullable|string|max:50',
            'discount_amount' => 'sometimes|nullable|numeric|min:0',
            'discount_type' => 'sometimes|nullable|in:fixed,percentage',
            'custom_pricing' => 'sometimes|nullable|array',
            'custom_pricing.amount' => 'nullable|numeric|min:0',
            'custom_pricing.currency' => 'nullable|string|size:3',
            'custom_pricing.reason' => 'nullable|string|max:500',
            'notes' => 'sometimes|nullable|string|max:1000',
            'metadata' => 'sometimes|nullable|array',
            'status' => 'sometimes|nullable|in:active,cancelled,suspended,expired',
            'ends_at' => 'sometimes|nullable|date|after:today',
            'trial_ends_at' => 'sometimes|nullable|date',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'plan_id.required' => 'The subscription plan is required.',
            'plan_id.exists' => 'The selected subscription plan is invalid.',
            'billing_cycle.required' => 'The billing cycle is required.',
            'billing_cycle.in' => 'Billing cycle must be one of: monthly, yearly, quarterly.',
            'payment_method.in' => 'Payment method must be one of: credit_card, bank_transfer, paypal, stripe, manual.',
            'payment_details.card_last_four.size' => 'Card last four digits must be exactly 4 characters.',
            'discount_amount.numeric' => 'Discount amount must be a number.',
            'discount_amount.min' => 'Discount amount must be at least 0.',
            'discount_type.in' => 'Discount type must be either fixed or percentage.',
            'custom_pricing.amount.numeric' => 'Custom pricing amount must be a number.',
            'custom_pricing.amount.min' => 'Custom pricing amount must be at least 0.',
            'custom_pricing.currency.size' => 'Currency code must be exactly 3 characters.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'status.in' => 'Status must be one of: active, cancelled, suspended, expired.',
            'ends_at.date' => 'The end date must be a valid date.',
            'ends_at.after' => 'The end date must be in the future.',
            'trial_ends_at.date' => 'The trial end date must be a valid date.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'plan_id' => 'subscription plan',
            'billing_cycle' => 'billing cycle',
            'auto_renew' => 'auto renewal',
            'payment_method' => 'payment method',
            'payment_details' => 'payment details',
            'discount_code' => 'discount code',
            'discount_amount' => 'discount amount',
            'discount_type' => 'discount type',
            'custom_pricing' => 'custom pricing',
            'metadata' => 'metadata',
            'ends_at' => 'end date',
            'trial_ends_at' => 'trial end date',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Normalize currency code to uppercase
        if ($this->has('custom_pricing.currency')) {
            $customPricing = $this->input('custom_pricing');
            $customPricing['currency'] = strtoupper($customPricing['currency']);
            $this->merge(['custom_pricing' => $customPricing]);
        }

        // Normalize discount code to uppercase
        if ($this->has('discount_code')) {
            $this->merge([
                'discount_code' => strtoupper($this->discount_code)
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate discount logic
            if ($this->has('discount_amount') || $this->has('discount_type')) {
                $this->validateDiscount($validator);
            }

            // Validate custom pricing
            if ($this->has('custom_pricing')) {
                $this->validateCustomPricing($validator);
            }

            // Validate payment details based on payment method
            if ($this->has('payment_method')) {
                $this->validatePaymentDetails($validator);
            }

            // Validate status transitions
            if ($this->has('status')) {
                $this->validateStatusTransition($validator);
            }

            // Validate plan change
            if ($this->has('plan_id')) {
                $this->validatePlanChange($validator);
            }
        });
    }

    /**
     * Validate discount logic
     */
    protected function validateDiscount($validator): void
    {
        $discountAmount = $this->input('discount_amount');
        $discountType = $this->input('discount_type');

        // If discount amount is provided, discount type is required
        if ($discountAmount && !$discountType) {
            $validator->errors()->add(
                'discount_type',
                'Discount type is required when discount amount is provided.'
            );
        }

        // If discount type is provided, discount amount is required
        if ($discountType && !$discountAmount) {
            $validator->errors()->add(
                'discount_amount',
                'Discount amount is required when discount type is provided.'
            );
        }

        // Validate percentage discount
        if ($discountType === 'percentage' && $discountAmount > 100) {
            $validator->errors()->add(
                'discount_amount',
                'Percentage discount cannot exceed 100%.'
            );
        }
    }

    /**
     * Validate custom pricing
     */
    protected function validateCustomPricing($validator): void
    {
        $customPricing = $this->input('custom_pricing', []);

        // If custom pricing is provided, amount and currency are required
        if (!empty($customPricing)) {
            if (!isset($customPricing['amount'])) {
                $validator->errors()->add(
                    'custom_pricing.amount',
                    'Custom pricing amount is required when using custom pricing.'
                );
            }

            if (!isset($customPricing['currency'])) {
                $validator->errors()->add(
                    'custom_pricing.currency',
                    'Custom pricing currency is required when using custom pricing.'
                );
            }
        }
    }

    /**
     * Validate payment details based on payment method
     */
    protected function validatePaymentDetails($validator): void
    {
        $paymentMethod = $this->input('payment_method');
        $paymentDetails = $this->input('payment_details', []);

        if ($paymentMethod === 'credit_card' || $paymentMethod === 'stripe') {
            if (empty($paymentDetails['payment_method_id'])) {
                $validator->errors()->add(
                    'payment_details.payment_method_id',
                    'Payment method ID is required for credit card payments.'
                );
            }
        }
    }

    /**
     * Validate status transitions
     */
    protected function validateStatusTransition($validator): void
    {
        $newStatus = $this->input('status');
        $subscriptionId = $this->route('subscription');
        
        // Get current subscription status (you might want to inject the subscription model)
        // For now, we'll skip this validation as it requires database access
        // In a real implementation, you would check valid status transitions
        
        // Example validation rules:
        // - Cannot change from 'cancelled' to 'active' directly
        // - Cannot change from 'expired' to 'active' without renewal
        
        if ($newStatus === 'cancelled' && $this->has('ends_at')) {
            $endsAt = $this->input('ends_at');
            if ($endsAt && \Carbon\Carbon::parse($endsAt)->isPast()) {
                $validator->errors()->add(
                    'ends_at',
                    'End date must be in the future when cancelling a subscription.'
                );
            }
        }
    }

    /**
     * Validate plan change
     */
    protected function validatePlanChange($validator): void
    {
        $newPlanId = $this->input('plan_id');
        $subscriptionId = $this->route('subscription');
        
        // Get current subscription (you might want to inject the subscription model)
        // For now, we'll skip detailed validation
        
        // Example validation:
        // - Check if the new plan is compatible with current usage
        // - Validate upgrade/downgrade rules
        // - Check if immediate change is allowed or should be scheduled
        
        // Basic validation: ensure plan exists and is active
        if ($newPlanId) {
            $plan = \App\Models\SubscriptionPlan::find($newPlanId);
            if ($plan && !$plan->is_active) {
                $validator->errors()->add(
                    'plan_id',
                    'The selected subscription plan is not currently available.'
                );
            }
        }
    }
}
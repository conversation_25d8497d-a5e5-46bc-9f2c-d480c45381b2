<?php

namespace Modules\Settings\Services;

use App\Models\Setting;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class SettingService
{
    /**
     * Get all settings with optional filters
     *
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAll($filters = [])
    {
        try {
            $query = Setting::query();
            
            if (isset($filters['tenant_id'])) {
                $query->where('tenant_id', $filters['tenant_id']);
            }
            
            if (isset($filters['branch_id'])) {
                $query->where('branch_id', $filters['branch_id']);
            }
            
            if (isset($filters['category'])) {
                $query->where('category', $filters['category']);
            }
            
            return $query->orderBy('category')->orderBy('key')->get();
        } catch (\Exception $e) {
            Log::error('Failed to retrieve settings', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Get a specific setting by key and optional filters
     *
     * @param string $key
     * @param int|null $tenantId
     * @param int|null $branchId
     * @param string|null $category
     * @return Setting|null
     */
    public function get($key, $tenantId = null, $branchId = null, $category = null)
    {
        try {
            $query = Setting::where('key', $key);
            
            if ($tenantId) {
                $query->where('tenant_id', $tenantId);
            }
            
            if ($branchId) {
                $query->where('branch_id', $branchId);
            }
            
            if ($category) {
                $query->where('category', $category);
            }
            
            return $query->first();
        } catch (\Exception $e) {
            Log::error('Failed to retrieve setting', [
                'key' => $key,
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'category' => $category,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Create or update a setting
     *
     * @param array $data
     * @return Setting
     * @throws \Exception
     */
    public function set($data)
    {
        try {
            // Validate required fields
            if (!isset($data['key']) || empty($data['key'])) {
                throw new \InvalidArgumentException('Setting key is required');
            }
            
            if (!isset($data['category']) || empty($data['category'])) {
                throw new \InvalidArgumentException('Setting category is required');
            }
            
            $conditions = [
                'key' => $data['key'],
                'tenant_id' => $data['tenant_id'] ?? null,
                'branch_id' => $data['branch_id'] ?? null,
                'category' => $data['category'],
            ];
            
            // Remove null values from data to avoid overwriting existing values with null
            $updateData = array_filter($data, function($value) {
                return $value !== null;
            });
            
            $setting = Setting::updateOrCreate($conditions, $updateData);
            
            Log::info('Setting saved successfully', [
                'key' => $data['key'],
                'category' => $data['category'],
                'tenant_id' => $data['tenant_id'] ?? null,
                'branch_id' => $data['branch_id'] ?? null
            ]);
            
            return $setting;
        } catch (\Exception $e) {
            Log::error('Failed to save setting', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Delete a setting
     *
     * @param string $key
     * @param int|null $tenantId
     * @param int|null $branchId
     * @param string|null $category
     * @return bool
     */
    public function delete($key, $tenantId = null, $branchId = null, $category = null)
    {
        try {
            $query = Setting::where('key', $key);
            
            if ($tenantId) {
                $query->where('tenant_id', $tenantId);
            }
            
            if ($branchId) {
                $query->where('branch_id', $branchId);
            }
            
            if ($category) {
                $query->where('category', $category);
            }
            
            $deletedCount = $query->delete();
            
            if ($deletedCount > 0) {
                Log::info('Setting deleted successfully', [
                    'key' => $key,
                    'tenant_id' => $tenantId,
                    'branch_id' => $branchId,
                    'category' => $category,
                    'deleted_count' => $deletedCount
                ]);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to delete setting', [
                'key' => $key,
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'category' => $category,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Get settings by category
     *
     * @param string $category
     * @param int|null $tenantId
     * @param int|null $branchId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByCategory($category, $tenantId = null, $branchId = null)
    {
        try {
            $query = Setting::where('category', $category);
            
            if ($tenantId) {
                $query->where('tenant_id', $tenantId);
            }
            
            if ($branchId) {
                $query->where('branch_id', $branchId);
            }
            
            return $query->orderBy('key')->get();
        } catch (\Exception $e) {
            Log::error('Failed to retrieve settings by category', [
                'category' => $category,
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Check if a setting exists
     *
     * @param string $key
     * @param int|null $tenantId
     * @param int|null $branchId
     * @param string|null $category
     * @return bool
     */
    public function exists($key, $tenantId = null, $branchId = null, $category = null)
    {
        try {
            $query = Setting::where('key', $key);
            
            if ($tenantId) {
                $query->where('tenant_id', $tenantId);
            }
            
            if ($branchId) {
                $query->where('branch_id', $branchId);
            }
            
            if ($category) {
                $query->where('category', $category);
            }
            
            return $query->exists();
        } catch (\Exception $e) {
            Log::error('Failed to check setting existence', [
                'key' => $key,
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'category' => $category,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    // ========== Specialized Setting Category Methods ==========

    /**
     * Get KOT (Kitchen Order Ticket) settings
     */
    public function getKotSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('kot_settings', $tenantId, $branchId);
    }

    /**
     * Set KOT setting
     */
    public function setKotSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'kot_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
        ]);
    }

    /**
     * Get language settings
     */
    public function getLanguageSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('language_settings', $tenantId, $branchId);
    }

    /**
     * Set language setting
     */
    public function setLanguageSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'language_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : 'string'
        ]);
    }

    /**
     * Get notification settings
     */
    public function getNotificationSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('notification_settings', $tenantId, $branchId);
    }

    /**
     * Set notification setting
     */
    public function setNotificationSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'notification_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
        ]);
    }

    /**
     * Get Pusher settings
     */
    public function getPusherSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('pusher_settings', $tenantId, $branchId);
    }

    /**
     * Set Pusher setting
     */
    public function setPusherSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'pusher_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => 'string'
        ]);
    }

    /**
     * Get receipt settings
     */
    public function getReceiptSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('receipt_settings', $tenantId, $branchId);
    }

    /**
     * Set receipt setting
     */
    public function setReceiptSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'receipt_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
        ]);
    }

    /**
     * Get reservation settings
     */
    public function getReservationSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('reservation_settings', $tenantId, $branchId);
    }

    /**
     * Set reservation setting
     */
    public function setReservationSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'reservation_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
        ]);
    }

    /**
     * Get branch delivery settings
     */
    public function getBranchDeliverySettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('branch_delivery_settings', $tenantId, $branchId);
    }

    /**
     * Set branch delivery setting
     */
    public function setBranchDeliverySetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'branch_delivery_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
        ]);
    }

    /**
     * Get email settings
     */
    public function getEmailSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('email_settings', $tenantId, $branchId);
    }

    /**
     * Set email setting
     */
    public function setEmailSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'email_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : 'string'
        ]);
    }

    /**
     * Get file storage settings
     */
    public function getFileStorageSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('file_storage_settings', $tenantId, $branchId);
    }

    /**
     * Set file storage setting
     */
    public function setFileStorageSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'file_storage_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : 'string'
        ]);
    }

    /**
     * Get front FAQ settings
     */
    public function getFrontFaqSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('front_faq_settings', $tenantId, $branchId);
    }

    /**
     * Set front FAQ setting
     */
    public function setFrontFaqSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'front_faq_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : 'string'
        ]);
    }

    /**
     * Get front review settings
     */
    public function getFrontReviewSettings($tenantId = null, $branchId = null)
    {
        return $this->getByCategory('front_review_settings', $tenantId, $branchId);
    }

    /**
     * Set front review setting
     */
    public function setFrontReviewSetting($key, $value, $tenantId = null, $branchId = null, $description = null)
    {
        return $this->set([
            'key' => $key,
            'value' => $value,
            'category' => 'front_review_settings',
            'tenant_id' => $tenantId,
            'branch_id' => $branchId,
            'description' => $description,
            'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
        ]);
    }

    /**
     * Get all available setting categories
     */
    public function getAvailableCategories()
    {
        return [
            'general',
            'kot_settings',
            'language_settings',
            'notification_settings',
            'pusher_settings',
            'receipt_settings',
            'reservation_settings',
            'branch_delivery_settings',
            'email_settings',
            'file_storage_settings',
            'front_faq_settings',
            'front_review_settings'
        ];
    }

    /**
     * Bulk update settings for a category
     */
    public function bulkUpdateCategory($category, $settings, $tenantId = null, $branchId = null)
    {
        try {
            $updatedSettings = [];
            
            foreach ($settings as $key => $value) {
                $setting = $this->set([
                    'key' => $key,
                    'value' => $value,
                    'category' => $category,
                    'tenant_id' => $tenantId,
                    'branch_id' => $branchId,
                    'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
                ]);
                
                $updatedSettings[] = $setting;
            }
            
            Log::info('Bulk settings update completed', [
                'category' => $category,
                'count' => count($updatedSettings),
                'tenant_id' => $tenantId,
                'branch_id' => $branchId
            ]);
            
            return $updatedSettings;
        } catch (\Exception $e) {
            Log::error('Failed to bulk update settings', [
                'category' => $category,
                'settings' => $settings,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
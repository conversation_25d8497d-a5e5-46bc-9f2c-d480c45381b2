@extends('docs.layout')

@section('title', 'Customers Module Documentation')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Module Header -->
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-lg mr-4">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Customers Module</h1>
                        <p class="text-gray-600 mt-1">Complete customer management system with profiles, preferences, and loyalty tracking</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="bg-gradient-to-r from-blue-100 to-purple-100 px-4 py-2 rounded-lg">
                        <span class="text-blue-800 font-semibold">v3.0.0</span>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                    <div class="flex items-center">
                        <i class="fas fa-globe text-blue-600 text-xl mr-3"></i>
                        <div>
                            <p class="text-blue-800 font-semibold">19 Total Endpoints</p>
                            <p class="text-blue-600 text-sm">API Routes</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                    <div class="flex items-center">
                        <i class="fas fa-unlock text-green-600 text-xl mr-3"></i>
                        <div>
                            <p class="text-green-800 font-semibold">4 Public Routes</p>
                            <p class="text-green-600 text-sm">No Auth Required</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-red-50 to-red-100 p-4 rounded-lg border border-red-200">
                    <div class="flex items-center">
                        <i class="fas fa-lock text-red-600 text-xl mr-3"></i>
                        <div>
                            <p class="text-red-800 font-semibold">15 Protected Routes</p>
                            <p class="text-red-600 text-sm">Auth Required</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                    <div class="flex items-center">
                        <i class="fas fa-layer-group text-purple-600 text-xl mr-3"></i>
                        <div>
                            <p class="text-purple-800 font-semibold">6 Categories</p>
                            <p class="text-purple-600 text-sm">Customer Features</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="bg-white rounded-lg shadow p-4 mb-8 border border-gray-100">
            <div class="flex items-center">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <input type="text" id="endpointSearch" placeholder="Search endpoints, methods, or descriptions..." 
                       class="flex-1 border-0 focus:ring-0 focus:outline-none text-gray-700 placeholder-gray-400">
            </div>
        </div>

        <!-- Public Routes Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-globe text-green-600 mr-3"></i>
                Public Routes
                <span class="ml-3 bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded">No Auth Required</span>
            </h2>

            <!-- Customer Registration Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/customers/register</h3>
                            <span class="ml-3 text-gray-600">Register new customer</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "password": "password123",
  "date_of_birth": "1990-01-01",
  "preferences": {
    "dietary_restrictions": ["vegetarian"],
    "favorite_cuisine": "italian"
  }
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Customer registered successfully",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "customer_code": "CUST001",
    "loyalty_points": 0,
    "created_at": "2024-01-01T00:00:00.000000Z"
  },
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Creates a new customer account with basic information and preferences. Returns authentication token.</p>
                    </div>
                </div>
            </div>

            <!-- Customer Login Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/customers/login</h3>
                            <span class="ml-3 text-gray-600">Customer login</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "email": "<EMAIL>",
  "password": "password123"
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Login successful",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "customer_code": "CUST001",
    "loyalty_points": 150,
    "tier": "Silver"
  },
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Authenticates customer credentials and returns access token with customer information.</p>
                    </div>
                </div>
            </div>

            <!-- Password Reset Endpoint -->
            <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                <div class="cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/customers/password/reset</h3>
                            <span class="ml-3 text-gray-600">Request password reset</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "email": "<EMAIL>"
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Password reset email sent successfully"
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Sends password reset email to customer's registered email address.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Protected Routes Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-lock text-red-600 mr-3"></i>
                Protected Routes
                <span class="ml-3 bg-red-100 text-red-800 text-sm font-medium px-2.5 py-0.5 rounded">Auth Required</span>
            </h2>

            <!-- Customer Profile Management -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-user text-blue-600 mr-2"></i>
                    Profile Management
                </h3>

                <!-- Get Customer Profile Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/profile</h3>
                                <span class="ml-3 text-gray-600">Get customer profile</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "customer_code": "CUST001",
    "date_of_birth": "1990-01-01",
    "loyalty_points": 150,
    "tier": "Silver",
    "preferences": {
      "dietary_restrictions": ["vegetarian"],
      "favorite_cuisine": "italian",
      "notifications": true
    },
    "addresses": [
      {
        "id": 1,
        "type": "home",
        "address": "123 Main St",
        "city": "New York",
        "is_default": true
      }
    ],
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves complete customer profile including preferences, addresses, and loyalty information.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Customer Profile Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/profile</h3>
                                <span class="ml-3 text-gray-600">Update customer profile</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "John Smith",
  "phone": "+1234567891",
  "date_of_birth": "1990-01-01",
  "preferences": {
    "dietary_restrictions": ["vegetarian", "gluten-free"],
    "favorite_cuisine": "mediterranean",
    "notifications": false
  }
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": 1,
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "+1234567891",
    "customer_code": "CUST001",
    "date_of_birth": "1990-01-01",
    "preferences": {
      "dietary_restrictions": ["vegetarian", "gluten-free"],
      "favorite_cuisine": "mediterranean",
      "notifications": false
    },
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates customer profile information. Only provided fields will be updated.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Management (CRUD) -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-users-cog text-blue-600 mr-2"></i>
                    Customer Management
                </h3>

                <!-- List Customers Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers</h3>
                                <span class="ml-3 text-gray-600">List all customers</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>page=1
per_page=15
search=john
status=active
tier=silver</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "customers": [
      {
        "id": 1,
        "customer_code": "CUST001",
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+**********",
        "loyalty_points": 150,
        "tier": "Silver",
        "status": "active",
        "created_at": "2024-01-01T00:00:00.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 67,
      "per_page": 15
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves paginated list of customers with optional filtering and search capabilities.</p>
                        </div>
                    </div>
                </div>

                <!-- Create Customer Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers</h3>
                                <span class="ml-3 text-gray-600">Create new customer</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "password": "password123",
  "date_of_birth": "1990-01-01",
  "preferences": {
    "dietary_restrictions": ["vegetarian"],
    "favorite_cuisine": "italian"
  }
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Customer created successfully",
  "data": {
    "id": 1,
    "customer_code": "CUST001",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "loyalty_points": 0,
    "tier": "Bronze",
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new customer account with automatic customer code generation and initial loyalty tier assignment.</p>
                        </div>
                    </div>
                </div>

                <!-- Show Customer Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}</h3>
                                <span class="ml-3 text-gray-600">Get customer details</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "customer_code": "CUST001",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "date_of_birth": "1990-01-01",
    "loyalty_points": 150,
    "tier": "Silver",
    "status": "active",
    "last_visit": "2024-01-15T00:00:00.000000Z",
    "total_orders": 12,
    "total_spent": 450.75,
    "preferences": {
      "dietary_restrictions": ["vegetarian"],
      "favorite_cuisine": "italian"
    },
    "addresses": [
      {
        "id": 1,
        "type": "home",
        "address": "123 Main St",
        "city": "New York",
        "is_default": true
      }
    ],
    "created_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves detailed information about a specific customer including addresses, preferences, and order history summary.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Customer Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}</h3>
                                <span class="ml-3 text-gray-600">Update customer</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "John Smith",
  "phone": "+1234567891",
  "date_of_birth": "1990-01-01",
  "preferences": {
    "dietary_restrictions": ["vegetarian", "gluten-free"],
    "favorite_cuisine": "mediterranean"
  }
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Customer updated successfully",
  "data": {
    "id": 1,
    "customer_code": "CUST001",
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "+1234567891",
    "date_of_birth": "1990-01-01",
    "loyalty_points": 150,
    "tier": "Silver",
    "preferences": {
      "dietary_restrictions": ["vegetarian", "gluten-free"],
      "favorite_cuisine": "mediterranean"
    },
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates customer information. Only provided fields will be updated. Email cannot be changed through this endpoint.</p>
                        </div>
                    </div>
                </div>

                <!-- Delete Customer Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}</h3>
                                <span class="ml-3 text-gray-600">Delete customer</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Customer deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Soft deletes a customer account. The customer data is retained for audit purposes but marked as deleted.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Search & Utilities -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-search text-green-600 mr-2"></i>
                    Search & Utilities
                </h3>

                <!-- Find Customer by Contact Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/find-by-contact</h3>
                                <span class="ml-3 text-gray-600">Find customer by contact</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>contact=<EMAIL>
# OR
contact=+**********</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "id": 1,
    "customer_code": "CUST001",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "loyalty_points": 150,
    "tier": "Silver",
    "status": "active"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Finds a customer by email address or phone number. Useful for order placement and customer lookup.</p>
                        </div>
                    </div>
                </div>

                <!-- Customer Statistics Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/stats</h3>
                                <span class="ml-3 text-gray-600">Get customer statistics</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "total_customers": 1250,
    "active_customers": 1180,
    "inactive_customers": 70,
    "new_this_month": 45,
    "tier_distribution": {
      "Bronze": 650,
      "Silver": 380,
      "Gold": 180,
      "Platinum": 40
    },
    "average_loyalty_points": 245.5,
    "total_loyalty_points": 306875
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Provides comprehensive statistics about the customer base including counts, tier distribution, and loyalty metrics.</p>
                        </div>
                    </div>
                </div>

                <!-- Top Customers Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/top</h3>
                                <span class="ml-3 text-gray-600">Get top customers</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>limit=10
by=total_spent
# Options: total_spent, order_count, loyalty_points</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": [
    {
      "id": 1,
      "customer_code": "CUST001",
      "name": "John Doe",
      "email": "<EMAIL>",
      "tier": "Platinum",
      "total_spent": 2450.75,
      "order_count": 45,
      "loyalty_points": 1250,
      "last_order": "2024-01-15T00:00:00.000000Z"
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves top customers ranked by total spending, order count, or loyalty points. Useful for VIP customer identification.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Status Management -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-toggle-on text-purple-600 mr-2"></i>
                    Status Management
                </h3>

                <!-- Activate Customer Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PATCH</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}/activate</h3>
                                <span class="ml-3 text-gray-600">Activate customer</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Customer activated successfully",
  "data": {
    "id": 1,
    "customer_code": "CUST001",
    "name": "John Doe",
    "status": "active",
    "activated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Activates a customer account, allowing them to place orders and access services.</p>
                        </div>
                    </div>
                </div>

                <!-- Deactivate Customer Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PATCH</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}/deactivate</h3>
                                <span class="ml-3 text-gray-600">Deactivate customer</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Customer deactivated successfully",
  "data": {
    "id": 1,
    "customer_code": "CUST001",
    "name": "John Doe",
    "status": "inactive",
    "deactivated_at": "2024-01-01T00:00:00.000000Z"
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Deactivates a customer account, preventing them from placing new orders while preserving their data.</p>
                        </div>
                    </div>
                </div>

                <!-- Update Visit Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PATCH</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}/visit</h3>
                                <span class="ml-3 text-gray-600">Update last visit</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Visit updated successfully",
  "data": {
    "id": 1,
    "customer_code": "CUST001",
    "name": "John Doe",
    "last_visit": "2024-01-01T00:00:00.000000Z",
    "visit_count": 25
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates the customer's last visit timestamp and increments visit count. Used for tracking customer engagement.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loyalty Points Management -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-star text-yellow-600 mr-2"></i>
                    Loyalty Points Management
                </h3>

                <!-- Add Loyalty Points Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}/loyalty/add</h3>
                                <span class="ml-3 text-gray-600">Add loyalty points</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "points": 50,
  "reason": "Order completion bonus",
  "reference_id": "ORD001",
  "reference_type": "order"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Loyalty points added successfully",
  "data": {
    "customer_id": 1,
    "points_added": 50,
    "total_points": 200,
    "previous_tier": "Bronze",
    "current_tier": "Silver",
    "tier_upgraded": true,
    "transaction": {
      "id": 123,
      "type": "earned",
      "points": 50,
      "reason": "Order completion bonus",
      "reference_id": "ORD001",
      "created_at": "2024-01-01T00:00:00.000000Z"
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Adds loyalty points to a customer's account with automatic tier upgrade detection and transaction logging.</p>
                        </div>
                    </div>
                </div>

                <!-- Redeem Loyalty Points Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}/loyalty/redeem</h3>
                                <span class="ml-3 text-gray-600">Redeem loyalty points</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "points": 100,
  "reason": "Discount on order",
  "reference_id": "ORD002",
  "reference_type": "order"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Loyalty points redeemed successfully",
  "data": {
    "customer_id": 1,
    "points_redeemed": 100,
    "remaining_points": 100,
    "discount_amount": 10.00,
    "transaction": {
      "id": 124,
      "type": "redeemed",
      "points": -100,
      "reason": "Discount on order",
      "reference_id": "ORD002",
      "created_at": "2024-01-01T00:00:00.000000Z"
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Redeems loyalty points for discounts or rewards with automatic balance validation and transaction logging.</p>
                        </div>
                    </div>
                </div>

                <!-- Loyalty History Endpoint -->
                <div class="bg-white rounded-lg shadow p-6 mb-4 border border-gray-100 endpoint-item">
                    <div class="cursor-pointer" onclick="toggleDropdown(this)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">/api/customers/{id}/loyalty/history</h3>
                                <span class="ml-3 text-gray-600">Get loyalty history</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                        </div>
                    </div>
                    <div class="endpoint-dropdown mt-4 pt-4 border-t border-gray-200" style="display: none;">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Path Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>id: Customer ID (integer)</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>page=1
per_page=20
type=earned
# Options: earned, redeemed, expired</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": 123,
        "type": "earned",
        "points": 50,
        "reason": "Order completion bonus",
        "reference_id": "ORD001",
        "reference_type": "order",
        "balance_after": 200,
        "created_at": "2024-01-01T00:00:00.000000Z"
      }
    ],
    "summary": {
      "total_earned": 500,
      "total_redeemed": 300,
      "current_balance": 200
    },
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "total_items": 45,
      "per_page": 20
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves paginated loyalty points transaction history with filtering options and summary statistics.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Architecture Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-cogs text-indigo-600 mr-3"></i>
                Technical Architecture
            </h2>

            <!-- Middleware Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-shield-alt text-blue-600 mr-2"></i>
                    Middleware
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-key text-green-500 mr-2"></i>
                            CustomerAuthMiddleware
                        </h4>
                        <p class="text-gray-600 mb-3">Handles customer authentication and session management</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>validateCustomerToken()</li>
                            <li>checkAccountStatus()</li>
                            <li>refreshTokenIfNeeded()</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-user-shield text-orange-500 mr-2"></i>
                            CustomerPrivacyMiddleware
                        </h4>
                        <p class="text-gray-600 mb-3">Ensures customer data privacy and GDPR compliance</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>maskSensitiveData()</li>
                            <li>logDataAccess()</li>
                            <li>validateConsent()</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Database Schema Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-database text-green-600 mr-2"></i>
                    Database Schema
                </h3>
                <div class="space-y-6">
                    <!-- Customers Table -->
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-table text-blue-500 mr-2"></i>
                            customers
                        </h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Primary key</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">customer_code</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(20)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Unique customer identifier</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">name</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Customer full name</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">email</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Email address (unique)</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">phone</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(20)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Phone number</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">date_of_birth</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">date</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Customer birth date</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">loyalty_points</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">integer</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Current loyalty points</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">tier</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">enum</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Loyalty tier (Bronze, Silver, Gold, Platinum)</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">preferences</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">json</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Customer preferences and settings</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Customer Addresses Table -->
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-table text-green-500 mr-2"></i>
                            customer_addresses
                        </h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Primary key</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">customer_id</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Foreign key to customers</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">type</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">enum</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Address type (home, work, other)</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">address</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Full address</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">city</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(100)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">City name</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">postal_code</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(20)</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Postal/ZIP code</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">is_default</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">boolean</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Default address flag</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Helper Classes Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-tools text-yellow-600 mr-2"></i>
                    Helper Classes
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-users text-blue-500 mr-2"></i>
                            CustomerHelper
                        </h4>
                        <p class="text-gray-600 mb-3">Utility functions for customer operations</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>generateCustomerCode()</li>
                            <li>calculateLoyaltyTier()</li>
                            <li>formatCustomerData()</li>
                            <li>validateCustomerInfo()</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-star text-yellow-500 mr-2"></i>
                            LoyaltyHelper
                        </h4>
                        <p class="text-gray-600 mb-3">Loyalty program management utilities</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>calculatePointsEarned()</li>
                            <li>checkTierUpgrade()</li>
                            <li>getExpiringPoints()</li>
                            <li>applyTierBenefits()</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Service Classes Section -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-cog text-purple-600 mr-2"></i>
                    Service Classes
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-user text-blue-500 mr-2"></i>
                            CustomerService
                        </h4>
                        <p class="text-gray-600 mb-3">Core customer management service</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>createCustomer()</li>
                            <li>updateProfile()</li>
                            <li>manageAddresses()</li>
                            <li>handleAuthentication()</li>
                            <li>processRegistration()</li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-gift text-green-500 mr-2"></i>
                            LoyaltyService
                        </h4>
                        <p class="text-gray-600 mb-3">Loyalty program management service</p>
                        <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                            <li>awardPoints()</li>
                            <li>redeemPoints()</li>
                            <li>updateTier()</li>
                            <li>trackTransactions()</li>
                            <li>generateReports()</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

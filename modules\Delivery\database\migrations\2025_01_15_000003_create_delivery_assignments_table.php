<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->foreignId('delivery_personnel_id')->constrained('delivery_personnel')->onDelete('cascade');
            $table->foreignId('delivery_zone_id')->nullable()->constrained('delivery_zones');
            $table->enum('status', ['assigned', 'picked_up', 'in_transit', 'delivered', 'failed', 'cancelled'])->default('assigned');
            $table->timestamp('assigned_at')->default(now());
            $table->timestamp('picked_up_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->text('delivery_notes')->nullable();
            $table->string('failure_reason')->nullable();
            $table->json('delivery_proof')->nullable()->comment('Photos, signatures, etc.');
            $table->decimal('distance_km', 8, 2)->nullable();
            $table->integer('estimated_duration_minutes')->nullable();
            $table->integer('actual_duration_minutes')->nullable();
            $table->decimal('delivery_fee_earned', 8, 2)->default(0);
            $table->timestamps();
            
            $table->index(['delivery_personnel_id', 'status']);
            $table->index(['order_id']);
            $table->index(['assigned_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_assignments');
    }
};
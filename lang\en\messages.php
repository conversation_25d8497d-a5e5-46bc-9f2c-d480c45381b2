<?php

return [
    'welcome' => 'Welcome to our application!',
    'dashboard' => 'Dashboard',
    'menus' => 'Menus',
    'tables' => 'Tables',
    'pos' => 'POS',
    'orders' => 'Orders',
    'customers' => 'Customers',
    'staff' => 'Staff',
    'delivery_executive' => 'Delivery Executive',
    'payments' => 'Payments',
    'reports' => 'Reports',
    'inventory' => 'Inventory',
    'settings' => 'Settings',
    'logout' => 'Logout',
    'profile' => 'Profile',
    'help' => 'Help',
    'today_sales' => 'Today\'s Sales',
    'total_orders' => 'Total Orders',
    'active_customers' => 'Active Customers',
    'staff_online' => 'Staff Online',
    'sales_trend' => 'Sales Trend',
    'category_sales' => 'Category Sales',
    'recent_orders' => 'Recent Orders',
    'quick_actions' => 'Quick Actions',
    'add_new_order' => 'Add New Order',
    'manage_menus' => 'Manage Menus',
    'view_reports' => 'View Reports',
    'update_settings' => 'Update Settings',
    'email' => 'Email',
    'password' => 'Password',
    'remember_me' => 'Remember me',
    'forgot_password' => 'Forgot your password?',
    'log_in' => 'Log in',
    'language' => 'Language',
    'theme' => 'Theme',
    'light' => 'Light',
    'dark' => 'Dark',
    'notifications' => 'Notifications',
    'view_all' => 'View All',
    'no_new_notifications' => 'No new notifications',
    'mark_as_read' => 'Mark all as read',
    'new_order_received' => 'New order received',
    'payment_successful' => 'Payment successful',
    'low_stock_alert' => 'Low stock alert',
    'table_booked' => 'Table booked',
    'customer_feedback' => 'New customer feedback',
    'view_details' => 'View Details',
    'welcome_back' => 'Welcome Back!',
    'sign_in_to_continue' => 'Sign in to continue',
    'or_continue_with' => 'Or continue with',
    'dont_have_an_account' => 'Don\'t have an account?',
    'register_now' => 'Register now',
    'toggle_password_visibility' => 'Toggle password visibility',
    
    // Additional Dashboard Elements
    'todays_sales' => "Today's Sales",
    'total_customers' => 'Total Customers',
    'staff_count' => 'Staff Count',
    'todays_expenses' => "Today's Expenses",
    'pending_orders' => 'Pending Orders',
    'sales_growth' => 'Sales Growth',
    'orders_growth' => 'Orders Growth',
    'customers_growth' => 'Customers Growth',
    'total_expenses' => 'Total Expenses',
    
    // Chart Labels
    'main_dishes' => 'Main Dishes',
    'appetizers' => 'Appetizers',
    'desserts' => 'Desserts',
    'beverages' => 'Beverages',
    'specials' => 'Specials',
    'sales' => 'Sales',
    
    // Quick Actions
    'new_order' => 'New Order',
    'add_customer' => 'Add Customer',
    'manage_inventory' => 'Manage Inventory',
    
    // Order Details
    'order_id' => 'Order ID',
    'customer_name' => 'Customer',
    'items_count' => 'Items',
    'order_total' => 'Total',
    'order_status' => 'Status',
    'order_time' => 'Time',
    'view_all_orders' => 'View All Orders',
    
    // Order Status
    'completed' => 'Completed',
    'preparing' => 'Preparing',
    'pending' => 'Pending',
    'cancelled' => 'Cancelled',
    
    // Notifications
    'no_notifications' => 'No new notifications',
    'mark_all_read' => 'Mark all as read',
    'notification_marked_read' => 'Notification marked as read',
    'new_order_notification' => 'New order received',
    'low_stock_notification' => 'Low stock alert',
    'payment_received_notification' => 'Payment received',
    
    // Theme
    'theme_updated' => 'Theme updated successfully',
    'dark_mode' => 'Dark Mode',
    'light_mode' => 'Light Mode',
    
    // Menu Structure
    'menu_items' => 'Menu Items',
    'item_categories' => 'Item Categories',
    'modifier_groups' => 'Modifier Groups',
    'item_modifiers' => 'Item Modifiers',
    'areas' => 'Areas',
    'qr_codes' => 'QR Codes',
    'waiter_requests' => 'Waiter Requests',
    'reservations' => 'Reservations',
    'due_payments' => 'Due Payments',
    'expenses' => 'Expenses',
    'expense_categories' => 'Expense Categories',
    'sales_report' => 'Sales Report',
    'item_report' => 'Item Report',
    'category_report' => 'Category Report',
    'expense_reports' => 'Expense Reports',
    'units' => 'Units',
    'inventory_items' => 'Inventory Items',
    'inventory_item_categories' => 'Inventory Item Categories',
    'inventory_stocks' => 'Inventory Stocks',
    'inventory_movements' => 'Inventory Movements',
    'recipes' => 'Recipes',
    'purchase_orders' => 'Purchase Orders',
    'suppliers' => 'Suppliers',
    'kitchens' => 'Kitchens',
    'all_kitchens' => 'All Kitchens',
    'default_kitchen' => 'Default Kitchen',
    'shisha' => 'Shisha',

    // Additional Menu Items
    'item_variations' => 'Item Variations',
    'item_addons' => 'Item Addons',
    'inventory_dashboard' => 'Inventory Dashboard',
    'inventory_reports' => 'Inventory Reports',
    'restaurant_pos' => 'Restaurant POS',
    'search_placeholder' => 'Search...',
    'skip_to_main_content' => 'Skip to main content',
    'breadcrumb' => 'Breadcrumb',
    
    // Menu Module CRUD
    'create' => 'Create',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'update' => 'Update',
    'actions' => 'Actions',
    'search' => 'Search',
    'filter' => 'Filter',
    'reset' => 'Reset',
    'show' => 'Show',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'status' => 'Status',
    'name' => 'Name',
    'description' => 'Description',
    'price' => 'Price',
    'image' => 'Image',
    'category' => 'Category',
    'menu' => 'Menu',
    'variant' => 'Variant',
    'addon' => 'Addon',
    'variants' => 'Variants',
    'addons' => 'Addons',
    'categories' => 'Categories',
    'featured' => 'Featured',
    'available' => 'Available',
    'unavailable' => 'Unavailable',
    'sort_order' => 'Sort Order',
    'is_required' => 'Required',
    'addon_group' => 'Addon Group',
    'menu_item' => 'Menu Item',
    'select_menu' => 'Select Menu',
    'select_category' => 'Select Category',
    'select_menu_item' => 'Select Menu Item',
    'no_data' => 'No data available',
    'confirm_delete' => 'Are you sure you want to delete this item?',
    'delete_warning' => 'This action cannot be undone.',
    'created_successfully' => 'Created successfully',
    'updated_successfully' => 'Updated successfully',
    'deleted_successfully' => 'Deleted successfully',
    'error_occurred' => 'An error occurred',
    'validation_error' => 'Please check the form for errors',
    'required_field' => 'This field is required',
    'upload_image' => 'Upload Image',
    'change_image' => 'Change Image',
    'remove_image' => 'Remove Image',
    'drag_drop_image' => 'Drag and drop an image here, or click to select',
    'max_file_size' => 'Maximum file size: 2MB',
    'allowed_formats' => 'Allowed formats: JPG, PNG, GIF',
    'close' => 'Close',
    'loading' => 'Loading...',
    'processing' => 'Processing...',
    'please_wait' => 'Please wait...',
    
    // Menu Management
    'addon_updated_successfully' => 'Addon updated successfully.',
    'addon_deleted_successfully' => 'Addon deleted successfully.',
    'addon_not_found' => 'Addon not found.',
    
  
];
<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Menu>
 */
class MenuFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->randomElement(['Breakfast', 'Lunch', 'Dinner', 'Drinks', 'Desserts']) . ' Menu';
        
        return [
            'tenant_id' => Tenant::factory(),
            'branch_id' => Branch::factory(),
            'name' => $name,
            'code' => Str::slug($name) . '-' . fake()->randomNumber(3),
            'description' => fake()->sentence(),
            'menu_type' => fake()->randomElement(['main', 'breakfast', 'lunch', 'dinner', 'drinks', 'desserts', 'seasonal']),
            'start_time' => fake()->time('H:i'),
            'end_time' => fake()->time('H:i'),
            'available_days' => fake()->randomElements(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'], fake()->numberBetween(3, 7)),
            'is_active' => true,
            'is_default' => fake()->boolean(20), // 20% chance of being default
            'sort_order' => fake()->numberBetween(1, 10),
        ];
    }

    /**
     * Indicate that the menu should be the default menu.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
            'menu_type' => 'main',
        ]);
    }

    /**
     * Indicate that the menu should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
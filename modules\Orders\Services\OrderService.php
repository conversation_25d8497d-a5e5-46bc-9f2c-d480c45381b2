<?php

namespace Modules\Orders\Services;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Orders\Helpers\OrderHelper;
use App\Services\InventoryDeductionService;
use Modules\Kitchen\Services\KOTService;

class OrderService
{
    protected $inventoryDeductionService;
    protected $kotService;

    public function __construct(InventoryDeductionService $inventoryDeductionService, KOTService $kotService)
    {
        $this->inventoryDeductionService = $inventoryDeductionService;
        $this->kotService = $kotService;
    }

    /**
     * Get all orders with pagination and filters
     */
    public function getAllOrders(array $filters = []): LengthAwarePaginator
    {
        $query = Order::with(['items', 'customer', 'table', 'payments']);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['table_id'])) {
            $query->where('table_id', $filters['table_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get order by ID
     */
    public function getOrderById(string $id): Order
    {
        return Order::with(['orderItems.menuItem', 'customer', 'table', 'payments'])
            ->findOrFail($id);
    }

    /**
     * Create a new order
     */
    public function createOrder(array $data): Order
    {
        $order = Order::create([
            'customer_id' => $data['customer_id'] ?? null,
            'table_id' => $data['table_id'] ?? null,
            'order_number' => OrderHelper::generateOrderNumber(),
            'status' => $data['status'] ?? 'pending',
            'order_type' => $data['order_type'] ?? 'dine_in',
            'subtotal' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
            'notes' => $data['notes'] ?? null,
            'delivery_man_id' => $data['delivery_man_id'] ?? null,
        ]);

        // Add order items if provided
        if (isset($data['items']) && is_array($data['items'])) {
            $this->addOrderItems($order, $data['items']);
        }

        // Auto-create KOT for dine_in or takeaway orders
        if (in_array($order->order_type, ['dine_in', 'takeaway'])) {
            $this->kotService->generateKOT($order);
        }

        return $order->load(['items.menuItem', 'customer', 'table']);
    }

    /**
     * Update an existing order
     */
    public function updateOrder(string $id, array $data): Order
    {
        $order = Order::findOrFail($id);
        
        $order->update([
            'status' => $data['status'] ?? $order->status,
            'notes' => $data['notes'] ?? $order->notes,
        ]);

        // Update order items if provided
        if (isset($data['items']) && is_array($data['items'])) {
            $order->items()->delete();
            $this->addOrderItems($order, $data['items']);
        }

        return $order->load(['items.menuItem', 'customer', 'table']);
    }

    /**
     * Delete an order
     */
    public function deleteOrder(string $id): void
    {
        $order = Order::findOrFail($id);
        $order->items()->delete();
        $order->delete();
    }

    /**
     * Add items to an order
     */
    private function addOrderItems(Order $order, array $items): void
    {
        $subtotal = 0;

        foreach ($items as $item) {
            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'menu_item_id' => $item['menu_item_id'],
                'variant_id' => $item['variant_id'] ?? null,
                'quantity' => $item['quantity'],
                'unit_price' => $item['unit_price'],
                'total_price' => $item['quantity'] * $item['unit_price'],
                'notes' => $item['notes'] ?? null,
            ]);

            $subtotal += $orderItem->total_price;

            // Handle addons for this order item
            if (isset($item['addons']) && is_array($item['addons'])) {
                foreach ($item['addons'] as $addon) {
                    $orderItem->addons()->create([
                        'addon_id' => $addon['addon_id'],
                        'quantity' => $addon['quantity'],
                        'unit_price' => $addon['unit_price'],
                        'total_price' => $addon['total_price'],
                    ]);
                }
            }
        }

        // Calculate totals
        $taxAmount = OrderHelper::calculateTax($subtotal);
        $totalAmount = $subtotal + $taxAmount;

        $order->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(string $id, string $status): Order
    {
        $order = Order::findOrFail($id);
        $order->update(['status' => $status]);

        // Deduct inventory when order status is 'completed' or 'served'
        if (in_array($status, ['completed', 'served'])) {
            foreach ($order->items as $orderItem) {
                $this->inventoryDeductionService->deductIngredientsForOrderItem($orderItem);
            }
        }
        
        return $order;
    }
}
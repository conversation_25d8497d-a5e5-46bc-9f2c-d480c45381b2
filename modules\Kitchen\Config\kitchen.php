<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Kitchen Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Kitchen module,
    | including default settings, validation rules, and operational parameters.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Kitchen Settings
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        'max_concurrent_orders' => 10,
        'average_prep_time_minutes' => 15,
        'priority_level' => 5,
        'station_type' => 'main',
        'is_active' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Station Types
    |--------------------------------------------------------------------------
    |
    | Available kitchen station types with their descriptions
    |
    */
    'station_types' => [
        'hot' => 'Hot Food Station',
        'cold' => 'Cold Food Station',
        'grill' => 'Grill Station',
        'fryer' => 'Fryer Station',
        'salad' => 'Salad Station',
        'dessert' => 'Dessert Station',
        'beverage' => 'Beverage Station',
        'prep' => 'Preparation Station',
        'main' => 'Main Kitchen',
        'other' => 'Other',
    ],

    /*
    |--------------------------------------------------------------------------
    | KOT Settings
    |--------------------------------------------------------------------------
    */
    'kot' => [
        'statuses' => [
            'pending' => 'Pending',
            'preparing' => 'Preparing',
            'ready' => 'Ready',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
        ],
        
        'priorities' => [
            'low' => 'Low Priority',
            'normal' => 'Normal Priority',
            'high' => 'High Priority',
            'urgent' => 'Urgent',
        ],
        
        'auto_create_from_order' => true,
        'auto_assign_to_kitchen' => true,
        'default_priority' => 'normal',
        'overdue_threshold_minutes' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'kitchen_name_max_length' => 255,
        'kitchen_code_max_length' => 20,
        'description_max_length' => 1000,
        'special_instructions_max_length' => 1000,
        'notes_max_length' => 500,
        'max_concurrent_orders_limit' => 100,
        'max_prep_time_minutes' => 300,
        'priority_level_min' => 1,
        'priority_level_max' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'ttl' => 3600, // 1 hour
        'prefix' => 'kitchen_',
        'tags' => ['kitchen', 'kot', 'menu_items'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Display Settings
    |--------------------------------------------------------------------------
    */
    'display' => [
        'items_per_page' => 15,
        'dashboard_refresh_interval' => 30, // seconds
        'show_prep_time' => true,
        'show_priority' => true,
        'show_kitchen_load' => true,
        'color_coding' => [
            'status' => [
                'pending' => '#fbbf24', // yellow
                'preparing' => '#3b82f6', // blue
                'ready' => '#10b981', // green
                'completed' => '#6b7280', // gray
                'cancelled' => '#ef4444', // red
            ],
            'priority' => [
                'low' => '#10b981', // green
                'normal' => '#3b82f6', // blue
                'high' => '#f59e0b', // orange
                'urgent' => '#ef4444', // red
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'enabled' => true,
        'channels' => ['database', 'broadcast'],
        'events' => [
            'kot_created' => true,
            'kot_started' => true,
            'kot_ready' => true,
            'kot_completed' => true,
            'kot_overdue' => true,
            'kitchen_overloaded' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'enable_query_optimization' => true,
        'enable_caching' => true,
        'batch_size' => 100,
        'max_concurrent_requests' => 50,
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integrations' => [
        'pos_system' => true,
        'inventory_management' => true,
        'reporting' => true,
        'analytics' => true,
    ],
];
<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Menu Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Menu module.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | The default currency symbol to use for price formatting.
    |
    */
    'currency' => env('MENU_CURRENCY', '$'),

    /*
    |--------------------------------------------------------------------------
    | Price Precision
    |--------------------------------------------------------------------------
    |
    | Number of decimal places for price calculations.
    |
    */
    'price_precision' => env('MENU_PRICE_PRECISION', 2),

    /*
    |--------------------------------------------------------------------------
    | Image Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for menu item images.
    |
    */
    'images' => [
        'max_size' => env('MENU_IMAGE_MAX_SIZE', 2048), // KB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'webp'],
        'storage_path' => 'menu-items',
        'thumbnail_size' => [300, 300],
    ],

    /*
    |--------------------------------------------------------------------------
    | Availability Settings
    |--------------------------------------------------------------------------
    |
    | Default availability settings for menu items.
    |
    */
    'availability' => [
        'default_start_time' => '00:00:00',
        'default_end_time' => '23:59:59',
        'timezone' => env('APP_TIMEZONE', 'UTC'),
        'advance_booking_days' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Pricing Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for pricing calculations.
    |
    */
    'pricing' => [
        'tax_rate' => env('MENU_TAX_RATE', 0.10), // 10%
        'service_charge_rate' => env('MENU_SERVICE_CHARGE_RATE', 0.05), // 5%
        'minimum_profit_margin' => env('MENU_MIN_PROFIT_MARGIN', 20), // 20%
        'allow_negative_pricing' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Category Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for menu categories.
    |
    */
    'categories' => [
        'max_depth' => 3, // Maximum category nesting level
        'default_sort_order' => 'sort_order',
        'auto_generate_codes' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Menu Item Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for menu items.
    |
    */
    'items' => [
        'auto_generate_sku' => true,
        'sku_prefix' => env('MENU_SKU_PREFIX', 'MENU'),
        'default_prep_time' => 15, // minutes
        'max_variants' => 10,
        'max_addons' => 20,
        'featured_items_limit' => 12,
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for menu search functionality.
    |
    */
    'search' => [
        'results_per_page' => 20,
        'min_search_length' => 2,
        'search_fields' => ['name', 'description', 'short_description'],
        'enable_fuzzy_search' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for menu caching.
    |
    */
    'cache' => [
        'enabled' => env('MENU_CACHE_ENABLED', true),
        'ttl' => env('MENU_CACHE_TTL', 3600), // 1 hour
        'prefix' => 'menu_',
        'tags' => ['menu', 'categories', 'items'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Default validation rules for menu components.
    |
    */
    'validation' => [
        'name_max_length' => 255,
        'description_max_length' => 1000,
        'short_description_max_length' => 255,
        'max_price' => 9999.99,
        'min_price' => 0.01,
        'max_prep_time' => 480, // 8 hours in minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Dietary Information
    |--------------------------------------------------------------------------
    |
    | Available dietary options and allergens.
    |
    */
    'dietary_options' => [
        'vegetarian' => 'Vegetarian',
        'vegan' => 'Vegan',
        'gluten_free' => 'Gluten Free',
        'dairy_free' => 'Dairy Free',
        'nut_free' => 'Nut Free',
        'halal' => 'Halal',
        'kosher' => 'Kosher',
        'keto' => 'Keto Friendly',
        'low_carb' => 'Low Carb',
        'high_protein' => 'High Protein',
    ],

    'allergens' => [
        'nuts' => 'Nuts',
        'dairy' => 'Dairy',
        'eggs' => 'Eggs',
        'soy' => 'Soy',
        'wheat' => 'Wheat/Gluten',
        'fish' => 'Fish',
        'shellfish' => 'Shellfish',
        'sesame' => 'Sesame',
    ],

    /*
    |--------------------------------------------------------------------------
    | Spice Levels
    |--------------------------------------------------------------------------
    |
    | Available spice levels for menu items.
    |
    */
    'spice_levels' => [
        1 => 'Mild',
        2 => 'Medium',
        3 => 'Hot',
        4 => 'Very Hot',
        5 => 'Extremely Hot',
    ],

    /*
    |--------------------------------------------------------------------------
    | Branch Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for branch-specific menu settings.
    |
    */
    'branches' => [
        'allow_price_override' => true,
        'allow_availability_override' => true,
        'inherit_from_main' => true,
        'sync_changes' => false,
    ],
];
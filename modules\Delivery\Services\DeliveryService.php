<?php

namespace Modules\Delivery\Services;

use App\Models\Order;
use Modules\Delivery\Models\DeliveryPersonnel;
use Modules\Delivery\Models\DeliveryZone;
use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryReview;
use Modules\Delivery\Models\DeliveryTip;
use Modules\Delivery\Models\DeliveryTracking;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeliveryService
{
    /**
     * Assign a delivery to a specific personnel.
     */
    public function assignDelivery(int $orderId, int $personnelId, ?int $zoneId = null): DeliveryAssignment
    {
        return DB::transaction(function () use ($orderId, $personnelId, $zoneId) {
            $order = Order::findOrFail($orderId);
            $personnel = DeliveryPersonnel::findOrFail($personnelId);

            // Validate assignment
            if (!$personnel->isAvailable()) {
                throw new \Exception('Delivery personnel is not available');
            }

            if ($order->order_type !== 'delivery') {
                throw new \Exception('Order is not a delivery order');
            }

            // Find delivery zone if not provided
            if (!$zoneId && $order->delivery_coordinates) {
                $coords = $order->delivery_coordinates;
                $zone = DeliveryZone::findZoneForLocation(
                    $order->branch_id,
                    $coords['lat'],
                    $coords['lng']
                );
                $zoneId = $zone?->id;
            }

            // Calculate distance and estimated duration
            $distance = $this->calculateDeliveryDistance($order, $personnel);
            $estimatedDuration = $this->calculateEstimatedDuration($distance, $zoneId);

            // Create assignment
            $assignment = DeliveryAssignment::create([
                'order_id' => $orderId,
                'delivery_personnel_id' => $personnelId,
                'delivery_zone_id' => $zoneId,
                'status' => 'assigned',
                'assigned_at' => now(),
                'distance_km' => $distance,
                'estimated_duration_minutes' => $estimatedDuration,
                'delivery_fee_earned' => $order->delivery_fee * 0.7, // 70% to personnel
            ]);

            // Update order
            $order->update([
                'delivery_man_id' => $personnelId,
                'status' => 'confirmed',
                'estimated_ready_time' => now()->addMinutes($estimatedDuration),
            ]);

            // Update personnel status
            $personnel->update(['status' => 'on_delivery']);

            return $assignment;
        });
    }

    /**
     * Auto-assign delivery to the best available personnel.
     */
    public function autoAssignDelivery(int $orderId): ?DeliveryAssignment
    {
        $order = Order::findOrFail($orderId);

        if ($order->order_type !== 'delivery' || !$order->delivery_coordinates) {
            return null;
        }

        $coords = $order->delivery_coordinates;
        $bestPersonnel = $this->findBestPersonnelForDelivery(
            $order->branch_id,
            $coords['lat'],
            $coords['lng']
        );

        if (!$bestPersonnel) {
            return null;
        }

        // Find delivery zone
        $zone = DeliveryZone::findZoneForLocation(
            $order->branch_id,
            $coords['lat'],
            $coords['lng']
        );

        return $this->assignDelivery($orderId, $bestPersonnel->id, $zone?->id);
    }

    /**
     * Update delivery status with appropriate actions.
     */
    public function updateDeliveryStatus(DeliveryAssignment $assignment, string $status, array $data = []): bool
    {
        return DB::transaction(function () use ($assignment, $status, $data) {
            switch ($status) {
                case 'picked_up':
                    $assignment->markAsPickedUp();
                    break;

                case 'in_transit':
                    $assignment->markAsInTransit();
                    break;

                case 'delivered':
                    $assignment->markAsDelivered(
                        $data['delivery_proof'] ?? [],
                        $data['delivery_notes'] ?? ''
                    );
                    break;

                case 'failed':
                    $assignment->markAsFailed($data['failure_reason'] ?? 'Unknown reason');
                    break;

                case 'cancelled':
                    $assignment->cancel();
                    break;

                default:
                    throw new \Exception('Invalid delivery status');
            }

            return true;
        });
    }

    /**
     * Find the best personnel for a delivery based on distance, availability, and rating.
     */
    protected function findBestPersonnelForDelivery(int $branchId, float $lat, float $lng): ?DeliveryPersonnel
    {
        $personnel = DeliveryPersonnel::where('branch_id', $branchId)
            ->available()
            ->withinRadius($lat, $lng, 15) // 15km radius
            ->with('user')
            ->get();

        if ($personnel->isEmpty()) {
            return null;
        }

        // Score each personnel based on distance, rating, and current load
        $scored = $personnel->map(function ($person) use ($lat, $lng) {
            $distance = $person->distanceFrom($lat, $lng);
            $activeDeliveries = $person->activeDeliveries()->count();
            $loadFactor = $activeDeliveries / $person->max_concurrent_deliveries;
            
            // Lower score is better
            $score = ($distance * 0.4) + // Distance weight: 40%
                    ((5 - $person->rating) * 0.3) + // Rating weight: 30% (inverted)
                    ($loadFactor * 0.3); // Load weight: 30%

            return [
                'personnel' => $person,
                'score' => $score,
                'distance' => $distance,
            ];
        })->sortBy('score');

        return $scored->first()['personnel'];
    }

    /**
     * Calculate delivery distance between personnel and destination.
     */
    protected function calculateDeliveryDistance(Order $order, DeliveryPersonnel $personnel): float
    {
        if (!$order->delivery_coordinates || !$personnel->current_latitude || !$personnel->current_longitude) {
            return 0;
        }

        $coords = $order->delivery_coordinates;
        return $personnel->distanceFrom($coords['lat'], $coords['lng']);
    }

    /**
     * Calculate estimated delivery duration.
     */
    protected function calculateEstimatedDuration(float $distance, ?int $zoneId = null): int
    {
        if ($zoneId) {
            $zone = DeliveryZone::find($zoneId);
            if ($zone) {
                return $zone->estimated_delivery_time_minutes;
            }
        }

        // Default calculation: 30 km/h average speed + 10 minutes preparation
        $baseMinutes = ($distance / 30) * 60 + 10;
        
        // Add traffic factor
        $hour = now()->hour;
        $isPeakHour = ($hour >= 11 && $hour <= 14) || ($hour >= 18 && $hour <= 21);
        
        if ($isPeakHour) {
            $baseMinutes *= 1.3;
        }

        return (int) ceil($baseMinutes);
    }

    /**
     * Get delivery statistics for a branch or system-wide.
     */
    public function getDeliveryStats(?int $branchId = null, ?string $startDate = null, ?string $endDate = null): array
    {
        $query = DeliveryAssignment::query();

        if ($branchId) {
            $query->whereHas('order', fn($q) => $q->where('branch_id', $branchId));
        }

        if ($startDate && $endDate) {
            $query->whereBetween('assigned_at', [$startDate, $endDate]);
        }

        $assignments = $query->with(['order', 'deliveryPersonnel'])->get();

        $totalDeliveries = $assignments->count();
        $completedDeliveries = $assignments->where('status', 'delivered')->count();
        $failedDeliveries = $assignments->where('status', 'failed')->count();
        $cancelledDeliveries = $assignments->where('status', 'cancelled')->count();

        $completedAssignments = $assignments->where('status', 'delivered');
        $avgDeliveryTime = $completedAssignments->avg('actual_duration_minutes');
        $totalDistance = $completedAssignments->sum('distance_km');
        $totalEarnings = $completedAssignments->sum('delivery_fee_earned');

        // Get reviews stats
        $reviewsQuery = DeliveryReview::whereIn('delivery_assignment_id', $assignments->pluck('id'));
        $avgRating = $reviewsQuery->avg('rating');
        $totalReviews = $reviewsQuery->count();

        // Get tips stats
        $tipsQuery = DeliveryTip::whereIn('delivery_assignment_id', $assignments->pluck('id'))->paid();
        $totalTips = $tipsQuery->sum('amount');
        $avgTip = $tipsQuery->avg('amount');

        return [
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'failed_deliveries' => $failedDeliveries,
            'cancelled_deliveries' => $cancelledDeliveries,
            'success_rate' => $totalDeliveries > 0 ? ($completedDeliveries / $totalDeliveries) * 100 : 0,
            'average_delivery_time_minutes' => round($avgDeliveryTime, 2),
            'total_distance_km' => round($totalDistance, 2),
            'total_earnings' => round($totalEarnings, 2),
            'average_rating' => round($avgRating, 2),
            'total_reviews' => $totalReviews,
            'total_tips' => round($totalTips, 2),
            'average_tip' => round($avgTip, 2),
            'deliveries_by_status' => [
                'assigned' => $assignments->where('status', 'assigned')->count(),
                'picked_up' => $assignments->where('status', 'picked_up')->count(),
                'in_transit' => $assignments->where('status', 'in_transit')->count(),
                'delivered' => $completedDeliveries,
                'failed' => $failedDeliveries,
                'cancelled' => $cancelledDeliveries,
            ],
        ];
    }

    /**
     * Get performance metrics for a delivery personnel.
     */
    public function getPersonnelPerformance(int $personnelId, ?string $startDate = null, ?string $endDate = null): array
    {
        $query = DeliveryAssignment::where('delivery_personnel_id', $personnelId);

        if ($startDate && $endDate) {
            $query->whereBetween('assigned_at', [$startDate, $endDate]);
        }

        $assignments = $query->with('order')->get();
        $personnel = DeliveryPersonnel::with('user')->find($personnelId);

        $totalDeliveries = $assignments->count();
        $completedDeliveries = $assignments->where('status', 'delivered')->count();
        $failedDeliveries = $assignments->where('status', 'failed')->count();

        $completedAssignments = $assignments->where('status', 'delivered');
        $avgDeliveryTime = $completedAssignments->avg('actual_duration_minutes');
        $totalDistance = $completedAssignments->sum('distance_km');
        $totalEarnings = $completedAssignments->sum('delivery_fee_earned');

        // Get reviews for this personnel
        $reviews = DeliveryReview::whereIn('delivery_assignment_id', $assignments->pluck('id'))->get();
        $avgRating = $reviews->avg('rating');
        $totalReviews = $reviews->count();

        // Get tips for this personnel
        $tips = DeliveryTip::whereIn('delivery_assignment_id', $assignments->pluck('id'))->paid()->get();
        $totalTips = $tips->sum('amount');
        $avgTip = $tips->avg('amount');

        // Calculate on-time delivery rate
        $onTimeDeliveries = $completedAssignments->filter(function ($assignment) {
            return $assignment->actual_duration_minutes <= $assignment->estimated_duration_minutes;
        })->count();

        return [
            'personnel' => $personnel,
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'failed_deliveries' => $failedDeliveries,
            'success_rate' => $totalDeliveries > 0 ? ($completedDeliveries / $totalDeliveries) * 100 : 0,
            'on_time_rate' => $completedDeliveries > 0 ? ($onTimeDeliveries / $completedDeliveries) * 100 : 0,
            'average_delivery_time_minutes' => round($avgDeliveryTime, 2),
            'total_distance_km' => round($totalDistance, 2),
            'total_earnings' => round($totalEarnings, 2),
            'average_rating' => round($avgRating, 2),
            'total_reviews' => $totalReviews,
            'total_tips' => round($totalTips, 2),
            'average_tip' => round($avgTip, 2),
            'rating_distribution' => [
                '5_star' => $reviews->where('rating', 5)->count(),
                '4_star' => $reviews->where('rating', 4)->count(),
                '3_star' => $reviews->where('rating', 3)->count(),
                '2_star' => $reviews->where('rating', 2)->count(),
                '1_star' => $reviews->where('rating', 1)->count(),
            ],
        ];
    }

    /**
     * Calculate delivery fee based on zone and distance.
     */
    public function calculateDeliveryFee(int $branchId, float $lat, float $lng): array
    {
        $zone = DeliveryZone::findZoneForLocation($branchId, $lat, $lng);

        if (!$zone) {
            return [
                'can_deliver' => false,
                'message' => 'Delivery not available in this area',
            ];
        }

        return [
            'can_deliver' => true,
            'zone' => $zone,
            'delivery_fee' => $zone->delivery_fee,
            'minimum_order_amount' => $zone->minimum_order_amount,
            'estimated_delivery_time_minutes' => $zone->estimated_delivery_time_minutes,
        ];
    }

    /**
     * Get real-time delivery tracking information.
     */
    public function getDeliveryTrackingInfo(int $assignmentId): array
    {
        $assignment = DeliveryAssignment::with(['order', 'deliveryPersonnel.user'])
            ->findOrFail($assignmentId);

        $currentLocation = $assignment->getCurrentLocation();
        $route = DeliveryTracking::getRouteForAssignment($assignmentId);
        $totalDistance = DeliveryTracking::getTotalDistanceForAssignment($assignmentId);

        return [
            'assignment' => $assignment,
            'current_location' => $currentLocation,
            'route' => $route,
            'total_distance_traveled' => $totalDistance,
            'estimated_arrival' => $assignment->estimated_ready_time,
        ];
    }
}
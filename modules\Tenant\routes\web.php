<?php

use Illuminate\Support\Facades\Route;
use Modules\Tenant\Http\Controllers\TenantController;
use Modules\Tenant\Http\Controllers\SubscriptionController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware('web')->group(function () {
    // Add your web routes here
});

// Tenant Management Web Routes
Route::prefix('admin/tenants')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [TenantController::class, 'index'])->name('tenants.index');
    Route::get('/create', [TenantController::class, 'create'])->name('tenants.create');
    Route::post('/', [TenantController::class, 'store'])->name('tenants.store');
    Route::get('/{tenant}', [TenantController::class, 'show'])->name('tenants.show');
    Route::get('/{tenant}/edit', [TenantController::class, 'edit'])->name('tenants.edit');
    Route::put('/{tenant}', [TenantController::class, 'update'])->name('tenants.update');
    Route::delete('/{tenant}', [TenantController::class, 'destroy'])->name('tenants.destroy');
    Route::post('/{tenant}/activate', [TenantController::class, 'activate'])->name('tenants.activate');
    Route::post('/{tenant}/deactivate', [TenantController::class, 'deactivate'])->name('tenants.deactivate');
});

// Subscription Management Web Routes
Route::prefix('admin/subscriptions')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('/create', [SubscriptionController::class, 'create'])->name('subscriptions.create');
    Route::post('/', [SubscriptionController::class, 'store'])->name('subscriptions.store');
    Route::get('/{subscription}', [SubscriptionController::class, 'show'])->name('subscriptions.show');
    Route::get('/{subscription}/edit', [SubscriptionController::class, 'edit'])->name('subscriptions.edit');
    Route::put('/{subscription}', [SubscriptionController::class, 'update'])->name('subscriptions.update');
    Route::post('/{subscription}/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    Route::post('/{subscription}/suspend', [SubscriptionController::class, 'suspend'])->name('subscriptions.suspend');
    Route::post('/{subscription}/reactivate', [SubscriptionController::class, 'reactivate'])->name('subscriptions.reactivate');
    Route::get('/{subscription}/upgrade', [SubscriptionController::class, 'upgradeForm'])->name('subscriptions.upgrade.form');
    Route::post('/{subscription}/upgrade', [SubscriptionController::class, 'upgrade'])->name('subscriptions.upgrade');
});

// Tenant Dashboard Routes (for individual tenants)
Route::prefix('tenant')->middleware(['auth', 'web', 'tenant'])->group(function () {
    Route::get('/dashboard', function () {
        return view('tenant::dashboard');
    })->name('tenant.dashboard');
    
    Route::get('/subscription', function () {
        return view('tenant::subscription');
    })->name('tenant.subscription');
    
    Route::get('/usage', function () {
        return view('tenant::usage');
    })->name('tenant.usage');
    
    Route::get('/billing', function () {
        return view('tenant::billing');
    })->name('tenant.billing');
});

// Public tenant routes (for subdomain access)
Route::domain('{subdomain}.'.config('app.domain', 'localhost'))->group(function () {
    Route::get('/', function ($subdomain) {
        $tenant = \App\Models\Tenant::where('subdomain', $subdomain)->firstOrFail();
        return view('tenant::public.home', compact('tenant'));
    })->name('tenant.public.home');
    
    Route::get('/menu', function ($subdomain) {
        $tenant = \App\Models\Tenant::where('subdomain', $subdomain)->firstOrFail();
        return view('tenant::public.menu', compact('tenant'));
    })->name('tenant.public.menu');
    
    Route::get('/contact', function ($subdomain) {
        $tenant = \App\Models\Tenant::where('subdomain', $subdomain)->firstOrFail();
        return view('tenant::public.contact', compact('tenant'));
    })->name('tenant.public.contact');
});

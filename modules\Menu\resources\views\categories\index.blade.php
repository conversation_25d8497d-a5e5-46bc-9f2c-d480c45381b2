@extends('menu::layouts.app')

@section('title', 'Categories')

@section('header-actions')
    <a href="{{ route('menu.web.categories.create') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle me-1"></i>
        Add Category
    </a>
@endsection

@section('content')
<div class="row">
    <!-- Filters -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ route('menu.web.categories.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Search categories...">
                    </div>
                    <div class="col-md-3">
                        <label for="menu_id" class="form-label">Menu</label>
                        <select class="form-select" id="menu_id" name="menu_id">
                            <option value="">All Menus</option>
                            @if(isset($menus))
                                @foreach($menus as $menu)
                                    <option value="{{ $menu->id }}" {{ request('menu_id') == $menu->id ? 'selected' : '' }}>
                                        {{ $menu->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="sort" class="form-label">Sort By</label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name</option>
                            <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Created Date</option>
                            <option value="sort_order" {{ request('sort') === 'sort_order' ? 'selected' : '' }}>Sort Order</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="bi bi-search"></i>
                        </button>
                        <a href="{{ route('menu.web.categories.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Categories Grid -->
    <div class="col-12">
        @if($categories && $categories->count() > 0)
            <div class="row">
                @foreach($categories as $category)
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title mb-0">{{ $category->name }}</h5>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="{{ route('menu.web.categories.show', $category->id) }}">
                                                <i class="bi bi-eye me-1"></i> View
                                            </a></li>
                                            <li><a class="dropdown-item" href="{{ route('menu.web.categories.edit', $category->id) }}">
                                                <i class="bi bi-pencil me-1"></i> Edit
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory({{ $category->id }})">
                                                <i class="bi bi-trash me-1"></i> Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                @if($category->description)
                                    <p class="card-text text-muted">{{ Str::limit($category->description, 100) }}</p>
                                @endif
                                
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="fw-bold text-primary">{{ $category->menuItems->count() ?? 0 }}</div>
                                            <small class="text-muted">Items</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="fw-bold text-info">{{ $category->sort_order ?? 0 }}</div>
                                            <small class="text-muted">Order</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="fw-bold text-success">{{ $category->menu->name ?? 'N/A' }}</div>
                                        <small class="text-muted">Menu</small>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        @if($category->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">Inactive</span>
                                        @endif
                                    </div>
                                    <small class="text-muted">{{ $category->created_at->format('M d, Y') }}</small>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <a href="{{ route('menu.web.categories.show', $category->id) }}" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-eye me-1"></i> View
                                    </a>
                                    <a href="{{ route('menu.web.categories.edit', $category->id) }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-pencil me-1"></i> Edit
                                    </a>
                                    <a href="{{ route('menu.web.menu-items.create') }}?category_id={{ $category->id }}" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-plus me-1"></i> Add Item
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if(method_exists($categories, 'links'))
                <div class="d-flex justify-content-center">
                    {{ $categories->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-grid-3x3-gap display-1 text-muted"></i>
                    <h4 class="mt-3">No Categories Found</h4>
                    <p class="text-muted">Start organizing your menu by creating categories.</p>
                    <a href="{{ route('menu.web.categories.create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>
                        Create Your First Category
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Warning!</strong> This will permanently delete the category and all its associated menu items.
                </div>
                <p>Are you sure you want to delete this category? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteCategoryForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>
                        Delete Category
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteCategory(categoryId) {
    const deleteForm = document.getElementById('deleteCategoryForm');
    deleteForm.action = `/menu/categories/${categoryId}`;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteCategoryModal'));
    deleteModal.show();
}

// Auto-submit form on filter change
document.getElementById('menu_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('sort').addEventListener('change', function() {
    this.form.submit();
});
</script>
@endpush
<?php

namespace Modules\Tenant\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateBranchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'tenant_id' => 'required|exists:tenants,id',
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:branches,code',
            'address' => 'nullable|string|max:500',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'manager_name' => 'nullable|string|max:255',
            'opening_hours' => 'nullable|array',
            'opening_hours.*.day' => 'required_with:opening_hours|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'opening_hours.*.open' => 'required_unless:opening_hours.*.is_closed,true|date_format:H:i',
            'opening_hours.*.close' => 'required_unless:opening_hours.*.is_closed,true|date_format:H:i|after:opening_hours.*.open',
            'opening_hours.*.is_closed' => 'boolean',
            'timezone' => 'nullable|string|timezone',
            'seating_capacity' => 'nullable|integer|min:1',
            'delivery_radius' => 'nullable|numeric|min:0',
            'is_delivery_enabled' => 'boolean',
            'is_takeaway_enabled' => 'boolean',
            'is_dine_in_enabled' => 'boolean',
            'is_online_ordering_enabled' => 'boolean',
            'printer_configurations' => 'nullable|array',
            'pos_terminal_id' => 'nullable|string',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'tenant_id.required' => 'The tenant is required.',
            'tenant_id.exists' => 'The selected tenant is invalid.',
            'name.required' => 'The branch name is required.',
            'name.max' => 'The branch name may not be greater than 255 characters.',
            'code.unique' => 'This branch code is already taken.',
            'code.max' => 'The branch code may not be greater than 50 characters.',
            'email.email' => 'Please provide a valid email address.',
            'phone.max' => 'The phone number may not be greater than 20 characters.',
            'address.max' => 'The address may not be greater than 500 characters.',
            'manager_name.max' => 'The manager name may not be greater than 255 characters.',
            'opening_hours.*.day.in' => 'Invalid day specified in opening hours.',
            'opening_hours.*.open_time.date_format' => 'Open time must be in HH:MM format.',
            'opening_hours.*.close_time.date_format' => 'Close time must be in HH:MM format.',
            'opening_hours.*.close_time.after' => 'Close time must be after open time.',
            'timezone.max' => 'The timezone may not be greater than 50 characters.',
            'currency_code.size' => 'Currency code must be exactly 3 characters.',
            'tax_rate.numeric' => 'Tax rate must be a number.',
            'tax_rate.min' => 'Tax rate must be at least 0.',
            'tax_rate.max' => 'Tax rate may not be greater than 100.',
            'service_charge_rate.numeric' => 'Service charge rate must be a number.',
            'service_charge_rate.min' => 'Service charge rate must be at least 0.',
            'service_charge_rate.max' => 'Service charge rate may not be greater than 100.',
            'latitude.between' => 'The latitude must be between -90 and 90.',
            'longitude.between' => 'The longitude must be between -180 and 180.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'tenant_id' => 'tenant',
            'name' => 'branch name',
            'code' => 'branch code',
            'address' => 'address',
            'phone' => 'phone number',
            'email' => 'email address',
            'manager_name' => 'manager name',
            'opening_hours' => 'opening hours',
            'timezone' => 'timezone',
            'currency_code' => 'currency code',
            'tax_rate' => 'tax rate',
            'service_charge_rate' => 'service charge rate',
            'latitude' => 'latitude',
            'longitude' => 'longitude',
            'is_active' => 'active status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Normalize currency code to uppercase
        if ($this->has('currency_code')) {
            $this->merge([
                'currency_code' => strtoupper($this->currency_code)
            ]);
        }

        // Normalize branch code
        if ($this->has('code')) {
            $this->merge([
                'code' => strtolower(str_replace(' ', '-', $this->code))
            ]);
        }

        // Convert string boolean values
        if ($this->has('is_active')) {
            $this->merge([
                'is_active' => filter_var($this->is_active, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation for opening hours
            if ($this->has('opening_hours')) {
                $this->validateOpeningHours($validator);
            }
        });
    }

    /**
     * Validate opening hours logic
     */
    protected function validateOpeningHours($validator): void
    {
        $openingHours = $this->input('opening_hours', []);
        $days = [];

        foreach ($openingHours as $index => $hours) {
            if (isset($hours['day'])) {
                if (in_array($hours['day'], $days)) {
                    $validator->errors()->add(
                        "opening_hours.{$index}.day",
                        "Duplicate day '{$hours['day']}' in opening hours."
                    );
                }
                $days[] = $hours['day'];
            }

            // Validate that closed days don't have open/close times
            if (isset($hours['is_closed']) && $hours['is_closed']) {
                if (isset($hours['open_time']) || isset($hours['close_time'])) {
                    $validator->errors()->add(
                        "opening_hours.{$index}",
                        "Closed days should not have open or close times."
                    );
                }
            }
        }
    }
}
<?php

namespace Modules\Menu\Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Menu;
use App\Models\MenuCategory;
use App\Models\MenuItem;
use App\Models\MenuItemVariant;
use App\Models\MenuItemAddon;
use App\Models\MenuAvailability;
use App\Models\MenuItemBranch;
use App\Models\Tenant;
use App\Models\Branch;
use Carbon\Carbon;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first tenant and branch for seeding
        $tenant = Tenant::first();
        $branch = Branch::first();
        
        if (!$tenant || !$branch) {
            $this->command->info('No tenant or branch found. Please seed tenants and branches first.');
            return;
        }

        // Create sample menu
        $menu = Menu::create([
            'tenant_id' => $tenant->id,
            'branch_id' => $branch->id,
            'name' => 'Main Menu',
            'code' => 'MAIN',
            'description' => 'Our main restaurant menu',
            'menu_type' => 'main',
            'start_time' => '06:00:00',
            'end_time' => '23:00:00',
            'available_days' => [0, 1, 2, 3, 4, 5, 6], // All days
            'is_active' => true,
            'is_default' => true,
            'sort_order' => 1,
        ]);

        // Create sample categories
        $categories = [
            [
                'menu_id' => $menu->id,
                'name' => 'Appetizers',
                'code' => 'APP',
                'description' => 'Start your meal with our delicious appetizers',
                'image_url' => 'appetizers.jpg',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'menu_id' => $menu->id,
                'name' => 'Main Courses',
                'code' => 'MAIN',
                'description' => 'Our signature main dishes',
                'image_url' => 'main-courses.jpg',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'menu_id' => $menu->id,
                'name' => 'Beverages',
                'code' => 'BEV',
                'description' => 'Refreshing drinks and beverages',
                'image_url' => 'beverages.jpg',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'menu_id' => $menu->id,
                'name' => 'Desserts',
                'code' => 'DES',
                'description' => 'Sweet treats to end your meal',
                'image_url' => 'desserts.jpg',
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($categories as $categoryData) {
            $category = MenuCategory::create($categoryData);
            
            // Create sample menu items for each category
            $this->createMenuItemsForCategory($category, $menu);
        }
    }

    /**
     * Create sample menu items for a category
     */
    private function createMenuItemsForCategory(MenuCategory $category, Menu $menu): void
    {
        $items = $this->getMenuItemsForCategory($category->name);
        
        foreach ($items as $itemData) {
            $itemData['menu_id'] = $menu->id;
            $itemData['category_id'] = $category->id;
            $menuItem = MenuItem::create($itemData);
            
            // Create variants for some items
            if (in_array($category->name, ['Main Courses', 'Beverages'])) {
                $this->createVariantsForItem($menuItem);
            }
            
            // Create addons for some items
            if (in_array($category->name, ['Main Courses', 'Appetizers'])) {
                $this->createAddonsForItem($menuItem);
            }
            
            // Create availability schedule
            $this->createAvailabilityForItem($menuItem);
        }
    }

    /**
     * Get menu items data for specific category
     */
    private function getMenuItemsForCategory(string $categoryName): array
    {
        switch ($categoryName) {
            case 'Appetizers':
                return [
                    [
                        'name' => 'Caesar Salad',
                        'code' => 'CS001',
                        'description' => 'Fresh romaine lettuce with caesar dressing',
                        'base_price' => 12.99,
                        'cost_price' => 4.50,
                        'prep_time_minutes' => 10,
                        'calories' => 280,
                        'is_active' => true,
                        'is_featured' => true,
                    ],
                    [
                        'name' => 'Chicken Wings',
                        'code' => 'CW001',
                        'description' => 'Spicy buffalo chicken wings',
                        'base_price' => 15.99,
                        'cost_price' => 6.00,
                        'prep_time_minutes' => 15,
                        'calories' => 450,
                        'is_active' => true,
                        'is_spicy' => true,
                        'spice_level' => 3,
                    ],
                ];
            
            case 'Main Courses':
                return [
                    [
                        'name' => 'Grilled Salmon',
                        'code' => 'GS001',
                        'description' => 'Fresh Atlantic salmon with herbs',
                        'base_price' => 24.99,
                        'cost_price' => 12.00,
                        'prep_time_minutes' => 20,
                        'calories' => 380,
                        'is_active' => true,
                        'is_featured' => true,
                    ],
                    [
                        'name' => 'Beef Burger',
                        'code' => 'BB001',
                        'description' => 'Juicy beef patty with fresh toppings',
                        'base_price' => 18.99,
                        'cost_price' => 8.50,
                        'prep_time_minutes' => 15,
                        'calories' => 650,
                        'is_active' => true,
                    ],
                ];
            
            case 'Beverages':
                return [
                    [
                        'name' => 'Fresh Orange Juice',
                        'code' => 'OJ001',
                        'description' => 'Freshly squeezed orange juice',
                        'base_price' => 5.99,
                        'cost_price' => 1.50,
                        'prep_time_minutes' => 3,
                        'calories' => 120,
                        'is_active' => true,
                    ],
                    [
                        'name' => 'Coffee',
                        'code' => 'CF001',
                        'description' => 'Premium roasted coffee beans',
                        'base_price' => 3.99,
                        'cost_price' => 0.80,
                        'prep_time_minutes' => 5,
                        'calories' => 5,
                        'is_active' => true,
                    ],
                ];
            
            case 'Desserts':
                return [
                    [
                        'name' => 'Chocolate Cake',
                        'code' => 'CC001',
                        'description' => 'Rich chocolate cake with frosting',
                        'base_price' => 8.99,
                        'cost_price' => 3.00,
                        'prep_time_minutes' => 5,
                        'calories' => 420,
                        'is_active' => true,
                    ],
                    [
                        'name' => 'Ice Cream',
                        'code' => 'IC001',
                        'description' => 'Vanilla ice cream with toppings',
                        'base_price' => 6.99,
                        'cost_price' => 2.00,
                        'prep_time_minutes' => 2,
                        'calories' => 250,
                        'is_active' => true,
                    ],
                ];
            
            default:
                return [];
        }
    }

    /**
     * Create variants for menu item
     */
    private function createVariantsForItem(MenuItem $menuItem): void
    {
        if ($menuItem->name === 'Grilled Salmon') {
            MenuItemVariant::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Regular',
                'code' => 'REG',
                'price_modifier' => 0.00,
                'cost_modifier' => 0.00,
                'is_default' => true,
            ]);
            
            MenuItemVariant::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Large',
                'code' => 'LRG',
                'price_modifier' => 5.00,
                'cost_modifier' => 2.50,
                'is_default' => false,
            ]);
        }
        
        if ($menuItem->name === 'Coffee') {
            MenuItemVariant::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Small',
                'code' => 'SML',
                'price_modifier' => -1.00,
                'cost_modifier' => -0.20,
                'is_default' => false,
            ]);
            
            MenuItemVariant::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Medium',
                'code' => 'MED',
                'price_modifier' => 0.00,
                'cost_modifier' => 0.00,
                'is_default' => true,
            ]);
            
            MenuItemVariant::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Large',
                'code' => 'LRG',
                'price_modifier' => 1.50,
                'cost_modifier' => 0.30,
                'is_default' => false,
            ]);
        }
    }

    /**
     * Create addons for menu item
     */
    private function createAddonsForItem(MenuItem $menuItem): void
    {
        if (in_array($menuItem->name, ['Grilled Salmon', 'Beef Burger'])) {
            MenuItemAddon::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Extra Sauce',
                'code' => 'SAUCE',
                'price' => 1.50,
                'cost' => 0.30,
                'is_required' => false,
            ]);
            
            MenuItemAddon::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Side Salad',
                'code' => 'SALAD',
                'price' => 4.99,
                'cost' => 1.50,
                'is_required' => false,
            ]);
        }
        
        if ($menuItem->name === 'Caesar Salad') {
            MenuItemAddon::create([
                'menu_item_id' => $menuItem->id,
                'name' => 'Grilled Chicken',
                'code' => 'CHICKEN',
                'price' => 6.00,
                'cost' => 2.50,
                'is_required' => false,
            ]);
        }
    }

    /**
     * Create availability schedule for menu item
     */
    private function createAvailabilityForItem(MenuItem $menuItem): void
    {
        // Create default availability (all day, all week)
        MenuAvailability::create([
            'menu_item_id' => $menuItem->id,
            'day_of_week' => null, // null means all days
            'start_time' => '00:00:00',
            'end_time' => '23:59:59',
            'is_available' => true,
            'date_specific' => null,
        ]);
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_zones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('coordinates')->comment('Polygon coordinates defining the zone');
            $table->decimal('delivery_fee', 8, 2);
            $table->decimal('minimum_order_amount', 8, 2)->default(0);
            $table->integer('estimated_delivery_time_minutes');
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(1)->comment('Higher priority zones are checked first');
            $table->timestamps();
            
            $table->index(['branch_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_zones');
    }
};
<?php

namespace Modules\Payment\Helpers;

use App\Models\Payment;
use App\Models\Transaction;
use App\Models\PaymentMethod;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class PaymentHelper
{
    /**
     * Format currency amount
     */
    public static function formatCurrency(float $amount, string $currency = 'USD', int $decimals = 2): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'CHF' => 'CHF',
            'CNY' => '¥',
            'INR' => '₹',
            'BRL' => 'R$'
        ];
        
        $symbol = $symbols[$currency] ?? $currency;
        
        return $symbol . number_format($amount, $decimals);
    }

    /**
     * Get payment status badge class
     */
    public static function getStatusBadgeClass(string $status): string
    {
        $classes = [
            'pending' => 'badge-warning',
            'processing' => 'badge-info',
            'completed' => 'badge-success',
            'failed' => 'badge-danger',
            'cancelled' => 'badge-secondary',
            'refunded' => 'badge-dark',
            'partially_refunded' => 'badge-warning'
        ];
        
        return $classes[$status] ?? 'badge-secondary';
    }

    /**
     * Get payment method icon
     */
    public static function getPaymentMethodIcon(string $type): string
    {
        $icons = [
            'cash' => 'fas fa-money-bill-wave',
            'card' => 'fas fa-credit-card',
            'digital_wallet' => 'fas fa-wallet',
            'bank_transfer' => 'fas fa-university',
            'crypto' => 'fab fa-bitcoin',
            'check' => 'fas fa-money-check',
            'gift_card' => 'fas fa-gift'
        ];
        
        return $icons[$type] ?? 'fas fa-payment';
    }

    /**
     * Calculate payment fees
     */
    public static function calculateFees(float $amount, PaymentMethod $paymentMethod): array
    {
        $credentials = $paymentMethod->credentials ?? [];
        
        $fixedFee = $credentials['fixed_fee'] ?? 0;
        $percentageFee = $credentials['percentage_fee'] ?? 0;
        
        $calculatedFee = $fixedFee + ($amount * $percentageFee / 100);
        
        return [
            'fixed_fee' => $fixedFee,
            'percentage_fee' => $percentageFee,
            'calculated_fee' => round($calculatedFee, 2),
            'net_amount' => round($amount - $calculatedFee, 2)
        ];
    }

    /**
     * Validate payment amount
     */
    public static function validatePaymentAmount(float $amount, PaymentMethod $paymentMethod): array
    {
        $errors = [];
        $credentials = $paymentMethod->credentials ?? [];
        
        // Check minimum amount
        $minAmount = $credentials['min_amount'] ?? 0;
        if ($amount < $minAmount) {
            $errors[] = "Amount must be at least " . self::formatCurrency($minAmount);
        }
        
        // Check maximum amount
        $maxAmount = $credentials['max_amount'] ?? PHP_FLOAT_MAX;
        if ($amount > $maxAmount) {
            $errors[] = "Amount cannot exceed " . self::formatCurrency($maxAmount);
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get payment summary for a transaction
     */
    public static function getPaymentSummary(Transaction $transaction): array
    {
        $payments = $transaction->payments;
        
        $totalPaid = $payments->where('status', 'completed')
            ->where('amount', '>', 0)
            ->sum('amount');
        
        $totalRefunded = abs($payments->where('status', 'completed')
            ->where('amount', '<', 0)
            ->sum('amount'));
        
        $pendingPayments = $payments->where('status', 'pending')
            ->where('amount', '>', 0)
            ->sum('amount');
        
        $netAmount = $totalPaid - $totalRefunded;
        $remainingAmount = max(0, $transaction->amount - $netAmount);
        
        return [
            'transaction_amount' => $transaction->amount,
            'total_paid' => $totalPaid,
            'total_refunded' => $totalRefunded,
            'net_amount' => $netAmount,
            'pending_payments' => $pendingPayments,
            'remaining_amount' => $remainingAmount,
            'is_fully_paid' => $remainingAmount <= 0.01,
            'is_overpaid' => $netAmount > $transaction->amount,
            'payment_status' => self::determinePaymentStatus($transaction->amount, $netAmount, $pendingPayments)
        ];
    }

    /**
     * Determine payment status based on amounts
     */
    protected static function determinePaymentStatus(float $transactionAmount, float $netAmount, float $pendingAmount): string
    {
        if ($netAmount >= $transactionAmount) {
            return 'fully_paid';
        } elseif ($netAmount > 0) {
            return 'partially_paid';
        } elseif ($pendingAmount > 0) {
            return 'pending';
        } else {
            return 'unpaid';
        }
    }

    /**
     * Get payment method display name
     */
    public static function getPaymentMethodDisplayName(PaymentMethod $paymentMethod): string
    {
        $typeNames = [
            'cash' => 'Cash',
            'card' => 'Credit/Debit Card',
            'digital_wallet' => 'Digital Wallet',
            'bank_transfer' => 'Bank Transfer',
            'crypto' => 'Cryptocurrency',
            'check' => 'Check',
            'gift_card' => 'Gift Card'
        ];
        
        $typeName = $typeNames[$paymentMethod->type] ?? ucfirst($paymentMethod->type);
        
        if ($paymentMethod->provider) {
            return $typeName . ' (' . $paymentMethod->provider . ')';
        }
        
        return $typeName;
    }

    /**
     * Generate payment receipt data
     */
    public static function generateReceiptData(Payment $payment): array
    {
        $transaction = $payment->transaction;
        
        return [
            'payment_number' => $payment->payment_number,
            'transaction_number' => $transaction->transaction_number ?? null,
            'amount' => $payment->amount,
            'currency' => $payment->currency,
            'payment_method' => self::getPaymentMethodDisplayName($payment->paymentMethod),
            'status' => $payment->status,
            'payment_date' => $payment->payment_date->format('Y-m-d H:i:s'),
            'reference_number' => $payment->reference_number,
            'processed_by' => $payment->processedBy->name ?? 'System',
            'gateway_transaction_id' => $payment->gateway_transaction_id,
            'notes' => $payment->notes,
            'transaction_details' => $transaction ? [
                'type' => $transaction->type,
                'description' => $transaction->description,
                'entity_type' => $transaction->transactionable_type,
                'entity_id' => $transaction->transactionable_id
            ] : null
        ];
    }

    /**
     * Calculate change amount for cash payments
     */
    public static function calculateChange(float $paymentAmount, float $receivedAmount): array
    {
        $changeAmount = $receivedAmount - $paymentAmount;
        
        return [
            'change_amount' => max(0, $changeAmount),
            'is_exact' => abs($changeAmount) < 0.01,
            'is_insufficient' => $changeAmount < 0
        ];
    }

    /**
     * Get payment trends for dashboard
     */
    public static function getPaymentTrends(int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);
        
        $payments = Payment::where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->where('amount', '>', 0)
            ->get();
        
        $dailyTotals = $payments->groupBy(function ($payment) {
            return $payment->created_at->format('Y-m-d');
        })->map(function ($dayPayments) {
            return [
                'count' => $dayPayments->count(),
                'total' => $dayPayments->sum('amount'),
                'average' => $dayPayments->avg('amount')
            ];
        });
        
        $methodBreakdown = $payments->groupBy('paymentMethod.type')
            ->map(function ($methodPayments) {
                return [
                    'count' => $methodPayments->count(),
                    'total' => $methodPayments->sum('amount'),
                    'percentage' => 0 // Will be calculated below
                ];
            });
        
        // Calculate percentages
        $totalAmount = $payments->sum('amount');
        if ($totalAmount > 0) {
            $methodBreakdown = $methodBreakdown->map(function ($method) use ($totalAmount) {
                $method['percentage'] = round(($method['total'] / $totalAmount) * 100, 2);
                return $method;
            });
        }
        
        return [
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => Carbon::now()->format('Y-m-d'),
                'days' => $days
            ],
            'summary' => [
                'total_payments' => $payments->count(),
                'total_amount' => $totalAmount,
                'average_payment' => $payments->avg('amount'),
                'daily_average' => $totalAmount / max(1, $days)
            ],
            'daily_totals' => $dailyTotals,
            'method_breakdown' => $methodBreakdown
        ];
    }

    /**
     * Mask sensitive payment information
     */
    public static function maskSensitiveData(array $data): array
    {
        $sensitiveFields = [
            'card_number' => function ($value) {
                return '**** **** **** ' . substr($value, -4);
            },
            'cvv' => function ($value) {
                return '***';
            },
            'account_number' => function ($value) {
                return '****' . substr($value, -4);
            },
            'routing_number' => function ($value) {
                return '****' . substr($value, -4);
            }
        ];
        
        foreach ($sensitiveFields as $field => $maskFunction) {
            if (isset($data[$field])) {
                $data[$field] = $maskFunction($data[$field]);
            }
        }
        
        return $data;
    }

    /**
     * Convert amount between currencies (simplified)
     */
    public static function convertCurrency(float $amount, string $fromCurrency, string $toCurrency): float
    {
        // This is a simplified implementation
        // In production, you would integrate with a real currency conversion API
        
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }
        
        // Mock exchange rates (replace with real API)
        $exchangeRates = [
            'USD' => 1.0,
            'EUR' => 0.85,
            'GBP' => 0.73,
            'JPY' => 110.0,
            'CAD' => 1.25,
            'AUD' => 1.35
        ];
        
        $usdAmount = $amount / ($exchangeRates[$fromCurrency] ?? 1.0);
        $convertedAmount = $usdAmount * ($exchangeRates[$toCurrency] ?? 1.0);
        
        return round($convertedAmount, 2);
    }
}
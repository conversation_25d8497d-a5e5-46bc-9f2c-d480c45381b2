<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StaffAttendance extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'user_id',
        'date',
        'status',
        'check_in_time',
        'check_out_time',
        'scheduled_start_time',
        'scheduled_end_time',
        'late_minutes',
        'early_departure_minutes',
        'overtime_minutes',
        'break_duration_minutes',
        'notes',
        'reason',
        'approved_by',
        'approved_at',
        'location_check_in',
        'location_check_out',
        'ip_address_check_in',
        'ip_address_check_out',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'date' => 'date',
            'check_in_time' => 'datetime',
            'check_out_time' => 'datetime',
            'scheduled_start_time' => 'datetime',
            'scheduled_end_time' => 'datetime',
            'approved_at' => 'datetime',
            'location_check_in' => 'array',
            'location_check_out' => 'array',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByDate($query, $date)
    {
        return $query->where('date', $date);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePresent($query)
    {
        return $query->where('status', 'present');
    }

    public function scopeAbsent($query)
    {
        return $query->where('status', 'absent');
    }

    public function scopeLate($query)
    {
        return $query->where('late_minutes', '>', 0);
    }

    public function scopeEarlyDeparture($query)
    {
        return $query->where('early_departure_minutes', '>', 0);
    }

    public function scopeOvertime($query)
    {
        return $query->where('overtime_minutes', '>', 0);
    }

    // Methods
    public function checkIn($location = null, $ipAddress = null)
    {
        $this->update([
            'check_in_time' => now(),
            'status' => 'present',
            'location_check_in' => $location,
            'ip_address_check_in' => $ipAddress,
        ]);

        $this->calculateLateMinutes();
    }

    public function checkOut($location = null, $ipAddress = null)
    {
        $this->update([
            'check_out_time' => now(),
            'location_check_out' => $location,
            'ip_address_check_out' => $ipAddress,
        ]);

        $this->calculateEarlyDeparture();
        $this->calculateOvertime();
    }

    public function calculateLateMinutes()
    {
        if (!$this->check_in_time || !$this->scheduled_start_time) {
            return;
        }

        $lateMinutes = max(0, $this->check_in_time->diffInMinutes($this->scheduled_start_time, false));
        
        $this->update([
            'late_minutes' => $lateMinutes,
        ]);
    }

    public function calculateEarlyDeparture()
    {
        if (!$this->check_out_time || !$this->scheduled_end_time) {
            return;
        }

        $earlyMinutes = max(0, $this->scheduled_end_time->diffInMinutes($this->check_out_time, false));
        
        $this->update([
            'early_departure_minutes' => $earlyMinutes,
        ]);
    }

    public function calculateOvertime()
    {
        if (!$this->check_out_time || !$this->scheduled_end_time) {
            return;
        }

        $overtimeMinutes = max(0, $this->check_out_time->diffInMinutes($this->scheduled_end_time, false));
        
        $this->update([
            'overtime_minutes' => $overtimeMinutes,
        ]);
    }

    public function getTotalWorkedHours()
    {
        if (!$this->check_in_time || !$this->check_out_time) {
            return 0;
        }

        $totalMinutes = $this->check_out_time->diffInMinutes($this->check_in_time);
        $workingMinutes = $totalMinutes - $this->break_duration_minutes;
        
        return $workingMinutes / 60;
    }

    public function isLate()
    {
        return $this->late_minutes > 0;
    }

    public function hasEarlyDeparture()
    {
        return $this->early_departure_minutes > 0;
    }

    public function hasOvertime()
    {
        return $this->overtime_minutes > 0;
    }
}
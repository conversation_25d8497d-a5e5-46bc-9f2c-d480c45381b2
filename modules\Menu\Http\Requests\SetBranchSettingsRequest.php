<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SetBranchSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'menu_item_id' => 'required|exists:menu_items,id',
            'branch_id' => 'required|exists:branches,id',
            'branch_price' => 'nullable|numeric|min:0',
            'is_available' => 'boolean',
            'stock_quantity' => 'nullable|integer|min:0',
            'low_stock_threshold' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_item_id.required' => 'Menu item is required.',
            'menu_item_id.exists' => 'Selected menu item does not exist.',
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'branch_price.numeric' => 'Branch price must be a valid number.',
            'branch_price.min' => 'Branch price must be greater than or equal to 0.',
            'stock_quantity.integer' => 'Stock quantity must be a valid number.',
            'stock_quantity.min' => 'Stock quantity must be greater than or equal to 0.',
            'low_stock_threshold.integer' => 'Low stock threshold must be a valid number.',
            'low_stock_threshold.min' => 'Low stock threshold must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'menu_item_id' => 'menu item',
            'branch_id' => 'branch',
            'branch_price' => 'branch price',
            'is_available' => 'availability status',
            'stock_quantity' => 'stock quantity',
            'low_stock_threshold' => 'low stock threshold',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_available')) {
            $this->merge([
                'is_available' => filter_var($this->is_available, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
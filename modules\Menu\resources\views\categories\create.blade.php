@extends('menu::layouts.app')

@section('title', 'Create Category')

@section('header-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('menu.web.categories.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Categories
        </a>
    </div>
@endsection

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Create New Category</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('menu.web.categories.store') }}" method="POST">
                    @csrf
                    
                    <div class="row">
                        <!-- Category Name -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required 
                                   placeholder="e.g., Appetizers, Main Course">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Menu Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="menu_id" class="form-label">Menu <span class="text-danger">*</span></label>
                            <select class="form-select @error('menu_id') is-invalid @enderror" 
                                    id="menu_id" name="menu_id" required>
                                <option value="">Select Menu</option>
                                @if(isset($menus))
                                    @foreach($menus as $menu)
                                        <option value="{{ $menu->id }}" 
                                                {{ old('menu_id', request('menu_id')) == $menu->id ? 'selected' : '' }}>
                                            {{ $menu->name }} ({{ ucfirst(str_replace('_', ' ', $menu->menu_type)) }})
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            @error('menu_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" 
                                  placeholder="Optional description for this category">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <!-- Sort Order -->
                        <div class="col-md-6 mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" 
                                   min="0" placeholder="0">
                            <small class="text-muted">Lower numbers appear first</small>
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Status -->
                        <div class="col-md-6 mb-3 d-flex align-items-center">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" 
                                       id="is_active" name="is_active" value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    <strong>Active Category</strong>
                                </label>
                            </div>
                            <div class="ms-3">
                                <small class="text-muted">Inactive categories won't be visible to customers</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Category Image (Optional) -->
                    <div class="mb-3">
                        <label for="image_url" class="form-label">Category Image URL</label>
                        <input type="url" class="form-control @error('image_url') is-invalid @enderror" 
                               id="image_url" name="image_url" value="{{ old('image_url') }}" 
                               placeholder="https://example.com/image.jpg">
                        <small class="text-muted">Optional: Add an image URL to represent this category</small>
                        @error('image_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Preview Section -->
                    <div class="mb-4">
                        <h6>Preview</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;" id="category-icon">
                                            <i class="bi bi-grid-3x3-gap text-white"></i>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <h6 class="mb-1" id="preview-name">Category Name</h6>
                                        <p class="mb-0 text-muted small" id="preview-description">Category description will appear here</p>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge bg-success" id="preview-status">Active</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('menu.web.categories.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-1"></i>
                            Cancel
                        </a>
                        <div>
                            <button type="submit" name="action" value="save" class="btn btn-primary me-2">
                                <i class="bi bi-check-circle me-1"></i>
                                Create Category
                            </button>
                            <button type="submit" name="action" value="save_and_add_item" class="btn btn-success">
                                <i class="bi bi-plus-circle me-1"></i>
                                Create & Add Item
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Quick Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightbulb me-1"></i>
                    Quick Tips
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><strong>Category Names:</strong> Use clear, descriptive names like "Appetizers", "Main Course", "Desserts"</li>
                    <li><strong>Sort Order:</strong> Use increments of 10 (10, 20, 30) to allow easy reordering later</li>
                    <li><strong>Descriptions:</strong> Help customers understand what types of items are in this category</li>
                    <li><strong>Menu Assignment:</strong> Each category belongs to one menu (Breakfast, Lunch, Dinner, etc.)</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Real-time preview updates
document.getElementById('name').addEventListener('input', function() {
    const previewName = document.getElementById('preview-name');
    previewName.textContent = this.value || 'Category Name';
});

document.getElementById('description').addEventListener('input', function() {
    const previewDescription = document.getElementById('preview-description');
    previewDescription.textContent = this.value || 'Category description will appear here';
});

document.getElementById('is_active').addEventListener('change', function() {
    const previewStatus = document.getElementById('preview-status');
    if (this.checked) {
        previewStatus.textContent = 'Active';
        previewStatus.className = 'badge bg-success';
    } else {
        previewStatus.textContent = 'Inactive';
        previewStatus.className = 'badge bg-secondary';
    }
});

// Image preview
document.getElementById('image_url').addEventListener('input', function() {
    const categoryIcon = document.getElementById('category-icon');
    if (this.value) {
        categoryIcon.innerHTML = `<img src="${this.value}" alt="Category" style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;">`;
    } else {
        categoryIcon.innerHTML = '<i class="bi bi-grid-3x3-gap text-white"></i>';
    }
});

// Auto-suggest sort order based on existing categories
document.getElementById('menu_id').addEventListener('change', function() {
    if (this.value) {
        // In a real application, you might fetch the next sort order via AJAX
        // For now, we'll just suggest a default
        const sortOrderInput = document.getElementById('sort_order');
        if (!sortOrderInput.value) {
            sortOrderInput.value = 10;
        }
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const menuId = document.getElementById('menu_id').value;
    
    if (!name) {
        e.preventDefault();
        alert('Please enter a category name');
        document.getElementById('name').focus();
        return;
    }
    
    if (!menuId) {
        e.preventDefault();
        alert('Please select a menu');
        document.getElementById('menu_id').focus();
        return;
    }
});
</script>
@endpush
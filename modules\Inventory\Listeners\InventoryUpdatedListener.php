<?php

namespace Modules\Inventory\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\Inventory\Jobs\ProcessInventoryAlerts;
use App\Models\BranchInventory;
use Modules\Inventory\Helpers\InventoryHelper;
use Modules\Inventory\Notifications\LowStockAlert;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class InventoryUpdatedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        try {
            // Check if the event has inventory data
            $inventory = $this->getInventoryFromEvent($event);
            
            if (!$inventory) {
                return;
            }
            
            Log::info('Processing inventory update event', [
                'inventory_id' => $inventory->id,
                'product_name' => $inventory->product->name,
                'current_stock' => $inventory->current_stock,
                'minimum_level' => $inventory->minimum_stock_level
            ]);
            
            // Check for immediate alerts that need to be sent
            $this->checkImmediateAlerts($inventory);
            
        } catch (\Exception $e) {
            Log::error('Error in inventory updated listener', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Extract inventory from event.
     */
    protected function getInventoryFromEvent($event): ?BranchInventory
    {
        // Handle different event types
        if (isset($event->inventory)) {
            return $event->inventory;
        }
        
        if (isset($event->branchInventory)) {
            return $event->branchInventory;
        }
        
        if (isset($event->model) && $event->model instanceof BranchInventory) {
            return $event->model;
        }
        
        // If event has inventory_id, fetch the model
        if (isset($event->inventory_id)) {
            return BranchInventory::with(['product', 'branch'])->find($event->inventory_id);
        }
        
        return null;
    }

    /**
     * Check for alerts that need immediate attention.
     */
    protected function checkImmediateAlerts(BranchInventory $inventory): void
    {
        $alerts = [];
        
        // Check for critical alerts (out of stock, expired)
        if (InventoryHelper::isOutOfStock($inventory)) {
            $alerts[] = 'out_of_stock';
        }
        
        // Check for expired items
        if ($inventory->expiry_date && $inventory->expiry_date < now()) {
            $alerts[] = 'expired';
        }
        
        // Check for low stock (only if not out of stock)
        if (!in_array('out_of_stock', $alerts) && InventoryHelper::isLowStock($inventory)) {
            $alerts[] = 'low_stock';
        }
        
        // Send immediate alerts for critical situations
        foreach ($alerts as $alertType) {
            if (in_array($alertType, ['out_of_stock', 'expired'])) {
                $this->sendImmediateAlert($inventory, $alertType);
            }
        }
        
        // Queue a job to process all alerts for this branch
        // This ensures comprehensive checking while avoiding duplicate immediate alerts
        ProcessInventoryAlerts::dispatch(
            $inventory->branch_id,
            ['low_stock', 'overstocked', 'expiring_soon']
        )->delay(now()->addMinutes(5)); // Delay to avoid spam
    }

    /**
     * Send immediate alert for critical situations.
     */
    protected function sendImmediateAlert(BranchInventory $inventory, string $alertType): void
    {
        try {
            // Check if we've already sent this alert recently (within last hour for critical alerts)
            $cacheKey = "critical_alert_{$inventory->id}_{$alertType}";
            
            if (cache()->has($cacheKey)) {
                return;
            }
            
            // Get users who should receive critical alerts
            $users = $this->getCriticalAlertRecipients($inventory->branch_id);
            
            if ($users->isEmpty()) {
                Log::warning('No recipients found for critical inventory alert', [
                    'inventory_id' => $inventory->id,
                    'alert_type' => $alertType,
                    'branch_id' => $inventory->branch_id
                ]);
                return;
            }
            
            // Send notification
            Notification::send($users, new LowStockAlert($inventory, $alertType));
            
            // Cache to prevent duplicate alerts for 1 hour
            cache()->put($cacheKey, true, now()->addHour());
            
            // Log the alert
            InventoryHelper::logAction(
                $inventory->id,
                'critical_alert_sent',
                $inventory->current_stock,
                $inventory->current_stock,
                $alertType,
                "Critical alert sent to {$users->count()} recipient(s)"
            );
            
            Log::info('Critical inventory alert sent', [
                'inventory_id' => $inventory->id,
                'product_name' => $inventory->product->name,
                'alert_type' => $alertType,
                'recipients_count' => $users->count()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error sending critical inventory alert', [
                'inventory_id' => $inventory->id,
                'alert_type' => $alertType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get users who should receive critical alerts.
     */
    protected function getCriticalAlertRecipients(string $branchId): \Illuminate\Database\Eloquent\Collection
    {
        // Get users with high-priority roles for critical alerts
        return User::whereHas('roles', function ($query) {
                $query->whereIn('name', ['admin', 'inventory_manager', 'branch_manager']);
            })
            ->where(function ($query) use ($branchId) {
                // Users assigned to this branch or global users
                $query->whereHas('branches', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                })->orWhereHas('roles', function ($q) {
                    $q->where('name', 'admin');
                });
            })
            ->where('is_active', true)
            ->get();
    }

    /**
     * Handle a job failure.
     */
    public function failed($event, \Throwable $exception): void
    {
        Log::error('Inventory updated listener failed', [
            'event' => get_class($event),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }

    /**
     * Get the number of times to retry the job.
     */
    public function tries(): int
    {
        return 3;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 120, 300]; // 30 seconds, 2 minutes, 5 minutes
    }
}
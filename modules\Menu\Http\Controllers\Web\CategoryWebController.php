<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreCategoryRequest;
use Modules\Menu\Http\Requests\UpdateCategoryRequest;

class CategoryWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of categories
     */
    public function index(): View
    {
        $categories = $this->menuService->getCategories();
        return view('menu::categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new category
     */
    public function create(): View
    {
        $menus = $this->menuService->getAllMenus();
        $categories = $this->menuService->getCategories(); // For parent categories
        return view('menu::categories.create', compact('menus', 'categories'));
    }

    /**
     * Store a newly created category
     */
    public function store(StoreCategoryRequest $request): RedirectResponse
    {
        try {
            $category = $this->menuService->createCategory($request->validated());
            return redirect()->route('menu.web.categories.index')
                ->with('success', 'Category created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to create category: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified category
     */
    public function show(string $id): View
    {
        $category = $this->menuService->getCategoryById($id);
        $menuItems = $this->menuService->getItemsByCategory($id);
        return view('menu::categories.show', compact('category', 'menuItems'));
    }

    /**
     * Show the form for editing the specified category
     */
    public function edit(string $id): View
    {
        $category = $this->menuService->getCategoryById($id);
        $menus = $this->menuService->getAllMenus();
        $categories = $this->menuService->getCategories(); // For parent categories
        return view('menu::categories.edit', compact('category', 'menus', 'categories'));
    }

    /**
     * Update the specified category
     */
    public function update(UpdateCategoryRequest $request, string $id): RedirectResponse
    {
        try {
            $category = $this->menuService->updateCategory($id, $request->validated());
            return redirect()->route('menu.web.categories.index')
                ->with('success', 'Category updated successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to update category: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified category
     */
    public function destroy(string $id): RedirectResponse
    {
        try {
            $this->menuService->deleteCategory($id);
            return redirect()->route('menu.web.categories.index')
                ->with('success', 'Category deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete category: ' . $e->getMessage());
        }
    }
}
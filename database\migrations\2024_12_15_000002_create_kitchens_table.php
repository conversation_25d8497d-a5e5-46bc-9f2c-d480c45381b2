<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kitchens', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('station_type', ['hot', 'cold', 'grill', 'fryer', 'salad', 'dessert', 'beverage', 'prep', 'main', 'other'])->default('main');
            $table->integer('max_concurrent_orders')->default(10);
            $table->integer('average_prep_time_minutes')->default(15);
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);
            $table->foreignId('manager_id')->nullable()->constrained('users')->onDelete('set null');
            $table->json('equipment_list')->nullable();
            $table->json('operating_hours')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['tenant_id', 'branch_id']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['branch_id', 'is_active']);
            $table->index('station_type');
            $table->index('display_order');
            
            // Unique constraint for kitchen code within tenant
            $table->unique(['tenant_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kitchens');
    }
};
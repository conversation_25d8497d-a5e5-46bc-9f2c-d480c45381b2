<?php

namespace Modules\Kitchen\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Tenant;
use App\Models\MenuItem;

class KitchenMenuItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'kitchen_id',
        'menu_item_id',
        'prep_time_minutes',
        'priority_level',
        'is_active',
        'special_instructions',
        'assigned_by',
        'assigned_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'prep_time_minutes' => 'integer',
            'priority_level' => 'integer',
            'assigned_at' => 'datetime',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function kitchen()
    {
        return $this->belongsTo(Kitchen::class);
    }

    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    public function assignedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'assigned_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeForKitchen($query, $kitchenId)
    {
        return $query->where('kitchen_id', $kitchenId);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority_level', $priority);
    }

    public function scopeHighPriority($query)
    {
        return $query->where('priority_level', '>=', 8);
    }

    // Helper methods
    public function getPriorityLevelText()
    {
        $levels = [
            1 => 'Very Low',
            2 => 'Low',
            3 => 'Low',
            4 => 'Normal',
            5 => 'Normal',
            6 => 'Normal',
            7 => 'High',
            8 => 'High',
            9 => 'Very High',
            10 => 'Critical'
        ];

        return $levels[$this->priority_level] ?? 'Normal';
    }

    public function getEstimatedPrepTime()
    {
        return $this->prep_time_minutes ?? $this->menuItem->prep_time_minutes ?? 15;
    }
}
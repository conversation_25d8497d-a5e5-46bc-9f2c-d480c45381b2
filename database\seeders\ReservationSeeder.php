<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Reservation;

class ReservationSeeder extends Seeder
{
    public function run()
    {
        $branch = \App\Models\Branch::first();
        $customer = \App\Models\Customer::first();
        $table = \App\Models\Table::first();

        if (!$branch || !$customer || !$table) {
            $this->command->info('No branch, customer or table found. Skipping ReservationSeeder.');
            return;
        }

        Reservation::create([
            'branch_id' => $branch->id,
            'customer_id' => $customer->id,
            'table_id' => $table->id,
            'reservation_number' => 'RES' . now()->format('YmdHis'),
            'customer_name' => $customer->first_name . ' ' . $customer->last_name,
            'customer_phone' => $customer->phone,
            'party_size' => 2,
            'reservation_datetime' => now()->addHours(2),
            'customer_id' => 1,
            'table_id' => 1,
            'status' => 'pending',
            // Add other required fields here
        ]);
    }
}
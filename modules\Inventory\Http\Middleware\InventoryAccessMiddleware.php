<?php

namespace Modules\Inventory\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use App\Models\BranchInventory;
use App\Models\PurchaseOrder;
use App\Models\Supplier;

class InventoryAccessMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $permission = null): Response
    {
        if (!Auth::check()) {
            return response()->json([
                'error' => 'Unauthorized',
                'message' => 'Authentication required'
            ], 401);
        }

        $user = Auth::user();
        
        // Check if user has inventory module access
        if (!$this->hasInventoryAccess($user)) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => 'You do not have access to the inventory module'
            ], 403);
        }

        // Check specific permission if provided
        if ($permission && !$this->hasPermission($user, $permission)) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => "You do not have permission: {$permission}"
            ], 403);
        }

        // Check branch-specific access for inventory items
        if ($this->isInventoryItemRequest($request)) {
            if (!$this->hasBranchInventoryAccess($request, $user)) {
                return response()->json([
                    'error' => 'Forbidden',
                    'message' => 'You do not have access to this branch inventory'
                ], 403);
            }
        }

        // Check supplier access
        if ($this->isSupplierRequest($request)) {
            if (!$this->hasSupplierAccess($request, $user)) {
                return response()->json([
                    'error' => 'Forbidden',
                    'message' => 'You do not have access to this supplier'
                ], 403);
            }
        }

        // Check purchase order access
        if ($this->isPurchaseOrderRequest($request)) {
            if (!$this->hasPurchaseOrderAccess($request, $user)) {
                return response()->json([
                    'error' => 'Forbidden',
                    'message' => 'You do not have access to this purchase order'
                ], 403);
            }
        }

        return $next($request);
    }

    /**
     * Check if user has inventory module access.
     */
    protected function hasInventoryAccess($user): bool
    {
        return $user->hasAnyRole([
            'admin',
            'inventory_manager',
            'branch_manager',
            'staff'
        ]) || $user->hasPermissionTo('inventory.access');
    }

    /**
     * Check if user has specific permission.
     */
    protected function hasPermission($user, string $permission): bool
    {
        // Admin users have all permissions
        if ($user->hasRole('admin')) {
            return true;
        }

        return $user->hasPermissionTo($permission);
    }

    /**
     * Check if this is an inventory item request.
     */
    protected function isInventoryItemRequest(Request $request): bool
    {
        return $request->route('inventory') || 
               $request->route('inventoryId') ||
               str_contains($request->path(), 'inventory/items');
    }

    /**
     * Check if this is a supplier request.
     */
    protected function isSupplierRequest(Request $request): bool
    {
        return $request->route('supplier') || 
               $request->route('supplierId') ||
               str_contains($request->path(), 'suppliers');
    }

    /**
     * Check if this is a purchase order request.
     */
    protected function isPurchaseOrderRequest(Request $request): bool
    {
        return $request->route('purchaseOrder') || 
               $request->route('purchaseOrderId') ||
               str_contains($request->path(), 'purchase-orders');
    }

    /**
     * Check branch inventory access.
     */
    protected function hasBranchInventoryAccess(Request $request, $user): bool
    {
        // Admin users have access to all branches
        if ($user->hasRole('admin')) {
            return true;
        }

        $inventoryId = $request->route('inventory') ?? $request->route('inventoryId');
        
        if (!$inventoryId) {
            // For listing endpoints, check if user has access to any branch
            return $this->hasAnyBranchAccess($user);
        }

        $inventory = BranchInventory::find($inventoryId);
        
        if (!$inventory) {
            return false;
        }

        return $this->hasBranchAccess($user, $inventory->branch_id);
    }

    /**
     * Check supplier access.
     */
    protected function hasSupplierAccess(Request $request, $user): bool
    {
        // Admin users have access to all suppliers
        if ($user->hasRole('admin')) {
            return true;
        }

        $supplierId = $request->route('supplier') ?? $request->route('supplierId');
        
        if (!$supplierId) {
            // For listing endpoints, allow if user has inventory access
            return $user->hasAnyRole(['inventory_manager', 'branch_manager']);
        }

        $supplier = Supplier::find($supplierId);
        
        if (!$supplier) {
            return false;
        }

        // Check if user has access to any branch that uses this supplier
        return $this->hasSupplierBranchAccess($user, $supplier);
    }

    /**
     * Check purchase order access.
     */
    protected function hasPurchaseOrderAccess(Request $request, $user): bool
    {
        // Admin users have access to all purchase orders
        if ($user->hasRole('admin')) {
            return true;
        }

        $purchaseOrderId = $request->route('purchaseOrder') ?? $request->route('purchaseOrderId');
        
        if (!$purchaseOrderId) {
            // For listing endpoints, allow if user has inventory access
            return $user->hasAnyRole(['inventory_manager', 'branch_manager']);
        }

        $purchaseOrder = PurchaseOrder::find($purchaseOrderId);
        
        if (!$purchaseOrder) {
            return false;
        }

        return $this->hasBranchAccess($user, $purchaseOrder->branch_id);
    }

    /**
     * Check if user has access to specific branch.
     */
    protected function hasBranchAccess($user, string $branchId): bool
    {
        // Check if user is assigned to this branch
        return $user->branches()->where('branch_id', $branchId)->exists() ||
               $user->hasRole('admin');
    }

    /**
     * Check if user has access to any branch.
     */
    protected function hasAnyBranchAccess($user): bool
    {
        return $user->branches()->exists() || $user->hasRole('admin');
    }

    /**
     * Check if user has access to supplier through branch access.
     */
    protected function hasSupplierBranchAccess($user, Supplier $supplier): bool
    {
        // If user has inventory manager role, they can access all suppliers
        if ($user->hasRole('inventory_manager')) {
            return true;
        }

        // Check if user has access to any branch that has purchase orders with this supplier
        $userBranchIds = $user->branches()->pluck('branch_id');
        
        return PurchaseOrder::where('supplier_id', $supplier->id)
            ->whereIn('branch_id', $userBranchIds)
            ->exists();
    }

    /**
     * Get user's accessible branch IDs.
     */
    public static function getUserBranchIds($user): array
    {
        if ($user->hasRole('admin')) {
            return \App\Models\Branch::pluck('id')->toArray();
        }

        return $user->branches()->pluck('branch_id')->toArray();
    }

    /**
     * Filter query by user's accessible branches.
     */
    public static function filterByUserBranches($query, $user, string $branchColumn = 'branch_id')
    {
        if ($user->hasRole('admin')) {
            return $query;
        }

        $branchIds = self::getUserBranchIds($user);
        
        return $query->whereIn($branchColumn, $branchIds);
    }
}
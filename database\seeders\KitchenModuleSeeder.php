<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KitchenMenuItem;
use App\Models\Tenant;
use App\Models\Branch;
use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class KitchenModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            $this->seedKitchens();
            $this->seedKitchenMenuItems();
        });
    }

    /**
     * Seed default kitchens for existing tenants
     */
    private function seedKitchens(): void
    {
        $tenants = Tenant::with('branches')->get();

        foreach ($tenants as $tenant) {
            foreach ($tenant->branches as $branch) {
                $this->createDefaultKitchensForBranch($tenant, $branch);
            }
        }
    }

    /**
     * Create default kitchens for a branch
     */
    private function createDefaultKitchensForBranch(Tenant $tenant, Branch $branch): void
    {
        $defaultKitchens = [
            [
                'name' => 'Main Kitchen',
                'code' => 'MAIN01',
                'description' => 'Primary kitchen station for main course preparation',
                'station_type' => 'main',
                'max_concurrent_orders' => 15,
                'average_prep_time_minutes' => 20,
                'display_order' => 1,
                'equipment_list' => ['Stove', 'Oven', 'Prep Counter', 'Refrigerator'],
                'operating_hours' => [
                    'monday' => ['start' => '06:00', 'end' => '23:00'],
                    'tuesday' => ['start' => '06:00', 'end' => '23:00'],
                    'wednesday' => ['start' => '06:00', 'end' => '23:00'],
                    'thursday' => ['start' => '06:00', 'end' => '23:00'],
                    'friday' => ['start' => '06:00', 'end' => '23:00'],
                    'saturday' => ['start' => '06:00', 'end' => '23:00'],
                    'sunday' => ['start' => '06:00', 'end' => '23:00'],
                ],
            ],
            [
                'name' => 'Grill Station',
                'code' => 'GRILL01',
                'description' => 'Dedicated grill station for grilled items',
                'station_type' => 'grill',
                'max_concurrent_orders' => 8,
                'average_prep_time_minutes' => 15,
                'display_order' => 2,
                'equipment_list' => ['Grill', 'Tongs', 'Thermometer'],
                'operating_hours' => [
                    'monday' => ['start' => '11:00', 'end' => '22:00'],
                    'tuesday' => ['start' => '11:00', 'end' => '22:00'],
                    'wednesday' => ['start' => '11:00', 'end' => '22:00'],
                    'thursday' => ['start' => '11:00', 'end' => '22:00'],
                    'friday' => ['start' => '11:00', 'end' => '23:00'],
                    'saturday' => ['start' => '11:00', 'end' => '23:00'],
                    'sunday' => ['start' => '11:00', 'end' => '22:00'],
                ],
            ],
            [
                'name' => 'Cold Station',
                'code' => 'COLD01',
                'description' => 'Cold food preparation station for salads and cold appetizers',
                'station_type' => 'cold',
                'max_concurrent_orders' => 12,
                'average_prep_time_minutes' => 8,
                'display_order' => 3,
                'equipment_list' => ['Refrigerated Counter', 'Cutting Boards', 'Knives'],
                'operating_hours' => [
                    'monday' => ['start' => '08:00', 'end' => '22:00'],
                    'tuesday' => ['start' => '08:00', 'end' => '22:00'],
                    'wednesday' => ['start' => '08:00', 'end' => '22:00'],
                    'thursday' => ['start' => '08:00', 'end' => '22:00'],
                    'friday' => ['start' => '08:00', 'end' => '23:00'],
                    'saturday' => ['start' => '08:00', 'end' => '23:00'],
                    'sunday' => ['start' => '08:00', 'end' => '22:00'],
                ],
            ],
            [
                'name' => 'Beverage Station',
                'code' => 'BEV01',
                'description' => 'Beverage preparation station for drinks and smoothies',
                'station_type' => 'beverage',
                'max_concurrent_orders' => 20,
                'average_prep_time_minutes' => 5,
                'display_order' => 4,
                'equipment_list' => ['Blender', 'Coffee Machine', 'Ice Machine', 'Juicer'],
                'operating_hours' => [
                    'monday' => ['start' => '06:00', 'end' => '23:00'],
                    'tuesday' => ['start' => '06:00', 'end' => '23:00'],
                    'wednesday' => ['start' => '06:00', 'end' => '23:00'],
                    'thursday' => ['start' => '06:00', 'end' => '23:00'],
                    'friday' => ['start' => '06:00', 'end' => '24:00'],
                    'saturday' => ['start' => '06:00', 'end' => '24:00'],
                    'sunday' => ['start' => '06:00', 'end' => '23:00'],
                ],
            ],
            [
                'name' => 'Dessert Station',
                'code' => 'DESS01',
                'description' => 'Dessert preparation station for sweets and pastries',
                'station_type' => 'dessert',
                'max_concurrent_orders' => 6,
                'average_prep_time_minutes' => 12,
                'display_order' => 5,
                'equipment_list' => ['Oven', 'Mixer', 'Decorating Tools', 'Refrigerator'],
                'operating_hours' => [
                    'monday' => ['start' => '10:00', 'end' => '22:00'],
                    'tuesday' => ['start' => '10:00', 'end' => '22:00'],
                    'wednesday' => ['start' => '10:00', 'end' => '22:00'],
                    'thursday' => ['start' => '10:00', 'end' => '22:00'],
                    'friday' => ['start' => '10:00', 'end' => '23:00'],
                    'saturday' => ['start' => '10:00', 'end' => '23:00'],
                    'sunday' => ['start' => '10:00', 'end' => '22:00'],
                ],
            ],
        ];

        // Get a manager user for the tenant (if available)
        $manager = User::where('tenant_id', $tenant->id)
            ->whereHas('roles', function ($query) {
                $query->whereIn('name', ['manager', 'admin', 'kitchen_manager']);
            })
            ->first();

        foreach ($defaultKitchens as $kitchenData) {
            Kitchen::create(array_merge($kitchenData, [
                'tenant_id' => $tenant->id,
                'branch_id' => $branch->id,
                'manager_id' => $manager?->id,
                'is_active' => true,
                'created_by' => $manager?->id,
            ]));
        }
    }

    /**
     * Seed kitchen menu item assignments
     */
    private function seedKitchenMenuItems(): void
    {
        $tenants = Tenant::with(['branches.kitchens'])->get();

        foreach ($tenants as $tenant) {
            $this->assignMenuItemsToKitchens($tenant);
        }
    }

    /**
     * Assign menu items to appropriate kitchens based on categories
     */
    private function assignMenuItemsToKitchens(Tenant $tenant): void
    {
        // Get menu items for this tenant
        $menuItems = MenuItem::where('tenant_id', $tenant->id)->get();
        
        if ($menuItems->isEmpty()) {
            return;
        }

        // Get kitchens for this tenant
        $kitchens = Kitchen::where('tenant_id', $tenant->id)->get()->keyBy('station_type');

        // Define menu item to kitchen mappings based on common categories
        $categoryMappings = [
            // Main dishes go to main kitchen
            'main' => ['main_course', 'entree', 'pasta', 'rice', 'noodles', 'curry'],
            // Grilled items go to grill station
            'grill' => ['grilled', 'barbecue', 'steak', 'burger', 'kebab'],
            // Cold items go to cold station
            'cold' => ['salad', 'cold_appetizer', 'sandwich', 'wrap'],
            // Beverages go to beverage station
            'beverage' => ['drink', 'beverage', 'juice', 'coffee', 'tea', 'smoothie'],
            // Desserts go to dessert station
            'dessert' => ['dessert', 'cake', 'ice_cream', 'pastry', 'sweet'],
        ];

        foreach ($menuItems as $menuItem) {
            $assignedKitchen = $this->determineKitchenForMenuItem($menuItem, $kitchens, $categoryMappings);
            
            if ($assignedKitchen) {
                KitchenMenuItem::create([
                    'tenant_id' => $tenant->id,
                    'kitchen_id' => $assignedKitchen->id,
                    'menu_item_id' => $menuItem->id,
                    'prep_time_minutes' => $this->estimatePrepTime($menuItem, $assignedKitchen),
                    'priority_level' => $this->determinePriority($menuItem),
                    'is_active' => true,
                    'assigned_at' => now(),
                ]);
            }
        }
    }

    /**
     * Determine which kitchen should handle a menu item
     */
    private function determineKitchenForMenuItem($menuItem, $kitchens, $categoryMappings)
    {
        $itemName = strtolower($menuItem->name);
        $itemCategory = strtolower($menuItem->category ?? '');
        $itemDescription = strtolower($menuItem->description ?? '');
        
        // Check each kitchen type mapping
        foreach ($categoryMappings as $kitchenType => $keywords) {
            foreach ($keywords as $keyword) {
                if (str_contains($itemName, $keyword) || 
                    str_contains($itemCategory, $keyword) || 
                    str_contains($itemDescription, $keyword)) {
                    
                    if (isset($kitchens[$kitchenType])) {
                        return $kitchens[$kitchenType];
                    }
                }
            }
        }
        
        // Default to main kitchen if no specific match
        return $kitchens['main'] ?? $kitchens->first();
    }

    /**
     * Estimate preparation time based on menu item and kitchen
     */
    private function estimatePrepTime($menuItem, $kitchen)
    {
        // Base prep time from menu item or kitchen default
        $basePrepTime = $menuItem->prep_time_minutes ?? $kitchen->average_prep_time_minutes;
        
        // Adjust based on kitchen type
        $adjustments = [
            'beverage' => 0.3, // Beverages are typically faster
            'cold' => 0.5,     // Cold items are faster
            'dessert' => 1.2,  // Desserts might take longer
            'grill' => 1.1,    // Grilled items take a bit longer
            'main' => 1.0,     // Main kitchen baseline
        ];
        
        $multiplier = $adjustments[$kitchen->station_type] ?? 1.0;
        
        return max(1, round($basePrepTime * $multiplier));
    }

    /**
     * Determine priority level for menu item
     */
    private function determinePriority($menuItem)
    {
        // Higher priority for popular or premium items
        if (str_contains(strtolower($menuItem->name), 'special') || 
            str_contains(strtolower($menuItem->name), 'signature')) {
            return 8; // High priority
        }
        
        if (str_contains(strtolower($menuItem->name), 'quick') || 
            str_contains(strtolower($menuItem->name), 'express')) {
            return 7; // Above normal priority
        }
        
        return 5; // Normal priority
    }
}
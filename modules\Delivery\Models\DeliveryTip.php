<?php

namespace Modules\Delivery\Models;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryTip extends Model
{
    use HasFactory;

    protected $fillable = [
        'delivery_assignment_id',
        'customer_id',
        'amount',
        'payment_method',
        'transaction_reference',
        'status',
        'note',
        'paid_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the delivery assignment this tip belongs to.
     */
    public function deliveryAssignment(): BelongsTo
    {
        return $this->belongsTo(DeliveryAssignment::class);
    }

    /**
     * Get the customer who gave this tip.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the delivery personnel through the assignment.
     */
    public function deliveryPersonnel()
    {
        return $this->hasOneThrough(
            DeliveryPersonnel::class,
            DeliveryAssignment::class,
            'id',
            'id',
            'delivery_assignment_id',
            'delivery_personnel_id'
        );
    }

    /**
     * Mark tip as paid.
     */
    public function markAsPaid(string $transactionReference = null): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'transaction_reference' => $transactionReference,
        ]);

        // Update delivery personnel earnings
        $personnel = $this->deliveryAssignment->deliveryPersonnel;
        if ($personnel) {
            $personnel->increment('total_earnings', $this->amount);
        }
    }

    /**
     * Mark tip as failed.
     */
    public function markAsFailed(): void
    {
        $this->update(['status' => 'failed']);
    }

    /**
     * Process refund for the tip.
     */
    public function refund(): void
    {
        if ($this->status !== 'paid') {
            throw new \Exception('Can only refund paid tips');
        }

        $this->update(['status' => 'refunded']);

        // Deduct from delivery personnel earnings
        $personnel = $this->deliveryAssignment->deliveryPersonnel;
        if ($personnel) {
            $personnel->decrement('total_earnings', $this->amount);
        }
    }

    /**
     * Get formatted payment method name.
     */
    public function getFormattedPaymentMethod(): string
    {
        return match($this->payment_method) {
            'cash' => 'Cash',
            'card' => 'Credit/Debit Card',
            'digital_wallet' => 'Digital Wallet',
            'app_credit' => 'App Credit',
            default => ucfirst(str_replace('_', ' ', $this->payment_method))
        };
    }

    /**
     * Check if tip can be refunded.
     */
    public function canBeRefunded(): bool
    {
        return $this->status === 'paid' && 
               $this->paid_at && 
               $this->paid_at->diffInDays(now()) <= 30; // 30-day refund window
    }

    /**
     * Get tip percentage based on order total.
     */
    public function getTipPercentage(): float
    {
        $orderTotal = $this->deliveryAssignment->order->total_amount;
        
        if ($orderTotal <= 0) {
            return 0;
        }

        return ($this->amount / $orderTotal) * 100;
    }

    /**
     * Scope for paid tips.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for pending tips.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for failed tips.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for tips by payment method.
     */
    public function scopeByPaymentMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope for tips within date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for tips above amount.
     */
    public function scopeAboveAmount($query, float $amount)
    {
        return $query->where('amount', '>', $amount);
    }

    /**
     * Get tips statistics for a delivery personnel.
     */
    public static function getPersonnelTipStats(int $personnelId, $startDate = null, $endDate = null): array
    {
        $query = static::whereHas('deliveryAssignment', function ($q) use ($personnelId) {
            $q->where('delivery_personnel_id', $personnelId);
        })->paid();

        if ($startDate && $endDate) {
            $query->withinDateRange($startDate, $endDate);
        }

        $tips = $query->get();

        return [
            'total_tips' => $tips->count(),
            'total_amount' => $tips->sum('amount'),
            'average_tip' => $tips->avg('amount') ?? 0,
            'highest_tip' => $tips->max('amount') ?? 0,
            'by_payment_method' => $tips->groupBy('payment_method')
                ->map(function ($group) {
                    return [
                        'count' => $group->count(),
                        'total' => $group->sum('amount'),
                    ];
                })->toArray(),
        ];
    }
}
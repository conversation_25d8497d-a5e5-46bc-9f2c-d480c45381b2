<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuItemAddon extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'menu_item_id',
        'addon_group_name',
        'name',
        'code',
        'price',
        'cost',
        'is_required',
        'max_quantity',
        'sort_order',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'cost' => 'decimal:2',
            'is_required' => 'boolean',
            'max_quantity' => 'integer',
            'sort_order' => 'integer',
        ];
    }

    // Relationships
    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    public function orderItemAddons()
    {
        return $this->hasMany(OrderItemAddon::class, 'addon_id');
    }
}
<?php

namespace Modules\Inventory\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\PurchaseOrder;
use App\Models\Supplier;
use App\Models\User;

class PurchaseOrderStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    protected $purchaseOrder;
    protected $oldStatus;
    protected $newStatus;
    protected $changedBy;
    protected $notes;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        PurchaseOrder $purchaseOrder, 
        string $oldStatus, 
        string $newStatus, 
        User $changedBy = null,
        string $notes = null
    ) {
        $this->purchaseOrder = $purchaseOrder;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
        $this->changedBy = $changedBy;
        $this->notes = $notes;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $subject = $this->getSubject();
        $greeting = $this->getGreeting();
        
        $mail = (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($this->getMessage())
            ->line('Purchase Order: ' . $this->purchaseOrder->po_number)
            ->line('Supplier: ' . $this->purchaseOrder->supplier->name)
            ->line('Total Amount: ' . number_format($this->purchaseOrder->total_amount, 2))
            ->line('Previous Status: ' . ucwords(str_replace('_', ' ', $this->oldStatus)))
            ->line('New Status: ' . ucwords(str_replace('_', ' ', $this->newStatus)));
            
        if ($this->changedBy) {
            $mail->line('Changed by: ' . $this->changedBy->name);
        }
        
        if ($this->notes) {
            $mail->line('Notes: ' . $this->notes);
        }
        
        $mail->action('View Purchase Order', url('/purchase-orders/' . $this->purchaseOrder->id));
        
        // Add status-specific information
        switch ($this->newStatus) {
            case 'approved':
                $mail->line('The purchase order has been approved and can now be sent to the supplier.');
                break;
            case 'rejected':
                $mail->line('The purchase order has been rejected. Please review and make necessary changes.');
                break;
            case 'received':
                $mail->line('The purchase order has been received. Inventory levels have been updated.');
                break;
            case 'cancelled':
                $mail->line('The purchase order has been cancelled.');
                break;
            case 'partially_received':
                $mail->line('The purchase order has been partially received. Some items are still pending.');
                break;
        }
        
        return $mail;
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        return [
            'type' => 'purchase_order_status_changed',
            'title' => $this->getSubject(),
            'message' => $this->getMessage(),
            'data' => [
                'purchase_order_id' => $this->purchaseOrder->id,
                'po_number' => $this->purchaseOrder->po_number,
                'supplier_id' => $this->purchaseOrder->supplier_id,
                'supplier_name' => $this->purchaseOrder->supplier->name,
                'total_amount' => $this->purchaseOrder->total_amount,
                'old_status' => $this->oldStatus,
                'new_status' => $this->newStatus,
                'changed_by_id' => $this->changedBy?->id,
                'changed_by_name' => $this->changedBy?->name,
                'notes' => $this->notes,
                'priority' => $this->getPriority(),
                'expected_delivery_date' => $this->purchaseOrder->expected_delivery_date,
            ],
            'action_url' => url('/purchase-orders/' . $this->purchaseOrder->id),
            'created_at' => now(),
        ];
    }

    /**
     * Get the notification subject.
     */
    protected function getSubject(): string
    {
        $statusText = ucwords(str_replace('_', ' ', $this->newStatus));
        return "Purchase Order {$statusText} - {$this->purchaseOrder->po_number}";
    }

    /**
     * Get the notification greeting.
     */
    protected function getGreeting(): string
    {
        switch ($this->newStatus) {
            case 'approved':
                return 'Purchase Order Approved!';
            case 'rejected':
                return 'Purchase Order Rejected';
            case 'received':
                return 'Purchase Order Received!';
            case 'cancelled':
                return 'Purchase Order Cancelled';
            case 'partially_received':
                return 'Purchase Order Partially Received';
            default:
                return 'Purchase Order Status Update';
        }
    }

    /**
     * Get the notification message.
     */
    protected function getMessage(): string
    {
        switch ($this->newStatus) {
            case 'approved':
                return 'Your purchase order has been approved and is ready to be processed.';
            case 'rejected':
                return 'Your purchase order has been rejected. Please review the details and resubmit if necessary.';
            case 'received':
                return 'Your purchase order has been fully received and inventory has been updated.';
            case 'cancelled':
                return 'Your purchase order has been cancelled.';
            case 'partially_received':
                return 'Your purchase order has been partially received. Some items are still pending delivery.';
            default:
                return "Your purchase order status has been updated to: " . ucwords(str_replace('_', ' ', $this->newStatus));
        }
    }

    /**
     * Get the priority level for this notification.
     */
    protected function getPriority(): string
    {
        switch ($this->newStatus) {
            case 'approved':
            case 'received':
                return 'high';
            case 'rejected':
            case 'cancelled':
                return 'medium';
            case 'partially_received':
                return 'medium';
            default:
                return 'low';
        }
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType($notifiable): string
    {
        return 'purchase_order_update';
    }
}
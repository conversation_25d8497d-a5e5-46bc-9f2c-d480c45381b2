<?php

namespace Modules\Delivery\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Delivery\Services\DeliveryService;

class DeliveryServiceProvider extends ServiceProvider
{
    /**
     * The module namespace to assume when generating URLs to actions.
     */
    protected string $moduleNamespace = 'Modules\Delivery\Http\Controllers';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path('Delivery', 'database/migrations'));
        $this->registerRoutes();
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {

        
        // Register the delivery service
        $this->app->singleton(DeliveryService::class, function ($app) {
            return new DeliveryService();
        });
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $this->publishes([
            module_path('Delivery', 'config/config.php') => config_path('delivery.php'),
        ], 'config');
        
        $this->mergeConfigFrom(
            module_path('Delivery', 'config/config.php'), 'delivery'
        );
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/' . $this->getModuleName());
        $sourcePath = module_path('Delivery', 'resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ], ['views', $this->getModuleName() . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), 'delivery');
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/' . $this->getModuleName());

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'delivery');
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path('Delivery', 'resources/lang'), 'delivery');
            $this->loadJsonTranslationsFrom(module_path('Delivery', 'resources/lang'));
        }
    }

    /**
     * Register routes.
     */
    protected function registerRoutes(): void
    {
        $this->mapApiRoutes();
        $this->mapWebRoutes();
    }

    /**
     * Define the "api" routes for the module.
     */
    protected function mapApiRoutes(): void
    {
        Route::prefix('api')
            ->middleware('api')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Delivery', 'routes/api.php'));
    }

    /**
     * Define the "web" routes for the module.
     */
    protected function mapWebRoutes(): void
    {
        Route::middleware('web')
            ->namespace($this->moduleNamespace)
            ->group(module_path('Delivery', 'routes/web.php'));
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            DeliveryService::class,
        ];
    }

    /**
     * Get the module name.
     */
    protected function getModuleName(): string
    {
        return 'Delivery';
    }

    /**
     * Get the publishable view paths.
     */
    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->getModuleName())) {
                $paths[] = $path . '/modules/' . $this->getModuleName();
            }
        }
        return $paths;
    }
}

/**
 * Helper function to get module path.
 */
if (!function_exists('module_path')) {
    function module_path(string $module, string $path = ''): string
    {
        $modulePath = base_path('modules/' . $module);
        return $path ? $modulePath . '/' . ltrim($path, '/') : $modulePath;
    }
}

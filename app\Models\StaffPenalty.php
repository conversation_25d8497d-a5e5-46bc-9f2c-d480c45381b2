<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class StaffPenalty extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'user_id',
        'type',
        'amount',
        'reason',
        'applied_date',
        'status',
        'waived_at',
        'waived_reason',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'applied_date' => 'date',
        'waived_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the penalty
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user that the penalty belongs to
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get active penalties
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get waived penalties
     */
    public function scopeWaived($query)
    {
        return $query->where('status', 'waived');
    }

    /**
     * Scope to get paid penalties
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to filter by penalty type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('applied_date', [$startDate, $endDate]);
    }

    /**
     * Waive the penalty
     */
    public function waive(string $reason = null): void
    {
        $this->update([
            'status' => 'waived',
            'waived_at' => now(),
            'waived_reason' => $reason,
        ]);
    }

    /**
     * Mark penalty as paid
     */
    public function markAsPaid(): void
    {
        $this->update(['status' => 'paid']);
    }

    /**
     * Check if penalty is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if penalty is waived
     */
    public function isWaived(): bool
    {
        return $this->status === 'waived';
    }

    /**
     * Check if penalty is paid
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Get formatted penalty amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 2);
    }

    /**
     * Get penalty type label
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            'late' => 'Late Arrival',
            'absence' => 'Absence',
            'misconduct' => 'Misconduct',
            'other' => 'Other',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'waived' => 'Waived',
            'paid' => 'Paid',
            default => ucfirst($this->status),
        };
    }
}
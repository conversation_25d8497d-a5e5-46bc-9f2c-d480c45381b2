<?php

namespace Modules\Tenant\Http\Controllers;

use App\Models\Tenant;
use Illuminate\Http\Request;
use App\Models\SubscriptionPlan;
use Illuminate\Http\JsonResponse;
use App\Models\TenantSubscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Modules\Tenant\Services\TenantService;
use Modules\Tenant\Http\Requests\CreateTenantRequest;
use Modules\Tenant\Http\Requests\UpdateTenantRequest;

class TenantController extends Controller
{
    protected $tenantService;

    public function __construct(TenantService $tenantService)
    {
        $this->tenantService = $tenantService;
    }

    /**
     * Display a listing of tenants
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $status = $request->get('status');
            
            $query = Tenant::with(['country', 'subscriptions.plan']);
            
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('contact_email', 'like', "%{$search}%");
                });
            }
            
            if ($status) {
                $query->where('status', $status);
            }
            
            $tenants = $query->orderBy('created_at', 'desc')->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => $tenants,
                'message' => 'Tenants retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenants: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created tenant
     */
    // public function store(CreateTenantRequest $request): JsonResponse
    public function store(CreateTenantRequest $request): JsonResponse
    {
        Log::info('Create Tenant Request', $request->all());
        try {
            DB::beginTransaction();
            
            $tenant = $this->tenantService->createTenant($request->validated());
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $tenant->load(['country', 'subscriptions.plan']),
                'message' => 'Tenant created successfully'
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified tenant
     */
    public function show(Tenant $tenant): JsonResponse
    {
        try {
            $tenant->load([
                'country',
                'subscriptions.plan',
                'branches',
                'settings'
            ]);
            
            return response()->json([
                'success' => true,
                'data' => $tenant,
                'message' => 'Tenant retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified tenant
     */
    public function update(UpdateTenantRequest $request, Tenant $tenant): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $updatedTenant = $this->tenantService->updateTenant($tenant, $request->validated());
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $updatedTenant->load(['country', 'subscriptions.plan']),
                'message' => 'Tenant updated successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified tenant
     */
    public function destroy(Tenant $tenant): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $this->tenantService->deleteTenant($tenant);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Tenant deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tenant statistics
     */
    public function statistics(Tenant $tenant): JsonResponse
    {
        try {
            $stats = $this->tenantService->getTenantStatistics($tenant);
            
            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Tenant statistics retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Activate tenant
     */
    public function activate(Tenant $tenant): JsonResponse
    {
        try {
            $tenant = $this->tenantService->activateTenant($tenant);
            
            return response()->json([
                'success' => true,
                'data' => $tenant,
                'message' => 'Tenant activated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to activate tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deactivate tenant
     */
    public function deactivate(Tenant $tenant): JsonResponse
    {
        try {
            $tenant = $this->tenantService->deactivateTenant($tenant);
            
            return response()->json([
                'success' => true,
                'data' => $tenant,
                'message' => 'Tenant deactivated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to deactivate tenant: ' . $e->getMessage()
            ], 500);
        }
    }
}
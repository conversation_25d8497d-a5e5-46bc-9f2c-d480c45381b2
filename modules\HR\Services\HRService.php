<?php

namespace Modules\HR\Services;

use App\Models\User;
use App\Models\Shift;
use App\Models\ShiftType;
use App\Models\ShiftAssignment;
use App\Models\StaffAttendance;
use App\Models\LeaveRequest;

use App\Models\Payslip;
use App\Models\PayPeriod;
use App\Models\StaffPenalty;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class HRService
{
    /**
     * Get all staff members for a tenant with pagination
     */
    public function getStaff(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = User::where('tenant_id', $tenantId)
                    ->with(['roles', 'shiftAssignments', 'staffAttendances']);

        // Apply filters
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if (!empty($filters['role'])) {
            $query->whereHas('roles', function ($q) use ($filters) {
                $q->where('name', $filters['role']);
            });
        }

        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        return $query->orderBy('name')->paginate($perPage);
    }

    /**
     * Get staff member by ID
     */
    public function getStaffById(int $tenantId, int $userId): ?User
    {
        return User::where('tenant_id', $tenantId)
                  ->where('id', $userId)
                  ->with(['roles', 'shiftAssignments.shift', 'staffAttendances', 'leaveRequests'])
                  ->first();
    }

    /**
     * Create shift assignment
     */
    public function assignShift(int $tenantId, array $data): ShiftAssignment
    {
        $data['tenant_id'] = $tenantId;
        $data['assigned_at'] = now();
        $data['status'] = 'pending';
        
        return ShiftAssignment::create($data);
    }

    /**
     * Accept shift assignment
     */
    public function acceptShiftAssignment(ShiftAssignment $assignment): void
    {
        $assignment->accept();
        
        // Update shift assigned staff count
        $shift = $assignment->shift;
        $shift->increment('assigned_staff_count');
    }

    /**
     * Decline shift assignment
     */
    public function declineShiftAssignment(ShiftAssignment $assignment, string $reason = null): void
    {
        $assignment->decline($reason);
    }

    /**
     * Check in staff member
     */
    public function checkIn(int $tenantId, int $userId, array $data = []): StaffAttendance
    {
        $today = Carbon::today();
        
        // Find or create attendance record for today
        $attendance = StaffAttendance::firstOrCreate(
            [
                'tenant_id' => $tenantId,
                'user_id' => $userId,
                'date' => $today,
            ],
            [
                'branch_id' => $data['branch_id'] ?? null,
                'status' => 'absent',
            ]
        );

        // Perform check-in
        $attendance->checkIn(
            $data['location'] ?? null,
            $data['ip_address'] ?? request()->ip()
        );

        return $attendance;
    }

    /**
     * Check out staff member
     */
    public function checkOut(int $tenantId, int $userId, array $data = []): ?StaffAttendance
    {
        $today = Carbon::today();
        
        $attendance = StaffAttendance::where('tenant_id', $tenantId)
                                   ->where('user_id', $userId)
                                   ->where('date', $today)
                                   ->first();

        if (!$attendance) {
            return null;
        }

        $attendance->checkOut(
            $data['location'] ?? null,
            $data['ip_address'] ?? request()->ip()
        );

        return $attendance;
    }

    /**
     * Get attendance records with filters
     */
    public function getAttendance(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = StaffAttendance::where('tenant_id', $tenantId)
                               ->with(['user', 'branch']);

        // Apply filters
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date', '<=', $filters['date_to']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->orderBy('date', 'desc')
                    ->orderBy('check_in_time', 'desc')
                    ->paginate($perPage);
    }

    /**
     * Create or update shift
     */
    public function createShift(int $tenantId, array $data): Shift
    {
        $data['tenant_id'] = $tenantId;
        $data['assigned_staff_count'] = 0;
        $data['status'] = 'active';
        
        return Shift::create($data);
    }

    /**
     * Update shift
     */
    public function updateShift(Shift $shift, array $data): Shift
    {
        $shift->update($data);
        return $shift->fresh();
    }

    /**
     * Get shifts with filters
     */
    public function getShifts(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Shift::where('tenant_id', $tenantId)
                     ->with(['shiftType', 'branch', 'assignments.user']);

        // Apply filters
        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date', '<=', $filters['date_to']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['shift_type_id'])) {
            $query->where('shift_type_id', $filters['shift_type_id']);
        }

        return $query->orderBy('date', 'desc')
                    ->orderBy('start_time')
                    ->paginate($perPage);
    }

    /**
     * Get shift types
     */
    public function getShiftTypes(int $tenantId, ?int $branchId = null): Collection
    {
        $query = ShiftType::where('tenant_id', $tenantId)->active();
        
        if ($branchId) {
            $query->where(function ($q) use ($branchId) {
                $q->where('branch_id', $branchId)
                  ->orWhereNull('branch_id');
            });
        }
        
        return $query->orderBy('name')->get();
    }

    /**
     * Create shift type
     */
    public function createShiftType(int $tenantId, array $data): ShiftType
    {
        $data['tenant_id'] = $tenantId;
        return ShiftType::create($data);
    }

    /**
     * Update shift type
     */
    public function updateShiftType(ShiftType $shiftType, array $data): ShiftType
    {
        $shiftType->update($data);
        return $shiftType->fresh();
    }

    /**
     * Get staff schedule for a date range
     */
    public function getStaffSchedule(int $tenantId, string $startDate, string $endDate, ?int $branchId = null): Collection
    {
        $query = ShiftAssignment::where('tenant_id', $tenantId)
                               ->with(['user', 'shift', 'shiftType'])
                               ->whereHas('shift', function ($q) use ($startDate, $endDate) {
                                   $q->whereBetween('date', [$startDate, $endDate]);
                               })
                               ->accepted();

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->get();
    }

    /**
     * Get attendance statistics
     */
    public function getAttendanceStats(int $tenantId, string $startDate, string $endDate, ?int $branchId = null): array
    {
        $query = StaffAttendance::where('tenant_id', $tenantId)
                               ->whereBetween('date', [$startDate, $endDate]);

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        $totalRecords = $query->count();
        $presentCount = $query->where('status', 'present')->count();
        $absentCount = $query->where('status', 'absent')->count();
        $leaveCount = $query->where('status', 'leave')->count();
        $lateCount = $query->where('late_minutes', '>', 0)->count();
        $overtimeCount = $query->where('overtime_minutes', '>', 0)->count();

        return [
            'total_records' => $totalRecords,
            'present_count' => $presentCount,
            'absent_count' => $absentCount,
            'leave_count' => $leaveCount,
            'late_count' => $lateCount,
            'overtime_count' => $overtimeCount,
            'attendance_rate' => $totalRecords > 0 ? round(($presentCount / $totalRecords) * 100, 2) : 0,
        ];
    }

    /**
     * Get staff working hours for payroll
     */
    public function getStaffWorkingHours(int $tenantId, int $userId, string $startDate, string $endDate): array
    {
        $attendances = StaffAttendance::where('tenant_id', $tenantId)
                                    ->where('user_id', $userId)
                                    ->whereBetween('date', [$startDate, $endDate])
                                    ->where('status', 'present')
                                    ->get();

        $totalHours = 0;
        $regularHours = 0;
        $overtimeHours = 0;
        $totalLateMinutes = 0;
        $workingDays = 0;

        foreach ($attendances as $attendance) {
            if ($attendance->check_in_time && $attendance->check_out_time) {
                $hoursWorked = $attendance->getTotalWorkedHours();
                $totalHours += $hoursWorked;
                
                // Assuming 8 hours is regular time
                $regularHours += min($hoursWorked, 8);
                $overtimeHours += max(0, $hoursWorked - 8);
                
                $totalLateMinutes += $attendance->late_minutes;
                $workingDays++;
            }
        }

        return [
            'total_hours' => round($totalHours, 2),
            'regular_hours' => round($regularHours, 2),
            'overtime_hours' => round($overtimeHours, 2),
            'total_late_minutes' => $totalLateMinutes,
            'working_days' => $workingDays,
        ];
    }

    /**
     * Request shift replacement
     */
    public function requestShiftReplacement(ShiftAssignment $assignment, string $reason): void
    {
        $assignment->requestReplacement($reason);
    }

    /**
     * Get understaffed shifts
     */
    public function getUnderstaffedShifts(int $tenantId, ?int $branchId = null): Collection
    {
        $query = Shift::where('tenant_id', $tenantId)
                     ->underStaffed()
                     ->active()
                     ->with(['shiftType', 'branch', 'assignments.user']);

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->orderBy('date')
                    ->orderBy('start_time')
                    ->get();
    }

    /**
     * Get available staff for shift
     */
    public function getAvailableStaffForShift(int $tenantId, int $shiftId): Collection
    {
        $shift = Shift::find($shiftId);
        
        if (!$shift) {
            return collect();
        }

        // Get staff not already assigned to this shift
        $assignedUserIds = ShiftAssignment::where('shift_id', $shiftId)
                                         ->pluck('user_id')
                                         ->toArray();

        $query = User::where('tenant_id', $tenantId)
                    ->whereNotIn('id', $assignedUserIds);

        if ($shift->branch_id) {
            $query->where('branch_id', $shift->branch_id);
        }

        return $query->with('roles')->get();
    }

    /**
     * Calculate staff salary for a pay period
     */
    public function calculateSalary(int $userId, PayPeriod $payPeriod): array
    {
        $user = User::findOrFail($userId);
        $workingHours = $this->getStaffWorkingHours(
            $user->tenant_id,
            $userId,
            $payPeriod->start_date->format('Y-m-d'),
            $payPeriod->end_date->format('Y-m-d')
        );

        $baseSalary = $user->base_salary ?? 0;
        $hourlyRate = $user->hourly_rate ?? 0;
        
        // Calculate base pay
        $regularPay = $workingHours['regular_hours'] * $hourlyRate;
        $overtimePay = $workingHours['overtime_hours'] * ($hourlyRate * 1.5); // 1.5x for overtime
        
        // Calculate penalties
        $latePenalty = $this->calculateLatePenalty($userId, $payPeriod);
        $absencePenalty = $this->calculateAbsencePenalty($userId, $payPeriod);
        
        // Calculate bonuses
        $performanceBonus = $this->calculatePerformanceBonus($userId, $payPeriod);
        
        $grossSalary = $baseSalary + $regularPay + $overtimePay + $performanceBonus;
        $totalPenalties = $latePenalty + $absencePenalty;
        $netSalary = $grossSalary - $totalPenalties;

        return [
            'base_salary' => $baseSalary,
            'regular_pay' => $regularPay,
            'overtime_pay' => $overtimePay,
            'performance_bonus' => $performanceBonus,
            'gross_salary' => $grossSalary,
            'late_penalty' => $latePenalty,
            'absence_penalty' => $absencePenalty,
            'total_penalties' => $totalPenalties,
            'net_salary' => $netSalary,
            'working_hours' => $workingHours,
        ];
    }

    /**
     * Calculate late penalty
     */
    private function calculateLatePenalty(int $userId, PayPeriod $payPeriod): float
    {
        $lateMinutes = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->sum('late_minutes');

        // Penalty: $1 per minute late (configurable)
        $penaltyPerMinute = 1.0;
        return $lateMinutes * $penaltyPerMinute;
    }

    /**
     * Calculate absence penalty
     */
    private function calculateAbsencePenalty(int $userId, PayPeriod $payPeriod): float
    {
        $user = User::findOrFail($userId);
        $absentDays = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->where('status', 'absent')
            ->count();

        // Penalty: daily rate for each absent day
        $dailyRate = ($user->base_salary ?? 0) / 30; // Assuming 30 working days per month
        return $absentDays * $dailyRate;
    }

    /**
     * Calculate performance bonus
     */
    private function calculatePerformanceBonus(int $userId, PayPeriod $payPeriod): float
    {
        $attendanceRate = $this->getStaffAttendanceRate($userId, $payPeriod);
        $user = User::findOrFail($userId);
        
        // Bonus based on attendance rate
        if ($attendanceRate >= 95) {
            return ($user->base_salary ?? 0) * 0.1; // 10% bonus for 95%+ attendance
        } elseif ($attendanceRate >= 90) {
            return ($user->base_salary ?? 0) * 0.05; // 5% bonus for 90%+ attendance
        }
        
        return 0;
    }

    /**
     * Get staff attendance rate for a pay period
     */
    private function getStaffAttendanceRate(int $userId, PayPeriod $payPeriod): float
    {
        $totalDays = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->count();

        $presentDays = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->where('status', 'present')
            ->count();

        return $totalDays > 0 ? ($presentDays / $totalDays) * 100 : 0;
    }

    /**
     * Generate payslip
     */
    public function generatePayslip(int $userId, PayPeriod $payPeriod): Payslip
    {
        $salaryData = $this->calculateSalary($userId, $payPeriod);
        $user = User::findOrFail($userId);

        return Payslip::create([
            'tenant_id' => $user->tenant_id,
            'user_id' => $userId,
            'pay_period_id' => $payPeriod->id,
            'base_salary' => $salaryData['base_salary'],
            'regular_pay' => $salaryData['regular_pay'],
            'overtime_pay' => $salaryData['overtime_pay'],
            'bonuses' => $salaryData['performance_bonus'],
            'gross_salary' => $salaryData['gross_salary'],
            'deductions' => $salaryData['total_penalties'],
            'net_salary' => $salaryData['net_salary'],
            'working_hours' => $salaryData['working_hours']['total_hours'],
            'overtime_hours' => $salaryData['working_hours']['overtime_hours'],
            'generated_at' => now(),
            'status' => 'pending',
        ]);
    }

    /**
     * Apply penalty to staff
     */
    public function applyPenalty(int $userId, string $type, float $amount, string $reason, ?string $date = null): array
    {
        $user = User::findOrFail($userId);
        $penaltyDate = $date ? Carbon::parse($date) : now();

        $penalty = StaffPenalty::create([
            'tenant_id' => $user->tenant_id,
            'user_id' => $userId,
            'type' => $type, // 'late', 'absence', 'misconduct', 'other'
            'amount' => $amount,
            'reason' => $reason,
            'applied_date' => $penaltyDate,
            'status' => 'active',
        ]);

        return [
            'success' => true,
            'message' => 'Penalty applied successfully',
            'penalty_id' => $penalty->id,
            'penalty_amount' => $amount,
            'penalty_type' => $type,
        ];
    }

    /**
     * Get staff penalties
     */
    public function getStaffPenalties(int $userId, ?string $startDate = null, ?string $endDate = null): Collection
    {
        $query = StaffPenalty::where('user_id', $userId)
            ->where('status', 'active')
            ->with('user');

        if ($startDate) {
            $query->where('applied_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('applied_date', '<=', $endDate);
        }

        return $query->orderBy('applied_date', 'desc')->get();
    }

    /**
     * Process bulk attendance
     */
    public function processBulkAttendance(int $tenantId, array $attendanceData): array
    {
        $processed = [];
        $errors = [];

        DB::beginTransaction();
        try {
            foreach ($attendanceData as $data) {
                try {
                    $attendance = $this->markAttendance(
                        $data['user_id'],
                        $data['date'],
                        $data['status'],
                        $data['check_in_time'] ?? null,
                        $data['check_out_time'] ?? null
                    );
                    $processed[] = $attendance;
                } catch (Exception $e) {
                    $errors[] = [
                        'user_id' => $data['user_id'],
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        return [
            'processed' => count($processed),
            'errors' => $errors,
            'success_rate' => count($attendanceData) > 0 ? (count($processed) / count($attendanceData)) * 100 : 0
        ];
    }

    /**
     * Get staff performance metrics
     */
    public function getStaffPerformanceMetrics(int $userId, string $startDate, string $endDate): array
    {
        $user = User::findOrFail($userId);
        
        // Attendance metrics
        $attendanceStats = $this->getAttendanceStats(
            $user->tenant_id,
            $startDate,
            $endDate
        );

        // Working hours
        $workingHours = $this->getStaffWorkingHours(
            $user->tenant_id,
            $userId,
            $startDate,
            $endDate
        );

        // Penalties
        $penalties = $this->getStaffPenalties($userId, $startDate, $endDate);
        $totalPenalties = $penalties->sum('amount');

        // Leave requests
        $leaveRequests = LeaveRequest::where('user_id', $userId)
            ->whereBetween('start_date', [$startDate, $endDate])
            ->count();

        return [
            'attendance_rate' => $this->getStaffAttendanceRate(
                $userId,
                (object)['start_date' => $startDate, 'end_date' => $endDate]
            ),
            'total_working_hours' => $workingHours['total_hours'],
            'overtime_hours' => $workingHours['overtime_hours'],
            'late_minutes' => $workingHours['total_late_minutes'],
            'total_penalties' => $totalPenalties,
            'leave_requests' => $leaveRequests,
            'performance_score' => $this->calculatePerformanceScore($userId, $startDate, $endDate),
        ];
    }

    /**
     * Calculate performance score
     */
    private function calculatePerformanceScore(int $userId, string $startDate, string $endDate): float
    {
        $attendanceRate = $this->getStaffAttendanceRate(
            $userId,
            (object)['start_date' => $startDate, 'end_date' => $endDate]
        );

        $workingHours = $this->getStaffWorkingHours(
            User::findOrFail($userId)->tenant_id,
            $userId,
            $startDate,
            $endDate
        );

        $penalties = $this->getStaffPenalties($userId, $startDate, $endDate);
        $penaltyCount = $penalties->count();

        // Performance score calculation (0-100)
        $score = $attendanceRate * 0.4; // 40% weight for attendance
        $score += min(100, ($workingHours['total_hours'] / 160) * 100) * 0.3; // 30% weight for hours worked
        $score += max(0, 100 - ($penaltyCount * 10)) * 0.3; // 30% weight for penalties (deduct 10 points per penalty)

        return round(min(100, max(0, $score)), 2);
    }

    /**
     * Mark attendance for a staff member
     */
    public function markAttendance(int $userId, string $date, string $status, ?string $checkInTime = null, ?string $checkOutTime = null): StaffAttendance
    {
        $user = User::findOrFail($userId);
        $attendanceDate = Carbon::parse($date);

        $attendance = StaffAttendance::updateOrCreate(
            [
                'tenant_id' => $user->tenant_id,
                'user_id' => $userId,
                'date' => $attendanceDate,
            ],
            [
                'status' => $status,
                'check_in_time' => $checkInTime ? Carbon::parse($checkInTime) : null,
                'check_out_time' => $checkOutTime ? Carbon::parse($checkOutTime) : null,
                'branch_id' => $user->branch_id,
            ]
        );

        return $attendance;
    }

    /**
     * Waive a penalty
     */
    public function waivePenalty(int $penaltyId, string $reason = null): bool
    {
        $penalty = StaffPenalty::findOrFail($penaltyId);
        $penalty->waive($reason);
        return true;
    }

    /**
     * Get penalty statistics for a tenant
     */
    public function getPenaltyStats(int $tenantId, string $startDate, string $endDate): array
    {
        $penalties = StaffPenalty::where('tenant_id', $tenantId)
            ->betweenDates($startDate, $endDate)
            ->get();

        $totalAmount = $penalties->sum('amount');
        $totalCount = $penalties->count();
        $activeCount = $penalties->where('status', 'active')->count();
        $waivedCount = $penalties->where('status', 'waived')->count();
        $paidCount = $penalties->where('status', 'paid')->count();

        $byType = $penalties->groupBy('type')->map(function ($group) {
            return [
                'count' => $group->count(),
                'total_amount' => $group->sum('amount'),
            ];
        });

        return [
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
            'active_count' => $activeCount,
            'waived_count' => $waivedCount,
            'paid_count' => $paidCount,
            'by_type' => $byType,
        ];
    }
}
<?php

namespace Modules\Kitchen\Services;

use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KitchenMenuItem;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\KotOrderItem;
use App\Models\Order;
use App\Models\MenuItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Exception;

class KitchenService
{
    /**
     * Get all kitchens for a tenant/branch
     */
    public function getKitchens($tenantId, $branchId = null, $activeOnly = true)
    {
        $query = Kitchen::forTenant($tenantId);
        
        if ($branchId) {
            $query->forBranch($branchId);
        }
        
        if ($activeOnly) {
            $query->active();
        }
        
        return $query->orderBy('display_order')->orderBy('name')->get();
    }

    /**
     * Create a new kitchen
     */
    public function createKitchen(array $data)
    {
        DB::beginTransaction();
        try {
            // Generate unique code if not provided
            if (!isset($data['code'])) {
                $data['code'] = $this->generateKitchenCode($data['tenant_id'], $data['name']);
            }
            
            $kitchen = Kitchen::create($data);
            
            // Clear cache
            $this->clearKitchenCache($data['tenant_id'], $data['branch_id'] ?? null);
            
            DB::commit();
            return $kitchen;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update kitchen
     */
    public function updateKitchen(Kitchen $kitchen, array $data)
    {
        DB::beginTransaction();
        try {
            $kitchen->update($data);
            
            // Clear cache
            $this->clearKitchenCache($kitchen->tenant_id, $kitchen->branch_id);
            
            DB::commit();
            return $kitchen->fresh();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Assign menu item to kitchen
     */
    public function assignMenuItemToKitchen($tenantId, $kitchenId, $menuItemId, array $options = [])
    {
        DB::beginTransaction();
        try {
            // Check if menu item is already assigned to another kitchen in this tenant
            $existingAssignment = KitchenMenuItem::forTenant($tenantId)
                ->where('menu_item_id', $menuItemId)
                ->where('is_active', true)
                ->first();
            
            if ($existingAssignment && $existingAssignment->kitchen_id !== $kitchenId) {
                throw new Exception('Menu item is already assigned to another kitchen in this tenant.');
            }
            
            // Create or update assignment
            $assignment = KitchenMenuItem::updateOrCreate(
                [
                    'tenant_id' => $tenantId,
                    'kitchen_id' => $kitchenId,
                    'menu_item_id' => $menuItemId,
                ],
                array_merge([
                    'is_active' => true,
                    'assigned_at' => now(),
                ], $options)
            );
            
            // Clear cache
            $this->clearMenuItemAssignmentCache($tenantId);
            
            DB::commit();
            return $assignment;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Remove menu item from kitchen
     */
    public function removeMenuItemFromKitchen($tenantId, $menuItemId)
    {
        DB::beginTransaction();
        try {
            $assignment = KitchenMenuItem::forTenant($tenantId)
                ->where('menu_item_id', $menuItemId)
                ->first();
            
            if ($assignment) {
                $assignment->update(['is_active' => false]);
                
                // Clear cache
                $this->clearMenuItemAssignmentCache($tenantId);
            }
            
            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get menu items assigned to a kitchen
     */
    public function getKitchenMenuItems($kitchenId, $activeOnly = true)
    {
        $query = KitchenMenuItem::forKitchen($kitchenId)
            ->with(['menuItem', 'assignedBy']);
        
        if ($activeOnly) {
            $query->active();
        }
        
        return $query->orderBy('priority_level', 'desc')
                    ->orderBy('created_at')
                    ->get();
    }

    /**
     * Get kitchen for a specific menu item
     */
    public function getKitchenForMenuItem($tenantId, $menuItemId)
    {
        $cacheKey = "kitchen_for_menu_item_{$tenantId}_{$menuItemId}";
        
        return Cache::remember($cacheKey, 3600, function () use ($tenantId, $menuItemId) {
            $assignment = KitchenMenuItem::forTenant($tenantId)
                ->where('menu_item_id', $menuItemId)
                ->where('is_active', true)
                ->with('kitchen')
                ->first();
            
            return $assignment ? $assignment->kitchen : null;
        });
    }

    /**
     * Create KOT from order
     */
    public function createKotFromOrder(Order $order, array $options = [])
    {
        DB::beginTransaction();
        try {
            $kotOrders = [];
            
            // Group order items by kitchen
            $itemsByKitchen = $this->groupOrderItemsByKitchen($order);
            
            foreach ($itemsByKitchen as $kitchenId => $items) {
                if (empty($items)) continue;
                
                $kitchen = Kitchen::find($kitchenId);
                if (!$kitchen || !$kitchen->is_active) continue;
                
                // Create KOT order
                $kotOrder = KotOrder::create([
                    'tenant_id' => $order->tenant_id,
                    'branch_id' => $order->branch_id,
                    'kitchen_id' => $kitchenId,
                    'order_id' => $order->id,
                    'kot_number' => $this->generateKotNumber($order->tenant_id),
                    'status' => 'pending',
                    'priority' => $options['priority'] ?? 'normal',
                    'estimated_prep_time_minutes' => $this->calculateEstimatedPrepTime($items),
                    'items_data' => $this->prepareItemsSnapshot($items),
                    'special_instructions' => $options['special_instructions'] ?? null,
                    'sent_to_kitchen_at' => now(),
                    'created_by' => $options['created_by'] ?? null,
                ]);
                
                // Create KOT order items
                foreach ($items as $item) {
                    KotOrderItem::create([
                        'kot_order_id' => $kotOrder->id,
                        'order_item_id' => $item->id,
                        'menu_item_id' => $item->menu_item_id,
                        'quantity' => $item->quantity,
                        'status' => 'pending',
                        'special_instructions' => $item->special_instructions,
                        'modifications' => $item->modifications ?? [],
                        'prep_time_minutes' => $this->getMenuItemPrepTime($kitchenId, $item->menu_item_id),
                    ]);
                }
                
                $kotOrders[] = $kotOrder;
            }
            
            DB::commit();
            return $kotOrders;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update KOT status
     */
    public function updateKotStatus(KotOrder $kotOrder, string $status, $userId = null)
    {
        DB::beginTransaction();
        try {
            switch ($status) {
                case 'preparing':
                    $kotOrder->markAsStarted($userId);
                    break;
                case 'ready':
                    $kotOrder->markAsReady($userId);
                    break;
                case 'completed':
                    $kotOrder->markAsCompleted($userId);
                    break;
                default:
                    $kotOrder->update(['status' => $status]);
            }
            
            DB::commit();
            return $kotOrder->fresh();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get active KOTs for kitchen
     */
    public function getActiveKotsForKitchen($kitchenId)
    {
        return KotOrder::forKitchen($kitchenId)
            ->active()
            ->with(['order', 'kotItems.menuItem', 'assignedTo'])
            ->orderBy('priority', 'desc')
            ->orderBy('created_at')
            ->get();
    }

    /**
     * Get kitchen dashboard data
     */
    public function getKitchenDashboard($kitchenId)
    {
        $kitchen = Kitchen::with(['activeKotOrders'])->find($kitchenId);
        
        if (!$kitchen) {
            throw new Exception('Kitchen not found');
        }
        
        $activeKots = $this->getActiveKotsForKitchen($kitchenId);
        
        return [
            'kitchen' => $kitchen,
            'active_kots' => $activeKots,
            'current_load' => $kitchen->getCurrentLoad(),
            'load_percentage' => $kitchen->getLoadPercentage(),
            'can_accept_orders' => $kitchen->canAcceptNewOrder(),
            'is_operating' => $kitchen->isOperatingNow(),
            'pending_count' => $activeKots->where('status', 'pending')->count(),
            'preparing_count' => $activeKots->where('status', 'preparing')->count(),
            'overdue_count' => $activeKots->filter->isOverdue()->count(),
        ];
    }

    // Private helper methods
    private function generateKitchenCode($tenantId, $name)
    {
        $baseCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 3));
        $counter = 1;
        
        do {
            $code = $baseCode . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $exists = Kitchen::forTenant($tenantId)->where('code', $code)->exists();
            $counter++;
        } while ($exists);
        
        return $code;
    }

    private function generateKotNumber($tenantId)
    {
        $today = now()->format('Ymd');
        $prefix = "KOT{$today}";
        
        $lastKot = KotOrder::forTenant($tenantId)
            ->where('kot_number', 'like', $prefix . '%')
            ->orderBy('kot_number', 'desc')
            ->first();
        
        if ($lastKot) {
            $lastNumber = (int) substr($lastKot->kot_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    private function groupOrderItemsByKitchen(Order $order)
    {
        $itemsByKitchen = [];
        
        foreach ($order->orderItems as $item) {
            $kitchen = $this->getKitchenForMenuItem($order->tenant_id, $item->menu_item_id);
            
            if ($kitchen) {
                if (!isset($itemsByKitchen[$kitchen->id])) {
                    $itemsByKitchen[$kitchen->id] = [];
                }
                $itemsByKitchen[$kitchen->id][] = $item;
            }
        }
        
        return $itemsByKitchen;
    }

    private function calculateEstimatedPrepTime($items)
    {
        $totalTime = 0;
        
        foreach ($items as $item) {
            $prepTime = $item->menuItem->prep_time_minutes ?? 15;
            $totalTime += $prepTime * $item->quantity;
        }
        
        return max($totalTime, 5); // Minimum 5 minutes
    }

    private function prepareItemsSnapshot($items)
    {
        return $items->map(function ($item) {
            return [
                'id' => $item->id,
                'menu_item_id' => $item->menu_item_id,
                'menu_item_name' => $item->menuItem->name ?? 'Unknown',
                'quantity' => $item->quantity,
                'special_instructions' => $item->special_instructions,
                'modifications' => $item->modifications ?? [],
            ];
        })->toArray();
    }

    private function getMenuItemPrepTime($kitchenId, $menuItemId)
    {
        $assignment = KitchenMenuItem::forKitchen($kitchenId)
            ->where('menu_item_id', $menuItemId)
            ->first();
        
        return $assignment->prep_time_minutes ?? 15;
    }

    private function clearKitchenCache($tenantId, $branchId = null)
    {
        Cache::forget("kitchens_{$tenantId}_{$branchId}");
        Cache::forget("kitchens_{$tenantId}");
    }

    private function clearMenuItemAssignmentCache($tenantId)
    {
        // Clear all menu item assignment cache for tenant
        $pattern = "kitchen_for_menu_item_{$tenantId}_*";
        // Note: In production, you might want to use Redis SCAN or similar
        // For now, we'll rely on cache expiration
    }
}
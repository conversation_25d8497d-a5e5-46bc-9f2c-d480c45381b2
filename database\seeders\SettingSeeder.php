<?php

namespace Modules\Settings\Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingSeeder extends Seeder
{
    public function run()
    {
        Setting::updateOrCreate([
            'key' => 'site_name',
            'category' => 'general',
        ], [
            'value' => 'My Restaurant',
            'data_type' => 'string',
            'description' => 'The name of the system',
            'is_public' => true,
        ]);
        Setting::updateOrCreate([
            'key' => 'logo_url',
            'category' => 'general',
        ], [
            'value' => 'https://example.com/logo.png',
            'data_type' => 'string',
            'description' => 'Logo URL',
            'is_public' => true,
        ]);
        Setting::updateOrCreate([
            'key' => 'support_email',
            'category' => 'general',
        ], [
            'value' => '<EMAIL>',
            'data_type' => 'string',
            'description' => 'Support email address',
            'is_public' => true,
        ]);
        Setting::updateOrCreate([
            'key' => 'support_phone',
            'category' => 'general',
        ], [
            'value' => '+1234567890',
            'data_type' => 'string',
            'description' => 'Support phone number',
            'is_public' => true,
        ]);
        Setting::updateOrCreate([
            'key' => 'address',
            'category' => 'general',
        ], [
            'value' => '123 Main St, City, Country',
            'data_type' => 'string',
            'description' => 'Business address',
            'is_public' => true,
        ]);
        Setting::updateOrCreate([
            'key' => 'website_url',
            'category' => 'general',
        ], [
            'value' => 'https://example.com',
            'data_type' => 'string',
            'description' => 'Website URL',
            'is_public' => true,
        ]);
        Setting::updateOrCreate([
            'key' => 'social_links',
            'category' => 'general',
        ], [
            'value' => json_encode([
                'facebook' => 'https://facebook.com/example',
                'twitter' => 'https://twitter.com/example',
                'instagram' => 'https://instagram.com/example',
            ]),
            'data_type' => 'json',
            'description' => 'Social media links',
            'is_public' => true,
        ]);
    }
} 
<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreVariantRequest;
use Modules\Menu\Http\Requests\UpdateVariantRequest;

class VariantWebController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of variants for a menu item
     */
    public function index(string $menuItemId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        $variants = $this->menuService->getMenuItemVariants($menuItemId);
        return view('menu::variations.index', compact('menuItem', 'variants'));
    }

    /**
     * Show the form for creating a new variant
     */
    public function create(string $menuItemId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        return view('menu::variations.create', compact('menuItem'));
    }

    /**
     * Store a newly created variant
     */
    public function store(StoreVariantRequest $request): RedirectResponse
    {
        try {
            $variant = $this->menuService->createVariant($request->menu_item_id, $request->validated());
            return redirect()->route('menu.web.variants.index', $request->menu_item_id)
                ->with('success', 'Variant created successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to create variant: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified variant
     */
    public function show(string $menuItemId, string $variantId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        $variant = $this->menuService->getVariantById($variantId);
        return view('menu::variations.show', compact('menuItem', 'variant'));
    }

    /**
     * Show the form for editing the specified variant
     */
    public function edit(string $menuItemId, string $variantId): View
    {
        $menuItem = $this->menuService->getMenuItemById($menuItemId);
        $variant = $this->menuService->getVariantById($variantId);
        return view('menu::variations.edit', compact('menuItem', 'variant'));
    }

    /**
     * Update the specified variant
     */
    public function update(UpdateVariantRequest $request, string $menuItemId, string $variantId): RedirectResponse
    {
        try {
            $variant = $this->menuService->updateVariant($variantId, $request->validated());
            return redirect()->route('menu.web.variants.index', $menuItemId)
                ->with('success', 'Variant updated successfully.');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to update variant: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified variant
     */
    public function destroy(string $menuItemId, string $variantId): RedirectResponse
    {
        try {
            $this->menuService->deleteVariant($variantId);
            return redirect()->route('menu.web.variants.index', $menuItemId)
                ->with('success', 'Variant deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete variant: ' . $e->getMessage());
        }
    }
}
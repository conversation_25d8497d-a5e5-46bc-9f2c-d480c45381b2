<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'sometimes|integer|exists:branches,id',
            'shift_type_id' => 'sometimes|integer|exists:shift_types,id',
            'date' => 'sometimes|date',
            'start_time' => 'sometimes|date_format:H:i',
            'end_time' => 'sometimes|date_format:H:i|after:start_time',
            'break_duration_minutes' => 'nullable|integer|min:0|max:480',
            'required_staff_count' => 'sometimes|integer|min:1|max:50',
            'notes' => 'nullable|string|max:1000',
            'status' => 'sometimes|string|in:draft,published,cancelled',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'branch_id.exists' => 'Selected branch does not exist.',
            'shift_type_id.exists' => 'Selected shift type does not exist.',
            'date.date' => 'Invalid date format.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'end_time.after' => 'End time must be after start time.',
            'break_duration_minutes.integer' => 'Break duration must be a number.',
            'break_duration_minutes.min' => 'Break duration cannot be negative.',
            'break_duration_minutes.max' => 'Break duration cannot exceed 8 hours.',
            'required_staff_count.min' => 'At least 1 staff member is required.',
            'required_staff_count.max' => 'Cannot require more than 50 staff members.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'status.in' => 'Status must be draft, published, or cancelled.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('break_duration_minutes') && $this->break_duration_minutes === null) {
            $this->merge(['break_duration_minutes' => 0]);
        }
    }
}
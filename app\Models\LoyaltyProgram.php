<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LoyaltyProgram extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'name',
        'description',
        'type',
        'points_per_currency_unit',
        'currency_per_point',
        'minimum_points_redemption',
        'maximum_points_redemption',
        'points_expiry_days',
        'tier_thresholds',
        'tier_benefits',
        'bonus_multipliers',
        'is_active',
        'start_date',
        'end_date',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'points_per_currency_unit' => 'decimal:2',
            'currency_per_point' => 'decimal:4',
            'tier_thresholds' => 'array',
            'tier_benefits' => 'array',
            'bonus_multipliers' => 'array',
            'is_active' => 'boolean',
            'start_date' => 'date',
            'end_date' => 'date',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function loyaltyTransactions()
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('start_date', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    // Methods
    public function calculatePointsEarned($amount)
    {
        return floor($amount * $this->points_per_currency_unit);
    }

    public function calculateRedemptionValue($points)
    {
        return $points * $this->currency_per_point;
    }

    public function getCustomerTier($totalSpent)
    {
        if (!$this->tier_thresholds) {
            return null;
        }

        $tier = 'bronze'; // default
        foreach ($this->tier_thresholds as $tierName => $threshold) {
            if ($totalSpent >= $threshold) {
                $tier = $tierName;
            }
        }

        return $tier;
    }
}
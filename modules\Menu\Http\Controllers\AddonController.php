<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreAddonRequest;
use Modules\Menu\Http\Requests\UpdateAddonRequest;
use Modules\Menu\Http\Resources\AddonResource;

class AddonController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Create menu item addon
     */
    public function store(StoreAddonRequest $request): JsonResponse
    {
        $addon = $this->menuService->createAddon($request->menu_item_id, $request->validated());
        return response()->json(new AddonResource($addon), 201);
    }

    /**
     * Update menu item addon
     */
    public function update(UpdateAddonRequest $request, string $addonId): JsonResponse
    {
        $addon = $this->menuService->updateAddon($addonId, $request->validated());
        return response()->json(new AddonResource($addon));
    }

    /**
     * Delete menu item addon
     */
    public function destroy(string $addonId): JsonResponse
    {
        $this->menuService->deleteAddon($addonId);
        return response()->json(['message' => 'Addon deleted successfully'], 200);
    }

    /**
     * Get addons for a menu item
     */
    public function index(string $menuItemId): JsonResponse
    {
        $addons = $this->menuService->getMenuItemAddons($menuItemId);
        return response()->json(AddonResource::collection($addons));
    }
} 
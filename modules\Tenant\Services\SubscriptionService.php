<?php

namespace Modules\Tenant\Services;

use App\Models\Tenant;
use App\Models\TenantSubscription;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubscriptionService
{
    /**
     * Create a new subscription
     */
    public function createSubscription(array $data): TenantSubscription
    {
        $plan = SubscriptionPlan::findOrFail($data['plan_id']);
        $tenant = Tenant::findOrFail($data['tenant_id']);

        // Cancel any existing active subscriptions
        $this->cancelExistingSubscriptions($tenant);

        // Set default values
        $subscriptionData = array_merge([
            'status' => 'trial',
            'start_date' => Carbon::now(),
            'trial_days' => $data['trial_days'] ?? 14,
            'billing_cycle' => $data['billing_cycle'] ?? 'monthly',
        ], $data);

        // Calculate end date and next billing date
        if ($subscriptionData['status'] === 'trial') {
            $subscriptionData['end_date'] = Carbon::now()->addDays($subscriptionData['trial_days']);
            $subscriptionData['next_billing_date'] = $subscriptionData['end_date'];
        } else {
            $subscriptionData['next_billing_date'] = $this->calculateNextBillingDate(
                $subscriptionData['start_date'],
                $subscriptionData['billing_cycle']
            );
        }

        return TenantSubscription::create($subscriptionData);
    }

    /**
     * Update subscription
     */
    public function updateSubscription(TenantSubscription $subscription, array $data): TenantSubscription
    {
        // If billing cycle is changed, recalculate next billing date
        if (isset($data['billing_cycle']) && $data['billing_cycle'] !== $subscription->billing_cycle) {
            $data['next_billing_date'] = $this->calculateNextBillingDate(
                Carbon::now(),
                $data['billing_cycle']
            );
        }

        $subscription->update($data);
        return $subscription->fresh();
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $subscription->update([
            'status' => 'cancelled',
            'cancelled_at' => Carbon::now(),
            'cancellation_reason' => 'Manual cancellation'
        ]);

        return $subscription->fresh();
    }

    /**
     * Suspend subscription
     */
    public function suspendSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $subscription->update([
            'status' => 'suspended',
            'suspended_at' => Carbon::now()
        ]);

        return $subscription->fresh();
    }

    /**
     * Reactivate subscription
     */
    public function reactivateSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $subscription->update([
            'status' => 'active',
            'suspended_at' => null,
            'next_billing_date' => $this->calculateNextBillingDate(
                Carbon::now(),
                $subscription->billing_cycle
            )
        ]);

        return $subscription->fresh();
    }

    /**
     * Upgrade subscription to a new plan
     */
    public function upgradeSubscription(TenantSubscription $subscription, int $newPlanId): TenantSubscription
    {
        $newPlan = SubscriptionPlan::findOrFail($newPlanId);
        $oldPlan = $subscription->plan;

        // Calculate prorated amount if needed
        $proratedAmount = $this->calculateProratedAmount($subscription, $newPlan);

        $subscription->update([
            'plan_id' => $newPlanId,
            'upgraded_at' => Carbon::now(),
            'prorated_amount' => $proratedAmount,
            'previous_plan_id' => $oldPlan->id
        ]);

        return $subscription->fresh();
    }

    /**
     * Process subscription renewal
     */
    public function renewSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $nextBillingDate = $this->calculateNextBillingDate(
            $subscription->next_billing_date,
            $subscription->billing_cycle
        );

        $subscription->update([
            'status' => 'active',
            'current_period_start' => $subscription->next_billing_date,
            'current_period_end' => $nextBillingDate,
            'next_billing_date' => $nextBillingDate,
            'renewed_at' => Carbon::now()
        ]);

        return $subscription->fresh();
    }

    /**
     * Convert trial to paid subscription
     */
    public function convertTrialToPaid(TenantSubscription $subscription): TenantSubscription
    {
        $nextBillingDate = $this->calculateNextBillingDate(
            Carbon::now(),
            $subscription->billing_cycle
        );

        $subscription->update([
            'status' => 'active',
            'trial_ends_at' => Carbon::now(),
            'current_period_start' => Carbon::now(),
            'current_period_end' => $nextBillingDate,
            'next_billing_date' => $nextBillingDate,
            'converted_at' => Carbon::now()
        ]);

        return $subscription->fresh();
    }

    /**
     * Get subscription usage statistics
     */
    public function getSubscriptionUsage(TenantSubscription $subscription): array
    {
        $tenant = $subscription->tenant;
        $plan = $subscription->plan;

        $usage = [
            'plan_limits' => [
                'max_branches' => $plan->max_branches,
                'max_users' => $plan->max_users,
                'max_menu_items' => $plan->max_menu_items,
                'max_orders_per_month' => $plan->max_orders_per_month,
            ],
            'current_usage' => [
                'branches' => $tenant->branches()->count(),
                'users' => $tenant->users()->count(),
                'menu_items' => $tenant->menuItems()->count(),
                'monthly_orders' => $this->getMonthlyOrderCount($tenant),
            ],
            'usage_percentage' => [],
            'features' => $plan->features ?? [],
        ];

        // Calculate usage percentages
        foreach ($usage['plan_limits'] as $key => $limit) {
            $currentKey = str_replace('max_', '', $key);
            if ($currentKey === 'orders_per_month') {
                $currentKey = 'monthly_orders';
            }
            
            if (isset($usage['current_usage'][$currentKey]) && $limit > 0) {
                $usage['usage_percentage'][$currentKey] = round(
                    ($usage['current_usage'][$currentKey] / $limit) * 100,
                    2
                );
            }
        }

        return $usage;
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(TenantSubscription $subscription): bool
    {
        if ($subscription->status === 'trial') {
            return $subscription->end_date && Carbon::now()->isAfter($subscription->end_date);
        }

        return $subscription->current_period_end && Carbon::now()->isAfter($subscription->current_period_end);
    }

    /**
     * Get expiring subscriptions
     */
    public function getExpiringSubscriptions(int $daysAhead = 7): \Illuminate\Database\Eloquent\Collection
    {
        $expirationDate = Carbon::now()->addDays($daysAhead);

        return TenantSubscription::where('status', 'active')
            ->where('next_billing_date', '<=', $expirationDate)
            ->with(['tenant', 'plan'])
            ->get();
    }

    /**
     * Calculate next billing date
     */
    protected function calculateNextBillingDate(string $currentDate, string $billingCycle): Carbon
    {
        $date = Carbon::parse($currentDate);

        switch ($billingCycle) {
            case 'monthly':
                return $date->addMonth();
            case 'yearly':
                return $date->addYear();
            case 'quarterly':
                return $date->addMonths(3);
            default:
                return $date->addMonth();
        }
    }

    /**
     * Calculate prorated amount for plan upgrade
     */
    protected function calculateProratedAmount(TenantSubscription $subscription, SubscriptionPlan $newPlan): float
    {
        $oldPlan = $subscription->plan;
        $currentPeriodStart = Carbon::parse($subscription->current_period_start);
        $currentPeriodEnd = Carbon::parse($subscription->current_period_end);
        $now = Carbon::now();

        // Calculate remaining days in current period
        $totalDays = $currentPeriodStart->diffInDays($currentPeriodEnd);
        $remainingDays = $now->diffInDays($currentPeriodEnd);

        if ($totalDays <= 0) {
            return 0;
        }

        // Calculate prorated amounts
        $oldPlanPrice = $subscription->billing_cycle === 'yearly' ? $oldPlan->yearly_price : $oldPlan->monthly_price;
        $newPlanPrice = $subscription->billing_cycle === 'yearly' ? $newPlan->yearly_price : $newPlan->monthly_price;

        $oldPlanProrated = ($oldPlanPrice / $totalDays) * $remainingDays;
        $newPlanProrated = ($newPlanPrice / $totalDays) * $remainingDays;

        return max(0, $newPlanProrated - $oldPlanProrated);
    }

    /**
     * Cancel existing active subscriptions for tenant
     */
    protected function cancelExistingSubscriptions(Tenant $tenant): void
    {
        $tenant->subscriptions()
            ->whereIn('status', ['active', 'trial'])
            ->update([
                'status' => 'cancelled',
                'cancelled_at' => Carbon::now(),
                'cancellation_reason' => 'Replaced by new subscription'
            ]);
    }

    /**
     * Get monthly order count for tenant
     */
    protected function getMonthlyOrderCount(Tenant $tenant): int
    {
        $currentMonth = Carbon::now()->startOfMonth();
        
        return $tenant->orders()
            ->where('created_at', '>=', $currentMonth)
            ->count();
    }

    /**
     * Get subscription billing history
     */
    public function getBillingHistory(TenantSubscription $subscription): array
    {
        // This would typically integrate with a billing system
        // For now, return basic subscription events
        return [
            'subscription_created' => $subscription->created_at,
            'trial_started' => $subscription->start_date,
            'trial_ends' => $subscription->end_date,
            'last_billing' => $subscription->current_period_start,
            'next_billing' => $subscription->next_billing_date,
            'upgrades' => $subscription->upgraded_at ? [$subscription->upgraded_at] : [],
            'cancellations' => $subscription->cancelled_at ? [$subscription->cancelled_at] : [],
        ];
    }
}
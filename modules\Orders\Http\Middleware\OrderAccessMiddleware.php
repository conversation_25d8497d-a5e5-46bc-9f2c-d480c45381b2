<?php

namespace Modules\Orders\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class OrderAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user has permission to access orders
        if (!auth()->check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = auth()->user();
        
        // // Check if user has order management permissions
        // if (!$user->can('manage-orders') && !$user->can('view-orders')) {
        //     return response()->json(['error' => 'Forbidden - Insufficient permissions'], 403);
        // }

        return $next($request);
    }
}
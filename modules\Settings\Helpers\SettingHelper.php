<?php

namespace Modules\Settings\Helpers;

use Mo<PERSON>les\Settings\Services\SettingService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SettingHelper
{
    protected static $service;

    /**
     * Get the setting service instance
     */
    protected static function getService()
    {
        if (!static::$service) {
            static::$service = app(SettingService::class);
        }
        return static::$service;
    }

    /**
     * Get a setting value with caching
     */
    public static function get($key, $default = null, $tenantId = null, $branchId = null, $category = null)
    {
        $cacheKey = "setting_{$key}_" . ($tenantId ?? 'null') . '_' . ($branchId ?? 'null') . '_' . ($category ?? 'null');
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default, $tenantId, $branchId, $category) {
            try {
                $setting = static::getService()->get($key, $tenantId, $branchId, $category);
                return $setting ? $setting->value : $default;
            } catch (\Exception $e) {
                Log::warning('Failed to get setting', [
                    'key' => $key,
                    'error' => $e->getMessage()
                ]);
                return $default;
            }
        });
    }

    /**
     * Set a setting value and clear cache
     */
    public static function set($key, $value, $tenantId = null, $branchId = null, $category = 'general', $description = null)
    {
        try {
            $result = static::getService()->set([
                'key' => $key,
                'value' => $value,
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'category' => $category,
                'description' => $description,
                'data_type' => is_array($value) ? 'json' : (is_bool($value) ? 'boolean' : 'string')
            ]);
            
            // Clear cache
            $cacheKey = "setting_{$key}_" . ($tenantId ?? 'null') . '_' . ($branchId ?? 'null') . '_' . ($category ?? 'null');
            Cache::forget($cacheKey);
            
            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to set setting', [
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    // ========== KOT Settings Helpers ==========
    
    public static function getKotPrintEnabled($tenantId = null, $branchId = null)
    {
        return static::get('kot_print_enabled', true, $tenantId, $branchId, 'kot_settings');
    }
    
    public static function getKotPrinterName($tenantId = null, $branchId = null)
    {
        return static::get('kot_printer_name', 'default', $tenantId, $branchId, 'kot_settings');
    }
    
    public static function getKotTemplate($tenantId = null, $branchId = null)
    {
        return static::get('kot_template', 'standard', $tenantId, $branchId, 'kot_settings');
    }

    // ========== Language Settings Helpers ==========
    
    public static function getDefaultLanguage($tenantId = null, $branchId = null)
    {
        return static::get('default_language', 'en', $tenantId, $branchId, 'language_settings');
    }
    
    public static function getAvailableLanguages($tenantId = null, $branchId = null)
    {
        return static::get('available_languages', ['en', 'es', 'fr'], $tenantId, $branchId, 'language_settings');
    }
    
    public static function getDateFormat($tenantId = null, $branchId = null)
    {
        return static::get('date_format', 'Y-m-d', $tenantId, $branchId, 'language_settings');
    }
    
    public static function getTimeFormat($tenantId = null, $branchId = null)
    {
        return static::get('time_format', 'H:i:s', $tenantId, $branchId, 'language_settings');
    }

    // ========== Notification Settings Helpers ==========
    
    public static function getEmailNotificationsEnabled($tenantId = null, $branchId = null)
    {
        return static::get('email_notifications_enabled', true, $tenantId, $branchId, 'notification_settings');
    }
    
    public static function getSmsNotificationsEnabled($tenantId = null, $branchId = null)
    {
        return static::get('sms_notifications_enabled', false, $tenantId, $branchId, 'notification_settings');
    }
    
    public static function getPushNotificationsEnabled($tenantId = null, $branchId = null)
    {
        return static::get('push_notifications_enabled', true, $tenantId, $branchId, 'notification_settings');
    }

    // ========== Pusher Settings Helpers ==========
    
    public static function getPusherAppId($tenantId = null, $branchId = null)
    {
        return static::get('pusher_app_id', null, $tenantId, $branchId, 'pusher_settings');
    }
    
    public static function getPusherKey($tenantId = null, $branchId = null)
    {
        return static::get('pusher_key', null, $tenantId, $branchId, 'pusher_settings');
    }
    
    public static function getPusherSecret($tenantId = null, $branchId = null)
    {
        return static::get('pusher_secret', null, $tenantId, $branchId, 'pusher_settings');
    }
    
    public static function getPusherCluster($tenantId = null, $branchId = null)
    {
        return static::get('pusher_cluster', 'mt1', $tenantId, $branchId, 'pusher_settings');
    }

    // ========== Receipt Settings Helpers ==========
    
    public static function getReceiptTemplate($tenantId = null, $branchId = null)
    {
        return static::get('receipt_template', 'default', $tenantId, $branchId, 'receipt_settings');
    }
    
    public static function getReceiptPrinterEnabled($tenantId = null, $branchId = null)
    {
        return static::get('receipt_printer_enabled', true, $tenantId, $branchId, 'receipt_settings');
    }
    
    public static function getReceiptEmailEnabled($tenantId = null, $branchId = null)
    {
        return static::get('receipt_email_enabled', false, $tenantId, $branchId, 'receipt_settings');
    }

    // ========== Reservation Settings Helpers ==========
    
    public static function getReservationsEnabled($tenantId = null, $branchId = null)
    {
        return static::get('reservations_enabled', true, $tenantId, $branchId, 'reservation_settings');
    }
    
    public static function getMaxReservationDays($tenantId = null, $branchId = null)
    {
        return static::get('max_reservation_days', 30, $tenantId, $branchId, 'reservation_settings');
    }
    
    public static function getReservationTimeSlots($tenantId = null, $branchId = null)
    {
        return static::get('reservation_time_slots', ['12:00', '13:00', '14:00', '19:00', '20:00', '21:00'], $tenantId, $branchId, 'reservation_settings');
    }

    // ========== Branch Delivery Settings Helpers ==========
    
    public static function getDeliveryEnabled($tenantId = null, $branchId = null)
    {
        return static::get('delivery_enabled', false, $tenantId, $branchId, 'branch_delivery_settings');
    }
    
    public static function getDeliveryRadius($tenantId = null, $branchId = null)
    {
        return static::get('delivery_radius', 5.0, $tenantId, $branchId, 'branch_delivery_settings');
    }
    
    public static function getDeliveryFee($tenantId = null, $branchId = null)
    {
        return static::get('delivery_fee', 0.0, $tenantId, $branchId, 'branch_delivery_settings');
    }
    
    public static function getMinOrderForDelivery($tenantId = null, $branchId = null)
    {
        return static::get('min_order_for_delivery', 20.0, $tenantId, $branchId, 'branch_delivery_settings');
    }

    // ========== Email Settings Helpers ==========
    
    public static function getSmtpHost($tenantId = null, $branchId = null)
    {
        return static::get('smtp_host', 'localhost', $tenantId, $branchId, 'email_settings');
    }
    
    public static function getSmtpPort($tenantId = null, $branchId = null)
    {
        return static::get('smtp_port', 587, $tenantId, $branchId, 'email_settings');
    }
    
    public static function getSmtpUsername($tenantId = null, $branchId = null)
    {
        return static::get('smtp_username', null, $tenantId, $branchId, 'email_settings');
    }
    
    public static function getFromEmail($tenantId = null, $branchId = null)
    {
        return static::get('from_email', '<EMAIL>', $tenantId, $branchId, 'email_settings');
    }

    // ========== File Storage Settings Helpers ==========
    
    public static function getStorageDriver($tenantId = null, $branchId = null)
    {
        return static::get('storage_driver', 'local', $tenantId, $branchId, 'file_storage_settings');
    }
    
    public static function getMaxFileSize($tenantId = null, $branchId = null)
    {
        return static::get('max_file_size', 10240, $tenantId, $branchId, 'file_storage_settings'); // 10MB in KB
    }
    
    public static function getAllowedFileTypes($tenantId = null, $branchId = null)
    {
        return static::get('allowed_file_types', ['jpg', 'jpeg', 'png', 'gif', 'pdf'], $tenantId, $branchId, 'file_storage_settings');
    }

    // ========== Front FAQ Settings Helpers ==========
    
    public static function getFaqEnabled($tenantId = null, $branchId = null)
    {
        return static::get('faq_enabled', true, $tenantId, $branchId, 'front_faq_settings');
    }
    
    public static function getFaqCategories($tenantId = null, $branchId = null)
    {
        return static::get('faq_categories', ['General', 'Orders', 'Payments', 'Delivery'], $tenantId, $branchId, 'front_faq_settings');
    }

    // ========== Front Review Settings Helpers ==========
    
    public static function getReviewsEnabled($tenantId = null, $branchId = null)
    {
        return static::get('reviews_enabled', true, $tenantId, $branchId, 'front_review_settings');
    }
    
    public static function getReviewModerationEnabled($tenantId = null, $branchId = null)
    {
        return static::get('review_moderation_enabled', true, $tenantId, $branchId, 'front_review_settings');
    }
    
    public static function getMinReviewRating($tenantId = null, $branchId = null)
    {
        return static::get('min_review_rating', 1, $tenantId, $branchId, 'front_review_settings');
    }
    
    public static function getMaxReviewRating($tenantId = null, $branchId = null)
    {
        return static::get('max_review_rating', 5, $tenantId, $branchId, 'front_review_settings');
    }

    /**
     * Clear all setting caches
     */
    public static function clearCache($tenantId = null, $branchId = null)
    {
        $pattern = 'setting_*';
        if ($tenantId || $branchId) {
            $pattern .= '_' . ($tenantId ?? 'null') . '_' . ($branchId ?? 'null') . '_*';
        }
        
        // Note: This is a simplified cache clearing. In production, you might want to use tags or a more sophisticated approach
        Cache::flush();
    }

    /**
     * Get all settings for a category with caching
     */
    public static function getCategorySettings($category, $tenantId = null, $branchId = null)
    {
        $cacheKey = "settings_category_{$category}_" . ($tenantId ?? 'null') . '_' . ($branchId ?? 'null');
        
        return Cache::remember($cacheKey, 3600, function () use ($category, $tenantId, $branchId) {
            try {
                return static::getService()->getByCategory($category, $tenantId, $branchId);
            } catch (\Exception $e) {
                Log::warning('Failed to get category settings', [
                    'category' => $category,
                    'error' => $e->getMessage()
                ]);
                return collect();
            }
        });
    }
}
<?php

namespace Modules\Tenant\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Tenant;
use Modules\Tenant\Helpers\TenantHelper;
use Modules\Tenant\Helpers\SubscriptionHelper;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class SubscriptionMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$features): BaseResponse
    {
        $tenant = $this->getTenant($request);
        
        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant required',
                'message' => 'This endpoint requires a valid tenant context'
            ], 400);
        }
        
        // Check if tenant has an active subscription
        $subscription = $tenant->activeSubscription;
        
        if (!$subscription) {
            return response()->json([
                'error' => 'No active subscription',
                'message' => 'This tenant does not have an active subscription',
                'action_required' => 'subscribe'
            ], 402); // Payment Required
        }
        
        // Check subscription status
        $statusCheck = $this->checkSubscriptionStatus($subscription);
        if ($statusCheck) {
            return $statusCheck;
        }
        
        // Check feature access if features are specified
        if (!empty($features)) {
            $featureCheck = $this->checkFeatureAccess($tenant, $features);
            if ($featureCheck) {
                return $featureCheck;
            }
        }
        
        // Add subscription info to request
        $request->merge([
            'subscription' => $subscription,
            'subscription_status' => TenantHelper::getSubscriptionStatus($tenant)
        ]);
        
        return $next($request);
    }
    
    /**
     * Get tenant from request
     */
    protected function getTenant(Request $request): ?Tenant
    {
        // Try to get tenant from request (set by TenantMiddleware)
        if ($request->has('tenant')) {
            return $request->get('tenant');
        }
        
        // Try to get from app container
        if (app()->bound('tenant')) {
            return app('tenant');
        }
        
        return null;
    }
    
    /**
     * Check subscription status
     */
    protected function checkSubscriptionStatus($subscription): ?BaseResponse
    {
        $now = now();
        
        // Check if subscription is cancelled
        if ($subscription->status === 'cancelled') {
            return response()->json([
                'error' => 'Subscription cancelled',
                'message' => 'This subscription has been cancelled',
                'cancelled_at' => $subscription->cancelled_at,
                'action_required' => 'reactivate'
            ], 402);
        }
        
        // Check if subscription is suspended
        if ($subscription->status === 'suspended') {
            return response()->json([
                'error' => 'Subscription suspended',
                'message' => 'This subscription has been suspended due to payment issues',
                'action_required' => 'update_payment'
            ], 402);
        }
        
        // Check if subscription is expired
        if ($subscription->status === 'expired' || 
            ($subscription->ends_at && $now->gt($subscription->ends_at))) {
            return response()->json([
                'error' => 'Subscription expired',
                'message' => 'This subscription has expired',
                'expired_at' => $subscription->ends_at,
                'action_required' => 'renew'
            ], 402);
        }
        
        // Check if trial has expired
        if ($subscription->trial_ends_at && 
            $now->gt($subscription->trial_ends_at) && 
            $subscription->status !== 'active') {
            return response()->json([
                'error' => 'Trial expired',
                'message' => 'The trial period has expired',
                'trial_ended_at' => $subscription->trial_ends_at,
                'action_required' => 'upgrade'
            ], 402);
        }
        
        return null;
    }
    
    /**
     * Check feature access
     */
    protected function checkFeatureAccess(Tenant $tenant, array $features): ?BaseResponse
    {
        foreach ($features as $feature) {
            // Parse feature with optional usage check
            $featureParts = explode(':', $feature);
            $featureName = $featureParts[0];
            $checkUsage = isset($featureParts[1]) && $featureParts[1] === 'usage';
            
            // Check if tenant has access to the feature
            if (!TenantHelper::hasFeatureAccess($tenant, $featureName)) {
                return response()->json([
                    'error' => 'Feature not available',
                    'message' => "The '{$featureName}' feature is not available in your current plan",
                    'feature' => $featureName,
                    'action_required' => 'upgrade_plan'
                ], 403);
            }
            
            // Check usage limits if requested
            if ($checkUsage) {
                $currentUsage = TenantHelper::getCurrentUsage($tenant, $featureName);
                
                if (TenantHelper::hasReachedLimit($tenant, $featureName, $currentUsage)) {
                    $limit = TenantHelper::getFeatureLimit($tenant, $featureName);
                    
                    return response()->json([
                        'error' => 'Usage limit reached',
                        'message' => "You have reached the limit for '{$featureName}'",
                        'feature' => $featureName,
                        'current_usage' => $currentUsage,
                        'limit' => $limit,
                        'action_required' => 'upgrade_plan'
                    ], 429); // Too Many Requests
                }
            }
        }
        
        return null;
    }
}
@extends('docs.layout')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- <PERSON><PERSON><PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <div class="p-4 rounded-full bg-indigo-100 mr-4">
                <i class="fas fa-shield-alt text-indigo-600 text-2xl"></i>
            </div>
            <div>
                <h1 class="text-4xl font-bold text-gray-900">Authentication Module</h1>
                <p class="text-xl text-gray-600 mt-2">Complete authentication system with JWT tokens, role-based access control, and user management</p>
            </div>
        </div>
        
        <!-- Module Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-indigo-600">18</div>
                <div class="text-gray-600 text-sm">Total Endpoints</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-green-600">6</div>
                <div class="text-gray-600 text-sm">Public Routes</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-orange-600">12</div>
                <div class="text-gray-600 text-sm">Protected Routes</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 border border-gray-100">
                <div class="text-2xl font-bold text-blue-600">4</div>
                <div class="text-gray-600 text-sm">Middleware</div>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="relative mb-6">
            <input type="text" id="endpointSearch" placeholder="Search authentication endpoints..." 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <i class="fas fa-search absolute right-4 top-4 text-gray-400"></i>
        </div>
    </div>

    <!-- Public Routes Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-globe text-green-600 mr-3"></i>
            Public Routes
            <span class="ml-3 bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded">No Auth Required</span>
        </h2>

        <!-- User Registration Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/auth/register</h3>
                        <span class="ml-3 text-gray-600">Register a new user</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123"
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2024-01-16T14:30:00Z"
    },
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Creates a new user account and returns an authentication token. Password must be at least 8 characters long.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Login Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/auth/login</h3>
                        <span class="ml-3 text-gray-600">Authenticate user and get token</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "email": "<EMAIL>",
  "password": "password123"
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "roles": ["user"]
    },
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_at": "2024-01-17T14:30:00Z"
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Authenticates a user with email and password, returning a JWT token for subsequent API requests.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Password Reset Request Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/auth/password/reset-request</h3>
                        <span class="ml-3 text-gray-600">Request password reset</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "email": "<EMAIL>"
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Password reset email sent successfully"
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Sends a password reset email to the specified email address if the account exists.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Password Reset Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/auth/password/reset</h3>
                        <span class="ml-3 text-gray-600">Reset password with token</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "email": "<EMAIL>",
  "token": "abc123def456",
  "password": "newpassword123",
  "password_confirmation": "newpassword123"
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Password reset successfully"
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Resets the user's password using the token received via email.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Verification Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/auth/email/verify</h3>
                        <span class="ml-3 text-gray-600">Verify email address</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Request Body</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "email": "<EMAIL>",
  "verification_token": "abc123def456"
}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Email verified successfully"
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Verifies the user's email address using the verification token sent via email.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Refresh Token Endpoint -->
        <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
            <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                        <h3 class="text-lg font-semibold text-gray-900">/api/auth/refresh</h3>
                        <span class="ml-3 text-gray-600">Refresh authentication token</span>
                    </div>
                    <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                </div>
            </div>
            <div class="endpoint-dropdown px-4 pb-4">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                            <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {refresh_token}</code></pre>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                            <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_at": "2024-01-17T14:30:00Z"
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                        <p class="text-gray-600">Refreshes an expired or soon-to-expire JWT token using a valid refresh token.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Protected Routes Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-lock text-orange-600 mr-3"></i>
            Protected Routes
            <span class="ml-3 bg-orange-100 text-orange-800 text-sm font-medium px-2.5 py-0.5 rounded">Auth Required</span>
        </h2>

        <!-- User Profile Management -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-user text-blue-600 mr-2"></i>
                User Profile Management
            </h3>

            <!-- Get User Profile Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/user</h3>
                            <span class="ml-3 text-gray-600">Get authenticated user profile</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "email_verified_at": "2024-01-16T14:30:00Z",
      "roles": ["user"],
      "permissions": ["read_profile"],
      "created_at": "2024-01-16T14:30:00Z",
      "updated_at": "2024-01-16T14:30:00Z"
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves the authenticated user's profile information including roles and permissions.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Update User Profile Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/user</h3>
                            <span class="ml-3 text-gray-600">Update user profile</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "John Smith",
  "email": "<EMAIL>"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "John Smith",
      "email": "<EMAIL>",
      "email_verified_at": null,
      "updated_at": "2024-01-16T15:30:00Z"
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates the authenticated user's profile information. Email changes require re-verification.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change Password Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/password/change</h3>
                            <span class="ml-3 text-gray-600">Change user password</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "current_password": "oldpassword123",
  "password": "newpassword123",
  "password_confirmation": "newpassword123"
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Password changed successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Changes the authenticated user's password. Requires current password for verification.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logout Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/logout</h3>
                            <span class="ml-3 text-gray-600">Logout user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {access_token}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Logged out successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Logs out the authenticated user and invalidates the current token.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin User Management -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-users-cog text-purple-600 mr-2"></i>
                Admin User Management
            </h3>

            <!-- List Users Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">GET</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users</h3>
                            <span class="ml-3 text-gray-600">Get all users</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Query Parameters</h4>
                                <pre class="bg-gray-900 text-yellow-400 p-4 rounded-lg text-sm overflow-x-auto"><code>page=1
per_page=20
search=john
role=admin</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "roles": ["admin"],
        "created_at": "2024-01-16T14:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 50
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Retrieves a paginated list of all users. Requires admin privileges.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create User Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users</h3>
                            <span class="ml-3 text-gray-600">Create new user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "roles": ["user"]
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (201)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "User created successfully",
  "data": {
    "user": {
      "id": 2,
      "name": "Jane Doe",
      "email": "<EMAIL>",
      "roles": ["user"],
      "created_at": "2024-01-16T15:30:00Z"
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Creates a new user account with specified roles. Requires admin privileges.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Update User Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">PUT</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users/{user}</h3>
                            <span class="ml-3 text-gray-600">Update user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "is_active": true
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "User updated successfully",
  "data": {
    "user": {
      "id": 2,
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "is_active": true,
      "updated_at": "2024-01-16T16:30:00Z"
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Updates user information including name, email, and active status. Requires admin privileges.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delete User Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users/{user}</h3>
                            <span class="ml-3 text-gray-600">Delete user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "User deleted successfully"
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Permanently deletes a user account. This action cannot be undone. Requires admin privileges.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assign User Roles Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users/{user}/roles</h3>
                            <span class="ml-3 text-gray-600">Assign roles to user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "roles": [
    "manager",
    "cashier"
  ]
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Roles assigned to user successfully",
  "data": {
    "user": {
      "id": 5,
      "name": "John Doe",
      "email": "<EMAIL>",
      "roles": [
        "manager",
        "cashier"
      ]
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Assigns multiple roles to a specific user. This will add the roles to the user's existing roles.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revoke User Roles Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users/{user}/roles</h3>
                            <span class="ml-3 text-gray-600">Revoke roles from user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "roles": [
    "cashier"
  ]
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Roles revoked from user successfully",
  "data": {
    "user": {
      "id": 5,
      "name": "John Doe",
      "email": "<EMAIL>",
      "roles": [
        "manager"
      ]
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Revokes specific roles from a user. The specified roles will be removed from the user's role list.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assign User Permissions Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">POST</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users/{user}/permissions</h3>
                            <span class="ml-3 text-gray-600">Assign permissions directly to user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "permissions": [
    "view_special_reports",
    "access_admin_panel"
  ]
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Permissions assigned to user successfully",
  "data": {
    "user": {
      "id": 5,
      "name": "John Doe",
      "email": "<EMAIL>",
      "direct_permissions": [
        "view_special_reports",
        "access_admin_panel"
      ]
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Assigns permissions directly to a user, bypassing role-based permissions. Useful for granting specific permissions to individual users.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revoke User Permissions Endpoint -->
            <div class="bg-white rounded-lg shadow-lg mb-4 border border-gray-100 endpoint-item">
                <div class="p-4 cursor-pointer" onclick="toggleDropdown(this)">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-3">DELETE</span>
                            <h3 class="text-lg font-semibold text-gray-900">/api/auth/admin/users/{user}/permissions</h3>
                            <span class="ml-3 text-gray-600">Revoke permissions from user</span>
                        </div>
                        <i class="fas fa-chevron-down dropdown-icon text-gray-400 transition-transform"></i>
                    </div>
                </div>
                <div class="endpoint-dropdown px-4 pb-4">
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Headers</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>Authorization: Bearer {admin_token}
Content-Type: application/json</code></pre>
                                <h4 class="font-semibold text-gray-900 mb-3 mt-4">Request Body</h4>
                                <pre class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "permissions": [
    "access_admin_panel"
  ]
}</code></pre>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-3">Response (200)</h4>
                                <pre class="bg-gray-900 text-blue-400 p-4 rounded-lg text-sm overflow-x-auto"><code>{
  "success": true,
  "message": "Permissions revoked from user successfully",
  "data": {
    "user": {
      "id": 5,
      "name": "John Doe",
      "email": "<EMAIL>",
      "direct_permissions": [
        "view_special_reports"
      ]
    }
  }
}</code></pre>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Description</h4>
                            <p class="text-gray-600">Revokes specific direct permissions from a user. This only affects permissions assigned directly to the user, not those inherited from roles.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Technical Architecture Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="fas fa-cogs text-gray-600 mr-3"></i>
            Technical Architecture
        </h2>

        <!-- Middleware Section -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-filter text-indigo-600 mr-2"></i>
                Middleware
            </h3>
            <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">AuthMiddleware</h4>
                        <p class="text-gray-600 mb-2">Validates JWT tokens and authenticates users</p>
                        <ul class="text-sm text-gray-500 list-disc list-inside">
                            <li>Token validation</li>
                            <li>User authentication</li>
                            <li>Request rate limiting</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">RoleMiddleware</h4>
                        <p class="text-gray-600 mb-2">Enforces role-based access control</p>
                        <ul class="text-sm text-gray-500 list-disc list-inside">
                            <li>Role verification</li>
                            <li>Permission checking</li>
                            <li>Access control</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">AdminMiddleware</h4>
                        <p class="text-gray-600 mb-2">Restricts access to admin-only endpoints</p>
                        <ul class="text-sm text-gray-500 list-disc list-inside">
                            <li>Admin role verification</li>
                            <li>Elevated permission checks</li>
                            <li>Audit logging</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">ThrottleMiddleware</h4>
                        <p class="text-gray-600 mb-2">Rate limiting for authentication endpoints</p>
                        <ul class="text-sm text-gray-500 list-disc list-inside">
                            <li>Login attempt limiting</li>
                            <li>Password reset throttling</li>
                            <li>Brute force protection</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Schema Section -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-database text-blue-600 mr-2"></i>
                Database Schema
            </h3>
            <div class="space-y-4">
                <!-- Users Table -->
                <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                    <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                        <i class="fas fa-table text-blue-500 mr-2"></i>
                        users
                    </h4>
                    <div class="overflow-x-auto">
                         <table class="min-w-full divide-y divide-gray-200">
                             <thead class="bg-gray-50">
                                 <tr>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                 </tr>
                             </thead>
                             <tbody class="bg-white divide-y divide-gray-200">
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Primary key</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">name</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">User's full name</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">email</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Unique email address</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">email_verified_at</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Email verification timestamp</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">password</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Hashed password</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">is_active</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">boolean</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Account status</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">created_at</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Record creation time</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">updated_at</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">timestamp</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Last update time</td>
                                 </tr>
                             </tbody>
                         </table>
                     </div>
                 </div>

                 <!-- Roles Table -->
                 <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                     <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                         <i class="fas fa-table text-green-500 mr-2"></i>
                         roles
                     </h4>
                     <div class="overflow-x-auto">
                         <table class="min-w-full divide-y divide-gray-200">
                             <thead class="bg-gray-50">
                                 <tr>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                 </tr>
                             </thead>
                             <tbody class="bg-white divide-y divide-gray-200">
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Primary key</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">name</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Role identifier</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">display_name</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Human-readable name</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">description</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Role description</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">guard_name</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Guard context</td>
                                 </tr>
                             </tbody>
                         </table>
                     </div>
                 </div>

                 <!-- Permissions Table -->
                 <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                     <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                         <i class="fas fa-table text-purple-500 mr-2"></i>
                         permissions
                     </h4>
                     <div class="overflow-x-auto">
                         <table class="min-w-full divide-y divide-gray-200">
                             <thead class="bg-gray-50">
                                 <tr>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                     <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                 </tr>
                             </thead>
                             <tbody class="bg-white divide-y divide-gray-200">
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">id</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">bigint</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Primary key</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">name</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Permission identifier</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">display_name</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Human-readable name</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">description</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">text</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Permission description</td>
                                 </tr>
                                 <tr>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">guard_name</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">varchar(255)</td>
                                     <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Guard context</td>
                                 </tr>
                             </tbody>
                         </table>
                     </div>
                 </div>
             </div>
         </div>

         <!-- Security Features Section -->
         <div class="mb-6">
             <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                 <i class="fas fa-shield-alt text-red-600 mr-2"></i>
                 Security Features
             </h3>
             <div class="bg-white rounded-lg shadow p-6 border border-gray-100">
                 <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                     <div>
                         <h4 class="font-semibold text-gray-900 mb-3">JWT Token Security</h4>
                         <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                             <li>HS256 algorithm for token signing</li>
                             <li>Configurable token expiration</li>
                             <li>Refresh token mechanism</li>
                             <li>Token blacklisting on logout</li>
                         </ul>
                     </div>
                     <div>
                         <h4 class="font-semibold text-gray-900 mb-3">Password Security</h4>
                         <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                             <li>Bcrypt hashing algorithm</li>
                             <li>Minimum 8 character requirement</li>
                             <li>Password confirmation validation</li>
                             <li>Secure password reset flow</li>
                         </ul>
                     </div>
                     <div>
                         <h4 class="font-semibold text-gray-900 mb-3">Rate Limiting</h4>
                         <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                             <li>Login attempt throttling</li>
                             <li>Password reset rate limiting</li>
                             <li>API request rate limiting</li>
                             <li>Brute force attack protection</li>
                         </ul>
                     </div>
                     <div>
                         <h4 class="font-semibold text-gray-900 mb-3">Email Verification</h4>
                         <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                             <li>Email verification tokens</li>
                             <li>Secure verification links</li>
                             <li>Token expiration handling</li>
                             <li>Resend verification capability</li>
                         </ul>
                     </div>
                 </div>
             </div>
         </div>
     </div>
</div>
@endsection
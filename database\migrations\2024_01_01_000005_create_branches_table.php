<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('name');
            $table->string('code', 50)->comment('Unique within tenant');
            $table->text('address')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('phone', 20)->nullable();
            $table->string('email')->nullable();
            $table->string('manager_name')->nullable();
            $table->integer('seating_capacity')->default(0);
            $table->decimal('delivery_radius', 8, 2)->nullable()->comment('In kilometers');
            $table->json('operating_hours')->nullable()->comment('Daily hours');
            $table->string('timezone', 50)->nullable();
            $table->boolean('is_delivery_enabled')->default(false);
            $table->boolean('is_takeaway_enabled')->default(true);
            $table->boolean('is_dine_in_enabled')->default(true);
            $table->boolean('is_online_ordering_enabled')->default(false);
            $table->json('printer_configurations')->nullable()->comment('Printer settings');
            $table->string('pos_terminal_id', 100)->nullable();
            $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
            $table->timestamps();
            $table->softDeletes();
            
            $table->unique(['tenant_id', 'code']);
            
            // Performance indexes
            $table->index(['tenant_id', 'status']);
            $table->index(['status', 'is_delivery_enabled']);
            $table->index(['is_online_ordering_enabled']);
            $table->index(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branches');
    }
};
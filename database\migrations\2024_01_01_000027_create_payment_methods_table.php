<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // e.g., 'credit_card', 'cash', 'mobile_money'
            $table->json('credentials')->nullable(); // Store encrypted credentials or API keys
            $table->boolean('is_active')->default(true);
            $table->string('provider')->nullable(); // e.g., 'Stripe', 'PayPal', 'M-Pesa'
            $table->timestamps();
            
            // Performance indexes
            $table->index(['type', 'is_active']);
            $table->index(['provider', 'is_active']);
            $table->index(['name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};

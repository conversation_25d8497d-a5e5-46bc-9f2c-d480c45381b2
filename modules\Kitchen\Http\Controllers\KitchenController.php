<?php

namespace Modules\Kitchen\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Kitchen\Services\KitchenService;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Http\Requests\CreateKitchenRequest;
use Modules\Kitchen\Http\Requests\UpdateKitchenRequest;
use Modules\Kitchen\Http\Requests\AssignMenuItemRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class KitchenController extends Controller
{
    protected $kitchenService;

    public function __construct(KitchenService $kitchenService)
    {
        $this->kitchenService = $kitchenService;
    }

    /**
     * Get all kitchens for tenant/branch
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            $branchId = $request->get('branch_id');
            $activeOnly = $request->boolean('active_only', true);

            $kitchens = $this->kitchenService->getKitchens($tenantId, $branchId, $activeOnly);

            return response()->json([
                'success' => true,
                'data' => $kitchens,
                'message' => 'Kitchens retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve kitchens: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific kitchen details
     */
    public function show(Kitchen $kitchen): JsonResponse
    {
        try {
            $kitchen->load(['manager', 'menuItems.menuItem', 'activeKotOrders']);

            return response()->json([
                'success' => true,
                'data' => $kitchen,
                'message' => 'Kitchen details retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve kitchen details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new kitchen
     */
    public function store(CreateKitchenRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $data['tenant_id'] = $request->user()->tenant_id;
            $data['created_by'] = $request->user()->id;

            $kitchen = $this->kitchenService->createKitchen($data);

            return response()->json([
                'success' => true,
                'data' => $kitchen,
                'message' => 'Kitchen created successfully'
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create kitchen: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update kitchen
     */
    public function update(UpdateKitchenRequest $request, Kitchen $kitchen): JsonResponse
    {
        try {
            $data = $request->validated();
            $updatedKitchen = $this->kitchenService->updateKitchen($kitchen, $data);

            return response()->json([
                'success' => true,
                'data' => $updatedKitchen,
                'message' => 'Kitchen updated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update kitchen: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete kitchen
     */
    public function destroy(Kitchen $kitchen): JsonResponse
    {
        try {
            // Check if kitchen has active KOTs
            $activeKots = $kitchen->activeKotOrders()->count();
            if ($activeKots > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete kitchen with active KOT orders'
                ], 422);
            }

            $kitchen->delete();

            return response()->json([
                'success' => true,
                'message' => 'Kitchen deleted successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete kitchen: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign menu item to kitchen
     */
    public function assignMenuItem(AssignMenuItemRequest $request, Kitchen $kitchen): JsonResponse
    {
        try {
            $data = $request->validated();
            $data['assigned_by'] = $request->user()->id;

            $assignment = $this->kitchenService->assignMenuItemToKitchen(
                $request->user()->tenant_id,
                $kitchen->id,
                $data['menu_item_id'],
                $data
            );

            return response()->json([
                'success' => true,
                'data' => $assignment,
                'message' => 'Menu item assigned to kitchen successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign menu item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove menu item from kitchen
     */
    public function removeMenuItem(Request $request, Kitchen $kitchen, $menuItemId): JsonResponse
    {
        try {
            $this->kitchenService->removeMenuItemFromKitchen(
                $request->user()->tenant_id,
                $menuItemId
            );

            return response()->json([
                'success' => true,
                'message' => 'Menu item removed from kitchen successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove menu item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get kitchen menu items
     */
    public function getMenuItems(Kitchen $kitchen): JsonResponse
    {
        try {
            $menuItems = $this->kitchenService->getKitchenMenuItems($kitchen->id);

            return response()->json([
                'success' => true,
                'data' => $menuItems,
                'message' => 'Kitchen menu items retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve menu items: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get kitchen dashboard
     */
    public function dashboard(Kitchen $kitchen): JsonResponse
    {
        try {
            $dashboard = $this->kitchenService->getKitchenDashboard($kitchen->id);

            return response()->json([
                'success' => true,
                'data' => $dashboard,
                'message' => 'Kitchen dashboard data retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active KOTs for kitchen
     */
    public function getActiveKots(Kitchen $kitchen): JsonResponse
    {
        try {
            $kots = $this->kitchenService->getActiveKotsForKitchen($kitchen->id);

            return response()->json([
                'success' => true,
                'data' => $kots,
                'message' => 'Active KOTs retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active KOTs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle kitchen status
     */
    public function toggleStatus(Kitchen $kitchen): JsonResponse
    {
        try {
            $kitchen->update(['is_active' => !$kitchen->is_active]);

            return response()->json([
                'success' => true,
                'data' => $kitchen->fresh(),
                'message' => 'Kitchen status updated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update kitchen status: ' . $e->getMessage()
            ], 500);
        }
    }
}
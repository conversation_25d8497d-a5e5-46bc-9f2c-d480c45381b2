<?php

namespace Modules\Tenant\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'tenant_id' => 'required|exists:tenants,id',
            'plan_id' => 'required|exists:subscription_plans,id',
            'billing_cycle' => 'required|in:monthly,yearly,quarterly',
            'starts_at' => 'nullable|date|after_or_equal:today',
            'trial_ends_at' => 'nullable|date|after:starts_at',
            'auto_renew' => 'nullable|boolean',
            'payment_method' => 'nullable|string|in:credit_card,bank_transfer,paypal,stripe,manual',
            'payment_details' => 'nullable|array',
            'payment_details.card_last_four' => 'nullable|string|size:4',
            'payment_details.card_brand' => 'nullable|string|max:50',
            'payment_details.payment_method_id' => 'nullable|string|max:255',
            'discount_code' => 'nullable|string|max:50',
            'discount_amount' => 'nullable|numeric|min:0',
            'discount_type' => 'nullable|in:fixed,percentage',
            'custom_pricing' => 'nullable|array',
            'custom_pricing.amount' => 'nullable|numeric|min:0',
            'custom_pricing.currency' => 'nullable|string|size:3',
            'custom_pricing.reason' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'tenant_id.required' => 'The tenant is required.',
            'tenant_id.exists' => 'The selected tenant is invalid.',
            'plan_id.required' => 'The subscription plan is required.',
            'plan_id.exists' => 'The selected subscription plan is invalid.',
            'billing_cycle.required' => 'The billing cycle is required.',
            'billing_cycle.in' => 'Billing cycle must be one of: monthly, yearly, quarterly.',
            'starts_at.date' => 'The start date must be a valid date.',
            'starts_at.after_or_equal' => 'The start date must be today or in the future.',
            'trial_ends_at.date' => 'The trial end date must be a valid date.',
            'trial_ends_at.after' => 'The trial end date must be after the start date.',
            'payment_method.in' => 'Payment method must be one of: credit_card, bank_transfer, paypal, stripe, manual.',
            'payment_details.card_last_four.size' => 'Card last four digits must be exactly 4 characters.',
            'discount_amount.numeric' => 'Discount amount must be a number.',
            'discount_amount.min' => 'Discount amount must be at least 0.',
            'discount_type.in' => 'Discount type must be either fixed or percentage.',
            'custom_pricing.amount.numeric' => 'Custom pricing amount must be a number.',
            'custom_pricing.amount.min' => 'Custom pricing amount must be at least 0.',
            'custom_pricing.currency.size' => 'Currency code must be exactly 3 characters.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'tenant_id' => 'tenant',
            'plan_id' => 'subscription plan',
            'billing_cycle' => 'billing cycle',
            'starts_at' => 'start date',
            'trial_ends_at' => 'trial end date',
            'auto_renew' => 'auto renewal',
            'payment_method' => 'payment method',
            'payment_details' => 'payment details',
            'discount_code' => 'discount code',
            'discount_amount' => 'discount amount',
            'discount_type' => 'discount type',
            'custom_pricing' => 'custom pricing',
            'metadata' => 'metadata',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default start date to today if not provided
        if (!$this->has('starts_at')) {
            $this->merge([
                'starts_at' => now()->toDateString()
            ]);
        }

        // Set default auto_renew to true if not provided
        if (!$this->has('auto_renew')) {
            $this->merge([
                'auto_renew' => true
            ]);
        }

        // Normalize currency code to uppercase
        if ($this->has('custom_pricing.currency')) {
            $customPricing = $this->input('custom_pricing');
            $customPricing['currency'] = strtoupper($customPricing['currency']);
            $this->merge(['custom_pricing' => $customPricing]);
        }

        // Normalize discount code to uppercase
        if ($this->has('discount_code')) {
            $this->merge([
                'discount_code' => strtoupper($this->discount_code)
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate discount logic
            if ($this->has('discount_amount') || $this->has('discount_type')) {
                $this->validateDiscount($validator);
            }

            // Validate custom pricing
            if ($this->has('custom_pricing')) {
                $this->validateCustomPricing($validator);
            }

            // Validate payment details based on payment method
            if ($this->has('payment_method')) {
                $this->validatePaymentDetails($validator);
            }

            // Validate trial period
            if ($this->has('trial_ends_at')) {
                $this->validateTrialPeriod($validator);
            }
        });
    }

    /**
     * Validate discount logic
     */
    protected function validateDiscount($validator): void
    {
        $discountAmount = $this->input('discount_amount');
        $discountType = $this->input('discount_type');

        // If discount amount is provided, discount type is required
        if ($discountAmount && !$discountType) {
            $validator->errors()->add(
                'discount_type',
                'Discount type is required when discount amount is provided.'
            );
        }

        // If discount type is provided, discount amount is required
        if ($discountType && !$discountAmount) {
            $validator->errors()->add(
                'discount_amount',
                'Discount amount is required when discount type is provided.'
            );
        }

        // Validate percentage discount
        if ($discountType === 'percentage' && $discountAmount > 100) {
            $validator->errors()->add(
                'discount_amount',
                'Percentage discount cannot exceed 100%.'
            );
        }
    }

    /**
     * Validate custom pricing
     */
    protected function validateCustomPricing($validator): void
    {
        $customPricing = $this->input('custom_pricing', []);

        // If custom pricing is provided, amount and currency are required
        if (!empty($customPricing)) {
            if (!isset($customPricing['amount'])) {
                $validator->errors()->add(
                    'custom_pricing.amount',
                    'Custom pricing amount is required when using custom pricing.'
                );
            }

            if (!isset($customPricing['currency'])) {
                $validator->errors()->add(
                    'custom_pricing.currency',
                    'Custom pricing currency is required when using custom pricing.'
                );
            }
        }
    }

    /**
     * Validate payment details based on payment method
     */
    protected function validatePaymentDetails($validator): void
    {
        $paymentMethod = $this->input('payment_method');
        $paymentDetails = $this->input('payment_details', []);

        if ($paymentMethod === 'credit_card' || $paymentMethod === 'stripe') {
            if (empty($paymentDetails['payment_method_id'])) {
                $validator->errors()->add(
                    'payment_details.payment_method_id',
                    'Payment method ID is required for credit card payments.'
                );
            }
        }
    }

    /**
     * Validate trial period
     */
    protected function validateTrialPeriod($validator): void
    {
        $trialEndsAt = $this->input('trial_ends_at');
        $startsAt = $this->input('starts_at', now()->toDateString());

        if ($trialEndsAt && $startsAt) {
            $trialStart = \Carbon\Carbon::parse($startsAt);
            $trialEnd = \Carbon\Carbon::parse($trialEndsAt);
            
            // Trial period should be reasonable (not more than 1 year)
            if ($trialEnd->diffInDays($trialStart) > 365) {
                $validator->errors()->add(
                    'trial_ends_at',
                    'Trial period cannot exceed 365 days.'
                );
            }

            // Trial period should be at least 1 day
            if ($trialEnd->diffInDays($trialStart) < 1) {
                $validator->errors()->add(
                    'trial_ends_at',
                    'Trial period must be at least 1 day.'
                );
            }
        }
    }
}
<?php

namespace Modules\Tenant\Services;

use App\Models\Tenant;
use App\Models\TenantSubscription;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BillingService
{
    /**
     * Process billing for a subscription
     */
    public function processBilling(TenantSubscription $subscription): array
    {
        try {
            DB::beginTransaction();

            $plan = $subscription->plan;
            $amount = $this->calculateBillingAmount($subscription);

            // Create billing record
            $billingRecord = $this->createBillingRecord($subscription, $amount);

            // Process payment (integrate with payment gateway)
            $paymentResult = $this->processPayment($subscription, $amount);

            if ($paymentResult['success']) {
                // Update subscription for next billing cycle
                $this->updateSubscriptionAfterPayment($subscription);
                
                // Log successful billing
                $this->logBillingEvent($subscription, 'payment_success', $amount);
                
                DB::commit();
                
                return [
                    'success' => true,
                    'amount' => $amount,
                    'billing_record' => $billingRecord,
                    'payment_id' => $paymentResult['payment_id'] ?? null,
                    'message' => 'Billing processed successfully'
                ];
            } else {
                // Handle payment failure
                $this->handlePaymentFailure($subscription, $paymentResult['error']);
                
                DB::rollBack();
                
                return [
                    'success' => false,
                    'error' => $paymentResult['error'],
                    'message' => 'Payment failed'
                ];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Billing processing failed', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Billing processing failed'
            ];
        }
    }

    /**
     * Calculate billing amount for subscription
     */
    public function calculateBillingAmount(TenantSubscription $subscription): float
    {
        $plan = $subscription->plan;
        
        $baseAmount = $subscription->billing_cycle === 'yearly' 
            ? $plan->yearly_price 
            : $plan->monthly_price;

        // Add any prorated amounts from upgrades
        $proratedAmount = $subscription->prorated_amount ?? 0;

        // Apply any discounts
        $discountAmount = $this->calculateDiscounts($subscription);

        // Calculate taxes
        $taxAmount = $this->calculateTaxes($subscription, $baseAmount + $proratedAmount - $discountAmount);

        return round($baseAmount + $proratedAmount - $discountAmount + $taxAmount, 2);
    }

    /**
     * Create billing record
     */
    protected function createBillingRecord(TenantSubscription $subscription, float $amount): array
    {
        // This would typically create a record in a billing_records table
        // For now, return a mock billing record
        return [
            'id' => uniqid('bill_'),
            'subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'amount' => $amount,
            'billing_date' => Carbon::now(),
            'period_start' => $subscription->current_period_start,
            'period_end' => $subscription->current_period_end,
            'status' => 'pending'
        ];
    }

    /**
     * Process payment (mock implementation)
     */
    protected function processPayment(TenantSubscription $subscription, float $amount): array
    {
        // This would integrate with actual payment gateways like Stripe, PayPal, etc.
        // For now, return a mock successful payment
        
        $tenant = $subscription->tenant;
        
        // Simulate payment processing
        $paymentSuccess = true; // In real implementation, this would be the actual payment result
        
        if ($paymentSuccess) {
            return [
                'success' => true,
                'payment_id' => uniqid('pay_'),
                'transaction_id' => uniqid('txn_'),
                'amount' => $amount,
                'currency' => $tenant->currency_code ?? 'USD',
                'processed_at' => Carbon::now()
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Payment declined by bank',
                'error_code' => 'PAYMENT_DECLINED'
            ];
        }
    }

    /**
     * Update subscription after successful payment
     */
    protected function updateSubscriptionAfterPayment(TenantSubscription $subscription): void
    {
        $nextBillingDate = $this->calculateNextBillingDate(
            $subscription->next_billing_date,
            $subscription->billing_cycle
        );

        $subscription->update([
            'current_period_start' => $subscription->next_billing_date,
            'current_period_end' => $nextBillingDate,
            'next_billing_date' => $nextBillingDate,
            'last_payment_date' => Carbon::now(),
            'payment_failures' => 0, // Reset failure count
            'prorated_amount' => null // Clear prorated amount after billing
        ]);
    }

    /**
     * Handle payment failure
     */
    protected function handlePaymentFailure(TenantSubscription $subscription, string $error): void
    {
        $failureCount = ($subscription->payment_failures ?? 0) + 1;
        
        $updateData = [
            'payment_failures' => $failureCount,
            'last_payment_attempt' => Carbon::now(),
            'last_payment_error' => $error
        ];

        // Suspend subscription after 3 failed attempts
        if ($failureCount >= 3) {
            $updateData['status'] = 'suspended';
            $updateData['suspended_at'] = Carbon::now();
            $updateData['suspension_reason'] = 'Payment failures';
        }

        $subscription->update($updateData);
        
        // Log payment failure
        $this->logBillingEvent($subscription, 'payment_failure', 0, $error);
    }

    /**
     * Calculate discounts for subscription
     */
    protected function calculateDiscounts(TenantSubscription $subscription): float
    {
        $discountAmount = 0;
        
        // Apply yearly discount if applicable
        if ($subscription->billing_cycle === 'yearly') {
            $plan = $subscription->plan;
            $monthlyTotal = $plan->monthly_price * 12;
            $yearlyPrice = $plan->yearly_price;
            $discountAmount = max(0, $monthlyTotal - $yearlyPrice);
        }

        // Apply any promotional discounts
        // This would typically check for active promotions/coupons
        
        return $discountAmount;
    }

    /**
     * Calculate taxes for subscription
     */
    protected function calculateTaxes(TenantSubscription $subscription, float $amount): float
    {
        $tenant = $subscription->tenant;
        
        // This would typically integrate with tax calculation services
        // For now, return a simple tax calculation based on tenant's country
        
        $taxRate = $this->getTaxRateForCountry($tenant->country_id);
        
        return round($amount * ($taxRate / 100), 2);
    }

    /**
     * Get tax rate for country (mock implementation)
     */
    protected function getTaxRateForCountry(?int $countryId): float
    {
        // This would typically look up actual tax rates
        // For now, return default rates
        
        $defaultRates = [
            1 => 10.0, // USA - 10%
            2 => 20.0, // UK - 20%
            3 => 19.0, // Germany - 19%
            // Add more countries as needed
        ];
        
        return $defaultRates[$countryId] ?? 0.0;
    }

    /**
     * Calculate next billing date
     */
    protected function calculateNextBillingDate(string $currentDate, string $billingCycle): Carbon
    {
        $date = Carbon::parse($currentDate);

        switch ($billingCycle) {
            case 'monthly':
                return $date->addMonth();
            case 'yearly':
                return $date->addYear();
            case 'quarterly':
                return $date->addMonths(3);
            default:
                return $date->addMonth();
        }
    }

    /**
     * Log billing event
     */
    protected function logBillingEvent(TenantSubscription $subscription, string $event, float $amount, ?string $error = null): void
    {
        $logData = [
            'subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'event' => $event,
            'amount' => $amount,
            'timestamp' => Carbon::now()
        ];
        
        if ($error) {
            $logData['error'] = $error;
        }
        
        Log::info('Billing event', $logData);
    }

    /**
     * Get billing summary for tenant
     */
    public function getBillingSummary(Tenant $tenant, ?string $period = null): array
    {
        $period = $period ?? 'current_month';
        
        switch ($period) {
            case 'current_month':
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;
            case 'last_month':
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                break;
            case 'current_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;
            default:
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
        }

        // This would typically query actual billing records
        // For now, return mock data based on active subscription
        $activeSubscription = $tenant->subscriptions()
            ->where('status', 'active')
            ->with('plan')
            ->first();

        if (!$activeSubscription) {
            return [
                'total_amount' => 0,
                'period' => $period,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'billing_records' => []
            ];
        }

        $monthlyAmount = $this->calculateBillingAmount($activeSubscription);
        
        return [
            'total_amount' => $monthlyAmount,
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'current_plan' => $activeSubscription->plan->name,
            'billing_cycle' => $activeSubscription->billing_cycle,
            'next_billing_date' => $activeSubscription->next_billing_date,
            'billing_records' => [
                // This would contain actual billing records
            ]
        ];
    }

    /**
     * Generate invoice for subscription billing
     */
    public function generateInvoice(TenantSubscription $subscription, array $billingRecord): array
    {
        $tenant = $subscription->tenant;
        $plan = $subscription->plan;
        
        return [
            'invoice_number' => 'INV-' . date('Y') . '-' . str_pad($subscription->id, 6, '0', STR_PAD_LEFT),
            'invoice_date' => Carbon::now(),
            'due_date' => Carbon::now()->addDays(30),
            'tenant' => [
                'name' => $tenant->name,
                'email' => $tenant->contact_email,
                'address' => $tenant->business_address
            ],
            'subscription' => [
                'plan_name' => $plan->name,
                'billing_cycle' => $subscription->billing_cycle,
                'period_start' => $billingRecord['period_start'],
                'period_end' => $billingRecord['period_end']
            ],
            'amount' => $billingRecord['amount'],
            'currency' => $tenant->currency_code ?? 'USD',
            'status' => 'pending'
        ];
    }
}
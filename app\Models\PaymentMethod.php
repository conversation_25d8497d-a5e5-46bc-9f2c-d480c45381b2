<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'description',
        'is_active',
        'requires_reference',
        'allows_partial',
        'processing_fee_percentage',
        'processing_fee_fixed',
        'gateway_config',
        'sort_order',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'requires_reference' => 'boolean',
            'allows_partial' => 'boolean',
            'processing_fee_percentage' => 'decimal:4',
            'processing_fee_fixed' => 'decimal:2',
            'gateway_config' => 'array',
        ];
    }

    // Relationships
    public function payments()
    {
        return $this->hasMany(Payment::class, 'payment_method_uuid');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Methods
    public function calculateProcessingFee($amount)
    {
        $percentageFee = $amount * ($this->processing_fee_percentage / 100);
        return $percentageFee + $this->processing_fee_fixed;
    }
}
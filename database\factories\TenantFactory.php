<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tenant>
 */
class TenantFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = fake()->company();
        
        return [
            'name' => $name,
            'code' => Str::slug($name) . '-' . fake()->randomNumber(4),
            'business_type' => fake()->randomElement(['Restaurant', 'Cafe', 'Fast Food', 'Fine Dining']),
            'country_id' => null, // No country constraint for tests
            'primary_contact_name' => fake()->name(),
            'contact_email' => fake()->companyEmail(),
            'contact_phone' => fake()->phoneNumber(),
            'business_address' => fake()->address(),
            'timezone' => 'UTC',
            'currency_code' => 'USD',
            'language_code' => 'en',
            'status' => 'active',
        ];
    }
}
<?php

use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Controllers\OrderController;
use Modules\Orders\Http\Middleware\OrderAccessMiddleware;

/*
|--------------------------------------------------------------------------
| API Routes for Orders Module
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Orders module.
|
*/

Route::prefix('api/orders')
    ->middleware(['auth:api', OrderAccessMiddleware::class])
    ->group(function () {
        // Order CRUD operations
        Route::get('/', [OrderController::class, 'index'])->name('orders.index');
        Route::post('/', [OrderController::class, 'store'])->name('orders.store');
        Route::get('/{id}', [OrderController::class, 'show'])->name('orders.show');
        Route::put('/{id}', [OrderController::class, 'update'])->name('orders.update');
        Route::delete('/{id}', [OrderController::class, 'destroy'])->name('orders.destroy');
        
        // Additional order operations
        Route::patch('/{id}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::get('/{id}/items', [OrderController::class, 'getOrderItems'])->name('orders.items');
        Route::post('/{id}/items', [OrderController::class, 'addOrderItem'])->name('orders.add-item');
        Route::delete('/{id}/items/{itemId}', [OrderController::class, 'removeOrderItem'])->name('orders.remove-item');
    });

// Public routes (no authentication required)
Route::prefix('api/orders')
    ->group(function () {
        Route::get('/statuses', function () {
            return response()->json(\Modules\Orders\Helpers\OrderHelper::getOrderStatuses());
        })->name('orders.statuses');
        
        Route::get('/types', function () {
            return response()->json(\Modules\Orders\Helpers\OrderHelper::getOrderTypes());
        })->name('orders.types');
    });
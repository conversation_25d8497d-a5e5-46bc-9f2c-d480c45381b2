<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'menu_id' => $this->menu_id,
            'parent_category_id' => $this->parent_category_id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'image_url' => $this->image_url,
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'menu' => $this->whenLoaded('menu', function() {
                return [
                    'id' => $this->menu->id,
                    'name' => $this->menu->name,
                    'code' => $this->menu->code,
                ];
            }),
            'parent_category' => $this->whenLoaded('parentCategory', function() {
                return [
                    'id' => $this->parentCategory->id,
                    'name' => $this->parentCategory->name,
                    'code' => $this->parentCategory->code,
                ];
            }),
            'child_categories' => $this->whenLoaded('childCategories', function() {
                return $this->childCategories->map(function($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'code' => $category->code,
                        'is_active' => $category->is_active,
                    ];
                });
            }),
            'menu_items' => MenuItemResource::collection($this->whenLoaded('menuItems')),
        ];
    }
}
<?php

namespace Modules\Payment\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\PaymentMethod;

class ProcessPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Implement your authorization logic here
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'transaction_id' => 'sometimes|uuid|exists:transactions,id',
            'payment_method_id' => 'required|uuid|exists:payment_methods,id',
            'amount' => 'required|numeric|min:0.01|max:999999.99',
            'currency' => 'sometimes|string|size:3|in:USD,EUR,GBP,JPY,CAD,AUD,CHF,CNY,INR,BRL',
            'reference_number' => 'sometimes|string|max:100',
            'notes' => 'sometimes|string|max:1000',
            'metadata' => 'sometimes|array',
            
            // Cash payment specific fields
            'received_amount' => 'sometimes|numeric|min:0.01',
            
            // Card payment specific fields
            'card_number' => 'sometimes|string|regex:/^[0-9]{13,19}$/',
            'card_holder_name' => 'sometimes|string|max:100',
            'expiry_month' => 'sometimes|integer|between:1,12',
            'expiry_year' => 'sometimes|integer|min:' . date('Y'),
            'cvv' => 'sometimes|string|regex:/^[0-9]{3,4}$/',
            
            // Bank transfer specific fields
            'account_number' => 'sometimes|string|max:50',
            'routing_number' => 'sometimes|string|max:20',
            'bank_name' => 'sometimes|string|max:100',
            
            // Digital wallet specific fields
            'wallet_id' => 'sometimes|string|max:100',
            'wallet_token' => 'sometimes|string|max:255',
            
            // Crypto payment specific fields
            'wallet_address' => 'sometimes|string|max:255',
            'crypto_currency' => 'sometimes|string|in:BTC,ETH,LTC,XRP,ADA,DOT',
            
            // Additional verification fields
            'billing_address' => 'sometimes|array',
            'billing_address.street' => 'sometimes|string|max:255',
            'billing_address.city' => 'sometimes|string|max:100',
            'billing_address.state' => 'sometimes|string|max:100',
            'billing_address.postal_code' => 'sometimes|string|max:20',
            'billing_address.country' => 'sometimes|string|size:2',
            
            // Security fields
            'device_fingerprint' => 'sometimes|string|max:255',
            'ip_address' => 'sometimes|ip',
            'user_agent' => 'sometimes|string|max:500'
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'payment_method_id.required' => 'Payment method is required.',
            'payment_method_id.exists' => 'Selected payment method is invalid.',
            'amount.required' => 'Payment amount is required.',
            'amount.min' => 'Payment amount must be at least $0.01.',
            'amount.max' => 'Payment amount cannot exceed $999,999.99.',
            'currency.size' => 'Currency code must be exactly 3 characters.',
            'currency.in' => 'Currency is not supported.',
            'card_number.regex' => 'Card number must be 13-19 digits.',
            'expiry_month.between' => 'Expiry month must be between 1 and 12.',
            'expiry_year.min' => 'Expiry year cannot be in the past.',
            'cvv.regex' => 'CVV must be 3-4 digits.',
            'received_amount.min' => 'Received amount must be at least $0.01.',
            'crypto_currency.in' => 'Cryptocurrency is not supported.',
            'billing_address.country.size' => 'Country code must be exactly 2 characters.',
            'ip_address.ip' => 'IP address format is invalid.'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate payment method is active
            if ($this->has('payment_method_id')) {
                $paymentMethod = PaymentMethod::find($this->payment_method_id);
                if ($paymentMethod && !$paymentMethod->is_active) {
                    $validator->errors()->add('payment_method_id', 'Selected payment method is not available.');
                }
            }
            
            // Validate cash payment received amount
            if ($this->has('payment_method_id') && $this->has('received_amount')) {
                $paymentMethod = PaymentMethod::find($this->payment_method_id);
                if ($paymentMethod && $paymentMethod->type === 'cash') {
                    if ($this->received_amount < $this->amount) {
                        $validator->errors()->add('received_amount', 'Received amount cannot be less than payment amount.');
                    }
                }
            }
            
            // Validate card expiry date
            if ($this->has('expiry_month') && $this->has('expiry_year')) {
                $expiryDate = \Carbon\Carbon::createFromDate($this->expiry_year, $this->expiry_month, 1)->endOfMonth();
                if ($expiryDate->isPast()) {
                    $validator->errors()->add('expiry_month', 'Card has expired.');
                }
            }
            
            // Validate payment method specific required fields
            if ($this->has('payment_method_id')) {
                $paymentMethod = PaymentMethod::find($this->payment_method_id);
                if ($paymentMethod) {
                    $this->validatePaymentMethodFields($validator, $paymentMethod);
                }
            }
        });
    }

    /**
     * Validate payment method specific fields
     */
    protected function validatePaymentMethodFields($validator, PaymentMethod $paymentMethod): void
    {
        switch ($paymentMethod->type) {
            case 'card':
                $requiredFields = ['card_number', 'card_holder_name', 'expiry_month', 'expiry_year', 'cvv'];
                foreach ($requiredFields as $field) {
                    if (!$this->has($field)) {
                        $validator->errors()->add($field, ucfirst(str_replace('_', ' ', $field)) . ' is required for card payments.');
                    }
                }
                break;
                
            case 'bank_transfer':
                $requiredFields = ['account_number', 'routing_number'];
                foreach ($requiredFields as $field) {
                    if (!$this->has($field)) {
                        $validator->errors()->add($field, ucfirst(str_replace('_', ' ', $field)) . ' is required for bank transfers.');
                    }
                }
                break;
                
            case 'digital_wallet':
                if (!$this->has('wallet_id') && !$this->has('wallet_token')) {
                    $validator->errors()->add('wallet_id', 'Wallet ID or token is required for digital wallet payments.');
                }
                break;
                
            case 'crypto':
                $requiredFields = ['wallet_address', 'crypto_currency'];
                foreach ($requiredFields as $field) {
                    if (!$this->has($field)) {
                        $validator->errors()->add($field, ucfirst(str_replace('_', ' ', $field)) . ' is required for cryptocurrency payments.');
                    }
                }
                break;
        }
    }

    /**
     * Get validated data with defaults
     */
    public function validatedWithDefaults(): array
    {
        $validated = $this->validated();
        
        // Set default currency if not provided
        if (!isset($validated['currency'])) {
            $validated['currency'] = config('app.default_currency', 'USD');
        }
        
        // Set default metadata if not provided
        if (!isset($validated['metadata'])) {
            $validated['metadata'] = [];
        }
        
        // Add request metadata
        $validated['metadata']['request_ip'] = $this->ip();
        $validated['metadata']['user_agent'] = $this->userAgent();
        $validated['metadata']['timestamp'] = now()->toISOString();
        
        return $validated;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and format card number
        if ($this->has('card_number')) {
            $this->merge([
                'card_number' => preg_replace('/\D/', '', $this->card_number)
            ]);
        }
        
        // Ensure amount is properly formatted
        if ($this->has('amount')) {
            $this->merge([
                'amount' => round((float) $this->amount, 2)
            ]);
        }
        
        // Ensure received_amount is properly formatted
        if ($this->has('received_amount')) {
            $this->merge([
                'received_amount' => round((float) $this->received_amount, 2)
            ]);
        }
        
        // Uppercase currency code
        if ($this->has('currency')) {
            $this->merge([
                'currency' => strtoupper($this->currency)
            ]);
        }
    }
}
<?php

namespace Modules\Inventory\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;
use App\Models\BranchInventory;
use App\Models\User;
use Modules\Inventory\Notifications\LowStockAlert;
use Modules\Inventory\Helpers\InventoryHelper;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ProcessInventoryAlerts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $branchId;
    protected $alertTypes;

    /**
     * Create a new job instance.
     */
    public function __construct(string $branchId = null, array $alertTypes = [])
    {
        $this->branchId = $branchId;
        $this->alertTypes = $alertTypes ?: [
            'low_stock',
            'out_of_stock', 
            'overstocked',
            'expiring_soon',
            'expired'
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting inventory alerts processing', [
                'branch_id' => $this->branchId,
                'alert_types' => $this->alertTypes
            ]);

            $query = BranchInventory::with(['product', 'branch']);
            
            if ($this->branchId) {
                $query->where('branch_id', $this->branchId);
            }
            
            $inventories = $query->get();
            $alertsGenerated = 0;
            
            foreach ($inventories as $inventory) {
                $alerts = $this->checkInventoryAlerts($inventory);
                
                foreach ($alerts as $alertType) {
                    if (in_array($alertType, $this->alertTypes)) {
                        $this->sendAlert($inventory, $alertType);
                        $alertsGenerated++;
                    }
                }
            }
            
            Log::info('Inventory alerts processing completed', [
                'alerts_generated' => $alertsGenerated,
                'inventories_checked' => $inventories->count()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error processing inventory alerts', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * Check what alerts should be triggered for an inventory item.
     */
    protected function checkInventoryAlerts(BranchInventory $inventory): array
    {
        $alerts = [];
        
        // Check for out of stock
        if (InventoryHelper::isOutOfStock($inventory)) {
            $alerts[] = 'out_of_stock';
        }
        // Check for low stock (only if not out of stock)
        elseif (InventoryHelper::isLowStock($inventory)) {
            $alerts[] = 'low_stock';
        }
        
        // Check for overstocked
        if (InventoryHelper::isOverstocked($inventory)) {
            $alerts[] = 'overstocked';
        }
        
        // Check for expiry alerts
        if ($inventory->expiry_date) {
            if ($inventory->expiry_date < now()) {
                $alerts[] = 'expired';
            } elseif (InventoryHelper::isExpiringSoon($inventory, 7)) {
                $alerts[] = 'expiring_soon';
            }
        }
        
        return $alerts;
    }

    /**
     * Send alert notification.
     */
    protected function sendAlert(BranchInventory $inventory, string $alertType): void
    {
        try {
            // Check if we've already sent this alert recently (within last 24 hours)
            if ($this->hasRecentAlert($inventory, $alertType)) {
                return;
            }
            
            // Get users who should receive inventory alerts
            $users = $this->getAlertRecipients($inventory->branch_id, $alertType);
            
            if ($users->isEmpty()) {
                Log::warning('No recipients found for inventory alert', [
                    'inventory_id' => $inventory->id,
                    'alert_type' => $alertType,
                    'branch_id' => $inventory->branch_id
                ]);
                return;
            }
            
            // Send notification
            Notification::send($users, new LowStockAlert($inventory, $alertType));
            
            // Log the alert
            $this->logAlert($inventory, $alertType, $users->count());
            
            Log::info('Inventory alert sent', [
                'inventory_id' => $inventory->id,
                'product_name' => $inventory->product->name,
                'alert_type' => $alertType,
                'recipients_count' => $users->count()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error sending inventory alert', [
                'inventory_id' => $inventory->id,
                'alert_type' => $alertType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check if we've sent this alert recently.
     */
    protected function hasRecentAlert(BranchInventory $inventory, string $alertType): bool
    {
        // Check if there's a recent notification in the database
        // This would require a notifications table or alert log table
        // For now, we'll use a simple cache-based approach
        
        $cacheKey = "inventory_alert_{$inventory->id}_{$alertType}";
        
        if (cache()->has($cacheKey)) {
            return true;
        }
        
        // Set cache for 24 hours to prevent duplicate alerts
        cache()->put($cacheKey, true, now()->addHours(24));
        
        return false;
    }

    /**
     * Get users who should receive alerts for this branch and alert type.
     */
    protected function getAlertRecipients(string $branchId, string $alertType): \Illuminate\Database\Eloquent\Collection
    {
        // Get users with inventory management permissions for this branch
        // This would depend on your user/role system
        
        return User::whereHas('roles', function ($query) {
                $query->whereIn('name', ['admin', 'inventory_manager', 'branch_manager']);
            })
            ->where(function ($query) use ($branchId) {
                // Users assigned to this branch or global users
                $query->whereHas('branches', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                })->orWhereHas('roles', function ($q) {
                    $q->where('name', 'admin');
                });
            })
            ->where('is_active', true)
            ->get();
    }

    /**
     * Log the alert for audit purposes.
     */
    protected function logAlert(BranchInventory $inventory, string $alertType, int $recipientCount): void
    {
        // You could create an alert_logs table to track all alerts
        // For now, we'll use the inventory log system
        
        InventoryHelper::logAction(
            $inventory->id,
            'alert_sent',
            $inventory->current_stock,
            $inventory->current_stock,
            $alertType,
            "Alert sent to {$recipientCount} recipient(s)"
        );
    }

    /**
     * Get the number of times to retry the job.
     */
    public function tries(): int
    {
        return 3;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [60, 300, 900]; // 1 minute, 5 minutes, 15 minutes
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Inventory alerts job failed', [
            'branch_id' => $this->branchId,
            'alert_types' => $this->alertTypes,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
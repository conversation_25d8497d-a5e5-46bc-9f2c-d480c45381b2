<?php

namespace Modules\Settings\Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class NewSettingCategoriesSeeder extends Seeder
{
    public function run()
    {
        $this->seedKotSettings();
        $this->seedLanguageSettings();
        $this->seedNotificationSettings();
        $this->seedPusherSettings();
        $this->seedReceiptSettings();
        $this->seedReservationSettings();
        $this->seedBranchDeliverySettings();
        $this->seedEmailSettings();
        $this->seedFileStorageSettings();
        $this->seedFrontFaqSettings();
        $this->seedFrontReviewSettings();
    }

    private function seedKotSettings()
    {
        $kotSettings = [
            [
                'key' => 'kot_print_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable/disable KOT printing',
                'is_public' => false,
            ],
            [
                'key' => 'kot_printer_name',
                'value' => 'default',
                'data_type' => 'string',
                'description' => 'Default KOT printer name',
                'is_public' => false,
            ],
            [
                'key' => 'kot_template',
                'value' => 'standard',
                'data_type' => 'string',
                'description' => 'KOT template format',
                'is_public' => false,
            ],
            [
                'key' => 'kot_auto_print',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Automatically print KOT on order creation',
                'is_public' => false,
            ],
            [
                'key' => 'kot_include_customer_info',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Include customer information in KOT',
                'is_public' => false,
            ],
        ];

        foreach ($kotSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'kot_settings',
                ],
                $setting + ['category' => 'kot_settings']
            );
        }
    }

    private function seedLanguageSettings()
    {
        $languageSettings = [
            [
                'key' => 'default_language',
                'value' => 'en',
                'data_type' => 'string',
                'description' => 'Default system language',
                'is_public' => true,
            ],
            [
                'key' => 'available_languages',
                'value' => json_encode(['en', 'es', 'fr', 'de', 'it']),
                'data_type' => 'json',
                'description' => 'Available system languages',
                'is_public' => true,
            ],
            [
                'key' => 'date_format',
                'value' => 'Y-m-d',
                'data_type' => 'string',
                'description' => 'Default date format',
                'is_public' => true,
            ],
            [
                'key' => 'time_format',
                'value' => 'H:i:s',
                'data_type' => 'string',
                'description' => 'Default time format',
                'is_public' => true,
            ],
            [
                'key' => 'timezone',
                'value' => 'UTC',
                'data_type' => 'string',
                'description' => 'Default timezone',
                'is_public' => true,
            ],
            [
                'key' => 'currency_symbol',
                'value' => '$',
                'data_type' => 'string',
                'description' => 'Default currency symbol',
                'is_public' => true,
            ],
            [
                'key' => 'currency_position',
                'value' => 'before',
                'data_type' => 'string',
                'description' => 'Currency symbol position (before/after)',
                'is_public' => true,
            ],
        ];

        foreach ($languageSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'language_settings',
                ],
                $setting + ['category' => 'language_settings']
            );
        }
    }

    private function seedNotificationSettings()
    {
        $notificationSettings = [
            [
                'key' => 'email_notifications_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable email notifications',
                'is_public' => false,
            ],
            [
                'key' => 'sms_notifications_enabled',
                'value' => 'false',
                'data_type' => 'boolean',
                'description' => 'Enable SMS notifications',
                'is_public' => false,
            ],
            [
                'key' => 'push_notifications_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable push notifications',
                'is_public' => false,
            ],
            [
                'key' => 'order_notification_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable order notifications',
                'is_public' => false,
            ],
            [
                'key' => 'payment_notification_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable payment notifications',
                'is_public' => false,
            ],
            [
                'key' => 'inventory_notification_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable inventory notifications',
                'is_public' => false,
            ],
        ];

        foreach ($notificationSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'notification_settings',
                ],
                $setting + ['category' => 'notification_settings']
            );
        }
    }

    private function seedPusherSettings()
    {
        $pusherSettings = [
            [
                'key' => 'pusher_app_id',
                'value' => '',
                'data_type' => 'string',
                'description' => 'Pusher application ID',
                'is_public' => false,
            ],
            [
                'key' => 'pusher_key',
                'value' => '',
                'data_type' => 'string',
                'description' => 'Pusher application key',
                'is_public' => true,
            ],
            [
                'key' => 'pusher_secret',
                'value' => '',
                'data_type' => 'string',
                'description' => 'Pusher application secret',
                'is_public' => false,
            ],
            [
                'key' => 'pusher_cluster',
                'value' => 'mt1',
                'data_type' => 'string',
                'description' => 'Pusher cluster',
                'is_public' => true,
            ],
        ];

        foreach ($pusherSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'pusher_settings',
                ],
                $setting + ['category' => 'pusher_settings']
            );
        }
    }

    private function seedReceiptSettings()
    {
        $receiptSettings = [
            [
                'key' => 'receipt_template',
                'value' => 'default',
                'data_type' => 'string',
                'description' => 'Receipt template format',
                'is_public' => false,
            ],
            [
                'key' => 'receipt_printer_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable receipt printing',
                'is_public' => false,
            ],
            [
                'key' => 'receipt_email_enabled',
                'value' => 'false',
                'data_type' => 'boolean',
                'description' => 'Enable email receipts',
                'is_public' => false,
            ],
            [
                'key' => 'receipt_include_logo',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Include logo in receipts',
                'is_public' => false,
            ],
            [
                'key' => 'receipt_footer_text',
                'value' => 'Thank you for your business!',
                'data_type' => 'string',
                'description' => 'Receipt footer text',
                'is_public' => false,
            ],
        ];

        foreach ($receiptSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'receipt_settings',
                ],
                $setting + ['category' => 'receipt_settings']
            );
        }
    }

    private function seedReservationSettings()
    {
        $reservationSettings = [
            [
                'key' => 'reservations_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable table reservations',
                'is_public' => true,
            ],
            [
                'key' => 'max_reservation_days',
                'value' => '30',
                'data_type' => 'integer',
                'description' => 'Maximum days in advance for reservations',
                'is_public' => true,
            ],
            [
                'key' => 'reservation_time_slots',
                'value' => json_encode(['12:00', '12:30', '13:00', '13:30', '19:00', '19:30', '20:00', '20:30', '21:00']),
                'data_type' => 'json',
                'description' => 'Available reservation time slots',
                'is_public' => true,
            ],
            [
                'key' => 'min_reservation_duration',
                'value' => '60',
                'data_type' => 'integer',
                'description' => 'Minimum reservation duration in minutes',
                'is_public' => true,
            ],
            [
                'key' => 'max_reservation_duration',
                'value' => '180',
                'data_type' => 'integer',
                'description' => 'Maximum reservation duration in minutes',
                'is_public' => true,
            ],
        ];

        foreach ($reservationSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'reservation_settings',
                ],
                $setting + ['category' => 'reservation_settings']
            );
        }
    }

    private function seedBranchDeliverySettings()
    {
        $deliverySettings = [
            [
                'key' => 'delivery_enabled',
                'value' => 'false',
                'data_type' => 'boolean',
                'description' => 'Enable delivery service',
                'is_public' => true,
            ],
            [
                'key' => 'delivery_radius',
                'value' => '5.0',
                'data_type' => 'string',
                'description' => 'Delivery radius in kilometers',
                'is_public' => true,
            ],
            [
                'key' => 'delivery_fee',
                'value' => '0.0',
                'data_type' => 'string',
                'description' => 'Delivery fee amount',
                'is_public' => true,
            ],
            [
                'key' => 'min_order_for_delivery',
                'value' => '20.0',
                'data_type' => 'string',
                'description' => 'Minimum order amount for delivery',
                'is_public' => true,
            ],
            [
                'key' => 'estimated_delivery_time',
                'value' => '30',
                'data_type' => 'integer',
                'description' => 'Estimated delivery time in minutes',
                'is_public' => true,
            ],
        ];

        foreach ($deliverySettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'branch_delivery_settings',
                ],
                $setting + ['category' => 'branch_delivery_settings']
            );
        }
    }

    private function seedEmailSettings()
    {
        $emailSettings = [
            [
                'key' => 'smtp_host',
                'value' => 'localhost',
                'data_type' => 'string',
                'description' => 'SMTP server host',
                'is_public' => false,
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'data_type' => 'integer',
                'description' => 'SMTP server port',
                'is_public' => false,
            ],
            [
                'key' => 'smtp_username',
                'value' => '',
                'data_type' => 'string',
                'description' => 'SMTP username',
                'is_public' => false,
            ],
            [
                'key' => 'smtp_password',
                'value' => '',
                'data_type' => 'string',
                'description' => 'SMTP password',
                'is_public' => false,
            ],
            [
                'key' => 'from_email',
                'value' => '<EMAIL>',
                'data_type' => 'string',
                'description' => 'Default from email address',
                'is_public' => false,
            ],
            [
                'key' => 'from_name',
                'value' => 'Restaurant System',
                'data_type' => 'string',
                'description' => 'Default from name',
                'is_public' => false,
            ],
        ];

        foreach ($emailSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'email_settings',
                ],
                $setting + ['category' => 'email_settings']
            );
        }
    }

    private function seedFileStorageSettings()
    {
        $fileStorageSettings = [
            [
                'key' => 'storage_driver',
                'value' => 'local',
                'data_type' => 'string',
                'description' => 'File storage driver (local, s3, etc.)',
                'is_public' => false,
            ],
            [
                'key' => 'max_file_size',
                'value' => '10240',
                'data_type' => 'integer',
                'description' => 'Maximum file size in KB',
                'is_public' => false,
            ],
            [
                'key' => 'allowed_file_types',
                'value' => json_encode(['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']),
                'data_type' => 'json',
                'description' => 'Allowed file types for upload',
                'is_public' => false,
            ],
            [
                'key' => 'image_quality',
                'value' => '80',
                'data_type' => 'integer',
                'description' => 'Image compression quality (1-100)',
                'is_public' => false,
            ],
        ];

        foreach ($fileStorageSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'file_storage_settings',
                ],
                $setting + ['category' => 'file_storage_settings']
            );
        }
    }

    private function seedFrontFaqSettings()
    {
        $faqSettings = [
            [
                'key' => 'faq_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable FAQ section',
                'is_public' => true,
            ],
            [
                'key' => 'faq_categories',
                'value' => json_encode(['General', 'Orders', 'Payments', 'Delivery', 'Reservations']),
                'data_type' => 'json',
                'description' => 'FAQ categories',
                'is_public' => true,
            ],
            [
                'key' => 'faq_contact_info',
                'value' => 'For more questions, contact <NAME_EMAIL>',
                'data_type' => 'string',
                'description' => 'FAQ contact information',
                'is_public' => true,
            ],
        ];

        foreach ($faqSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'front_faq_settings',
                ],
                $setting + ['category' => 'front_faq_settings']
            );
        }
    }

    private function seedFrontReviewSettings()
    {
        $reviewSettings = [
            [
                'key' => 'reviews_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable customer reviews',
                'is_public' => true,
            ],
            [
                'key' => 'review_moderation_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable review moderation',
                'is_public' => false,
            ],
            [
                'key' => 'min_review_rating',
                'value' => '1',
                'data_type' => 'integer',
                'description' => 'Minimum review rating',
                'is_public' => true,
            ],
            [
                'key' => 'max_review_rating',
                'value' => '5',
                'data_type' => 'integer',
                'description' => 'Maximum review rating',
                'is_public' => true,
            ],
            [
                'key' => 'allow_anonymous_reviews',
                'value' => 'false',
                'data_type' => 'boolean',
                'description' => 'Allow anonymous reviews',
                'is_public' => true,
            ],
            [
                'key' => 'review_response_enabled',
                'value' => 'true',
                'data_type' => 'boolean',
                'description' => 'Enable restaurant responses to reviews',
                'is_public' => false,
            ],
        ];

        foreach ($reviewSettings as $setting) {
            Setting::updateOrCreate(
                [
                    'key' => $setting['key'],
                    'category' => 'front_review_settings',
                ],
                $setting + ['category' => 'front_review_settings']
            );
        }
    }
}
@extends('menu::layouts.app')

@section('title', $menu->name)

@section('header-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('menu.web.menus.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Menus
        </a>
        <a href="{{ route('menu.web.menus.edit', $menu->id) }}" class="btn btn-primary">
            <i class="bi bi-pencil me-1"></i>
            Edit Menu
        </a>
    </div>
@endsection

@section('content')
<div class="row">
    <!-- Menu Details -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Menu Details</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-4">Name:</dt>
                    <dd class="col-sm-8">{{ $menu->name }}</dd>
                    
                    <dt class="col-sm-4">Code:</dt>
                    <dd class="col-sm-8">
                        @if($menu->code)
                            <code>{{ $menu->code }}</code>
                        @else
                            <span class="text-muted">N/A</span>
                        @endif
                    </dd>
                    
                    <dt class="col-sm-4">Type:</dt>
                    <dd class="col-sm-8">
                        <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $menu->menu_type)) }}</span>
                    </dd>
                    
                    <dt class="col-sm-4">Status:</dt>
                    <dd class="col-sm-8">
                        @if($menu->is_active)
                            <span class="badge bg-success">Active</span>
                        @else
                            <span class="badge bg-secondary">Inactive</span>
                        @endif
                        @if($menu->is_default)
                            <span class="badge bg-primary ms-1">Default</span>
                        @endif
                    </dd>
                    
                    <dt class="col-sm-4">Time:</dt>
                    <dd class="col-sm-8">
                        @if($menu->start_time && $menu->end_time)
                            {{ $menu->start_time }} - {{ $menu->end_time }}
                        @else
                            <span class="text-muted">All Day</span>
                        @endif
                    </dd>
                    
                    <dt class="col-sm-4">Branch:</dt>
                    <dd class="col-sm-8">{{ $menu->branch->name ?? 'N/A' }}</dd>
                    
                    <dt class="col-sm-4">Sort Order:</dt>
                    <dd class="col-sm-8">{{ $menu->sort_order ?? 0 }}</dd>
                </dl>
                
                @if($menu->description)
                    <hr>
                    <h6>Description</h6>
                    <p class="text-muted">{{ $menu->description }}</p>
                @endif
                
                @if($menu->available_days)
                    <hr>
                    <h6>Available Days</h6>
                    <div class="d-flex flex-wrap gap-1">
                        @foreach(json_decode($menu->available_days, true) ?? [] as $day)
                            <span class="badge bg-light text-dark">{{ ucfirst($day) }}</span>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Categories -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Categories</h5>
                <a href="{{ route('menu.web.categories.create') }}?menu_id={{ $menu->id }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Category
                </a>
            </div>
            <div class="card-body">
                @if($menu->categories && $menu->categories->count() > 0)
                    <div class="row">
                        @foreach($menu->categories as $category)
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="card-title">{{ $category->name }}</h6>
                                                @if($category->description)
                                                    <p class="card-text text-muted small">{{ Str::limit($category->description, 80) }}</p>
                                                @endif
                                                <div class="d-flex align-items-center gap-2">
                                                    @if($category->is_active)
                                                        <span class="badge bg-success">Active</span>
                                                    @else
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    @endif
                                                    <small class="text-muted">{{ $category->menuItems->count() ?? 0 }} items</small>
                                                </div>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ route('menu.web.categories.show', $category->id) }}">View</a></li>
                                                    <li><a class="dropdown-item" href="{{ route('menu.web.categories.edit', $category->id) }}">Edit</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory({{ $category->id }})">Delete</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-grid-3x3-gap display-4 text-muted"></i>
                        <h6 class="mt-3">No Categories</h6>
                        <p class="text-muted">Add categories to organize your menu items.</p>
                        <a href="{{ route('menu.web.categories.create') }}?menu_id={{ $menu->id }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>
                            Add Category
                        </a>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- Recent Menu Items -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Menu Items</h5>
                <a href="{{ route('menu.web.menu-items.create') }}?menu_id={{ $menu->id }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Item
                </a>
            </div>
            <div class="card-body">
                @if($menu->menuItems && $menu->menuItems->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($menu->menuItems->take(5) as $item)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $item->name }}</h6>
                                    <p class="mb-1 text-muted small">{{ $item->category->name ?? 'No Category' }}</p>
                                    <small class="text-muted">${{ number_format($item->base_price, 2) }}</small>
                                </div>
                                <div>
                                    @if($item->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                    @if($menu->menuItems->count() > 5)
                        <div class="text-center mt-3">
                            <a href="{{ route('menu.web.menu-items.index') }}?menu_id={{ $menu->id }}" class="btn btn-outline-primary btn-sm">
                                View All Items ({{ $menu->menuItems->count() }})
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-3">
                        <i class="bi bi-card-list display-6 text-muted"></i>
                        <h6 class="mt-2">No Menu Items</h6>
                        <p class="text-muted small">Start adding items to this menu.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this category? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteCategoryForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteCategory(categoryId) {
    const deleteForm = document.getElementById('deleteCategoryForm');
    deleteForm.action = `/menu/categories/${categoryId}`;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteCategoryModal'));
    deleteModal.show();
}
</script>
@endpush
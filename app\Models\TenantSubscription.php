<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TenantSubscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'subscription_plan_id',
        'status',
        'started_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'billing_cycle',
        'price',
        'currency',
        'payment_method',
        'last_payment_at',
        'next_payment_at',
        'auto_renew',
        'cancellation_reason',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'started_at' => 'datetime',
            'ends_at' => 'datetime',
            'trial_ends_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'last_payment_at' => 'datetime',
            'next_payment_at' => 'datetime',
            'price' => 'decimal:2',
            'auto_renew' => 'boolean',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function subscriptionPlan()
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('ends_at', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('ends_at', '<=', now());
    }

    public function scopeTrialing($query)
    {
        return $query->where('status', 'trialing')
                    ->where('trial_ends_at', '>', now());
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeAutoRenewing($query)
    {
        return $query->where('auto_renew', true);
    }

    public function scopeExpiringIn($query, $days)
    {
        return $query->where('ends_at', '<=', now()->addDays($days))
                    ->where('ends_at', '>', now());
    }

    // Methods
    public function isActive()
    {
        return $this->status === 'active' && $this->ends_at > now();
    }

    public function isTrialing()
    {
        return $this->status === 'trialing' && $this->trial_ends_at > now();
    }

    public function isExpired()
    {
        return $this->ends_at <= now();
    }

    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    public function isGracePeriod()
    {
        return $this->status === 'past_due' && $this->ends_at > now();
    }

    public function daysUntilExpiry()
    {
        if ($this->ends_at <= now()) {
            return 0;
        }

        return now()->diffInDays($this->ends_at);
    }

    public function daysUntilTrialExpiry()
    {
        if (!$this->trial_ends_at || $this->trial_ends_at <= now()) {
            return 0;
        }

        return now()->diffInDays($this->trial_ends_at);
    }

    public function cancel($reason = null)
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'auto_renew' => false,
        ]);
    }

    public function renew($duration = null)
    {
        $duration = $duration ?? $this->getBillingCycleDuration();
        
        $this->update([
            'status' => 'active',
            'ends_at' => now()->add($duration),
            'last_payment_at' => now(),
            'next_payment_at' => now()->add($duration),
        ]);
    }

    public function suspend()
    {
        $this->update([
            'status' => 'suspended',
        ]);
    }

    public function reactivate()
    {
        $this->update([
            'status' => 'active',
        ]);
    }

    private function getBillingCycleDuration()
    {
        switch ($this->billing_cycle) {
            case 'monthly':
                return '1 month';
            case 'quarterly':
                return '3 months';
            case 'yearly':
                return '1 year';
            default:
                return '1 month';
        }
    }

    public function hasFeature($feature)
    {
        return $this->subscriptionPlan->hasFeature($feature);
    }

    public function withinLimit($limitation, $currentUsage)
    {
        $limit = $this->subscriptionPlan->getLimitValue($limitation);
        
        if ($limit === null) {
            return true; // No limit
        }
        
        return $currentUsage < $limit;
    }
}
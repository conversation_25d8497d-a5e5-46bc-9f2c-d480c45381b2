<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payslips', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('pay_period_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('set null');
            $table->string('payslip_number', 50)->unique();
            
            // Employee details at time of payroll
            $table->string('employee_name', 255);
            $table->string('employee_email', 255);
            $table->string('employee_id', 50)->nullable();
            $table->string('position', 100)->nullable();
            $table->string('department', 100)->nullable();
            
            // Pay calculation details
            $table->decimal('hourly_rate', 8, 2)->default(0);
            $table->decimal('salary', 10, 2)->default(0);
            $table->decimal('regular_hours', 8, 2)->default(0);
            $table->decimal('overtime_hours', 8, 2)->default(0);
            $table->decimal('overtime_rate', 8, 2)->default(0);
            $table->decimal('holiday_hours', 8, 2)->default(0);
            $table->decimal('sick_hours', 8, 2)->default(0);
            $table->decimal('vacation_hours', 8, 2)->default(0);
            
            // Earnings
            $table->decimal('regular_pay', 10, 2)->default(0);
            $table->decimal('overtime_pay', 10, 2)->default(0);
            $table->decimal('holiday_pay', 10, 2)->default(0);
            $table->decimal('bonus', 10, 2)->default(0);
            $table->decimal('commission', 10, 2)->default(0);
            $table->decimal('allowances', 10, 2)->default(0);
            $table->decimal('gross_pay', 10, 2)->default(0);
            
            // Deductions
            $table->decimal('tax_deduction', 10, 2)->default(0);
            $table->decimal('social_security', 10, 2)->default(0);
            $table->decimal('health_insurance', 10, 2)->default(0);
            $table->decimal('retirement_contribution', 10, 2)->default(0);
            $table->decimal('other_deductions', 10, 2)->default(0);
            $table->decimal('total_deductions', 10, 2)->default(0);
            
            // Final amounts
            $table->decimal('net_pay', 10, 2)->default(0);
            $table->decimal('year_to_date_gross', 12, 2)->default(0);
            $table->decimal('year_to_date_net', 12, 2)->default(0);
            
            // Status and processing
            $table->enum('status', ['draft', 'approved', 'paid', 'cancelled'])->default('draft');
            $table->datetime('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('paid_at')->nullable();
            $table->string('payment_method', 50)->nullable();
            $table->string('payment_reference', 100)->nullable();
            
            // Additional information
            $table->json('earnings_breakdown')->nullable(); // Detailed breakdown of earnings
            $table->json('deductions_breakdown')->nullable(); // Detailed breakdown of deductions
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['tenant_id', 'pay_period_id']);
            $table->index(['tenant_id', 'user_id']);
            $table->index(['tenant_id', 'status']);
            $table->index(['payslip_number']);
            $table->unique(['pay_period_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payslips');
    }
};
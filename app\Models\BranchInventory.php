<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchInventory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_id',
        'product_id',
        'current_stock',
        'reserved_stock',
        'available_stock',
        'minimum_stock_level',
        'maximum_stock_level',
        'reorder_point',
        'last_restocked_at',
        'last_counted_at',
        'unit_cost',
        'total_value',
        'location',
        'batch_number',
        'expiry_date',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'current_stock' => 'decimal:6',
            'reserved_stock' => 'decimal:6',
            'available_stock' => 'decimal:6',
            'minimum_stock_level' => 'decimal:6',
            'maximum_stock_level' => 'decimal:6',
            'reorder_point' => 'decimal:6',
            'unit_cost' => 'decimal:4',
            'total_value' => 'decimal:2',
            'last_restocked_at' => 'datetime',
            'last_counted_at' => 'datetime',
            'expiry_date' => 'date',
        ];
    }

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Scopes
    public function scopeLowStock($query)
    {
        return $query->whereColumn('current_stock', '<=', 'minimum_stock_level');
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('current_stock', '<=', 0);
    }

    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->whereNotNull('expiry_date');
    }

    public function logs()
    {
        return $this->hasMany(InventoryLog::class);
    }

    public function movements()
    {
        return $this->hasMany(InventoryMovement::class);
    }
}
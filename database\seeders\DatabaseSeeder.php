<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Database\Seeders\SettingsSeeder;
use Database\Seeders\RolesAndPermissionsSeeder;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

     
  
        
        // Seed settings and tables for all branches
        $this->call([
  
          
            RolesAndPermissionsSeeder::class,
            BranchSeeder::class,
            MenuCategorySeeder::class,
            MenuSeeder::class,
            MenuItemSeeder::class,
            UserSeeder::class,
            TenantSeeder::class,
            // TenantSubscriptionSeeder::class,
            // SubscriptionPlanSeeder::class,
            CustomerSeeder::class,
            OrderSeeder::class,
            AreaSeeder::class,
            ReservationSeeder::class,
            OrderItemSeeder::class,
            OrderItemAddonSeeder::class,
            MenuItemVariantSeeder::class,
            MenuItemAddonSeeder::class,
            MenuItemBranchSeeder::class,
            MenuAvailabilitySeeder::class,
            // Add calls to all model seeders here
            // Example: UserSeeder::class,
            // Add all generated seeders below
        ]);

        // // Seed plans
        // \App\Models\SubscriptionPlan::factory()->count(3)->create();
        // // Seed tenants
        // \App\Models\Tenant::factory()->count(5)->create();
        // // Seed tenant subscriptions
        // \App\Models\TenantSubscription::factory()->count(5)->create();
    }
}

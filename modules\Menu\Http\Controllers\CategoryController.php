<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreCategoryRequest;
use Modules\Menu\Http\Requests\UpdateCategoryRequest;
use Modules\Menu\Http\Resources\CategoryResource;
use Modules\Menu\Http\Resources\MenuItemResource;

class CategoryController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of menu categories.
     */
    public function index(): JsonResponse
    {
        $categories = $this->menuService->getCategories();
        return response()->json(CategoryResource::collection($categories));
    }

    /**
     * Store a newly created category.
     */
    public function store(StoreCategoryRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        $category = $this->menuService->createCategory($data);
        return response()->json(new CategoryResource($category), 201);
    }

    /**
     * Display the specified category.
     */
    public function show(string $id): JsonResponse
    {
        $category = $this->menuService->getCategoryById($id);
        return response()->json(new CategoryResource($category));
    }

    /**
     * Update the specified category.
     */
    public function update(UpdateCategoryRequest $request, string $id): JsonResponse
    {
        $category = $this->menuService->updateCategory($id, $request->validated());
        return response()->json(new CategoryResource($category));
    }

    /**
     * Remove the specified category.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $this->menuService->deleteCategory($id);
            return response()->json(['message' => 'Category deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Get category with its menu items.
     */
    public function getCategoryWithItems(string $id): JsonResponse
    {
        $category = $this->menuService->getCategoryWithItems($id);
        return response()->json(new CategoryResource($category));
    }

    /**
     * Get menu items by category.
     */
    public function getItemsByCategory(string $categoryId): JsonResponse
    {
        $items = $this->menuService->getItemsByCategory($categoryId);
        return response()->json(MenuItemResource::collection($items));
    }
}
<?php

namespace Modules\Inventory\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Notifications\Notification;
use App\Models\BranchInventory;
use App\Models\Product;
use App\Models\Branch;

class LowStockAlert extends Notification implements ShouldQueue
{
    use Queueable;

    protected $branchInventory;
    protected $product;
    protected $branch;
    protected $alertType;

    /**
     * Create a new notification instance.
     */
    public function __construct(BranchInventory $branchInventory, string $alertType = 'low_stock')
    {
        $this->branchInventory = $branchInventory;
        $this->product = $branchInventory->product;
        $this->branch = $branchInventory->branch;
        $this->alertType = $alertType;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $subject = $this->getSubject();
        $greeting = $this->getGreeting();
        $message = $this->getMessage();
        
        return (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($message)
            ->line('Product: ' . $this->product->name)
            ->line('SKU: ' . $this->product->sku)
            ->line('Branch: ' . $this->branch->name)
            ->line('Current Stock: ' . number_format($this->branchInventory->current_stock, 2) . ' ' . $this->product->unit)
            ->line('Minimum Level: ' . number_format($this->branchInventory->minimum_stock_level, 2) . ' ' . $this->product->unit)
            ->action('View Inventory', url('/inventory/items/' . $this->branchInventory->id))
            ->line('Please take immediate action to restock this item.');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase($notifiable): array
    {
        return [
            'type' => $this->alertType,
            'title' => $this->getSubject(),
            'message' => $this->getMessage(),
            'data' => [
                'branch_inventory_id' => $this->branchInventory->id,
                'product_id' => $this->product->id,
                'branch_id' => $this->branch->id,
                'product_name' => $this->product->name,
                'product_sku' => $this->product->sku,
                'branch_name' => $this->branch->name,
                'current_stock' => $this->branchInventory->current_stock,
                'minimum_level' => $this->branchInventory->minimum_stock_level,
                'maximum_level' => $this->branchInventory->maximum_stock_level,
                'unit' => $this->product->unit,
                'alert_level' => $this->getAlertLevel(),
            ],
            'action_url' => url('/inventory/items/' . $this->branchInventory->id),
            'created_at' => now(),
        ];
    }

    /**
     * Get the notification subject based on alert type.
     */
    protected function getSubject(): string
    {
        switch ($this->alertType) {
            case 'out_of_stock':
                return 'Out of Stock Alert - ' . $this->product->name;
            case 'low_stock':
                return 'Low Stock Alert - ' . $this->product->name;
            case 'overstocked':
                return 'Overstock Alert - ' . $this->product->name;
            case 'expiring_soon':
                return 'Expiring Soon Alert - ' . $this->product->name;
            case 'expired':
                return 'Expired Product Alert - ' . $this->product->name;
            default:
                return 'Inventory Alert - ' . $this->product->name;
        }
    }

    /**
     * Get the notification greeting.
     */
    protected function getGreeting(): string
    {
        switch ($this->alertType) {
            case 'out_of_stock':
                return 'Critical Stock Alert!';
            case 'low_stock':
                return 'Low Stock Warning!';
            case 'overstocked':
                return 'Overstock Notice';
            case 'expiring_soon':
            case 'expired':
                return 'Product Expiry Alert!';
            default:
                return 'Inventory Alert';
        }
    }

    /**
     * Get the notification message.
     */
    protected function getMessage(): string
    {
        switch ($this->alertType) {
            case 'out_of_stock':
                return 'The following product is completely out of stock and requires immediate attention.';
            case 'low_stock':
                return 'The following product has fallen below the minimum stock level and needs to be restocked.';
            case 'overstocked':
                return 'The following product has exceeded the maximum stock level. Consider reducing orders or implementing promotions.';
            case 'expiring_soon':
                $daysUntilExpiry = $this->branchInventory->expiry_date ? 
                    now()->diffInDays($this->branchInventory->expiry_date) : 0;
                return "The following product will expire in {$daysUntilExpiry} day(s). Please prioritize its sale or usage.";
            case 'expired':
                return 'The following product has expired and should be removed from inventory immediately.';
            default:
                return 'An inventory alert has been triggered for the following product.';
        }
    }

    /**
     * Get the alert level (priority).
     */
    protected function getAlertLevel(): string
    {
        switch ($this->alertType) {
            case 'out_of_stock':
            case 'expired':
                return 'critical';
            case 'low_stock':
            case 'expiring_soon':
                return 'high';
            case 'overstocked':
                return 'medium';
            default:
                return 'low';
        }
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType($notifiable): string
    {
        return 'inventory_alert';
    }
}
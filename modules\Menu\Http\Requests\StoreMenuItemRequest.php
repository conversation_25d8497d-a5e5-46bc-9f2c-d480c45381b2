<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMenuItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'menu_id' => 'required|exists:menus,id',
            'category_id' => 'required|exists:menu_categories,id',
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50',
            'description' => 'nullable|string|max:1000',
            'short_description' => 'nullable|string|max:500',
            'base_price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'image_urls' => 'nullable|array',
            'image_urls.*' => 'nullable|url|max:500',
            'prep_time_minutes' => 'nullable|integer|min:0|max:300',
            'calories' => 'nullable|integer|min:0',
            'nutritional_info' => 'nullable|array',
            'allergens' => 'nullable|array',
            'dietary_info' => 'nullable|array',
            'recipe_id' => 'nullable|exists:recipes,id',
            'barcode' => 'nullable|string|max:100',
            'sku' => 'nullable|string|max:100',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'is_spicy' => 'boolean',
            'spice_level' => 'nullable|integer|min:1|max:5',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_id.required' => 'Menu is required.',
            'menu_id.exists' => 'Selected menu does not exist.',
            'name.required' => 'Menu item name is required.',
            'name.max' => 'Menu item name cannot exceed 255 characters.',
            'code.required' => 'Menu item code is required.',
            'code.max' => 'Menu item code cannot exceed 50 characters.',
            'category_id.required' => 'Menu category is required.',
            'category_id.exists' => 'Selected menu category does not exist.',
            'base_price.required' => 'Base price is required.',
            'base_price.numeric' => 'Base price must be a valid number.',
            'base_price.min' => 'Base price must be greater than or equal to 0.',
            'cost_price.numeric' => 'Cost price must be a valid number.',
            'cost_price.min' => 'Cost price must be greater than or equal to 0.',
            'prep_time_minutes.integer' => 'Preparation time must be a valid number.',
            'prep_time_minutes.min' => 'Preparation time must be greater than or equal to 0.',
            'prep_time_minutes.max' => 'Preparation time cannot exceed 300 minutes.',
            'calories.integer' => 'Calories must be a valid number.',
            'calories.min' => 'Calories must be greater than or equal to 0.',
            'image_urls.array' => 'Image URLs must be an array.',
            'image_urls.*.url' => 'Each image URL must be a valid URL.',
            'nutritional_info.array' => 'Nutritional information must be an array.',
            'allergens.array' => 'Allergens must be an array.',
            'dietary_info.array' => 'Dietary information must be an array.',
            'spice_level.integer' => 'Spice level must be a valid number.',
            'spice_level.min' => 'Spice level must be at least 1.',
            'spice_level.max' => 'Spice level cannot exceed 5.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => 'menu category',
            'cost_price' => 'cost price',
            'prep_time_minutes' => 'preparation time',
            'is_active' => 'availability status',
            'is_featured' => 'featured status',
            'sort_order' => 'sort order',
            'image_urls' => 'image URLs',
            'dietary_info' => 'dietary information',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert boolean strings to actual booleans
        foreach ([
            'is_active', 'is_featured', 'is_spicy'
        ] as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->$field, FILTER_VALIDATE_BOOLEAN)
                ]);
            }
        }
    }
}
<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DocsController;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

Route::group(
    [
        'prefix' => LaravelLocalization::setLocale(),
        'middleware' => [
            'localeSessionRedirect',
            'localizationRedirect',
            'localeViewPath'
        ]
    ],
    function () {
Route::get('/', function () {
    return view('welcome');
});

        Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
            
            // Dashboard AJAX routes
            Route::post('/toggle-theme', [DashboardController::class, 'toggleTheme'])->name('toggle.theme');
            Route::post('/notifications/mark-read', [DashboardController::class, 'markNotificationRead'])->name('notifications.mark-read');
            Route::get('/dashboard/stats', [DashboardController::class, 'getStats'])->name('dashboard.stats');
            Route::get('/dashboard/chart-data', [DashboardController::class, 'getChartData'])->name('dashboard.chart-data');
        });

        Route::get('/docs', function () {
            return view('docs.index');
        })->name('docs');

require __DIR__.'/auth.php';
    }
);

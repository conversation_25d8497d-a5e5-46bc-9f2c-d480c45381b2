<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class SubmitLeaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'leave_type' => [
                'required',
                'string',
                Rule::in(['vacation', 'sick', 'personal', 'emergency'])
            ],
            'start_date' => [
                'required',
                'date',
                'after_or_equal:today'
            ],
            'end_date' => [
                'required',
                'date',
                'after_or_equal:start_date'
            ],
            'reason' => [
                'nullable',
                'string',
                'max:1000'
            ],
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'leave_type.required' => 'Leave type is required.',
            'leave_type.in' => 'Invalid leave type. Must be one of: vacation, sick, personal, emergency.',
            'start_date.required' => 'Start date is required.',
            'start_date.after_or_equal' => 'Start date cannot be in the past.',
            'end_date.required' => 'End date is required.',
            'end_date.after_or_equal' => 'End date must be on or after the start date.',
            'reason.max' => 'Reason cannot exceed 1000 characters.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if the leave duration is reasonable (not more than 365 days)
            if ($this->start_date && $this->end_date) {
                $startDate = Carbon::parse($this->start_date);
                $endDate = Carbon::parse($this->end_date);
                $daysDiff = $startDate->diffInDays($endDate) + 1;
                
                if ($daysDiff > 365) {
                    $validator->errors()->add('end_date', 'Leave duration cannot exceed 365 days.');
                }
            }

            // Check for overlapping leave requests
            if ($this->start_date && $this->end_date && Auth::check()) {
                $overlappingLeave = \App\Models\LeaveRequest::where('user_id', Auth::id())
                    ->where('tenant_id', Auth::user()->tenant_id)
                    ->where('status', '!=', 'rejected')
                    ->where('status', '!=', 'cancelled')
                    ->where(function ($query) {
                        $query->whereBetween('start_date', [$this->start_date, $this->end_date])
                              ->orWhereBetween('end_date', [$this->start_date, $this->end_date])
                              ->orWhere(function ($q) {
                                  $q->where('start_date', '<=', $this->start_date)
                                    ->where('end_date', '>=', $this->end_date);
                              });
                    })
                    ->exists();

                if ($overlappingLeave) {
                    $validator->errors()->add('start_date', 'You already have a leave request for this period.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Ensure dates are in the correct format
        if ($this->start_date) {
            $this->merge([
                'start_date' => Carbon::parse($this->start_date)->format('Y-m-d')
            ]);
        }
        
        if ($this->end_date) {
            $this->merge([
                'end_date' => Carbon::parse($this->end_date)->format('Y-m-d')
            ]);
        }
    }
}
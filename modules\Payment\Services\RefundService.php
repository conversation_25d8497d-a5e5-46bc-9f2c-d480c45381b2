<?php

namespace Modules\Payment\Services;

use App\Models\Payment;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Exception;

class RefundService
{
    /**
     * Process a refund
     */
    public function processRefund(Payment $payment, array $data): Payment
    {
        return DB::transaction(function () use ($payment, $data) {
            // Validate refund eligibility
            $this->validateRefundEligibility($payment, $data['amount']);

            // Create refund payment record
            $refund = Payment::create([
                'id' => Str::uuid(),
                'payment_number' => $this->generateRefundNumber(),
                'transaction_id' => $payment->transaction_id,
                'payment_method_uuid' => $payment->payment_method_uuid,
                'amount' => -abs($data['amount']), // Negative amount for refund
                'currency' => $payment->currency,
                'status' => 'pending',
                'payment_date' => now(),
                'reference_number' => $data['reference_number'] ?? null,
                'notes' => $data['notes'] ?? null,
                'processed_by' => Auth::id(),
                'parent_payment_id' => $payment->id,
                'metadata' => array_merge($data['metadata'] ?? [], [
                    'refund_type' => $data['refund_type'] ?? 'full',
                    'refund_reason' => $data['refund_reason'] ?? null,
                    'original_payment_id' => $payment->id
                ])
            ]);

            // Process refund based on original payment method
            $this->processRefundByMethod($refund, $payment, $data);

            // Update original payment if full refund
            if (abs($data['amount']) >= $payment->amount) {
                $payment->update(['status' => 'refunded']);
            }

            // Update transaction status
            $this->updateTransactionRefundStatus($payment->transaction_id);

            // Log refund activity
            $this->logRefundActivity($refund, 'refund_processed');

            return $refund->fresh();
        });
    }

    /**
     * Validate refund eligibility
     */
    protected function validateRefundEligibility(Payment $payment, float $refundAmount): void
    {
        if ($payment->status !== 'completed') {
            throw new Exception('Only completed payments can be refunded');
        }

        if ($refundAmount <= 0) {
            throw new Exception('Refund amount must be greater than zero');
        }

        if ($refundAmount > $payment->amount) {
            throw new Exception('Refund amount cannot exceed original payment amount');
        }

        // Check if payment has already been refunded
        $existingRefunds = Payment::where('parent_payment_id', $payment->id)
            ->where('status', 'completed')
            ->sum('amount');

        $totalRefundable = $payment->amount + $existingRefunds; // existingRefunds is negative
        
        if ($refundAmount > $totalRefundable) {
            throw new Exception('Refund amount exceeds refundable balance');
        }

        // Check refund time limit (if applicable)
        $refundTimeLimit = config('payment.refund_time_limit_days', 30);
        if ($refundTimeLimit && $payment->created_at->diffInDays(now()) > $refundTimeLimit) {
            throw new Exception('Refund time limit exceeded');
        }
    }

    /**
     * Process refund based on payment method
     */
    protected function processRefundByMethod(Payment $refund, Payment $originalPayment, array $data): void
    {
        $paymentMethod = $originalPayment->paymentMethod;
        
        switch ($paymentMethod->type) {
            case 'cash':
                $this->processCashRefund($refund, $data);
                break;
            case 'card':
                $this->processCardRefund($refund, $originalPayment, $data);
                break;
            case 'digital_wallet':
                $this->processDigitalWalletRefund($refund, $originalPayment, $data);
                break;
            case 'bank_transfer':
                $this->processBankTransferRefund($refund, $data);
                break;
            case 'crypto':
                $this->processCryptoRefund($refund, $data);
                break;
            default:
                throw new Exception('Refunds not supported for payment method: ' . $paymentMethod->type);
        }
    }

    /**
     * Process cash refund
     */
    protected function processCashRefund(Payment $refund, array $data): void
    {
        $refund->update([
            'status' => 'completed',
            'processed_at' => now(),
            'gateway_response' => [
                'type' => 'cash_refund',
                'refunded_amount' => abs($refund->amount),
                'refund_method' => 'cash'
            ]
        ]);
    }

    /**
     * Process card refund
     */
    protected function processCardRefund(Payment $refund, Payment $originalPayment, array $data): void
    {
        // Simulate card refund processing (integrate with actual payment gateway)
        $gatewayResponse = $this->processRefundWithGateway($originalPayment, $refund, $data);
        
        $refund->update([
            'status' => $gatewayResponse['status'],
            'processed_at' => $gatewayResponse['status'] === 'completed' ? now() : null,
            'gateway_transaction_id' => $gatewayResponse['refund_id'] ?? null,
            'gateway_response' => $gatewayResponse
        ]);
    }

    /**
     * Process digital wallet refund
     */
    protected function processDigitalWalletRefund(Payment $refund, Payment $originalPayment, array $data): void
    {
        // Simulate digital wallet refund processing
        $gatewayResponse = $this->processRefundWithGateway($originalPayment, $refund, $data);
        
        $refund->update([
            'status' => $gatewayResponse['status'],
            'processed_at' => $gatewayResponse['status'] === 'completed' ? now() : null,
            'gateway_transaction_id' => $gatewayResponse['refund_id'] ?? null,
            'gateway_response' => $gatewayResponse
        ]);
    }

    /**
     * Process bank transfer refund
     */
    protected function processBankTransferRefund(Payment $refund, array $data): void
    {
        $refund->update([
            'status' => 'processing',
            'gateway_response' => [
                'type' => 'bank_transfer_refund',
                'refund_method' => 'bank_transfer',
                'estimated_completion' => now()->addBusinessDays(3)->toISOString()
            ]
        ]);
    }

    /**
     * Process cryptocurrency refund
     */
    protected function processCryptoRefund(Payment $refund, array $data): void
    {
        $refund->update([
            'status' => 'processing',
            'gateway_response' => [
                'type' => 'crypto_refund',
                'refund_method' => 'crypto',
                'wallet_address' => $data['refund_wallet_address'] ?? null
            ]
        ]);
    }

    /**
     * Simulate gateway refund processing
     */
    protected function processRefundWithGateway(Payment $originalPayment, Payment $refund, array $data): array
    {
        // This is a simulation - replace with actual gateway integration
        $success = rand(1, 100) > 2; // 98% success rate for refunds
        
        return [
            'status' => $success ? 'completed' : 'failed',
            'refund_id' => $success ? 'rf_' . Str::random(16) : null,
            'original_transaction_id' => $originalPayment->gateway_transaction_id,
            'gateway' => $originalPayment->paymentMethod->provider,
            'response_code' => $success ? '00' : '05',
            'response_message' => $success ? 'Refund approved' : 'Refund declined',
            'refund_amount' => abs($refund->amount),
            'processed_at' => now()->toISOString()
        ];
    }

    /**
     * Update transaction refund status
     */
    protected function updateTransactionRefundStatus(string $transactionId): void
    {
        $transaction = Transaction::findOrFail($transactionId);
        
        // Calculate total refunded amount
        $totalRefunded = abs($transaction->payments()
            ->where('status', 'completed')
            ->where('amount', '<', 0)
            ->sum('amount'));
        
        // Calculate total paid amount
        $totalPaid = $transaction->payments()
            ->where('status', 'completed')
            ->where('amount', '>', 0)
            ->sum('amount');
        
        // Update transaction status based on refund status
        if ($totalRefunded >= $totalPaid) {
            $transaction->update(['status' => 'refunded']);
        } elseif ($totalRefunded > 0) {
            $transaction->update(['status' => 'partially_refunded']);
        }
    }

    /**
     * Get refund details
     */
    public function getRefundDetails(Payment $refund): array
    {
        $originalPayment = Payment::find($refund->parent_payment_id);
        
        return [
            'refund' => $refund->load(['paymentMethod', 'transaction', 'processedBy']),
            'original_payment' => $originalPayment,
            'refund_amount' => abs($refund->amount),
            'refund_percentage' => $originalPayment ? (abs($refund->amount) / $originalPayment->amount) * 100 : 0,
            'remaining_refundable' => $originalPayment ? $this->getRemainingRefundableAmount($originalPayment) : 0
        ];
    }

    /**
     * Get remaining refundable amount for a payment
     */
    public function getRemainingRefundableAmount(Payment $payment): float
    {
        if ($payment->status !== 'completed') {
            return 0;
        }
        
        $totalRefunded = abs(Payment::where('parent_payment_id', $payment->id)
            ->where('status', 'completed')
            ->sum('amount'));
        
        return max(0, $payment->amount - $totalRefunded);
    }

    /**
     * Get refund analytics
     */
    public function getRefundAnalytics(array $filters = []): array
    {
        $query = Payment::where('amount', '<', 0); // Refunds have negative amounts
        
        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        $refunds = $query->with(['paymentMethod'])->get();
        
        $totalRefunds = $refunds->count();
        $totalRefundAmount = abs($refunds->sum('amount'));
        
        // Get refund reasons breakdown
        $reasonBreakdown = $refunds->groupBy(function ($refund) {
            return $refund->metadata['refund_reason'] ?? 'Not specified';
        })->map(function ($group) {
            return [
                'count' => $group->count(),
                'total_amount' => abs($group->sum('amount'))
            ];
        });
        
        // Get refund method breakdown
        $methodBreakdown = $refunds->groupBy('paymentMethod.type')
            ->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_amount' => abs($group->sum('amount'))
                ];
            });
        
        // Get daily refund trends
        $dailyTrends = $refunds->groupBy(function ($refund) {
            return $refund->created_at->format('Y-m-d');
        })->map(function ($group) {
            return [
                'count' => $group->count(),
                'total_amount' => abs($group->sum('amount'))
            ];
        });
        
        return [
            'summary' => [
                'total_refunds' => $totalRefunds,
                'total_refund_amount' => $totalRefundAmount,
                'average_refund_amount' => $totalRefunds > 0 ? $totalRefundAmount / $totalRefunds : 0,
                'refund_rate' => $this->calculateRefundRate($filters)
            ],
            'reason_breakdown' => $reasonBreakdown,
            'method_breakdown' => $methodBreakdown,
            'daily_trends' => $dailyTrends
        ];
    }

    /**
     * Calculate refund rate
     */
    protected function calculateRefundRate(array $filters = []): float
    {
        $paymentQuery = Payment::where('amount', '>', 0);
        $refundQuery = Payment::where('amount', '<', 0);
        
        // Apply same date filters to both queries
        if (isset($filters['date_from'])) {
            $paymentQuery->whereDate('created_at', '>=', $filters['date_from']);
            $refundQuery->whereDate('created_at', '>=', $filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $paymentQuery->whereDate('created_at', '<=', $filters['date_to']);
            $refundQuery->whereDate('created_at', '<=', $filters['date_to']);
        }
        
        $totalPayments = $paymentQuery->count();
        $totalRefunds = $refundQuery->count();
        
        return $totalPayments > 0 ? ($totalRefunds / $totalPayments) * 100 : 0;
    }

    /**
     * Generate unique refund number
     */
    protected function generateRefundNumber(): string
    {
        $prefix = 'REF';
        $date = now()->format('Ymd');
        $sequence = Payment::where('amount', '<', 0)
            ->whereDate('created_at', today())
            ->count() + 1;
        
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Log refund activity
     */
    protected function logRefundActivity(Payment $refund, string $action): void
    {
        // Log to audit trail or activity log
        // This can be implemented based on your logging requirements
    }
}
<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDeliveryPersonnelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasPermission('manage_delivery_personnel');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $personnelId = $this->route('id');

        return [
            'branch_id' => [
                'sometimes',
                'integer',
                'exists:branches,id'
            ],
            'license_number' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('delivery_personnel', 'license_number')->ignore($personnelId)
            ],
            'license_expiry_date' => [
                'sometimes',
                'date',
                'after:today'
            ],
            'vehicle_type' => [
                'sometimes',
                'string',
                Rule::in(['motorcycle', 'bicycle', 'car', 'scooter'])
            ],
            'vehicle_model' => [
                'sometimes',
                'string',
                'max:100'
            ],
            'vehicle_plate_number' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('delivery_personnel', 'vehicle_plate_number')->ignore($personnelId)
            ],
            'phone_number' => [
                'sometimes',
                'string',
                'max:20'
            ],
            'emergency_contact_name' => [
                'sometimes',
                'string',
                'max:100'
            ],
            'emergency_contact_phone' => [
                'sometimes',
                'string',
                'max:20'
            ],
            'status' => [
                'sometimes',
                'string',
                Rule::in(['active', 'inactive', 'on_delivery', 'break'])
            ],
            'max_concurrent_deliveries' => [
                'sometimes',
                'integer',
                'min:1',
                'max:10'
            ],
            'working_hours' => [
                'sometimes',
                'array'
            ],
            'working_hours.*.day' => [
                'required_with:working_hours',
                'string',
                Rule::in(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])
            ],
            'working_hours.*.start_time' => [
                'required_with:working_hours',
                'date_format:H:i'
            ],
            'working_hours.*.end_time' => [
                'required_with:working_hours',
                'date_format:H:i',
                'after:working_hours.*.start_time'
            ],
            'is_verified' => [
                'sometimes',
                'boolean'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'license_number.unique' => 'This license number is already registered.',
            'license_expiry_date.after' => 'License expiry date must be in the future.',
            'vehicle_plate_number.unique' => 'This vehicle plate number is already registered.',
            'max_concurrent_deliveries.max' => 'Maximum concurrent deliveries cannot exceed 10.',
            'working_hours.*.end_time.after' => 'End time must be after start time.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'branch_id' => 'branch',
            'license_number' => 'license number',
            'license_expiry_date' => 'license expiry date',
            'vehicle_type' => 'vehicle type',
            'vehicle_model' => 'vehicle model',
            'vehicle_plate_number' => 'vehicle plate number',
            'phone_number' => 'phone number',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'max_concurrent_deliveries' => 'maximum concurrent deliveries',
        ];
    }
}
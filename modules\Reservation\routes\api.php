<?php

use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\AreaController;
use Modules\Reservation\Http\Controllers\TableController;
use Modules\Reservation\Http\Controllers\ReservationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->group(function () {
    // Add your API routes here
});

Route::prefix('reservation')->group(function () {
    Route::apiResource('areas', AreaController::class);
    Route::apiResource('tables', TableController::class);
    Route::apiResource('reservations', ReservationController::class);
    
    // Additional reservation routes
    Route::post('check-availability', [ReservationController::class, 'checkAvailability']);
    Route::post('reservations/{reservation}/cancel', [ReservationController::class, 'cancel']);
    Route::post('reservations/{reservation}/seat', [ReservationController::class, 'seat']);
    Route::post('reservations/{reservation}/complete', [ReservationController::class, 'complete']);
    Route::get('today-reservations', [ReservationController::class, 'todayReservations']);
});

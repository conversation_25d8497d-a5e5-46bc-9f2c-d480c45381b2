@extends('menu::layouts.app')

@section('title', 'Edit Menu')

@section('header-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('menu.web.menus.show', $menu->id) }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Menu
        </a>
        <a href="{{ route('menu.web.menus.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-list me-1"></i>
            All Menus
        </a>
    </div>
@endsection

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Edit Menu</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('menu.web.menus.update', $menu->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- Menu Name -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Menu Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $menu->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Menu Code -->
                        <div class="col-md-6 mb-3">
                            <label for="code" class="form-label">Menu Code</label>
                            <div class="input-group">
                                <input type="text" class="form-control @error('code') is-invalid @enderror" 
                                       id="code" name="code" value="{{ old('code', $menu->code) }}" 
                                       placeholder="Auto-generated if empty">
                                <button type="button" class="btn btn-outline-secondary" onclick="generateCode()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                            @error('code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" 
                                  placeholder="Optional description for this menu">{{ old('description', $menu->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <!-- Menu Type -->
                        <div class="col-md-6 mb-3">
                            <label for="menu_type" class="form-label">Menu Type <span class="text-danger">*</span></label>
                            <select class="form-select @error('menu_type') is-invalid @enderror" 
                                    id="menu_type" name="menu_type" required>
                                <option value="">Select Menu Type</option>
                                <option value="breakfast" {{ old('menu_type', $menu->menu_type) == 'breakfast' ? 'selected' : '' }}>Breakfast</option>
                                <option value="lunch" {{ old('menu_type', $menu->menu_type) == 'lunch' ? 'selected' : '' }}>Lunch</option>
                                <option value="dinner" {{ old('menu_type', $menu->menu_type) == 'dinner' ? 'selected' : '' }}>Dinner</option>
                                <option value="all_day" {{ old('menu_type', $menu->menu_type) == 'all_day' ? 'selected' : '' }}>All Day</option>
                                <option value="special" {{ old('menu_type', $menu->menu_type) == 'special' ? 'selected' : '' }}>Special</option>
                                <option value="seasonal" {{ old('menu_type', $menu->menu_type) == 'seasonal' ? 'selected' : '' }}>Seasonal</option>
                            </select>
                            @error('menu_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Sort Order -->
                        <div class="col-md-6 mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $menu->sort_order) }}" 
                                   min="0" placeholder="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Start Time -->
                        <div class="col-md-6 mb-3">
                            <label for="start_time" class="form-label">Start Time</label>
                            <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                   id="start_time" name="start_time" value="{{ old('start_time', $menu->start_time) }}">
                            @error('start_time')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- End Time -->
                        <div class="col-md-6 mb-3">
                            <label for="end_time" class="form-label">End Time</label>
                            <input type="time" class="form-control @error('end_time') is-invalid @enderror" 
                                   id="end_time" name="end_time" value="{{ old('end_time', $menu->end_time) }}">
                            @error('end_time')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Available Days -->
                    <div class="mb-3">
                        <label class="form-label">Available Days</label>
                        <div class="row">
                            @php
                                $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                $selectedDays = old('available_days', json_decode($menu->available_days, true) ?? []);
                            @endphp
                            @foreach($days as $day)
                                <div class="col-md-3 col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="day_{{ $day }}" name="available_days[]" value="{{ $day }}"
                                               {{ in_array($day, $selectedDays) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="day_{{ $day }}">
                                            {{ ucfirst($day) }}
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @error('available_days')
                            <div class="text-danger small mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Status Options -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" 
                                       id="is_active" name="is_active" value="1"
                                       {{ old('is_active', $menu->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Menu
                                </label>
                            </div>
                            <small class="text-muted">Inactive menus won't be visible to customers</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" 
                                       id="is_default" name="is_default" value="1"
                                       {{ old('is_default', $menu->is_default) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_default">
                                    Default Menu
                                </label>
                            </div>
                            <small class="text-muted">This will be the primary menu for the branch</small>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('menu.web.menus.show', $menu->id) }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-1"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            Update Menu
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function generateCode() {
    const nameInput = document.getElementById('name');
    const codeInput = document.getElementById('code');
    
    if (nameInput.value.trim()) {
        // Generate code from name
        let code = nameInput.value.trim()
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .substring(0, 20); // Limit length
        
        // Add timestamp for uniqueness
        const timestamp = Date.now().toString().slice(-4);
        code += '_' + timestamp;
        
        codeInput.value = code;
    } else {
        alert('Please enter a menu name first');
        nameInput.focus();
    }
}

// Auto-generate code when name changes (if code is empty)
document.getElementById('name').addEventListener('input', function() {
    const codeInput = document.getElementById('code');
    if (!codeInput.value.trim()) {
        generateCode();
    }
});

// Validate time range
document.getElementById('start_time').addEventListener('change', validateTimeRange);
document.getElementById('end_time').addEventListener('change', validateTimeRange);

function validateTimeRange() {
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;
    
    if (startTime && endTime && startTime >= endTime) {
        alert('End time must be after start time');
        document.getElementById('end_time').value = '';
    }
}
</script>
@endpush
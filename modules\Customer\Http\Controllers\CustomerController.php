<?php

namespace Modules\Customer\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Modules\Customer\Services\CustomerService;
use Modules\Customer\Http\Requests\StoreCustomerRequest;
use Modules\Customer\Http\Requests\UpdateCustomerRequest;
use Modules\Customer\Http\Requests\LoyaltyPointsRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CustomerController extends Controller
{
    protected CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Display a listing of customers
     */
    public function index(Request $request): JsonResponse
    {
        $filters = $request->only(['search', 'is_active', 'city']);
        $perPage = $request->get('per_page', 15);
        
        // $customers = $this->customerService->getCustomers(
        //     $this->getTenantId(),
        //     $filters,
        //     $perPage
        // );
        $customers = Customer::all();

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * Store a newly created customer
     */
    public function store(StoreCustomerRequest $request): JsonResponse
    {
        try {
            $customer = $this->customerService->createCustomer(
                $this->getTenantId(),
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Customer created successfully',
                'data' => $customer,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create customer',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified customer
     */
    public function show(int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $customer,
        ]);
    }

    /**
     * Update the specified customer
     */
    public function update(UpdateCustomerRequest $request, int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        try {
            $updatedCustomer = $this->customerService->updateCustomer(
                $customer,
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Customer updated successfully',
                'data' => $updatedCustomer,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update customer',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified customer
     */
    public function destroy(int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        try {
            $this->customerService->deleteCustomer($customer);

            return response()->json([
                'success' => true,
                'message' => 'Customer deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete customer',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Find customer by contact (phone or email)
     */
    public function findByContact(Request $request): JsonResponse
    {
        $request->validate([
            'contact' => 'required|string',
        ]);

        $customer = $this->customerService->findCustomerByContact(
            $this->getTenantId(),
            $request->contact
        );

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $customer,
        ]);
    }

    /**
     * Add loyalty points to customer
     */
    public function addLoyaltyPoints(LoyaltyPointsRequest $request, int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        try {
            $this->customerService->addLoyaltyPoints(
                $customer,
                $request->points,
                $request->description ?? 'Points added manually'
            );

            return response()->json([
                'success' => true,
                'message' => 'Loyalty points added successfully',
                'data' => $customer->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add loyalty points',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Redeem loyalty points
     */
    public function redeemLoyaltyPoints(LoyaltyPointsRequest $request, int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        try {
            $success = $this->customerService->redeemLoyaltyPoints(
                $customer,
                $request->points,
                $request->description ?? 'Points redeemed'
            );

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient loyalty points',
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Loyalty points redeemed successfully',
                'data' => $customer->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to redeem loyalty points',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get customer statistics
     */
    public function stats(): JsonResponse
    {
        $stats = $this->customerService->getCustomerStats($this->getTenantId());

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get top customers by loyalty points
     */
    public function topCustomers(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $customers = $this->customerService->getTopCustomers($this->getTenantId(), $limit);

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * Activate customer
     */
    public function activate(int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        $this->customerService->activateCustomer($customer);

        return response()->json([
            'success' => true,
            'message' => 'Customer activated successfully',
            'data' => $customer->fresh(),
        ]);
    }

    /**
     * Deactivate customer
     */
    public function deactivate(int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        $this->customerService->deactivateCustomer($customer);

        return response()->json([
            'success' => true,
            'message' => 'Customer deactivated successfully',
            'data' => $customer->fresh(),
        ]);
    }

    /**
     * Get customer loyalty history
     */
    public function loyaltyHistory(Request $request, int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        $perPage = $request->get('per_page', 15);
        $history = $this->customerService->getLoyaltyHistory($customer, $perPage);

        return response()->json([
            'success' => true,
            'data' => $history,
        ]);
    }

    /**
     * Update customer visit information
     */
    public function updateVisit(int $id): JsonResponse
    {
        $customer = $this->customerService->getCustomerById($this->getTenantId(), $id);

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found',
            ], 404);
        }

        $this->customerService->updateVisitInfo($customer);

        return response()->json([
            'success' => true,
            'message' => 'Customer visit updated successfully',
            'data' => $customer->fresh(),
        ]);
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branch_inventories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->decimal('current_stock', 15, 3)->default(0);
            $table->decimal('reserved_stock', 15, 3)->default(0)->comment('For pending orders');
            $table->decimal('minimum_level', 15, 3)->default(0);
            $table->decimal('maximum_level', 15, 3)->nullable();
            $table->decimal('reorder_point', 15, 3)->nullable();
            $table->decimal('average_cost', 10, 4)->nullable()->comment('Weighted average cost');
            $table->decimal('last_purchase_cost', 10, 4)->nullable();
            $table->timestamp('last_counted_at')->nullable();
            $table->timestamp('last_movement_at')->default(now());
            $table->timestamps();
            
            $table->unique(['branch_id', 'product_id']);
            
            // Performance indexes
            $table->index(['branch_id', 'current_stock']);
            $table->index(['product_id', 'current_stock']);
            $table->index(['current_stock', 'minimum_level']);
            $table->index(['reorder_point', 'current_stock']);
            $table->index(['last_movement_at']);
            $table->index(['last_counted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_inventories');
    }
};
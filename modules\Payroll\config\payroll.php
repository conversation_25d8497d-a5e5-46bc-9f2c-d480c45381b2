<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payroll Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Payroll module.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Tax Rates
    |--------------------------------------------------------------------------
    |
    | These are the default tax brackets used for payroll calculations.
    | You can customize these based on your local tax requirements.
    |
    */
    'tax_brackets' => [
        ['min' => 0, 'max' => 10000, 'rate' => 0.10],
        ['min' => 10000, 'max' => 40000, 'rate' => 0.12],
        ['min' => 40000, 'max' => 85000, 'rate' => 0.22],
        ['min' => 85000, 'max' => 160000, 'rate' => 0.24],
        ['min' => 160000, 'max' => 200000, 'rate' => 0.32],
        ['min' => 200000, 'max' => 500000, 'rate' => 0.35],
        ['min' => 500000, 'max' => PHP_INT_MAX, 'rate' => 0.37],
    ],

    /*
    |--------------------------------------------------------------------------
    | Deduction Rates
    |--------------------------------------------------------------------------
    |
    | Default rates for various payroll deductions.
    |
    */
    'deduction_rates' => [
        'social_security' => 0.062,  // 6.2%
        'medicare' => 0.0145,        // 1.45%
        'health_insurance' => 0.05,  // 5%
        'retirement' => 0.03,        // 3%
    ],

    /*
    |--------------------------------------------------------------------------
    | Overtime Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for overtime calculations.
    |
    */
    'overtime' => [
        'threshold_hours' => 40,     // Hours per week before overtime
        'multiplier' => 1.5,         // Overtime pay multiplier
    ],

    /*
    |--------------------------------------------------------------------------
    | Holiday Pay Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for holiday pay calculations.
    |
    */
    'holiday_pay' => [
        'multiplier' => 2.0,         // Holiday pay multiplier
    ],

    /*
    |--------------------------------------------------------------------------
    | Pay Period Types
    |--------------------------------------------------------------------------
    |
    | Available pay period types and their configurations.
    |
    */
    'pay_period_types' => [
        'weekly' => [
            'days' => 7,
            'tolerance_days' => 2,
        ],
        'bi-weekly' => [
            'days' => 14,
            'tolerance_days' => 2,
        ],
        'monthly' => [
            'days' => 30,
            'tolerance_days' => 2,
        ],
        'quarterly' => [
            'days' => 90,
            'tolerance_days' => 2,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Payslip Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for payslip generation and numbering.
    |
    */
    'payslip' => [
        'number_prefix' => 'PS',     // Prefix for payslip numbers
        'number_length' => 8,        // Total length of payslip number
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for currency formatting.
    |
    */
    'currency' => [
        'symbol' => '$',
        'decimal_places' => 2,
        'thousands_separator' => ',',
        'decimal_separator' => '.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Days Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for business day calculations.
    |
    */
    'business_days' => [
        'weekend_days' => [0, 6],    // Sunday = 0, Saturday = 6
        'public_holidays' => [       // Add your public holidays here
            // Format: 'YYYY-MM-DD'
            // '2025-01-01', // New Year's Day
            // '2025-07-04', // Independence Day
            // '2025-12-25', // Christmas Day
        ],
    ],
];
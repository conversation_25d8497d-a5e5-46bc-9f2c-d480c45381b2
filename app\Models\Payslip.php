<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Payslip extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'pay_period_id',
        'user_id',
        'branch_id',
        'payslip_number',
        'employee_name',
        'employee_email',
        'employee_id',
        'position',
        'department',
        'hourly_rate',
        'salary',
        'regular_hours',
        'overtime_hours',
        'overtime_rate',
        'holiday_hours',
        'sick_hours',
        'vacation_hours',
        'regular_pay',
        'overtime_pay',
        'holiday_pay',
        'bonus',
        'commission',
        'allowances',
        'gross_pay',
        'tax_deduction',
        'social_security',
        'health_insurance',
        'retirement_contribution',
        'other_deductions',
        'total_deductions',
        'net_pay',
        'year_to_date_gross',
        'year_to_date_net',
        'status',
        'approved_at',
        'approved_by',
        'paid_at',
        'payment_method',
        'payment_reference',
        'earnings_breakdown',
        'deductions_breakdown',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'salary' => 'decimal:2',
        'regular_hours' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'overtime_rate' => 'decimal:2',
        'holiday_hours' => 'decimal:2',
        'sick_hours' => 'decimal:2',
        'vacation_hours' => 'decimal:2',
        'regular_pay' => 'decimal:2',
        'overtime_pay' => 'decimal:2',
        'holiday_pay' => 'decimal:2',
        'bonus' => 'decimal:2',
        'commission' => 'decimal:2',
        'allowances' => 'decimal:2',
        'gross_pay' => 'decimal:2',
        'tax_deduction' => 'decimal:2',
        'social_security' => 'decimal:2',
        'health_insurance' => 'decimal:2',
        'retirement_contribution' => 'decimal:2',
        'other_deductions' => 'decimal:2',
        'total_deductions' => 'decimal:2',
        'net_pay' => 'decimal:2',
        'year_to_date_gross' => 'decimal:2',
        'year_to_date_net' => 'decimal:2',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
        'earnings_breakdown' => 'array',
        'deductions_breakdown' => 'array',
    ];

    /**
     * Get the tenant that owns the payslip.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the pay period that owns the payslip.
     */
    public function payPeriod(): BelongsTo
    {
        return $this->belongsTo(PayPeriod::class);
    }

    /**
     * Get the user (employee) that owns the payslip.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the branch associated with the payslip.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the user who approved this payslip.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope a query to only include payslips for a specific tenant.
     */
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Scope a query to only include payslips for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include payslips with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include payslips for a specific pay period.
     */
    public function scopeForPayPeriod($query, $payPeriodId)
    {
        return $query->where('pay_period_id', $payPeriodId);
    }

    /**
     * Check if the payslip can be approved.
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if the payslip can be paid.
     */
    public function canBePaid(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the payslip is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Generate a unique payslip number.
     */
    public static function generatePayslipNumber($tenantId, $payPeriodId): string
    {
        $payPeriod = PayPeriod::find($payPeriodId);
        $year = $payPeriod->start_date->year;
        $month = $payPeriod->start_date->format('m');
        
        // Get the next sequence number for this pay period
        $lastPayslip = static::where('pay_period_id', $payPeriodId)
            ->orderBy('id', 'desc')
            ->first();
        
        $sequence = $lastPayslip ? (int)substr($lastPayslip->payslip_number, -4) + 1 : 1;
        
        return sprintf('PS%s%s%04d', $year, $month, $sequence);
    }

    /**
     * Calculate gross pay from individual components.
     */
    public function calculateGrossPay(): float
    {
        return $this->regular_pay + $this->overtime_pay + $this->holiday_pay + 
               $this->bonus + $this->commission + $this->allowances;
    }

    /**
     * Calculate total deductions from individual components.
     */
    public function calculateTotalDeductions(): float
    {
        return $this->tax_deduction + $this->social_security + $this->health_insurance + 
               $this->retirement_contribution + $this->other_deductions;
    }

    /**
     * Calculate net pay (gross pay minus deductions).
     */
    public function calculateNetPay(): float
    {
        return $this->calculateGrossPay() - $this->calculateTotalDeductions();
    }

    /**
     * Update calculated fields.
     */
    public function updateCalculatedFields(): void
    {
        $this->gross_pay = $this->calculateGrossPay();
        $this->total_deductions = $this->calculateTotalDeductions();
        $this->net_pay = $this->calculateNetPay();
    }

    /**
     * Get total hours worked.
     */
    public function getTotalHours(): float
    {
        return $this->regular_hours + $this->overtime_hours + $this->holiday_hours;
    }

    /**
     * Get effective hourly rate (total pay divided by total hours).
     */
    public function getEffectiveHourlyRate(): float
    {
        $totalHours = $this->getTotalHours();
        return $totalHours > 0 ? $this->gross_pay / $totalHours : 0;
    }

    /**
     * Approve the payslip.
     */
    public function approve($approvedBy = null): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->status = 'approved';
        $this->approved_at = Carbon::now();
        $this->approved_by = $approvedBy;
        
        return $this->save();
    }

    /**
     * Mark the payslip as paid.
     */
    public function markAsPaid($paymentMethod = null, $paymentReference = null): bool
    {
        if (!$this->canBePaid()) {
            return false;
        }

        $this->status = 'paid';
        $this->paid_at = Carbon::now();
        $this->payment_method = $paymentMethod;
        $this->payment_reference = $paymentReference;
        
        return $this->save();
    }
}
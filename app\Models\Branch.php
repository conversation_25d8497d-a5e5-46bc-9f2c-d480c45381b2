<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Branch extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'name',
        'code',
        'address',
        'latitude',
        'longitude',
        'phone',
        'email',
        'manager_name',
        'seating_capacity',
        'delivery_radius',
        'operating_hours',
        'timezone',
        'is_delivery_enabled',
        'is_takeaway_enabled',
        'is_dine_in_enabled',
        'is_online_ordering_enabled',
        'printer_configurations',
        'pos_terminal_id',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:7',
            'longitude' => 'decimal:7',
            'delivery_radius' => 'decimal:2',
            'operating_hours' => 'array',
            'printer_configurations' => 'array',
            'is_delivery_enabled' => 'boolean',
            'is_takeaway_enabled' => 'boolean',
            'is_dine_in_enabled' => 'boolean',
            'is_online_ordering_enabled' => 'boolean',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function tables()
    {
        return $this->hasMany(Table::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }

    public function menuItemBranches()
    {
        return $this->hasMany(MenuItemBranch::class);
    }

    public function menus()
    {
        return $this->hasMany(Menu::class);
    }

    public function defaultMenu()
    {
        return $this->hasOne(Menu::class)->where('is_default', true);
    }

    public function branchInventory()
    {
        return $this->hasMany(BranchInventory::class);
    }

    public function inventoryMovements()
    {
        return $this->hasMany(InventoryMovement::class);
    }

    public function inventoryLogs()
    {
        return $this->hasMany(InventoryLog::class);
    }

    public function purchaseOrders()
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    public function kitchenDisplayOrders()
    {
        return $this->hasMany(KitchenDisplayOrder::class);
    }

    public function reports()
    {
        return $this->hasMany(Report::class);
    }

    public function settings()
    {
        return $this->hasMany(Setting::class);
    }

    public function shifts()
    {
        return $this->hasMany(Shift::class);
    }

    public function shiftTypes()
    {
        return $this->hasMany(ShiftType::class);
    }

    public function shiftAssignments()
    {
        return $this->hasMany(ShiftAssignment::class);
    }

    public function timesheets()
    {
        return $this->hasMany(Timesheet::class);
    }

    public function staffAttendances()
    {
        return $this->hasMany(StaffAttendance::class);
    }

    public function leaveRequests()
    {
        return $this->hasMany(LeaveRequest::class);
    }
}
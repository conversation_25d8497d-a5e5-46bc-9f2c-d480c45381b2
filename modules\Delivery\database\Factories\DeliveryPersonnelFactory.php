<?php

namespace Modules\Delivery\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;
use App\Models\Branch;
use Modules\Delivery\Models\DeliveryPersonnel;
use Carbon\Carbon;

class DeliveryPersonnelFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = DeliveryPersonnel::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $totalDeliveries = $this->faker->numberBetween(50, 500);
        $successfulDeliveries = $this->faker->numberBetween(
            (int)($totalDeliveries * 0.8), 
            $totalDeliveries
        );
        
        return [
            'user_id' => User::factory(),
            'branch_id' => Branch::factory(),
            'license_number' => 'DL' . $this->faker->unique()->numerify('######'),
            'license_expiry_date' => $this->faker->dateTimeBetween('now', '+3 years'),
            'vehicle_type' => $this->faker->randomElement(['motorcycle', 'bicycle', 'car', 'van']),
            'vehicle_model' => $this->faker->randomElement([
                'Honda CB150R',
                'Yamaha Vixion',
                'Toyota Avanza',
                'Honda Beat',
                'Suzuki Nex',
                'Mountain Bike',
                'Road Bike'
            ]),
            'vehicle_plate_number' => $this->faker->unique()->regexify('[A-Z]{1}[0-9]{4}[A-Z]{3}'),
            'phone_number' => $this->faker->phoneNumber,
            'emergency_contact_name' => $this->faker->name,
            'emergency_contact_phone' => $this->faker->phoneNumber,
            'status' => $this->faker->randomElement(['active', 'inactive', 'suspended', 'on_break']),
            'is_available' => $this->faker->boolean(70), // 70% chance of being available
            'is_verified' => $this->faker->boolean(80), // 80% chance of being verified
            'current_latitude' => $this->faker->latitude(-6.5, -5.9), // Jakarta area
            'current_longitude' => $this->faker->longitude(106.5, 107.1), // Jakarta area
            'last_location_update' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'max_concurrent_deliveries' => $this->faker->numberBetween(2, 5),
            'working_hours' => $this->generateWorkingHours(),
            'rating' => $this->faker->randomFloat(2, 3.0, 5.0),
            'total_reviews' => $this->faker->numberBetween(10, 200),
            'total_deliveries' => $totalDeliveries,
            'successful_deliveries' => $successfulDeliveries,
            'total_earnings' => $this->faker->randomFloat(2, 1000, 50000),
        ];
    }

    /**
     * Generate working hours for the personnel.
     */
    private function generateWorkingHours(): array
    {
        $workingDays = $this->faker->randomElements(
            ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
            $this->faker->numberBetween(5, 7)
        );
        
        $schedule = [];
        
        foreach ($workingDays as $day) {
            $startHour = $this->faker->numberBetween(6, 10);
            $endHour = $this->faker->numberBetween(16, 22);
            
            $schedule[] = [
                'day' => $day,
                'start_time' => sprintf('%02d:00', $startHour),
                'end_time' => sprintf('%02d:00', $endHour),
            ];
        }
        
        return $schedule;
    }

    /**
     * Indicate that the personnel is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'is_available' => true,
        ]);
    }

    /**
     * Indicate that the personnel is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => true,
        ]);
    }

    /**
     * Indicate that the personnel is available.
     */
    public function available(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_available' => true,
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the personnel is on break.
     */
    public function onBreak(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'on_break',
            'is_available' => false,
        ]);
    }

    /**
     * Indicate that the personnel uses a motorcycle.
     */
    public function motorcycle(): static
    {
        return $this->state(fn (array $attributes) => [
            'vehicle_type' => 'motorcycle',
            'vehicle_model' => $this->faker->randomElement([
                'Honda CB150R',
                'Yamaha Vixion',
                'Honda Beat',
                'Suzuki Nex',
                'Kawasaki Ninja'
            ]),
        ]);
    }

    /**
     * Indicate that the personnel uses a bicycle.
     */
    public function bicycle(): static
    {
        return $this->state(fn (array $attributes) => [
            'vehicle_type' => 'bicycle',
            'vehicle_model' => $this->faker->randomElement([
                'Mountain Bike',
                'Road Bike',
                'Hybrid Bike',
                'Electric Bike'
            ]),
            'vehicle_plate_number' => null, // Bicycles don't have plates
        ]);
    }

    /**
     * Indicate that the personnel has high ratings.
     */
    public function highRated(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->randomFloat(2, 4.5, 5.0),
            'total_reviews' => $this->faker->numberBetween(50, 200),
        ]);
    }

    /**
     * Indicate that the personnel is experienced.
     */
    public function experienced(): static
    {
        $totalDeliveries = $this->faker->numberBetween(200, 1000);
        
        return $this->state(fn (array $attributes) => [
            'total_deliveries' => $totalDeliveries,
            'successful_deliveries' => $this->faker->numberBetween(
                (int)($totalDeliveries * 0.9), 
                $totalDeliveries
            ),
            'total_earnings' => $this->faker->randomFloat(2, 10000, 100000),
            'rating' => $this->faker->randomFloat(2, 4.0, 5.0),
        ]);
    }

    /**
     * Indicate that the personnel is new.
     */
    public function newbie(): static
    {
        $totalDeliveries = $this->faker->numberBetween(0, 50);
        
        return $this->state(fn (array $attributes) => [
            'total_deliveries' => $totalDeliveries,
            'successful_deliveries' => $this->faker->numberBetween(
                (int)($totalDeliveries * 0.7), 
                $totalDeliveries
            ),
            'total_earnings' => $this->faker->randomFloat(2, 0, 5000),
            'rating' => $totalDeliveries > 0 ? $this->faker->randomFloat(2, 3.0, 4.5) : 0,
            'total_reviews' => $this->faker->numberBetween(0, 20),
        ]);
    }

    /**
     * Set specific location for the personnel.
     */
    public function atLocation(float $latitude, float $longitude): static
    {
        return $this->state(fn (array $attributes) => [
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
        ]);
    }

    /**
     * Set the personnel to work at a specific branch.
     */
    public function forBranch(int $branchId): static
    {
        return $this->state(fn (array $attributes) => [
            'branch_id' => $branchId,
        ]);
    }
}
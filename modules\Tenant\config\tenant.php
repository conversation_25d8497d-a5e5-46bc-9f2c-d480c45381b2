<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Tenant Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Tenant module.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        'timezone' => 'UTC',
        'currency' => 'USD',
        'language' => 'en',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i:s',
        'datetime_format' => 'Y-m-d H:i:s',
    ],

    /*
    |--------------------------------------------------------------------------
    | Tenant Code Generation
    |--------------------------------------------------------------------------
    */
    'code' => [
        'prefix' => 'TNT',
        'length' => 8,
        'characters' => '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    ],

    /*
    |--------------------------------------------------------------------------
    | Subdomain Configuration
    |--------------------------------------------------------------------------
    */
    'subdomain' => [
        'enabled' => true,
        'domain' => env('APP_DOMAIN', 'localhost'),
        'reserved' => ['www', 'api', 'admin', 'app', 'mail', 'ftp'],
        'min_length' => 3,
        'max_length' => 20,
    ],

    /*
    |--------------------------------------------------------------------------
    | Subscription Configuration
    |--------------------------------------------------------------------------
    */
    'subscription' => [
        'trial_days' => 14,
        'grace_period_days' => 3,
        'auto_suspend_after_days' => 7,
        'billing_cycles' => ['monthly', 'quarterly', 'yearly'],
        'default_billing_cycle' => 'monthly',
    ],

    /*
    |--------------------------------------------------------------------------
    | Usage Limits
    |--------------------------------------------------------------------------
    */
    'usage_limits' => [
        'cache_ttl' => 3600, // 1 hour
        'warning_threshold' => 0.8, // 80%
        'soft_limit_threshold' => 0.9, // 90%
    ],

    /*
    |--------------------------------------------------------------------------
    | Billing Configuration
    |--------------------------------------------------------------------------
    */
    'billing' => [
        'tax_rate' => 0.1, // 10%
        'currency' => 'USD',
        'payment_methods' => ['credit_card', 'bank_transfer', 'paypal'],
        'invoice_prefix' => 'INV',
        'retry_failed_payments' => true,
        'max_payment_retries' => 3,
        'retry_interval_days' => 3,
    ],

    /*
    |--------------------------------------------------------------------------
    | Multi-Store Configuration
    |--------------------------------------------------------------------------
    */
    'multi_store' => [
        'max_branches_per_tenant' => 10,
        'sync_menu_across_branches' => true,
        'centralized_inventory' => false,
        'shared_customer_database' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'prefix' => 'tenant',
        'ttl' => [
            'tenant_data' => 3600, // 1 hour
            'subscription_data' => 1800, // 30 minutes
            'usage_stats' => 300, // 5 minutes
            'billing_data' => 7200, // 2 hours
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'multi_store' => true,
        'custom_domains' => false,
        'white_labeling' => false,
        'api_access' => true,
        'advanced_reporting' => true,
        'data_export' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'tenant_name_max_length' => 100,
        'tenant_code_max_length' => 20,
        'business_hours_format' => 'H:i',
        'phone_regex' => '/^[+]?[0-9\s\-\(\)]+$/',
        'social_media_domains' => [
            'facebook.com',
            'twitter.com',
            'instagram.com',
            'linkedin.com',
            'youtube.com',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'subscription_expiry_warning_days' => [30, 7, 1],
        'usage_limit_warnings' => [80, 90, 95], // Percentage thresholds
        'billing_failure_notifications' => true,
        'new_tenant_notifications' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'data_isolation' => true,
        'tenant_context_required' => true,
        'cross_tenant_access_prevention' => true,
        'audit_trail' => true,
    ],
];
<?php

namespace Modules\Reservation\Http\Controllers;

use App\Models\Area;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class AreaController extends Controller
{
    public function index()
    {
        return response()->json(Area::with('branch')->get());
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);
        $area = Area::create($data);
        return response()->json($area, 201);
    }

    public function show(Area $area)
    {
        return response()->json($area->load('branch', 'tables'));
    }

    public function update(Request $request, Area $area)
    {
        $data = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
        ]);
        $area->update($data);
        return response()->json($area);
    }

    public function destroy(Area $area)
    {
        $area->delete();
        return response()->json(null, 204);
    }
} 
<?php

namespace Modules\Delivery\Helpers;

use Mo<PERSON>les\Delivery\Models\DeliveryZone;
use Modules\Delivery\Models\DeliveryPersonnel;
use Carbon\Carbon;

class DeliveryHelper
{
    /**
     * Calculate distance between two coordinates using Haversine formula.
     */
    public static function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Check if a point is inside a polygon using ray casting algorithm.
     */
    public static function isPointInPolygon(float $lat, float $lng, array $polygon): bool
    {
        $vertices = count($polygon);
        $inside = false;

        for ($i = 0, $j = $vertices - 1; $i < $vertices; $j = $i++) {
            $xi = $polygon[$i][0]; // latitude
            $yi = $polygon[$i][1]; // longitude
            $xj = $polygon[$j][0]; // latitude
            $yj = $polygon[$j][1]; // longitude

            if ((($yi > $lng) !== ($yj > $lng)) &&
                ($lat < ($xj - $xi) * ($lng - $yi) / ($yj - $yi) + $xi)) {
                $inside = !$inside;
            }
        }

        return $inside;
    }

    /**
     * Calculate estimated delivery time based on distance and vehicle type.
     */
    public static function calculateEstimatedDeliveryTime(
        float $distance,
        string $vehicleType,
        ?Carbon $orderTime = null
    ): int {
        $orderTime = $orderTime ?? now();
        
        // Get vehicle speed from config
        $vehicleConfig = config("delivery.vehicle_types.{$vehicleType}");
        $averageSpeed = $vehicleConfig['average_speed_kmh'] ?? 30;

        // Base time calculation (distance / speed * 60 minutes)
        $baseTime = ($distance / $averageSpeed) * 60;

        // Add preparation time (10 minutes)
        $preparationTime = 10;

        // Apply peak hour multiplier
        $peakMultiplier = self::getPeakHourMultiplier($orderTime);
        
        // Apply weather multiplier (if implemented)
        $weatherMultiplier = self::getWeatherMultiplier();

        $totalTime = ($baseTime + $preparationTime) * $peakMultiplier * $weatherMultiplier;

        return (int) ceil($totalTime);
    }

    /**
     * Get peak hour multiplier based on time of day.
     */
    public static function getPeakHourMultiplier(Carbon $time): float
    {
        $hour = $time->hour;
        
        // Peak hours: 11 AM - 2 PM and 6 PM - 9 PM
        $isPeakHour = ($hour >= 11 && $hour <= 14) || ($hour >= 18 && $hour <= 21);
        
        return $isPeakHour ? config('delivery.business_rules.peak_hour_multiplier', 1.3) : 1.0;
    }

    /**
     * Get weather multiplier (placeholder for weather API integration).
     */
    public static function getWeatherMultiplier(): float
    {
        // This could be enhanced to integrate with weather APIs
        // For now, return default multiplier
        return config('delivery.business_rules.weather_delay_multiplier', 1.0);
    }

    /**
     * Format delivery status for display.
     */
    public static function formatDeliveryStatus(string $status): string
    {
        return config("delivery.statuses.assignment.{$status}", ucfirst(str_replace('_', ' ', $status)));
    }

    /**
     * Format personnel status for display.
     */
    public static function formatPersonnelStatus(string $status): string
    {
        return config("delivery.statuses.personnel.{$status}", ucfirst(str_replace('_', ' ', $status)));
    }

    /**
     * Format payment method for display.
     */
    public static function formatPaymentMethod(string $method): string
    {
        return config("delivery.payment_methods.{$method}", ucfirst(str_replace('_', ' ', $method)));
    }

    /**
     * Calculate delivery fee based on distance and zone.
     */
    public static function calculateDeliveryFee(float $distance, ?DeliveryZone $zone = null): float
    {
        if ($zone) {
            return $zone->delivery_fee;
        }

        // Default fee calculation based on distance
        $baseFee = config('delivery.defaults.delivery_fee', 5.00);
        $perKmRate = 1.00; // $1 per km
        
        return $baseFee + ($distance * $perKmRate);
    }

    /**
     * Get available personnel within radius of a location.
     */
    public static function getAvailablePersonnelInRadius(
        int $branchId,
        float $lat,
        float $lng,
        float $radiusKm = 15
    ): \Illuminate\Database\Eloquent\Collection {
        return DeliveryPersonnel::where('branch_id', $branchId)
            ->available()
            ->withinRadius($lat, $lng, $radiusKm)
            ->with('user')
            ->get();
    }

    /**
     * Calculate personnel performance score.
     */
    public static function calculatePerformanceScore(
        float $rating,
        float $completionRate,
        float $onTimeRate
    ): float {
        // Weighted score calculation
        $ratingWeight = 0.4;
        $completionWeight = 0.35;
        $onTimeWeight = 0.25;

        $ratingScore = ($rating / 5) * 100; // Convert to percentage
        $completionScore = $completionRate * 100;
        $onTimeScore = $onTimeRate * 100;

        return ($ratingScore * $ratingWeight) +
               ($completionScore * $completionWeight) +
               ($onTimeScore * $onTimeWeight);
    }

    /**
     * Generate delivery tracking URL for customers.
     */
    public static function generateTrackingUrl(int $assignmentId, ?string $customerToken = null): string
    {
        $baseUrl = config('app.url');
        $trackingPath = "/delivery/track/{$assignmentId}";
        
        if ($customerToken) {
            $trackingPath .= "?token={$customerToken}";
        }
        
        return $baseUrl . $trackingPath;
    }

    /**
     * Validate GPS coordinates.
     */
    public static function validateCoordinates(float $lat, float $lng): bool
    {
        return ($lat >= -90 && $lat <= 90) && ($lng >= -180 && $lng <= 180);
    }

    /**
     * Calculate bearing between two points.
     */
    public static function calculateBearing(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $deltaLngRad = deg2rad($lng2 - $lng1);

        $y = sin($deltaLngRad) * cos($lat2Rad);
        $x = cos($lat1Rad) * sin($lat2Rad) - sin($lat1Rad) * cos($lat2Rad) * cos($deltaLngRad);

        $bearingRad = atan2($y, $x);
        $bearingDeg = rad2deg($bearingRad);

        return fmod($bearingDeg + 360, 360); // Normalize to 0-360
    }

    /**
     * Convert bearing to compass direction.
     */
    public static function bearingToCompass(float $bearing): string
    {
        $directions = [
            'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
            'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'
        ];
        
        $index = (int) round($bearing / 22.5) % 16;
        return $directions[$index];
    }

    /**
     * Format distance for display.
     */
    public static function formatDistance(float $distanceKm): string
    {
        if ($distanceKm < 1) {
            return round($distanceKm * 1000) . ' m';
        }
        
        return round($distanceKm, 1) . ' km';
    }

    /**
     * Format duration for display.
     */
    public static function formatDuration(int $minutes): string
    {
        if ($minutes < 60) {
            return $minutes . ' min';
        }
        
        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;
        
        if ($remainingMinutes === 0) {
            return $hours . ' hr';
        }
        
        return $hours . ' hr ' . $remainingMinutes . ' min';
    }

    /**
     * Check if current time is within working hours.
     */
    public static function isWithinWorkingHours(array $workingHours, ?Carbon $time = null): bool
    {
        $time = $time ?? now();
        $dayOfWeek = strtolower($time->format('l'));
        
        foreach ($workingHours as $schedule) {
            if ($schedule['day'] === $dayOfWeek) {
                $startTime = Carbon::createFromFormat('H:i', $schedule['start_time']);
                $endTime = Carbon::createFromFormat('H:i', $schedule['end_time']);
                
                $currentTime = Carbon::createFromFormat('H:i', $time->format('H:i'));
                
                return $currentTime->between($startTime, $endTime);
            }
        }
        
        return false;
    }

    /**
     * Generate unique delivery reference number.
     */
    public static function generateDeliveryReference(int $assignmentId): string
    {
        $prefix = 'DEL';
        $timestamp = now()->format('ymd');
        $paddedId = str_pad($assignmentId, 6, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $paddedId;
    }

    /**
     * Calculate tip percentage based on delivery fee.
     */
    public static function calculateTipPercentage(float $tipAmount, float $deliveryFee): float
    {
        if ($deliveryFee <= 0) {
            return 0;
        }
        
        return round(($tipAmount / $deliveryFee) * 100, 2);
    }

    /**
     * Get delivery time slots for a given date.
     */
    public static function getDeliveryTimeSlots(Carbon $date, int $intervalMinutes = 30): array
    {
        $slots = [];
        $startHour = 9; // 9 AM
        $endHour = 22; // 10 PM
        
        $current = $date->copy()->setTime($startHour, 0);
        $end = $date->copy()->setTime($endHour, 0);
        
        while ($current->lessThan($end)) {
            $next = $current->copy()->addMinutes($intervalMinutes);
            
            $slots[] = [
                'start' => $current->format('H:i'),
                'end' => $next->format('H:i'),
                'label' => $current->format('g:i A') . ' - ' . $next->format('g:i A'),
                'available' => $current->greaterThan(now()->addMinutes(60)) // At least 1 hour from now
            ];
            
            $current = $next;
        }
        
        return $slots;
    }
}
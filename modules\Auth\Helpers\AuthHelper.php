<?php

namespace Modules\Auth\Helpers;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\PersonalAccessToken;
use Carbon\Carbon;

class AuthHelper
{
    /**
     * Get the currently authenticated user
     */
    public static function user(): ?User
    {
        return Auth::guard('api')->user();
    }

    /**
     * Check if user is authenticated
     */
    public static function check(): bool
    {
        return Auth::guard('api')->check();
    }

    /**
     * Get user ID of authenticated user
     */
    public static function id(): ?string
    {
        return Auth::guard('api')->id();
    }

    /**
     * Check if user has specific role
     */
    public static function hasRole(string $role): bool
    {
        $user = self::user();
        return $user ? $user->hasRole($role) : false;
    }

    /**
     * Check if user has any of the specified roles
     */
    public static function hasAnyRole(array $roles): bool
    {
        $user = self::user();
        return $user ? $user->hasAnyRole($roles) : false;
    }

    /**
     * Check if user has all specified roles
     */
    public static function hasAllRoles(array $roles): bool
    {
        $user = self::user();
        return $user ? $user->hasAllRoles($roles) : false;
    }

    /**
     * Check if user has specific permission
     */
    public static function can(string $permission): bool
    {
        $user = self::user();
        return $user ? $user->can($permission) : false;
    }

    /**
     * Check if user has any of the specified permissions
     */
    public static function hasAnyPermission(array $permissions): bool
    {
        $user = self::user();
        return $user ? $user->hasAnyPermission($permissions) : false;
    }

    /**
     * Check if user has all specified permissions
     */
    public static function hasAllPermissions(array $permissions): bool
    {
        $user = self::user();
        return $user ? $user->hasAllPermissions($permissions) : false;
    }

    /**
     * Generate a secure random token
     */
    public static function generateToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Hash a password
     */
    public static function hashPassword(string $password): string
    {
        return Hash::make($password);
    }

    /**
     * Verify a password against a hash
     */
    public static function verifyPassword(string $password, string $hash): bool
    {
        return Hash::check($password, $hash);
    }

    /**
     * Get user's active tokens count
     */
    public static function getActiveTokensCount(?User $user = null): int
    {
        $user = $user ?: self::user();
        return $user ? $user->tokens()->count() : 0;
    }

    /**
     * Revoke all user tokens except current
     */
    public static function revokeOtherTokens(?User $user = null): bool
    {
        $user = $user ?: self::user();
        if (!$user) {
            return false;
        }

        $currentToken = $user->currentAccessToken();
        if ($currentToken) {
            $user->tokens()
                ->where('id', '!=', $currentToken->id)
                ->delete();
        }

        return true;
    }

    /**
     * Get token expiration time
     * Note: Sanctum tokens don't expire by default unless configured
     */
    public static function getTokenExpiration(?User $user = null): ?Carbon
    {
        $user = $user ?: self::user();
        if (!$user) {
            return null;
        }

        $token = $user->currentAccessToken();
        // Sanctum tokens don't have expires_at by default
        // You can configure expiration in config/sanctum.php
        return $token && isset($token->expires_at) ? $token->expires_at : null;
    }

    /**
     * Check if current token is expired
     * Note: Sanctum tokens don't expire by default unless configured
     */
    public static function isTokenExpired(?User $user = null): bool
    {
        $expiration = self::getTokenExpiration($user);
        return $expiration ? $expiration->isPast() : false;
    }

    /**
     * Get time until token expires (in minutes)
     * Note: Sanctum tokens don't expire by default unless configured
     */
    public static function getTokenTimeToExpiry(?User $user = null): ?int
    {
        $expiration = self::getTokenExpiration($user);
        return $expiration ? Carbon::now()->diffInMinutes($expiration, false) : null;
    }

    /**
     * Check if user email is verified
     */
    public static function isEmailVerified(?User $user = null): bool
    {
        $user = $user ?: self::user();
        return $user ? $user->hasVerifiedEmail() : false;
    }

    /**
     * Get user roles as array
     */
    public static function getUserRoles(?User $user = null): array
    {
        $user = $user ?: self::user();
        return $user ? $user->getRoleNames()->toArray() : [];
    }

    /**
     * Get user permissions as array
     */
    public static function getUserPermissions(?User $user = null): array
    {
        $user = $user ?: self::user();
        return $user ? $user->getAllPermissions()->pluck('name')->toArray() : [];
    }

    /**
     * Format user data for API response
     */
    public static function formatUserData(?User $user = null): ?array
    {
        $user = $user ?: self::user();
        if (!$user) {
            return null;
        }

        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified_at' => $user->email_verified_at,
            'roles' => self::getUserRoles($user),
            'permissions' => self::getUserPermissions($user),
            'created_at' => $user->created_at,
            'updated_at' => $user->updated_at,
        ];
    }

    /**
     * Check if user is super admin
     */
    public static function isSuperAdmin(?User $user = null): bool
    {
        return self::hasRole('super-admin');
    }

    /**
     * Check if user is admin
     */
    public static function isAdmin(?User $user = null): bool
    {
        return self::hasAnyRole(['super-admin', 'admin']);
    }

    /**
     * Check if user is manager
     */
    public static function isManager(?User $user = null): bool
    {
        return self::hasAnyRole(['super-admin', 'admin', 'manager']);
    }

    /**
     * Get user's last login time (if you have a sessions table)
     */
    public static function getLastLoginTime(?User $user = null): ?Carbon
    {
        $user = $user ?: self::user();
        if (!$user) {
            return null;
        }

        $lastSession = $user->userSessions()
            ->orderBy('created_at', 'desc')
            ->first();

        return $lastSession ? $lastSession->created_at : null;
    }

    /**
     * Log security event (if you have security logs)
     */
    public static function logSecurityEvent(string $event, array $data = [], ?User $user = null): void
    {
        $user = $user ?: self::user();
        if (!$user) {
            return;
        }

        $user->securityLogs()->create([
            'event' => $event,
            'data' => json_encode($data),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'created_at' => now(),
        ]);
    }

    /**
     * Create API response format
     */
    public static function apiResponse(bool $success, string $message, $data = null, int $statusCode = 200): array
    {
        $response = [
            'success' => $success,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return $response;
    }

    /**
     * Create success API response
     */
    public static function successResponse(string $message, $data = null, int $statusCode = 200): array
    {
        return self::apiResponse(true, $message, $data, $statusCode);
    }

    /**
     * Create error API response
     */
    public static function errorResponse(string $message, $data = null, int $statusCode = 400): array
    {
        return self::apiResponse(false, $message, $data, $statusCode);
    }
}
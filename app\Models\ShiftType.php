<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShiftType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'description',
        'default_start_time',
        'default_end_time',
        'default_break_duration_minutes',
        'hourly_rate',
        'overtime_rate_multiplier',
        'color_code',
        'is_active',
        'requires_approval',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'default_start_time' => 'datetime:H:i:s',
            'default_end_time' => 'datetime:H:i:s',
            'hourly_rate' => 'decimal:2',
            'overtime_rate_multiplier' => 'decimal:2',
            'is_active' => 'boolean',
            'requires_approval' => 'boolean',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function shifts()
    {
        return $this->hasMany(Shift::class);
    }

    public function assignments()
    {
        return $this->hasMany(ShiftAssignment::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    // Methods
    public function getDefaultDurationInHours()
    {
        $start = \Carbon\Carbon::parse($this->default_start_time);
        $end = \Carbon\Carbon::parse($this->default_end_time);
        
        // Handle overnight shifts
        if ($end->lt($start)) {
            $end->addDay();
        }
        
        $totalMinutes = $end->diffInMinutes($start);
        $workingMinutes = $totalMinutes - $this->default_break_duration_minutes;
        
        return $workingMinutes / 60;
    }

    public function calculatePay($hoursWorked)
    {
        $regularHours = min($hoursWorked, 8); // Assuming 8 hours is regular time
        $overtimeHours = max(0, $hoursWorked - 8);
        
        $regularPay = $regularHours * $this->hourly_rate;
        $overtimePay = $overtimeHours * $this->hourly_rate * $this->overtime_rate_multiplier;
        
        return $regularPay + $overtimePay;
    }
}
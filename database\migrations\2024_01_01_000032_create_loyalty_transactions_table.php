<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->foreignId('loyalty_program_id')->constrained('loyalty_programs')->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained('orders');
            $table->enum('transaction_type', ['earned', 'redeemed', 'expired', 'adjusted']);
            $table->decimal('points', 10, 2);
            $table->decimal('points_balance', 10, 2)->comment('Balance after this transaction');
            $table->text('description')->nullable();
            $table->date('expiry_date')->nullable();
            $table->foreignId('processed_by')->nullable()->constrained('users');
            $table->timestamps();
            
            $table->index(['customer_id', 'transaction_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_transactions');
    }
};
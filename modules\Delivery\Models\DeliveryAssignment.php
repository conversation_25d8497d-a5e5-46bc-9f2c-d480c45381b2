<?php

namespace Modules\Delivery\Models;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class DeliveryAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'delivery_personnel_id',
        'delivery_zone_id',
        'status',
        'assigned_at',
        'picked_up_at',
        'delivered_at',
        'delivery_notes',
        'failure_reason',
        'delivery_proof',
        'distance_km',
        'estimated_duration_minutes',
        'actual_duration_minutes',
        'delivery_fee_earned',
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'picked_up_at' => 'datetime',
        'delivered_at' => 'datetime',
        'delivery_proof' => 'array',
        'distance_km' => 'decimal:2',
        'delivery_fee_earned' => 'decimal:2',
    ];

    /**
     * Get the order for this assignment.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the delivery personnel for this assignment.
     */
    public function deliveryPersonnel(): BelongsTo
    {
        return $this->belongsTo(DeliveryPersonnel::class);
    }

    /**
     * Get the delivery zone for this assignment.
     */
    public function deliveryZone(): BelongsTo
    {
        return $this->belongsTo(DeliveryZone::class);
    }

    /**
     * Get tracking records for this assignment.
     */
    public function trackingRecords(): HasMany
    {
        return $this->hasMany(DeliveryTracking::class);
    }

    /**
     * Get the latest tracking record.
     */
    public function latestTracking(): HasOne
    {
        return $this->hasOne(DeliveryTracking::class)->latest('recorded_at');
    }

    /**
     * Get delivery review for this assignment.
     */
    public function review(): HasOne
    {
        return $this->hasOne(DeliveryReview::class);
    }

    /**
     * Get delivery tips for this assignment.
     */
    public function tips(): HasMany
    {
        return $this->hasMany(DeliveryTip::class);
    }

    /**
     * Get the total tip amount for this assignment.
     */
    public function getTotalTipAmount(): float
    {
        return $this->tips()->where('status', 'paid')->sum('amount');
    }

    /**
     * Mark as picked up.
     */
    public function markAsPickedUp(): void
    {
        $this->update([
            'status' => 'picked_up',
            'picked_up_at' => now(),
        ]);

        // Update order status
        $this->order->update(['status' => 'ready']);
    }

    /**
     * Mark as in transit.
     */
    public function markAsInTransit(): void
    {
        $this->update(['status' => 'in_transit']);
    }

    /**
     * Mark as delivered.
     */
    public function markAsDelivered(array $deliveryProof = [], string $notes = ''): void
    {
        $actualDuration = $this->picked_up_at ? 
            now()->diffInMinutes($this->picked_up_at) : null;

        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'delivery_notes' => $notes,
            'delivery_proof' => $deliveryProof,
            'actual_duration_minutes' => $actualDuration,
        ]);

        // Update order status
        $this->order->update([
            'status' => 'completed',
            'served_time' => now(),
        ]);

        // Update delivery personnel stats
        $this->deliveryPersonnel->increment('total_deliveries');
        $this->deliveryPersonnel->increment('total_earnings', $this->delivery_fee_earned);
    }

    /**
     * Mark as failed.
     */
    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
        ]);

        // Update delivery personnel status
        $this->deliveryPersonnel->update(['status' => 'active']);
    }

    /**
     * Cancel the assignment.
     */
    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
        
        // Update delivery personnel status
        $this->deliveryPersonnel->update(['status' => 'active']);
    }

    /**
     * Calculate estimated delivery time based on distance and traffic.
     */
    public function calculateEstimatedDuration(): int
    {
        if (!$this->distance_km) {
            return $this->deliveryZone?->estimated_delivery_time_minutes ?? 30;
        }

        // Base calculation: 30 km/h average speed + 5 minutes preparation
        $baseMinutes = ($this->distance_km / 30) * 60 + 5;
        
        // Add traffic factor (20% increase during peak hours)
        $hour = now()->hour;
        $isPeakHour = ($hour >= 11 && $hour <= 14) || ($hour >= 18 && $hour <= 21);
        
        if ($isPeakHour) {
            $baseMinutes *= 1.2;
        }

        return (int) ceil($baseMinutes);
    }

    /**
     * Get current location of the delivery.
     */
    public function getCurrentLocation(): ?array
    {
        $latest = $this->latestTracking;
        
        if (!$latest) {
            return null;
        }

        return [
            'latitude' => $latest->latitude,
            'longitude' => $latest->longitude,
            'accuracy' => $latest->accuracy,
            'speed' => $latest->speed,
            'bearing' => $latest->bearing,
            'recorded_at' => $latest->recorded_at,
        ];
    }

    /**
     * Scope for active assignments.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['assigned', 'picked_up', 'in_transit']);
    }

    /**
     * Scope for completed assignments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope for assignments by personnel.
     */
    public function scopeByPersonnel($query, int $personnelId)
    {
        return $query->where('delivery_personnel_id', $personnelId);
    }
}
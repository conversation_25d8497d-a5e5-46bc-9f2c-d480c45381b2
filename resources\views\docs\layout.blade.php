<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Documentation - EPSIS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar-item {
            transition: all 0.3s ease;
        }
        .sidebar-item:hover {
            transform: translateX(4px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .content-fade {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .module-card {
            transition: all 0.3s ease;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .endpoint-dropdown {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .endpoint-dropdown.open {
            max-height: 1000px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation Bar -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900">
                            <i class="fas fa-code text-blue-600 mr-2"></i>
                            EPSIS API Docs
                        </h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="globalSearch" placeholder="Search endpoints..." 
                               class="w-64 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute right-3 top-3 text-gray-400"></i>
                    </div>
               
                </div>
            </div>
        </div>
    </nav>

    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-layer-group mr-2 text-blue-600"></i>
                    Modules
                </h2>
                <nav class="space-y-2">
                    <a href="{{ route('docs.module', 'auth') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'auth' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-shield-alt mr-3"></i>
                        Authentication
                    </a>
                    <a href="{{ route('docs.module', 'menu') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'menu' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-utensils mr-3"></i>
                        Menu
                    </a>
                    <a href="{{ route('docs.module', 'restaurant') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'restaurant' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-store mr-3"></i>
                        restaurant
                    </a>
                    <a href="{{ route('docs.module', 'orders') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'orders' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-shopping-cart mr-3"></i>
                        Orders
                    </a>
                    <a href="{{ route('docs.module', 'customers') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'customers' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-users mr-3"></i>
                        Customers
                    </a>
                    <a href="{{ route('docs.module', 'hr') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'hr' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-user-tie mr-3"></i>
                        hr
                    </a>
                    <a href="{{ route('docs.module', 'inventory') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'inventory' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-boxes mr-3"></i>
                        Inventory
                    </a>
                    <a href="{{ route('docs.module', 'kitchen') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'kitchen' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-fire mr-3"></i>
                        Kitchen
                    </a>
                    <a href="{{ route('docs.module', 'payment') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'payment' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-credit-card mr-3"></i>
                        Payment
                    </a>
                    <a href="{{ route('docs.module', 'delivery') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'delivery' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-truck mr-3"></i>
                        Delivery
                    </a>

                    <a href="{{ route('docs.module', 'reports') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'reports' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Reports
                    </a>

                    <a href="{{ route('docs.module', 'settings') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'settings' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-cog mr-3"></i>
                        Settings
                    </a>
                    <a href="{{ route('docs.module', 'tenant') }}" 
                       class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:text-white {{ request()->route('module') == 'tenant' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white' : 'hover:bg-gray-100' }}">
                        <i class="fas fa-building mr-3"></i>
                        Tenant
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-y-auto">
            <div class="content-fade p-8">
                @yield('content')
            </div>
        </div>
    </div>

    <script>
        // Global search functionality
        document.getElementById('globalSearch').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const endpoints = document.querySelectorAll('.endpoint-item');
            
            endpoints.forEach(endpoint => {
                const text = endpoint.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    endpoint.style.display = 'block';
                } else {
                    endpoint.style.display = 'none';
                }
            });
        });

        // Dropdown toggle functionality
        function toggleDropdown(element) {
            const dropdown = element.nextElementSibling;
            const icon = element.querySelector('.dropdown-icon');
            
            dropdown.classList.toggle('open');
            icon.classList.toggle('fa-chevron-down');
            icon.classList.toggle('fa-chevron-up');
        }

        // Section toggle functionality for module documentation
        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            const icon = document.getElementById(sectionId + '-icon');
            
            if (section) {
                section.classList.toggle('hidden');
            }
            
            if (icon) {
                icon.classList.toggle('rotate-180');
            }
        }
    </script>
</body>
</html>
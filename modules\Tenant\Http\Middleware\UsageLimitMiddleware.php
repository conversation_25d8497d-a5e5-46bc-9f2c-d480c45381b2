<?php

namespace Modules\Tenant\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Tenant;
use Modules\Tenant\Helpers\TenantHelper;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class UsageLimitMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $feature, ?string $action = 'create'): BaseResponse
    {
        $tenant = $this->getTenant($request);
        
        if (!$tenant) {
            return response()->json([
                'error' => 'Tenant required',
                'message' => 'This endpoint requires a valid tenant context'
            ], 400);
        }
        
        // Check if tenant has access to the feature
        if (!TenantHelper::hasFeatureAccess($tenant, $feature)) {
            return response()->json([
                'error' => 'Feature not available',
                'message' => "The '{$feature}' feature is not available in your current plan",
                'feature' => $feature,
                'current_plan' => $tenant->activeSubscription?->plan?->name,
                'action_required' => 'upgrade_plan'
            ], 403);
        }
        
        // Only check usage limits for create/add actions
        if ($action === 'create' || $action === 'add') {
            $usageCheck = $this->checkUsageLimit($tenant, $feature);
            if ($usageCheck) {
                return $usageCheck;
            }
        }
        
        // Add usage info to request
        $request->merge([
            'feature_usage' => $this->getUsageInfo($tenant, $feature)
        ]);
        
        return $next($request);
    }
    
    /**
     * Get tenant from request
     */
    protected function getTenant(Request $request): ?Tenant
    {
        // Try to get tenant from request (set by TenantMiddleware)
        if ($request->has('tenant')) {
            return $request->get('tenant');
        }
        
        // Try to get from app container
        if (app()->bound('tenant')) {
            return app('tenant');
        }
        
        return null;
    }
    
    /**
     * Check usage limit for a feature
     */
    protected function checkUsageLimit(Tenant $tenant, string $feature): ?BaseResponse
    {
        $currentUsage = TenantHelper::getCurrentUsage($tenant, $feature);
        $limit = TenantHelper::getFeatureLimit($tenant, $feature);
        
        // If no limit is set or unlimited (-1), allow access
        if ($limit === null || $limit === -1) {
            return null;
        }
        
        // Check if limit is reached
        if ($currentUsage >= $limit) {
            $usageInfo = $this->getUsageInfo($tenant, $feature);
            
            return response()->json([
                'error' => 'Usage limit reached',
                'message' => $this->getUsageLimitMessage($feature, $currentUsage, $limit),
                'feature' => $feature,
                'usage_info' => $usageInfo,
                'upgrade_suggestions' => $this->getUpgradeSuggestions($tenant, $feature),
                'action_required' => 'upgrade_plan'
            ], 429); // Too Many Requests
        }
        
        // Check if approaching limit (90% threshold)
        $threshold = $limit * 0.9;
        if ($currentUsage >= $threshold) {
            // Add warning header but don't block the request
            response()->header('X-Usage-Warning', "Approaching limit for {$feature}: {$currentUsage}/{$limit}");
        }
        
        return null;
    }
    
    /**
     * Get usage information for a feature
     */
    protected function getUsageInfo(Tenant $tenant, string $feature): array
    {
        $currentUsage = TenantHelper::getCurrentUsage($tenant, $feature);
        $limit = TenantHelper::getFeatureLimit($tenant, $feature);
        
        $usagePercentage = 0;
        $remaining = null;
        $isUnlimited = $limit === -1;
        
        if ($limit > 0) {
            $usagePercentage = ($currentUsage / $limit) * 100;
            $remaining = max(0, $limit - $currentUsage);
        }
        
        return [
            'feature' => $feature,
            'current_usage' => $currentUsage,
            'limit' => $limit,
            'remaining' => $remaining,
            'usage_percentage' => round($usagePercentage, 2),
            'is_unlimited' => $isUnlimited,
            'is_over_limit' => $limit > 0 && $currentUsage > $limit,
            'is_approaching_limit' => $limit > 0 && $usagePercentage >= 90,
            'status' => $this->getUsageStatus($usagePercentage, $isUnlimited, $currentUsage > $limit)
        ];
    }
    
    /**
     * Get usage status
     */
    protected function getUsageStatus(float $percentage, bool $isUnlimited, bool $isOverLimit): string
    {
        if ($isUnlimited) {
            return 'unlimited';
        }
        
        if ($isOverLimit) {
            return 'over_limit';
        }
        
        if ($percentage >= 90) {
            return 'approaching_limit';
        }
        
        if ($percentage >= 75) {
            return 'high_usage';
        }
        
        if ($percentage >= 50) {
            return 'moderate_usage';
        }
        
        return 'low_usage';
    }
    
    /**
     * Get usage limit message
     */
    protected function getUsageLimitMessage(string $feature, int $currentUsage, int $limit): string
    {
        $featureMessages = [
            'branches' => "You have reached the maximum number of branches ({$limit}) allowed in your current plan.",
            'users' => "You have reached the maximum number of users ({$limit}) allowed in your current plan.",
            'menu_items' => "You have reached the maximum number of menu items ({$limit}) allowed in your current plan.",
            'orders_per_month' => "You have reached the monthly order limit ({$limit}) for your current plan.",
            'storage_mb' => "You have reached the storage limit ({$limit} MB) for your current plan.",
            'customers' => "You have reached the maximum number of customers ({$limit}) allowed in your current plan.",
            'staff' => "You have reached the maximum number of staff members ({$limit}) allowed in your current plan.",
            'tables' => "You have reached the maximum number of tables ({$limit}) allowed in your current plan.",
            'categories' => "You have reached the maximum number of categories ({$limit}) allowed in your current plan.",
        ];
        
        return $featureMessages[$feature] ?? "You have reached the limit for {$feature} ({$currentUsage}/{$limit}) in your current plan.";
    }
    
    /**
     * Get upgrade suggestions
     */
    protected function getUpgradeSuggestions(Tenant $tenant, string $feature): array
    {
        $currentPlan = $tenant->activeSubscription?->plan;
        
        if (!$currentPlan) {
            return [];
        }
        
        // Get available plans that have higher limits for this feature
        $availablePlans = TenantHelper::getAvailablePlans($tenant)
            ->filter(function ($plan) use ($feature, $currentPlan) {
                $currentLimit = $currentPlan->limits[$feature] ?? 0;
                $planLimit = $plan->limits[$feature] ?? 0;
                
                return $planLimit > $currentLimit || $planLimit === -1;
            })
            ->take(3)
            ->map(function ($plan) use ($feature) {
                $limit = $plan->limits[$feature] ?? 0;
                
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'price' => $plan->monthly_price,
                    'limit' => $limit,
                    'is_unlimited' => $limit === -1,
                    'description' => $plan->description,
                ];
            })
            ->values()
            ->toArray();
        
        return $availablePlans;
    }
}
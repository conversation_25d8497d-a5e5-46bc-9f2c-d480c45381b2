<?php

namespace Modules\Tenant\Http\Controllers;

use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Modules\Tenant\Services\BranchService;
use Modules\Tenant\Http\Requests\CreateBranchRequest;
use Modules\Tenant\Http\Requests\UpdateBranchRequest;

class BranchController extends Controller
{
    protected $branchService;

    public function __construct(BranchService $branchService)
    {
        $this->branchService = $branchService;
    }

    /**
     * Display a listing of branches
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $status = $request->get('status');
            $tenantId = $request->get('tenant_id');
            
            $query = Branch::with(['tenant']);
            
            if ($tenantId) {
                $query->where('tenant_id', $tenantId);
            }
            
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            }
            
            if ($status !== null) {
                $query->where('is_active', $status === 'active');
            }
            
            $branches = $query->orderBy('created_at', 'desc')->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => $branches,
                'message' => 'Branches retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve branches: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created branch
     */
    public function store(CreateBranchRequest $request): JsonResponse
    {
        Log::info('Create Branch Request', $request->all());
        try {
            DB::beginTransaction();
            
            $branch = $this->branchService->createBranch($request->validated());
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $branch->load(['tenant']),
                'message' => 'Branch created successfully'
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create branch: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified branch
     */
    public function show(Branch $branch): JsonResponse
    {
        try {
            $branch->load(['tenant']);
            
            return response()->json([
                'success' => true,
                'data' => $branch,
                'message' => 'Branch retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve branch: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified branch
     */
    public function update(UpdateBranchRequest $request, Branch $branch): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $updatedBranch = $this->branchService->updateBranch($branch, $request->validated());
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'data' => $updatedBranch->load(['tenant']),
                'message' => 'Branch updated successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update branch: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified branch
     */
    public function destroy(Branch $branch): JsonResponse
    {
        try {
            DB::beginTransaction();
            
            $this->branchService->deleteBranch($branch);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Branch deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete branch: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get branch statistics
     */
    public function statistics(Branch $branch): JsonResponse
    {
        try {
            $stats = $this->branchService->getBranchStatistics($branch);
            
            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Branch statistics retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve branch statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Activate branch
     */
    public function activate(Branch $branch): JsonResponse
    {
        try {
            $branch = $this->branchService->activateBranch($branch);
            
            return response()->json([
                'success' => true,
                'data' => $branch,
                'message' => 'Branch activated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to activate branch: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deactivate branch
     */
    public function deactivate(Branch $branch): JsonResponse
    {
        try {
            $branch = $this->branchService->deactivateBranch($branch);
            
            return response()->json([
                'success' => true,
                'data' => $branch,
                'message' => 'Branch deactivated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to deactivate branch: ' . $e->getMessage()
            ], 500);
        }
    }
}
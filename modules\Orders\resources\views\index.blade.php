@extends('layouts.app')

@section('title', 'Orders Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Orders Management</h3>
                    <a href="{{ route('orders.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> New Order
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-control" id="status-filter">
                                <option value="">All Statuses</option>
                                <option value="pending">Pending</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="preparing">Preparing</option>
                                <option value="ready">Ready</option>
                                <option value="served">Served</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="type-filter">
                                <option value="">All Types</option>
                                <option value="dine_in">Dine In</option>
                                <option value="takeaway">Takeaway</option>
                                <option value="delivery">Delivery</option>
                                <option value="online">Online Order</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="date-filter" placeholder="Filter by date">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-secondary" id="clear-filters">Clear Filters</button>
                        </div>
                    </div>

                    <!-- Orders Table -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="orders-table">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Table</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Orders will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div id="pagination-container"></div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let currentPage = 1;
    
    // Load orders
    function loadOrders(page = 1) {
        const filters = {
            status: $('#status-filter').val(),
            order_type: $('#type-filter').val(),
            date: $('#date-filter').val(),
            page: page
        };
        
        $.ajax({
            url: '/api/orders',
            method: 'GET',
            data: filters,
            success: function(response) {
                renderOrdersTable(response.data);
                renderPagination(response);
            },
            error: function(xhr) {
                console.error('Error loading orders:', xhr);
                alert('Error loading orders');
            }
        });
    }
    
    // Render orders table
    function renderOrdersTable(orders) {
        const tbody = $('#orders-table tbody');
        tbody.empty();
        
        orders.forEach(order => {
            const row = `
                <tr>
                    <td>#${order.order_number}</td>
                    <td>${order.customer ? order.customer.name : 'Walk-in'}</td>
                    <td>${order.table ? order.table.name : 'N/A'}</td>
                    <td><span class="badge badge-info">${order.order_type}</span></td>
                    <td><span class="badge badge-${getStatusColor(order.status)}">${order.status}</span></td>
                    <td>$${parseFloat(order.total_amount).toFixed(2)}</td>
                    <td>${new Date(order.created_at).toLocaleDateString()}</td>
                    <td>
                        <a href="/orders/${order.id}" class="btn btn-sm btn-info">View</a>
                        <a href="/orders/${order.id}/edit" class="btn btn-sm btn-warning">Edit</a>
                        <button class="btn btn-sm btn-danger" onclick="deleteOrder(${order.id})">Delete</button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }
    
    // Get status color for badge
    function getStatusColor(status) {
        const colors = {
            'pending': 'warning',
            'confirmed': 'info',
            'preparing': 'primary',
            'ready': 'success',
            'served': 'success',
            'completed': 'success',
            'cancelled': 'danger'
        };
        return colors[status] || 'secondary';
    }
    
    // Render pagination
    function renderPagination(response) {
        // Implementation for pagination
        $('#pagination-container').html('');
    }
    
    // Delete order
    window.deleteOrder = function(orderId) {
        if (confirm('Are you sure you want to delete this order?')) {
            $.ajax({
                url: `/api/orders/${orderId}`,
                method: 'DELETE',
                success: function() {
                    loadOrders(currentPage);
                    alert('Order deleted successfully');
                },
                error: function(xhr) {
                    console.error('Error deleting order:', xhr);
                    alert('Error deleting order');
                }
            });
        }
    };
    
    // Event listeners
    $('#status-filter, #type-filter, #date-filter').on('change', function() {
        currentPage = 1;
        loadOrders();
    });
    
    $('#clear-filters').on('click', function() {
        $('#status-filter, #type-filter, #date-filter').val('');
        currentPage = 1;
        loadOrders();
    });
    
    // Initial load
    loadOrders();
});
</script>
@endpush
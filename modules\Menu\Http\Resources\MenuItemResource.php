<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class MenuItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'menu_id' => $this->menu_id,
            'category_id' => $this->category_id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'short_description' => $this->short_description,
            'base_price' => $this->base_price,
            'cost_price' => $this->cost_price,
            'image_urls' => $this->image_urls,
            'prep_time_minutes' => $this->prep_time_minutes,
            'calories' => $this->calories,
            'nutritional_info' => $this->nutritional_info,
            'allergens' => $this->allergens,
            'dietary_info' => $this->dietary_info,
            'recipe_id' => $this->recipe_id,
            'barcode' => $this->barcode,
            'sku' => $this->sku,
            'is_active' => $this->is_active,
            'is_featured' => $this->is_featured,
            'is_spicy' => $this->is_spicy,
            'spice_level' => $this->spice_level,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
            
            // Relationships
            'menu' => $this->whenLoaded('menu', function() {
                return [
                    'id' => $this->menu->id,
                    'name' => $this->menu->name,
                    'code' => $this->menu->code,
                    'menu_type' => $this->menu->menu_type,
                ];
            }),
            'category' => $this->whenLoaded('category', function() {
                return [
                    'id' => $this->category->id,
                    'name' => $this->category->name,
                    'code' => $this->category->code,
                    'description' => $this->category->description,
                ];
            }),
            'recipe' => $this->whenLoaded('recipe'),
            'variants' => VariationResource::collection($this->whenLoaded('variants')),
            'addons' => AddonResource::collection($this->whenLoaded('addons')),
            'branches' => $this->whenLoaded('branches'),
            'availability' => $this->whenLoaded('availability'),
            'kitchen_menu_item' => $this->whenLoaded('kitchenMenuItem'),
            'assigned_kitchen' => $this->whenLoaded('assignedKitchen'),
        ];
    }
}
<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateDeliveryZoneRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasPermission('manage_delivery_zones');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'branch_id' => [
                'required',
                'integer',
                'exists:branches,id'
            ],
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('delivery_zones')->where(function ($query) {
                    return $query->where('branch_id', $this->branch_id);
                })
            ],
            'description' => [
                'nullable',
                'string',
                'max:500'
            ],
            'polygon_coordinates' => [
                'required',
                'array',
                'min:3' // At least 3 points to form a polygon
            ],
            'polygon_coordinates.*' => [
                'required',
                'array',
                'size:2' // Each coordinate should have exactly 2 elements [lat, lng]
            ],
            'polygon_coordinates.*.0' => [
                'required',
                'numeric',
                'between:-90,90' // Latitude range
            ],
            'polygon_coordinates.*.1' => [
                'required',
                'numeric',
                'between:-180,180' // Longitude range
            ],
            'delivery_fee' => [
                'required',
                'numeric',
                'min:0',
                'max:999.99'
            ],
            'minimum_order_amount' => [
                'required',
                'numeric',
                'min:0',
                'max:9999.99'
            ],
            'estimated_delivery_time_minutes' => [
                'required',
                'integer',
                'min:5',
                'max:300' // Max 5 hours
            ],
            'priority' => [
                'required',
                'integer',
                'min:1',
                'max:100'
            ],
            'is_active' => [
                'sometimes',
                'boolean'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.unique' => 'A delivery zone with this name already exists for this branch.',
            'polygon_coordinates.min' => 'A delivery zone must have at least 3 coordinate points.',
            'polygon_coordinates.*.size' => 'Each coordinate must contain exactly latitude and longitude.',
            'polygon_coordinates.*.0.between' => 'Latitude must be between -90 and 90 degrees.',
            'polygon_coordinates.*.1.between' => 'Longitude must be between -180 and 180 degrees.',
            'delivery_fee.max' => 'Delivery fee cannot exceed $999.99.',
            'minimum_order_amount.max' => 'Minimum order amount cannot exceed $9999.99.',
            'estimated_delivery_time_minutes.min' => 'Estimated delivery time must be at least 5 minutes.',
            'estimated_delivery_time_minutes.max' => 'Estimated delivery time cannot exceed 5 hours.',
            'priority.between' => 'Priority must be between 1 and 100.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'branch_id' => 'branch',
            'polygon_coordinates' => 'zone coordinates',
            'delivery_fee' => 'delivery fee',
            'minimum_order_amount' => 'minimum order amount',
            'estimated_delivery_time_minutes' => 'estimated delivery time',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure is_active defaults to true if not provided
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }
    }
}
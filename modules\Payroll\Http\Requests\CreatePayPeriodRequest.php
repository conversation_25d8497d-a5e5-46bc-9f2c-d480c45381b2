<?php

namespace Modules\Payroll\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreatePayPeriodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check() && in_array(Auth::user()->role, ['admin', 'manager']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'nullable|string|max:100',
            'period_type' => 'required|in:weekly,bi_weekly,monthly,quarterly',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'period_type.required' => 'The period type is required.',
            'period_type.in' => 'The period type must be one of: weekly, bi-weekly, monthly, quarterly.',
            'start_date.required' => 'The start date is required.',
            'start_date.date' => 'The start date must be a valid date.',
            'start_date.after_or_equal' => 'The start date must be today or a future date.',
            'end_date.required' => 'The end date is required.',
            'end_date.date' => 'The end date must be a valid date.',
            'end_date.after' => 'The end date must be after the start date.',
            'name.max' => 'The name may not be greater than 100 characters.',
            'notes.max' => 'The notes may not be greater than 1000 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'tenant_id' => Auth::user()->tenant_id,
        ]);
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add tenant_id to validated data
        $validated['tenant_id'] = Auth::user()->tenant_id;
        
        return $validated;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check for overlapping pay periods
            $this->validateNoOverlappingPeriods($validator);
            
            // Validate period duration based on type
            $this->validatePeriodDuration($validator);
        });
    }

    /**
     * Validate that the new pay period doesn't overlap with existing ones.
     */
    private function validateNoOverlappingPeriods($validator): void
    {
        if (!$this->has('start_date') || !$this->has('end_date')) {
            return;
        }

        $startDate = $this->input('start_date');
        $endDate = $this->input('end_date');
        $tenantId = Auth::user()->tenant_id;

        $overlapping = \App\Models\PayPeriod::where('tenant_id', $tenantId)
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                      ->orWhereBetween('end_date', [$startDate, $endDate])
                      ->orWhere(function ($q) use ($startDate, $endDate) {
                          $q->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                      });
            })
            ->exists();

        if ($overlapping) {
            $validator->errors()->add('date_range', 'The selected date range overlaps with an existing pay period.');
        }
    }

    /**
     * Validate that the period duration matches the expected duration for the type.
     */
    private function validatePeriodDuration($validator): void
    {
        if (!$this->has('start_date') || !$this->has('end_date') || !$this->has('period_type')) {
            return;
        }

        $startDate = \Carbon\Carbon::parse($this->input('start_date'));
        $endDate = \Carbon\Carbon::parse($this->input('end_date'));
        $periodType = $this->input('period_type');
        
        $actualDays = $startDate->diffInDays($endDate) + 1;
        $expectedDays = $this->getExpectedDaysForPeriodType($periodType);
        
        // Allow some flexibility in the duration (±2 days)
        $tolerance = 2;
        
        if (abs($actualDays - $expectedDays) > $tolerance) {
            $validator->errors()->add(
                'period_duration',
                "The duration for {$periodType} period should be approximately {$expectedDays} days, but {$actualDays} days were provided."
            );
        }
    }

    /**
     * Get expected number of days for a period type.
     */
    private function getExpectedDaysForPeriodType(string $periodType): int
    {
        switch ($periodType) {
            case 'weekly':
                return 7;
            case 'bi_weekly':
                return 14;
            case 'monthly':
                return 30; // Approximate
            case 'quarterly':
                return 90; // Approximate
            default:
                return 30;
        }
    }
}
<?php

namespace Modules\Inventory\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupplierResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'email' => $this->email,
            'phone' => $this->phone,
            'website' => $this->website,
            'contact_person' => $this->contact_person,
            'contact_phone' => $this->contact_phone,
            'contact_email' => $this->contact_email,
            
            // Address information
            'address' => [
                'street' => $this->address,
                'city' => $this->city,
                'state' => $this->state,
                'postal_code' => $this->postal_code,
                'country' => $this->country,
                'full_address' => trim(implode(', ', array_filter([
                    $this->address,
                    $this->city,
                    $this->state,
                    $this->postal_code,
                    $this->country
                ]))),
            ],
            
            // Financial information
            'financial' => [
                'tax_number' => $this->tax_number,
                'payment_terms' => $this->payment_terms,
                'credit_limit' => (float) $this->credit_limit,
                'current_balance' => (float) $this->current_balance,
                'credit_utilization' => $this->credit_limit > 0 ? 
                    round(($this->current_balance / $this->credit_limit) * 100, 2) : 0,
            ],
            
            // Operational information
            'operational' => [
                'category' => $this->category,
                'lead_time_days' => (int) $this->lead_time_days,
                'minimum_order_amount' => (float) $this->minimum_order_amount,
                'delivery_terms' => $this->delivery_terms,
                'quality_rating' => (float) $this->quality_rating,
                'is_active' => (bool) $this->is_active,
                'is_preferred' => (bool) $this->is_preferred,
            ],
            
            'notes' => $this->notes,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            
            // Performance metrics (if requested)
            'performance' => $this->when($request->get('include_performance'), function () {
                return [
                    'total_orders' => $this->purchaseOrders()->count(),
                    'total_value' => (float) $this->purchaseOrders()->sum('total_amount'),
                    'average_order_value' => (float) $this->purchaseOrders()->avg('total_amount'),
                    'on_time_delivery_rate' => $this->calculateOnTimeDeliveryRate(),
                    'average_delivery_time' => $this->calculateAverageDeliveryTime(),
                    'last_order_date' => $this->purchaseOrders()->latest()->first()?->created_at?->format('Y-m-d'),
                ];
            }),
            
            // Products (if requested)
            'products' => $this->whenLoaded('products', function () {
                return $this->products->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'unit' => $product->unit,
                        'category' => $product->category,
                        'supplier_product_code' => $product->pivot->supplier_product_code ?? null,
                        'supplier_unit_cost' => (float) ($product->pivot->unit_cost ?? 0),
                        'minimum_order_quantity' => (float) ($product->pivot->minimum_order_quantity ?? 0),
                    ];
                });
            }),
            
            // Recent purchase orders (if requested)
            'recent_orders' => $this->whenLoaded('purchaseOrders', function () {
                return $this->purchaseOrders->take(5)->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'po_number' => $order->po_number,
                        'status' => $order->status,
                        'total_amount' => (float) $order->total_amount,
                        'order_date' => $order->created_at->format('Y-m-d'),
                        'expected_delivery_date' => $order->expected_delivery_date?->format('Y-m-d'),
                        'actual_delivery_date' => $order->actual_delivery_date?->format('Y-m-d'),
                    ];
                });
            }),
            
            // Formatted values
            'formatted' => [
                'credit_limit' => number_format($this->credit_limit, 2),
                'current_balance' => number_format($this->current_balance, 2),
                'minimum_order_amount' => number_format($this->minimum_order_amount, 2),
                'quality_rating' => number_format($this->quality_rating, 1) . '/5.0',
                'lead_time' => $this->lead_time_days . ' day' . ($this->lead_time_days != 1 ? 's' : ''),
            ],
        ];
    }
    
    /**
     * Calculate on-time delivery rate.
     */
    protected function calculateOnTimeDeliveryRate(): float
    {
        $totalOrders = $this->purchaseOrders()
            ->whereNotNull('actual_delivery_date')
            ->count();
            
        if ($totalOrders === 0) {
            return 0;
        }
        
        $onTimeOrders = $this->purchaseOrders()
            ->whereNotNull('actual_delivery_date')
            ->whereRaw('actual_delivery_date <= expected_delivery_date')
            ->count();
            
        return round(($onTimeOrders / $totalOrders) * 100, 2);
    }
    
    /**
     * Calculate average delivery time.
     */
    protected function calculateAverageDeliveryTime(): float
    {
        $orders = $this->purchaseOrders()
            ->whereNotNull('actual_delivery_date')
            ->selectRaw('AVG(DATEDIFF(actual_delivery_date, created_at)) as avg_days')
            ->first();
            
        return round($orders->avg_days ?? 0, 1);
    }
    
    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'timestamp' => now()->toISOString(),
                'timezone' => config('app.timezone'),
            ],
        ];
    }
}
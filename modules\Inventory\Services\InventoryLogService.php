<?php

namespace Modules\Inventory\Services;

use App\Models\InventoryLog;
use App\Models\InventoryMovement;
use App\Models\BranchInventory;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class InventoryLogService
{
    /**
     * Get all inventory logs with filtering and pagination
     */
    public function getAllLogs(array $filters = [])
    {
        $query = InventoryLog::with([
            'branchInventory.product',
            'branchInventory.branch',
            'user'
        ])->whereHas('branchInventory', function ($q) {
            $q->where('branch_id', $this->getCurrentBranchId());
        });

        // Apply filters
        if (isset($filters['search'])) {
            $query->whereHas('branchInventory.product', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('sku', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (isset($filters['action'])) {
            $query->where('action', $filters['action']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['product_id'])) {
            $query->whereHas('branchInventory', function ($q) use ($filters) {
                $q->where('product_id', $filters['product_id']);
            });
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 20);
    }

    /**
     * Get logs for a specific inventory item
     */
    public function getItemLogs(string $inventoryId, array $filters = [])
    {
        $query = InventoryLog::with(['user'])
            ->where('branch_inventory_id', $inventoryId);

        if (isset($filters['action'])) {
            $query->where('action', $filters['action']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 20);
    }

    /**
     * Get logs for a specific user
     */
    public function getUserLogs(string $userId, array $filters = [])
    {
        $query = InventoryLog::with([
            'branchInventory.product',
            'branchInventory.branch'
        ])->where('user_id', $userId)
          ->whereHas('branchInventory', function ($q) {
              $q->where('branch_id', $this->getCurrentBranchId());
          });

        if (isset($filters['action'])) {
            $query->where('action', $filters['action']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 20);
    }

    /**
     * Get inventory movements summary
     */
    public function getMovementsSummary(array $filters = [])
    {
        $query = InventoryMovement::whereHas('branchInventory', function ($q) {
            $q->where('branch_id', $this->getCurrentBranchId());
        });

        // Apply filters
        if (isset($filters['start_date'])) {
            $query->whereDate('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->whereDate('created_at', '<=', $filters['end_date']);
        }

        if (isset($filters['product_id'])) {
            $query->whereHas('branchInventory', function ($q) use ($filters) {
                $q->where('product_id', $filters['product_id']);
            });
        }

        if (isset($filters['movement_type'])) {
            $query->where('type', $filters['movement_type']);
        }

        $movements = $query->with(['branchInventory.product'])->get();

        return [
            'total_movements' => $movements->count(),
            'movements_by_type' => $movements->groupBy('type')->map->count(),
            'total_quantity_in' => $movements->whereIn('type', ['add', 'initial_stock', 'return'])->sum('quantity'),
            'total_quantity_out' => $movements->whereIn('type', ['subtract', 'waste', 'transfer'])->sum('quantity'),
            'movements_by_product' => $movements->groupBy('branchInventory.product.name')->map->count(),
            'recent_movements' => $movements->sortByDesc('created_at')->take(10)->values(),
        ];
    }

    /**
     * Get stock level history for an item
     */
    public function getStockHistory(string $inventoryId, array $filters = [])
    {
        $inventory = BranchInventory::where('branch_id', $this->getCurrentBranchId())
            ->findOrFail($inventoryId);

        $query = InventoryLog::where('branch_inventory_id', $inventoryId);

        if (isset($filters['start_date'])) {
            $query->whereDate('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->whereDate('created_at', '<=', $filters['end_date']);
        }

        $logs = $query->orderBy('created_at')->get();
        $interval = $filters['interval'] ?? 'day';

        return $this->aggregateStockHistory($logs, $interval);
    }

    /**
     * Get inventory valuation history
     */
    public function getValuationHistory(array $filters = [])
    {
        $query = BranchInventory::where('branch_id', $this->getCurrentBranchId())
            ->with(['product', 'logs' => function ($q) use ($filters) {
                if (isset($filters['start_date'])) {
                    $q->whereDate('created_at', '>=', $filters['start_date']);
                }
                if (isset($filters['end_date'])) {
                    $q->whereDate('created_at', '<=', $filters['end_date']);
                }
                $q->orderBy('created_at');
            }]);

        $inventories = $query->get();
        $interval = $filters['interval'] ?? 'day';

        return $this->calculateValuationHistory($inventories, $interval);
    }

    /**
     * Get low stock alerts history
     */
    public function getLowStockAlerts(array $filters = [])
    {
        $query = InventoryLog::with([
            'branchInventory.product',
            'user'
        ])->whereHas('branchInventory', function ($q) {
            $q->where('branch_id', $this->getCurrentBranchId());
        })->where('action', 'low_stock_alert');

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['resolved'])) {
            $query->where('resolved', $filters['resolved']);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 20);
    }

    /**
     * Get waste tracking report
     */
    public function getWasteReport(array $filters = [])
    {
        $query = InventoryMovement::with([
            'branchInventory.product',
            'user'
        ])->whereHas('branchInventory', function ($q) {
            $q->where('branch_id', $this->getCurrentBranchId());
        })->where('type', 'waste');

        if (isset($filters['start_date'])) {
            $query->whereDate('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->whereDate('created_at', '<=', $filters['end_date']);
        }

        if (isset($filters['category'])) {
            $query->whereHas('branchInventory.product', function ($q) use ($filters) {
                $q->where('category', $filters['category']);
            });
        }

        if (isset($filters['reason'])) {
            $query->where('reason', 'like', '%' . $filters['reason'] . '%');
        }

        $wasteMovements = $query->get();

        return [
            'total_waste_items' => $wasteMovements->count(),
            'total_waste_quantity' => $wasteMovements->sum('quantity'),
            'total_waste_value' => $wasteMovements->sum(function ($movement) {
                return $movement->quantity * $movement->branchInventory->cost_per_unit;
            }),
            'waste_by_category' => $wasteMovements->groupBy('branchInventory.product.category')
                ->map(function ($items) {
                    return [
                        'count' => $items->count(),
                        'quantity' => $items->sum('quantity'),
                        'value' => $items->sum(function ($item) {
                            return $item->quantity * $item->branchInventory->cost_per_unit;
                        })
                    ];
                }),
            'waste_by_reason' => $wasteMovements->groupBy('reason')->map->count(),
            'waste_by_product' => $wasteMovements->groupBy('branchInventory.product.name')
                ->map(function ($items) {
                    return [
                        'quantity' => $items->sum('quantity'),
                        'value' => $items->sum(function ($item) {
                            return $item->quantity * $item->branchInventory->cost_per_unit;
                        })
                    ];
                })->sortByDesc('value'),
            'recent_waste' => $wasteMovements->sortByDesc('created_at')->take(10)->values(),
        ];
    }

    /**
     * Export inventory logs
     */
    public function exportLogs(array $filters = [])
    {
        $logs = $this->getAllLogs(array_merge($filters, ['per_page' => 10000]))->items();
        $format = $filters['format'] ?? 'csv';

        switch ($format) {
            case 'csv':
                return $this->exportToCSV($logs);
            case 'excel':
                return $this->exportToExcel($logs);
            case 'pdf':
                return $this->exportToPDF($logs);
            default:
                return $this->exportToCSV($logs);
        }
    }

    /**
     * Get audit trail for specific inventory changes
     */
    public function getAuditTrail(string $inventoryId, array $filters = [])
    {
        $query = InventoryLog::with(['user'])
            ->where('branch_inventory_id', $inventoryId);

        if (isset($filters['start_date'])) {
            $query->whereDate('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->whereDate('created_at', '<=', $filters['end_date']);
        }

        if (isset($filters['action'])) {
            $query->where('action', $filters['action']);
        }

        $logs = $query->orderBy('created_at')->get();

        return $logs->map(function ($log) {
            return [
                'id' => $log->id,
                'action' => $log->action,
                'quantity_before' => $log->quantity_before,
                'quantity_after' => $log->quantity_after,
                'quantity_changed' => $log->quantity_changed,
                'reason' => $log->reason,
                'user' => $log->user ? $log->user->name : 'System',
                'timestamp' => $log->created_at,
                'ip_address' => $log->ip_address,
                'user_agent' => $log->user_agent,
            ];
        });
    }

    /**
     * Get inventory discrepancy report
     */
    public function getDiscrepancyReport(array $filters = [])
    {
        $threshold = $filters['threshold'] ?? 5; // 5% threshold by default
        
        $query = BranchInventory::with(['product'])
            ->where('branch_id', $this->getCurrentBranchId());

        $inventories = $query->get();
        $discrepancies = [];

        foreach ($inventories as $inventory) {
            $expectedStock = $this->calculateExpectedStock($inventory, $filters);
            $actualStock = $inventory->current_stock;
            $difference = abs($expectedStock - $actualStock);
            $percentageDiff = $expectedStock > 0 ? ($difference / $expectedStock) * 100 : 0;

            if ($percentageDiff >= $threshold) {
                $discrepancies[] = [
                    'inventory_id' => $inventory->id,
                    'product' => $inventory->product->name,
                    'sku' => $inventory->product->sku,
                    'expected_stock' => $expectedStock,
                    'actual_stock' => $actualStock,
                    'difference' => $expectedStock - $actualStock,
                    'percentage_difference' => round($percentageDiff, 2),
                    'value_impact' => ($expectedStock - $actualStock) * $inventory->cost_per_unit,
                ];
            }
        }

        return collect($discrepancies)->sortByDesc('percentage_difference')->values();
    }

    /**
     * Private helper methods
     */
    private function getCurrentBranchId()
    {
        return Auth::user()->branch_id ?? 1;
    }

    private function aggregateStockHistory($logs, $interval)
    {
        $grouped = $logs->groupBy(function ($log) use ($interval) {
            return $this->getIntervalKey($log->created_at, $interval);
        });

        return $grouped->map(function ($intervalLogs, $key) {
            $lastLog = $intervalLogs->last();
            return [
                'period' => $key,
                'stock_level' => $lastLog->quantity_after,
                'movements_count' => $intervalLogs->count(),
                'total_in' => $intervalLogs->where('quantity_changed', '>', 0)->sum('quantity_changed'),
                'total_out' => $intervalLogs->where('quantity_changed', '<', 0)->sum('quantity_changed'),
            ];
        })->values();
    }

    private function calculateValuationHistory($inventories, $interval)
    {
        $valuationData = [];
        
        foreach ($inventories as $inventory) {
            foreach ($inventory->logs as $log) {
                $key = $this->getIntervalKey($log->created_at, $interval);
                
                if (!isset($valuationData[$key])) {
                    $valuationData[$key] = [
                        'period' => $key,
                        'total_value' => 0,
                        'total_quantity' => 0,
                    ];
                }
                
                $valuationData[$key]['total_value'] += $log->quantity_after * $inventory->cost_per_unit;
                $valuationData[$key]['total_quantity'] += $log->quantity_after;
            }
        }
        
        return collect($valuationData)->values();
    }

    private function getIntervalKey($date, $interval)
    {
        $carbon = Carbon::parse($date);
        
        return match ($interval) {
            'hour' => $carbon->format('Y-m-d H:00'),
            'day' => $carbon->format('Y-m-d'),
            'week' => $carbon->startOfWeek()->format('Y-m-d'),
            'month' => $carbon->format('Y-m'),
            default => $carbon->format('Y-m-d')
        };
    }

    private function calculateExpectedStock($inventory, $filters)
    {
        // This is a simplified calculation
        // In a real implementation, you would calculate based on:
        // - Initial stock
        // - All recorded movements
        // - Expected consumption based on sales/recipes
        
        $startDate = $filters['start_date'] ?? now()->subDays(30);
        
        $movements = InventoryMovement::where('branch_inventory_id', $inventory->id)
            ->whereDate('created_at', '>=', $startDate)
            ->get();
            
        $totalIn = $movements->whereIn('type', ['add', 'return'])->sum('quantity');
        $totalOut = $movements->whereIn('type', ['subtract', 'waste'])->sum('quantity');
        
        return $inventory->current_stock + $totalOut - $totalIn;
    }

    private function exportToCSV($logs)
    {
        $csv = "Date,Product,SKU,Action,Quantity Before,Quantity After,Quantity Changed,Reason,User\n";
        
        foreach ($logs as $log) {
            $csv .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                $log->created_at->format('Y-m-d H:i:s'),
                $log->branchInventory->product->name,
                $log->branchInventory->product->sku,
                $log->action,
                $log->quantity_before,
                $log->quantity_after,
                $log->quantity_changed,
                $log->reason,
                $log->user ? $log->user->name : 'System'
            );
        }
        
        return [
            'content' => $csv,
            'mime_type' => 'text/csv'
        ];
    }

    private function exportToExcel($logs)
    {
        // Placeholder for Excel export
        // Would use a library like PhpSpreadsheet
        return $this->exportToCSV($logs);
    }

    private function exportToPDF($logs)
    {
        // Placeholder for PDF export
        // Would use a library like DomPDF or TCPDF
        return [
            'content' => 'PDF export not implemented',
            'mime_type' => 'application/pdf'
        ];
    }
}
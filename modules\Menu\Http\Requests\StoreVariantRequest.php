<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'menu_item_id' => 'required|exists:menu_items,id',
            'name' => 'required|string|max:255',
            'price_adjustment' => 'required|numeric',
            'cost_adjustment' => 'nullable|numeric',
            'sku' => 'nullable|string|max:100|unique:menu_item_variants,sku',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_item_id.required' => 'Menu item is required.',
            'menu_item_id.exists' => 'Selected menu item does not exist.',
            'name.required' => 'Variant name is required.',
            'name.max' => 'Variant name cannot exceed 255 characters.',
            'price_adjustment.required' => 'Price adjustment is required.',
            'price_adjustment.numeric' => 'Price adjustment must be a valid number.',
            'cost_adjustment.numeric' => 'Cost adjustment must be a valid number.',
            'sku.max' => 'SKU cannot exceed 100 characters.',
            'sku.unique' => 'SKU must be unique.',
            'sort_order.integer' => 'Sort order must be a valid number.',
            'sort_order.min' => 'Sort order must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'menu_item_id' => 'menu item',
            'price_adjustment' => 'price adjustment',
            'cost_adjustment' => 'cost adjustment',
            'is_active' => 'active status',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_active')) {
            $this->merge([
                'is_active' => filter_var($this->is_active, FILTER_VALIDATE_BOOLEAN)
            ]);
        }
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kot_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('kitchen_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->string('kot_number')->unique();
            $table->enum('status', ['pending', 'preparing', 'ready', 'completed', 'cancelled'])->default('pending');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->integer('estimated_prep_time_minutes')->nullable();
            $table->integer('actual_prep_time_minutes')->nullable();
            $table->json('items_data')->nullable(); // Snapshot of items at time of KOT creation
            $table->text('special_instructions')->nullable();
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('sent_to_kitchen_at')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes
            $table->index(['tenant_id', 'branch_id']);
            $table->index(['kitchen_id', 'status']);
            $table->index(['order_id', 'status']);
            $table->index(['status', 'priority']);
            $table->index(['tenant_id', 'status']);
            $table->index(['branch_id', 'status']);
            $table->index('assigned_to');
            $table->index('created_at');
            $table->index(['status', 'started_at']);
            $table->index(['status', 'estimated_prep_time_minutes']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kot_orders');
    }
};
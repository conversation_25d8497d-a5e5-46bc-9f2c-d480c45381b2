<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * All module service providers
     */
    protected $moduleProviders = [
        \Modules\Menu\Providers\MenuServiceProvider::class,
        \Modules\Orders\Providers\OrderServiceProvider::class,
        \Modules\Inventory\Providers\InventoryServiceProvider::class,
        \Modules\Payment\Providers\PaymentServiceProvider::class,
        \Modules\Kitchen\Providers\KitchenServiceProvider::class,
        // \Modules\Staff\Providers\StaffServiceProvider::class,
        \Modules\Customer\Providers\CustomerServiceProvider::class,
        \Modules\Reports\Providers\ReportsServiceProvider::class,
        \Modules\Settings\Providers\SettingsServiceProvider::class,
        // \Modules\Notifications\Providers\NotificationsServiceProvider::class,
        \Modules\Tenant\Providers\TenantServiceProvider::class,
        \Modules\Reservation\Providers\ReservationServiceProvider::class,
        // \Modules\OnlineOrder\Providers\OnlineOrderServiceProvider::class,
        \Modules\Delivery\Providers\DeliveryServiceProvider::class,
        \Modules\Auth\Providers\AuthServiceProvider::class,
    ];

    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register module service providers
        foreach ($this->moduleProviders as $provider) {
            $this->app->register($provider);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

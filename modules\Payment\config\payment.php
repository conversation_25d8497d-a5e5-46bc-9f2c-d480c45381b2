<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payment Gateway Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for payment gateways and
    | payment processing settings.
    |
    */

    'default_currency' => env('PAYMENT_DEFAULT_CURRENCY', 'USD'),
    'default_locale' => env('PAYMENT_DEFAULT_LOCALE', 'en_US'),

    /*
    |--------------------------------------------------------------------------
    | Payment Methods
    |--------------------------------------------------------------------------
    |
    | Supported payment methods and their configurations
    |
    */
    'methods' => [
        'cash' => [
            'enabled' => true,
            'name' => 'Cash',
            'icon' => 'fas fa-money-bill-wave',
            'requires_change_calculation' => true,
            'supports_partial_payments' => true,
            'supports_refunds' => true,
        ],
        'card' => [
            'enabled' => true,
            'name' => 'Credit/Debit Card',
            'icon' => 'fas fa-credit-card',
            'requires_authorization' => true,
            'supports_partial_payments' => false,
            'supports_refunds' => true,
            'processing_fee_percentage' => 2.9,
            'processing_fee_fixed' => 0.30,
        ],
        'digital_wallet' => [
            'enabled' => true,
            'name' => 'Digital Wallet',
            'icon' => 'fas fa-wallet',
            'providers' => ['paypal', 'apple_pay', 'google_pay', 'samsung_pay'],
            'supports_partial_payments' => false,
            'supports_refunds' => true,
            'processing_fee_percentage' => 2.5,
        ],
        'bank_transfer' => [
            'enabled' => true,
            'name' => 'Bank Transfer',
            'icon' => 'fas fa-university',
            'requires_verification' => true,
            'processing_time_days' => 3,
            'supports_partial_payments' => true,
            'supports_refunds' => true,
        ],
        'crypto' => [
            'enabled' => false,
            'name' => 'Cryptocurrency',
            'icon' => 'fab fa-bitcoin',
            'supported_currencies' => ['BTC', 'ETH', 'LTC'],
            'requires_confirmation' => true,
            'confirmation_blocks' => 6,
            'supports_partial_payments' => false,
            'supports_refunds' => true,
        ],
        'gift_card' => [
            'enabled' => true,
            'name' => 'Gift Card',
            'icon' => 'fas fa-gift',
            'supports_partial_payments' => true,
            'supports_refunds' => false,
        ],
        'store_credit' => [
            'enabled' => true,
            'name' => 'Store Credit',
            'icon' => 'fas fa-coins',
            'supports_partial_payments' => true,
            'supports_refunds' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for transaction processing and management
    |
    */
    'transactions' => [
        'auto_generate_number' => true,
        'number_prefix' => 'TXN',
        'number_length' => 10,
        'default_status' => 'pending',
        'auto_expire_minutes' => 30,
        'require_approval_threshold' => 1000.00,
        'max_daily_amount' => 10000.00,
        'max_transaction_amount' => 5000.00,
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Processing
    |--------------------------------------------------------------------------
    |
    | Settings for payment processing and validation
    |
    */
    'processing' => [
        'timeout_seconds' => 30,
        'retry_attempts' => 3,
        'retry_delay_seconds' => 5,
        'require_cvv' => true,
        'require_billing_address' => false,
        'allow_duplicate_payments' => false,
        'duplicate_check_window_minutes' => 5,
    ],

    /*
    |--------------------------------------------------------------------------
    | Refund Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for refund processing
    |
    */
    'refunds' => [
        'enabled' => true,
        'time_limit_days' => 30,
        'authorization_threshold' => 500.00,
        'auto_process_threshold' => 100.00,
        'processing_fee_refundable' => false,
        'partial_refunds_enabled' => true,
        'max_refund_attempts' => 3,
        'notification_enabled' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security and fraud prevention settings
    |
    */
    'security' => [
        'encrypt_sensitive_data' => true,
        'mask_card_numbers' => true,
        'log_all_transactions' => true,
        'fraud_detection_enabled' => true,
        'max_failed_attempts' => 3,
        'lockout_duration_minutes' => 15,
        'require_3d_secure' => false,
        'velocity_checks' => [
            'enabled' => true,
            'max_transactions_per_hour' => 10,
            'max_amount_per_hour' => 2000.00,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for payment notifications
    |
    */
    'notifications' => [
        'payment_success' => [
            'enabled' => true,
            'channels' => ['email', 'sms'],
            'template' => 'payment.success',
        ],
        'payment_failed' => [
            'enabled' => true,
            'channels' => ['email'],
            'template' => 'payment.failed',
        ],
        'refund_processed' => [
            'enabled' => true,
            'channels' => ['email'],
            'template' => 'payment.refund',
        ],
        'transaction_created' => [
            'enabled' => false,
            'channels' => ['email'],
            'template' => 'payment.transaction',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Receipt Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for payment receipts
    |
    */
    'receipts' => [
        'enabled' => true,
        'auto_generate' => true,
        'include_qr_code' => true,
        'include_barcode' => false,
        'format' => 'pdf', // pdf, html, thermal
        'template' => 'payment.receipt',
        'logo_path' => 'images/logo.png',
        'footer_text' => 'Thank you for your business!',
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for payment analytics and reporting
    |
    */
    'analytics' => [
        'enabled' => true,
        'track_conversion_rates' => true,
        'track_payment_methods' => true,
        'track_failure_reasons' => true,
        'retention_days' => 365,
        'real_time_updates' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for payment API endpoints
    |
    */
    'api' => [
        'rate_limit' => [
            'enabled' => true,
            'max_attempts' => 60,
            'decay_minutes' => 1,
        ],
        'authentication' => [
            'required' => true,
            'type' => 'bearer', // bearer, api_key, oauth
        ],
        'versioning' => [
            'enabled' => true,
            'default_version' => 'v1',
            'header_name' => 'Accept-Version',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for third-party integrations
    |
    */
    'integrations' => [
        'stripe' => [
            'enabled' => env('STRIPE_ENABLED', false),
            'public_key' => env('STRIPE_PUBLIC_KEY'),
            'secret_key' => env('STRIPE_SECRET_KEY'),
            'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
        ],
        'paypal' => [
            'enabled' => env('PAYPAL_ENABLED', false),
            'client_id' => env('PAYPAL_CLIENT_ID'),
            'client_secret' => env('PAYPAL_CLIENT_SECRET'),
            'sandbox' => env('PAYPAL_SANDBOX', true),
        ],
        'square' => [
            'enabled' => env('SQUARE_ENABLED', false),
            'application_id' => env('SQUARE_APPLICATION_ID'),
            'access_token' => env('SQUARE_ACCESS_TOKEN'),
            'location_id' => env('SQUARE_LOCATION_ID'),
            'sandbox' => env('SQUARE_SANDBOX', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for payment logging
    |
    */
    'logging' => [
        'enabled' => true,
        'level' => 'info', // debug, info, warning, error
        'channels' => ['daily', 'database'],
        'retention_days' => 90,
        'include_request_data' => true,
        'include_response_data' => false,
        'mask_sensitive_fields' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for payment caching
    |
    */
    'cache' => [
        'enabled' => true,
        'ttl_minutes' => 60,
        'prefix' => 'payment:',
        'tags' => ['payments', 'transactions'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for payment queue processing
    |
    */
    'queue' => [
        'enabled' => true,
        'connection' => 'redis',
        'queue_name' => 'payments',
        'retry_after' => 90,
        'max_tries' => 3,
    ],
];
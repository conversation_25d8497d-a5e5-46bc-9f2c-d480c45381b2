<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->nullable()->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->string('category', 50)->comment('general, pos, kitchen, payment, etc.');
            $table->string('key', 100);
            $table->text('value')->nullable();
            $table->string('data_type', 20)->default('string')->comment('string, integer, boolean, json, etc.');
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false)->comment('Can be accessed by frontend');
            $table->string('logo_url')->nullable();
            $table->string('support_email')->nullable();
            $table->string('support_phone')->nullable();
            $table->string('address')->nullable();
            $table->string('website_url')->nullable();
            $table->json('social_links')->nullable();
            $table->timestamps();
            
            
            $table->unique(['tenant_id', 'branch_id', 'category', 'key']);
            $table->index(['tenant_id', 'category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
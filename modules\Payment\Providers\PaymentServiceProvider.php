<?php

namespace Modules\Payment\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Payment\Services\PaymentService;
use Modules\Payment\Services\TransactionService;
use Modules\Payment\Services\RefundService;
use Modules\Payment\Helpers\PaymentHelper;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register services as singletons for global access
        $this->app->singleton('payment.service', function ($app) {
            return new PaymentService();
        });
        
        $this->app->singleton('transaction.service', function ($app) {
            return new TransactionService();
        });
        
        $this->app->singleton('refund.service', function ($app) {
            return new RefundService();
        });
        
        $this->app->singleton('payment.helper', function ($app) {
            return new PaymentHelper();
        });
        
        // Register aliases for easier access
        $this->app->alias('payment.service', PaymentService::class);
        $this->app->alias('transaction.service', TransactionService::class);
        $this->app->alias('refund.service', RefundService::class);
        $this->app->alias('payment.helper', PaymentHelper::class);
        
        // Merge configuration
        $this->mergeConfigFrom(
            __DIR__ . '/../config/payment.php',
            'payment'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        
        // Load migrations
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'payment');
        
        // Publish configuration
        $this->publishes([
            __DIR__ . '/../config/payment.php' => config_path('payment.php'),
        ], 'payment-config');
        
        // Publish views
        $this->publishes([
            __DIR__ . '/../resources/views' => resource_path('views/vendor/payment'),
        ], 'payment-views');
        
        // Publish assets
        $this->publishes([
            __DIR__ . '/../resources/assets' => public_path('vendor/payment'),
        ], 'payment-assets');
        
        // Register global helper functions
        $this->registerGlobalHelpers();
        
        // Register event listeners
        $this->registerEventListeners();
        
        // Register middleware
        $this->registerMiddleware();
    }
    
    /**
     * Register global helper functions
     */
    protected function registerGlobalHelpers(): void
    {
        if (!function_exists('payment_service')) {
            /**
             * Get the payment service instance
             */
            function payment_service(): PaymentService
            {
                return app('payment.service');
            }
        }
        
        if (!function_exists('transaction_service')) {
            /**
             * Get the transaction service instance
             */
            function transaction_service(): TransactionService
            {
                return app('transaction.service');
            }
        }
        
        if (!function_exists('refund_service')) {
            /**
             * Get the refund service instance
             */
            function refund_service(): RefundService
            {
                return app('refund.service');
            }
        }
        
        if (!function_exists('payment_helper')) {
            /**
             * Get the payment helper instance
             */
            function payment_helper(): PaymentHelper
            {
                return app('payment.helper');
            }
        }
        
        if (!function_exists('format_currency')) {
            /**
             * Format currency amount
             */
            function format_currency(float $amount, string $currency = 'USD', string $locale = 'en_US'): string
            {
                return payment_helper()->formatCurrency($amount, $currency, $locale);
            }
        }
        
        if (!function_exists('create_transaction')) {
            /**
             * Create a new transaction
             */
            function create_transaction(array $data): \App\Models\Transaction
            {
                return transaction_service()->createTransaction($data);
            }
        }
        
        if (!function_exists('process_payment')) {
            /**
             * Process a payment
             */
            function process_payment(array $data): array
            {
                return payment_service()->processPayment($data);
            }
        }
        
        if (!function_exists('process_refund')) {
            /**
             * Process a refund
             */
            function process_refund(\App\Models\Payment $payment, array $data): array
            {
                return refund_service()->processRefund($payment, $data);
            }
        }
        
        if (!function_exists('payment_status_badge')) {
            /**
             * Get payment status badge class
             */
            function payment_status_badge(string $status): string
            {
                return payment_helper()->getStatusBadgeClass($status);
            }
        }
        
        if (!function_exists('payment_method_icon')) {
            /**
             * Get payment method icon
             */
            function payment_method_icon(string $method): string
            {
                return payment_helper()->getPaymentMethodIcon($method);
            }
        }
    }
    
    /**
     * Register event listeners
     */
    protected function registerEventListeners(): void
    {
        // Register payment event listeners
        \Event::listen('payment.processed', function ($payment) {
            \Log::info('Payment processed', ['payment_id' => $payment->id]);
        });
        
        \Event::listen('payment.failed', function ($payment, $error) {
            \Log::error('Payment failed', [
                'payment_id' => $payment->id,
                'error' => $error
            ]);
        });
        
        \Event::listen('transaction.created', function ($transaction) {
            \Log::info('Transaction created', ['transaction_id' => $transaction->id]);
        });
        
        \Event::listen('refund.processed', function ($refund) {
            \Log::info('Refund processed', ['refund_id' => $refund->id]);
        });
    }
    
    /**
     * Register middleware
     */
    protected function registerMiddleware(): void
    {
        // Register payment-specific middleware
        $router = $this->app['router'];
        
        $router->aliasMiddleware('payment.auth', \Modules\Payment\Http\Middleware\PaymentAuthMiddleware::class);
        $router->aliasMiddleware('payment.validate', \Modules\Payment\Http\Middleware\PaymentValidationMiddleware::class);
        $router->aliasMiddleware('payment.rate.limit', \Modules\Payment\Http\Middleware\PaymentRateLimitMiddleware::class);
    }
    
    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            'payment.service',
            'transaction.service',
            'refund.service',
            'payment.helper',
            PaymentService::class,
            TransactionService::class,
            RefundService::class,
            PaymentHelper::class,
        ];
    }
}
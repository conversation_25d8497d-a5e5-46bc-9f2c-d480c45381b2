<?php

namespace Modules\Payment\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Payment\Services\TransactionService;
use Modules\Payment\Http\Requests\CreateTransactionRequest;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class TransactionController extends Controller
{
    protected TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    /**
     * Create a new transaction
     */
    public function store(CreateTransactionRequest $request): JsonResponse
    {
        try {
            $transaction = $this->transactionService->createTransaction($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Transaction created successfully',
                'data' => $transaction->load(['transactionable', 'payments'])
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create transaction',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get transaction details
     */
    public function show(Transaction $transaction): JsonResponse
    {
        try {
            $transaction->load(['transactionable', 'payments.paymentMethod', 'createdBy']);

            return response()->json([
                'success' => true,
                'data' => $transaction
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transaction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * List transactions with filtering
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Transaction::with(['transactionable', 'payments.paymentMethod', 'createdBy']);

            // Apply filters
            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('transactionable_type')) {
                $query->where('transactionable_type', $request->transactionable_type);
            }

            if ($request->has('transactionable_id')) {
                $query->where('transactionable_id', $request->transactionable_id);
            }

            if ($request->has('date_from')) {
                $query->whereDate('transaction_date', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('transaction_date', '<=', $request->date_to);
            }

            if ($request->has('amount_min')) {
                $query->where('amount', '>=', $request->amount_min);
            }

            if ($request->has('amount_max')) {
                $query->where('amount', '<=', $request->amount_max);
            }

            if ($request->has('currency')) {
                $query->where('currency', $request->currency);
            }

            if ($request->has('search')) {
                $query->where(function ($q) use ($request) {
                    $q->where('transaction_number', 'like', '%' . $request->search . '%')
                      ->orWhere('reference_number', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            }

            $perPage = $request->get('per_page', 10);
            $transactions = $query->orderBy('transaction_date', 'desc')
                                 ->orderBy('created_at', 'desc')
                                 ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $transactions->items(),
                'meta' => [
                    'current_page' => $transactions->currentPage(),
                    'per_page' => $transactions->perPage(),
                    'total' => $transactions->total(),
                    'last_page' => $transactions->lastPage()
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transactions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update transaction
     */
    public function update(Request $request, Transaction $transaction): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'status' => 'sometimes|in:pending,processing,completed,failed,cancelled,refunded',
                'description' => 'sometimes|string|max:500',
                'notes' => 'sometimes|string|max:1000',
                'reference_number' => 'sometimes|string|max:100'
            ]);

            $updatedTransaction = $this->transactionService->updateTransaction($transaction, $validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Transaction updated successfully',
                'data' => $updatedTransaction
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update transaction',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Cancel transaction
     */
    public function cancel(Transaction $transaction): JsonResponse
    {
        try {
            $cancelledTransaction = $this->transactionService->cancelTransaction($transaction);

            return response()->json([
                'success' => true,
                'message' => 'Transaction cancelled successfully',
                'data' => $cancelledTransaction
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel transaction',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get transaction summary
     */
    public function summary(Request $request): JsonResponse
    {
        try {
            $summary = $this->transactionService->getTransactionSummary($request->all());

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transaction summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get transaction analytics
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $analytics = $this->transactionService->getTransactionAnalytics($request->all());

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transaction analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get transactions by entity
     */
    public function getByEntity(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'transactionable_type' => 'required|string',
                'transactionable_id' => 'required|string',
                'status' => 'sometimes|string',
                'type' => 'sometimes|string'
            ]);

            $transactions = $this->transactionService->getTransactionsByEntity(
                $validatedData['transactionable_type'],
                $validatedData['transactionable_id'],
                $request->only(['status', 'type'])
            );

            return response()->json([
                'success' => true,
                'data' => $transactions
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transactions',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Reconcile transactions
     */
    public function reconcile(Request $request): JsonResponse
    {
        try {
            $result = $this->transactionService->reconcileTransactions($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Transaction reconciliation completed',
                'data' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction reconciliation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export transactions
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $exportData = $this->transactionService->exportTransactions($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Transactions exported successfully',
                'data' => $exportData
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export transactions',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
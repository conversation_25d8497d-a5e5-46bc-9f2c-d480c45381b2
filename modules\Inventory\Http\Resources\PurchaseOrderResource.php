<?php

namespace Modules\Inventory\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'po_number' => $this->po_number,
            'supplier_id' => $this->supplier_id,
            'branch_id' => $this->branch_id,
            'status' => $this->status,
            'priority' => $this->priority,
            
            // Dates
            'order_date' => $this->created_at->format('Y-m-d'),
            'expected_delivery_date' => $this->expected_delivery_date?->format('Y-m-d'),
            'actual_delivery_date' => $this->actual_delivery_date?->format('Y-m-d'),
            'approved_at' => $this->approved_at?->format('Y-m-d H:i:s'),
            'received_at' => $this->received_at?->format('Y-m-d H:i:s'),
            
            // Financial information
            'financial' => [
                'subtotal' => (float) $this->subtotal,
                'discount_amount' => (float) $this->discount_amount,
                'tax_amount' => (float) $this->tax_amount,
                'shipping_cost' => (float) $this->shipping_cost,
                'total_amount' => (float) $this->total_amount,
                'discount_percentage' => $this->subtotal > 0 ? 
                    round(($this->discount_amount / $this->subtotal) * 100, 2) : 0,
                'tax_percentage' => $this->subtotal > 0 ? 
                    round(($this->tax_amount / $this->subtotal) * 100, 2) : 0,
            ],
            
            // Address information
            'addresses' => [
                'billing' => [
                    'address' => $this->billing_address,
                    'city' => $this->billing_city,
                    'state' => $this->billing_state,
                    'postal_code' => $this->billing_postal_code,
                    'country' => $this->billing_country,
                ],
                'shipping' => [
                    'address' => $this->shipping_address,
                    'city' => $this->shipping_city,
                    'state' => $this->shipping_state,
                    'postal_code' => $this->shipping_postal_code,
                    'country' => $this->shipping_country,
                ],
            ],
            
            'payment_terms' => $this->payment_terms,
            'notes' => $this->notes,
            'internal_notes' => $this->internal_notes,
            
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            
            // Relationships
            'supplier' => $this->whenLoaded('supplier', function () {
                return [
                    'id' => $this->supplier->id,
                    'name' => $this->supplier->name,
                    'code' => $this->supplier->code,
                    'email' => $this->supplier->email,
                    'phone' => $this->supplier->phone,
                    'contact_person' => $this->supplier->contact_person,
                    'payment_terms' => $this->supplier->payment_terms,
                    'lead_time_days' => $this->supplier->lead_time_days,
                ];
            }),
            
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                    'code' => $this->branch->code,
                ];
            }),
            
            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),
            
            'approved_by' => $this->whenLoaded('approvedBy', function () {
                return [
                    'id' => $this->approvedBy->id,
                    'name' => $this->approvedBy->name,
                    'email' => $this->approvedBy->email,
                ];
            }),
            
            // Items
            'items' => $this->whenLoaded('items', function () {
                return $this->items->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'quantity_ordered' => (float) $item->quantity_ordered,
                        'quantity_received' => (float) $item->quantity_received,
                        'quantity_pending' => (float) ($item->quantity_ordered - $item->quantity_received),
                        'unit_cost' => (float) $item->unit_cost,
                        'total_cost' => (float) $item->total_cost,
                        'notes' => $item->notes,
                        
                        'product' => $item->product ? [
                            'id' => $item->product->id,
                            'name' => $item->product->name,
                            'sku' => $item->product->sku,
                            'unit' => $item->product->unit,
                            'category' => $item->product->category,
                        ] : null,
                        
                        'status' => $this->getItemStatus($item),
                        'completion_percentage' => $item->quantity_ordered > 0 ? 
                            round(($item->quantity_received / $item->quantity_ordered) * 100, 2) : 0,
                        
                        'formatted' => [
                            'quantity_ordered' => number_format($item->quantity_ordered, 2) . ' ' . ($item->product->unit ?? 'units'),
                            'quantity_received' => number_format($item->quantity_received, 2) . ' ' . ($item->product->unit ?? 'units'),
                            'unit_cost' => number_format($item->unit_cost, 2),
                            'total_cost' => number_format($item->total_cost, 2),
                        ],
                    ];
                });
            }),
            
            // Status information
            'status_info' => [
                'label' => ucwords(str_replace('_', ' ', $this->status)),
                'color' => $this->getStatusColor(),
                'can_approve' => $this->status === 'pending',
                'can_reject' => in_array($this->status, ['pending', 'approved']),
                'can_receive' => $this->status === 'approved',
                'can_cancel' => !in_array($this->status, ['received', 'cancelled']),
                'can_edit' => $this->status === 'pending',
            ],
            
            // Progress information
            'progress' => [
                'total_items' => $this->items->count(),
                'received_items' => $this->items->where('quantity_received', '>', 0)->count(),
                'fully_received_items' => $this->items->filter(function ($item) {
                    return $item->quantity_received >= $item->quantity_ordered;
                })->count(),
                'completion_percentage' => $this->calculateCompletionPercentage(),
                'is_fully_received' => $this->isFullyReceived(),
                'is_partially_received' => $this->isPartiallyReceived(),
            ],
            
            // Delivery information
            'delivery' => [
                'is_overdue' => $this->isOverdue(),
                'days_until_delivery' => $this->getDaysUntilDelivery(),
                'delivery_status' => $this->getDeliveryStatus(),
            ],
            
            // Formatted values
            'formatted' => [
                'subtotal' => number_format($this->subtotal, 2),
                'discount_amount' => number_format($this->discount_amount, 2),
                'tax_amount' => number_format($this->tax_amount, 2),
                'shipping_cost' => number_format($this->shipping_cost, 2),
                'total_amount' => number_format($this->total_amount, 2),
                'priority' => ucfirst($this->priority),
                'status' => ucwords(str_replace('_', ' ', $this->status)),
            ],
        ];
    }
    
    /**
     * Get item status.
     */
    protected function getItemStatus($item): string
    {
        if ($item->quantity_received >= $item->quantity_ordered) {
            return 'fully_received';
        } elseif ($item->quantity_received > 0) {
            return 'partially_received';
        } else {
            return 'pending';
        }
    }
    
    /**
     * Get status color.
     */
    protected function getStatusColor(): string
    {
        return match ($this->status) {
            'pending' => 'orange',
            'approved' => 'blue',
            'received' => 'green',
            'partially_received' => 'yellow',
            'cancelled' => 'red',
            'rejected' => 'red',
            default => 'gray',
        };
    }
    
    /**
     * Calculate completion percentage.
     */
    protected function calculateCompletionPercentage(): float
    {
        $totalOrdered = $this->items->sum('quantity_ordered');
        $totalReceived = $this->items->sum('quantity_received');
        
        return $totalOrdered > 0 ? round(($totalReceived / $totalOrdered) * 100, 2) : 0;
    }
    
    /**
     * Check if fully received.
     */
    protected function isFullyReceived(): bool
    {
        return $this->items->every(function ($item) {
            return $item->quantity_received >= $item->quantity_ordered;
        });
    }
    
    /**
     * Check if partially received.
     */
    protected function isPartiallyReceived(): bool
    {
        return $this->items->some(function ($item) {
            return $item->quantity_received > 0 && $item->quantity_received < $item->quantity_ordered;
        });
    }
    
    /**
     * Check if overdue.
     */
    protected function isOverdue(): bool
    {
        return $this->expected_delivery_date && 
               $this->expected_delivery_date < now() && 
               !in_array($this->status, ['received', 'cancelled']);
    }
    
    /**
     * Get days until delivery.
     */
    protected function getDaysUntilDelivery(): ?int
    {
        if (!$this->expected_delivery_date || in_array($this->status, ['received', 'cancelled'])) {
            return null;
        }
        
        return now()->diffInDays($this->expected_delivery_date, false);
    }
    
    /**
     * Get delivery status.
     */
    protected function getDeliveryStatus(): string
    {
        if (in_array($this->status, ['received', 'cancelled'])) {
            return $this->status;
        }
        
        if ($this->isOverdue()) {
            return 'overdue';
        }
        
        $daysUntil = $this->getDaysUntilDelivery();
        
        if ($daysUntil === null) {
            return 'no_date';
        }
        
        if ($daysUntil <= 0) {
            return 'due_today';
        } elseif ($daysUntil <= 3) {
            return 'due_soon';
        } else {
            return 'on_schedule';
        }
    }
    
    /**
     * Get additional data that should be returned with the resource array.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'timestamp' => now()->toISOString(),
                'timezone' => config('app.timezone'),
            ],
        ];
    }
}
<?php

namespace Modules\Inventory\Services;

use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\BranchInventory;
use App\Models\Supplier;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;

class PurchaseOrderService
{
    protected $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * Get all purchase orders with filtering and pagination
     */
    public function getAllPurchaseOrders(array $filters = [])
    {
        $query = PurchaseOrder::with(['supplier', 'branch', 'items.product'])
            ->where('branch_id', $this->getCurrentBranchId());

        // Apply filters
        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('po_number', 'like', '%' . $filters['search'] . '%')
                  ->orWhereHas('supplier', function ($sq) use ($filters) {
                      $sq->where('name', 'like', '%' . $filters['search'] . '%');
                  });
            });
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['supplier_id'])) {
            $query->where('supplier_id', $filters['supplier_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy($filters['sort_by'] ?? 'created_at', $filters['sort_order'] ?? 'desc')
            ->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get purchase order by ID
     */
    public function getPurchaseOrderById(string $id)
    {
        return PurchaseOrder::with([
            'supplier',
            'branch',
            'items.product.unit',
            'createdBy',
            'approvedBy',
            'receivedBy'
        ])->where('branch_id', $this->getCurrentBranchId())
          ->findOrFail($id);
    }

    /**
     * Create new purchase order
     */
    public function createPurchaseOrder(array $data)
    {
        DB::beginTransaction();
        try {
            $purchaseOrder = PurchaseOrder::create([
                'po_number' => $this->generatePONumber(),
                'supplier_id' => $data['supplier_id'],
                'branch_id' => $this->getCurrentBranchId(),
                'status' => 'draft',
                'order_date' => $data['order_date'] ?? now(),
                'expected_delivery_date' => $data['expected_delivery_date'] ?? null,
                'notes' => $data['notes'] ?? null,
                'terms_and_conditions' => $data['terms_and_conditions'] ?? null,
                'subtotal' => 0,
                'tax_amount' => 0,
                'discount_amount' => $data['discount_amount'] ?? 0,
                'shipping_cost' => $data['shipping_cost'] ?? 0,
                'total_amount' => 0,
                'created_by' => Auth::id(),
            ]);

            // Add items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $itemData) {
                    $this->addItemToPurchaseOrder($purchaseOrder->id, $itemData);
                }
            }

            $this->recalculatePurchaseOrderTotals($purchaseOrder->id);

            DB::commit();
            return $purchaseOrder->load(['supplier', 'branch', 'items.product']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update purchase order
     */
    public function updatePurchaseOrder(string $id, array $data)
    {
        DB::beginTransaction();
        try {
            $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
                ->findOrFail($id);

            // Check if order can be updated
            if (in_array($purchaseOrder->status, ['completed', 'cancelled'])) {
                throw new Exception('Cannot update completed or cancelled purchase order.');
            }

            $purchaseOrder->update([
                'supplier_id' => $data['supplier_id'] ?? $purchaseOrder->supplier_id,
                'expected_delivery_date' => $data['expected_delivery_date'] ?? $purchaseOrder->expected_delivery_date,
                'notes' => $data['notes'] ?? $purchaseOrder->notes,
                'terms_and_conditions' => $data['terms_and_conditions'] ?? $purchaseOrder->terms_and_conditions,
                'discount_amount' => $data['discount_amount'] ?? $purchaseOrder->discount_amount,
                'shipping_cost' => $data['shipping_cost'] ?? $purchaseOrder->shipping_cost,
                'updated_by' => Auth::id(),
            ]);

            $this->recalculatePurchaseOrderTotals($purchaseOrder->id);

            DB::commit();
            return $purchaseOrder->load(['supplier', 'branch', 'items.product']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete purchase order
     */
    public function deletePurchaseOrder(string $id)
    {
        $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
            ->findOrFail($id);

        if (!in_array($purchaseOrder->status, ['draft', 'pending'])) {
            throw new Exception('Can only delete draft or pending purchase orders.');
        }

        $purchaseOrder->delete();
    }

    /**
     * Approve purchase order
     */
    public function approvePurchaseOrder(string $id)
    {
        DB::beginTransaction();
        try {
            $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
                ->findOrFail($id);

            if ($purchaseOrder->status !== 'pending') {
                throw new Exception('Only pending purchase orders can be approved.');
            }

            $purchaseOrder->update([
                'status' => 'approved',
                'approved_by' => Auth::id(),
                'approved_at' => now(),
            ]);

            DB::commit();
            return $purchaseOrder->load(['supplier', 'branch', 'items.product']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Reject purchase order
     */
    public function rejectPurchaseOrder(string $id, string $reason)
    {
        DB::beginTransaction();
        try {
            $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
                ->findOrFail($id);

            if ($purchaseOrder->status !== 'pending') {
                throw new Exception('Only pending purchase orders can be rejected.');
            }

            $purchaseOrder->update([
                'status' => 'rejected',
                'rejection_reason' => $reason,
                'rejected_by' => Auth::id(),
                'rejected_at' => now(),
            ]);

            DB::commit();
            return $purchaseOrder->load(['supplier', 'branch', 'items.product']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Receive purchase order
     */
    public function receivePurchaseOrder(string $id, array $data)
    {
        DB::beginTransaction();
        try {
            $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
                ->findOrFail($id);

            if ($purchaseOrder->status !== 'approved') {
                throw new Exception('Only approved purchase orders can be received.');
            }

            $allItemsReceived = true;

            foreach ($data['items'] as $itemData) {
                $item = PurchaseOrderItem::findOrFail($itemData['id']);
                
                $receivedQuantity = $itemData['received_quantity'];
                $newReceivedTotal = $item->received_quantity + $receivedQuantity;
                
                if ($newReceivedTotal > $item->quantity) {
                    throw new Exception("Cannot receive more than ordered quantity for {$item->product->name}");
                }

                $item->update([
                    'received_quantity' => $newReceivedTotal,
                    'unit_cost' => $itemData['unit_cost'] ?? $item->unit_cost,
                ]);

                // Update inventory
                if ($receivedQuantity > 0) {
                    $this->updateInventoryFromReceipt($item->product_id, $receivedQuantity, $item->unit_cost);
                }

                if ($newReceivedTotal < $item->quantity) {
                    $allItemsReceived = false;
                }
            }

            // Update purchase order status
            $newStatus = $allItemsReceived ? 'completed' : 'partial';
            $purchaseOrder->update([
                'status' => $newStatus,
                'received_by' => Auth::id(),
                'received_at' => now(),
                'delivery_notes' => $data['notes'] ?? null,
            ]);

            if ($allItemsReceived) {
                $purchaseOrder->update(['delivered_at' => now()]);
            }

            DB::commit();
            return $purchaseOrder->load(['supplier', 'branch', 'items.product']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Cancel purchase order
     */
    public function cancelPurchaseOrder(string $id, string $reason)
    {
        DB::beginTransaction();
        try {
            $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
                ->findOrFail($id);

            if (in_array($purchaseOrder->status, ['completed', 'cancelled'])) {
                throw new Exception('Cannot cancel completed or already cancelled purchase order.');
            }

            $purchaseOrder->update([
                'status' => 'cancelled',
                'cancellation_reason' => $reason,
                'cancelled_by' => Auth::id(),
                'cancelled_at' => now(),
            ]);

            DB::commit();
            return $purchaseOrder->load(['supplier', 'branch', 'items.product']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get purchase order items
     */
    public function getPurchaseOrderItems(string $id)
    {
        $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
            ->findOrFail($id);

        return $purchaseOrder->items()->with(['product.unit'])->get();
    }

    /**
     * Add item to purchase order
     */
    public function addItemToPurchaseOrder(string $orderId, array $itemData)
    {
        $purchaseOrder = PurchaseOrder::where('branch_id', $this->getCurrentBranchId())
            ->findOrFail($orderId);

        if (!in_array($purchaseOrder->status, ['draft', 'pending'])) {
            throw new Exception('Can only add items to draft or pending purchase orders.');
        }

        $item = PurchaseOrderItem::create([
            'purchase_order_id' => $orderId,
            'product_id' => $itemData['product_id'],
            'quantity' => $itemData['quantity'],
            'unit_cost' => $itemData['unit_cost'],
            'total_cost' => $itemData['quantity'] * $itemData['unit_cost'],
            'notes' => $itemData['notes'] ?? null,
        ]);

        $this->recalculatePurchaseOrderTotals($orderId);

        return $item->load('product.unit');
    }

    /**
     * Update purchase order item
     */
    public function updatePurchaseOrderItem(string $itemId, array $data)
    {
        $item = PurchaseOrderItem::findOrFail($itemId);
        $purchaseOrder = $item->purchaseOrder;

        if (!in_array($purchaseOrder->status, ['draft', 'pending'])) {
            throw new Exception('Can only update items in draft or pending purchase orders.');
        }

        $quantity = $data['quantity'] ?? $item->quantity;
        $unitCost = $data['unit_cost'] ?? $item->unit_cost;

        $item->update([
            'quantity' => $quantity,
            'unit_cost' => $unitCost,
            'total_cost' => $quantity * $unitCost,
            'notes' => $data['notes'] ?? $item->notes,
        ]);

        $this->recalculatePurchaseOrderTotals($purchaseOrder->id);

        return $item->load('product.unit');
    }

    /**
     * Remove item from purchase order
     */
    public function removeItemFromPurchaseOrder(string $itemId)
    {
        $item = PurchaseOrderItem::findOrFail($itemId);
        $purchaseOrder = $item->purchaseOrder;

        if (!in_array($purchaseOrder->status, ['draft', 'pending'])) {
            throw new Exception('Can only remove items from draft or pending purchase orders.');
        }

        $item->delete();
        $this->recalculatePurchaseOrderTotals($purchaseOrder->id);
    }

    /**
     * Generate purchase order PDF
     */
    public function generatePurchaseOrderPDF(string $id)
    {
        $purchaseOrder = $this->getPurchaseOrderById($id);
        
        // This would typically use a PDF library like DomPDF or TCPDF
        // For now, return a simple implementation
        $html = $this->generatePurchaseOrderHTML($purchaseOrder);
        
        // In a real implementation, you would convert HTML to PDF here
        return $html; // Placeholder
    }

    /**
     * Private helper methods
     */
    private function getCurrentBranchId()
    {
        return Auth::user()->branch_id ?? 1;
    }

    private function generatePONumber()
    {
        $branchId = $this->getCurrentBranchId();
        $year = date('Y');
        $month = date('m');
        
        $lastPO = PurchaseOrder::where('branch_id', $branchId)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();
            
        $sequence = $lastPO ? (int)substr($lastPO->po_number, -4) + 1 : 1;
        
        return sprintf('PO-%d-%s%s-%04d', $branchId, $year, $month, $sequence);
    }

    private function recalculatePurchaseOrderTotals(string $orderId)
    {
        $purchaseOrder = PurchaseOrder::findOrFail($orderId);
        $items = $purchaseOrder->items;
        
        $subtotal = $items->sum('total_cost');
        $taxAmount = $subtotal * 0.1; // Assuming 10% tax, should be configurable
        $total = $subtotal + $taxAmount + $purchaseOrder->shipping_cost - $purchaseOrder->discount_amount;
        
        $purchaseOrder->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $total,
        ]);
    }

    private function updateInventoryFromReceipt(int $productId, float $quantity, float $unitCost)
    {
        $inventory = BranchInventory::where('branch_id', $this->getCurrentBranchId())
            ->where('product_id', $productId)
            ->first();

        if ($inventory) {
            // Update existing inventory
            $this->inventoryService->updateStock($inventory->id, $quantity, 'add', 'Purchase order receipt');
            
            // Update cost if provided
            if ($unitCost > 0) {
                $inventory->update(['cost_per_unit' => $unitCost]);
            }
        } else {
            // Create new inventory record
            $this->inventoryService->createItem([
                'product_id' => $productId,
                'initial_stock' => $quantity,
                'minimum_stock' => 0,
                'cost_per_unit' => $unitCost,
            ]);
        }
    }

    private function generatePurchaseOrderHTML($purchaseOrder)
    {
        // Simple HTML template for purchase order
        return "
        <html>
        <head><title>Purchase Order #{$purchaseOrder->po_number}</title></head>
        <body>
            <h1>Purchase Order #{$purchaseOrder->po_number}</h1>
            <p>Supplier: {$purchaseOrder->supplier->name}</p>
            <p>Date: {$purchaseOrder->order_date->format('Y-m-d')}</p>
            <p>Status: {$purchaseOrder->status}</p>
            <table border='1'>
                <tr><th>Product</th><th>Quantity</th><th>Unit Cost</th><th>Total</th></tr>
        " . $purchaseOrder->items->map(function ($item) {
            return "<tr><td>{$item->product->name}</td><td>{$item->quantity}</td><td>{$item->unit_cost}</td><td>{$item->total_cost}</td></tr>";
        })->implode('') . "
            </table>
            <p>Total: {$purchaseOrder->total_amount}</p>
        </body>
        </html>
        ";
    }
}
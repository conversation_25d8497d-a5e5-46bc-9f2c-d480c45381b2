<?php

namespace Modules\HR\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateShiftRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'required|integer|exists:branches,id',
            'shift_type_id' => 'required|integer|exists:shift_types,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'break_duration_minutes' => 'nullable|integer|min:0|max:480',
            'required_staff_count' => 'required|integer|min:1|max:50',
            'notes' => 'nullable|string|max:1000',
            'is_recurring' => 'boolean',
            'recurring_pattern' => 'nullable|string|in:daily,weekly,monthly',
            'recurring_end_date' => 'nullable|date|after:date|required_if:is_recurring,true',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'shift_type_id.required' => 'Shift type is required.',
            'shift_type_id.exists' => 'Selected shift type does not exist.',
            'date.required' => 'Shift date is required.',
            'date.after_or_equal' => 'Shift date cannot be in the past.',
            'start_time.required' => 'Start time is required.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.required' => 'End time is required.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'end_time.after' => 'End time must be after start time.',
            'break_duration_minutes.integer' => 'Break duration must be a number.',
            'break_duration_minutes.min' => 'Break duration cannot be negative.',
            'break_duration_minutes.max' => 'Break duration cannot exceed 8 hours.',
            'required_staff_count.required' => 'Required staff count is required.',
            'required_staff_count.min' => 'At least 1 staff member is required.',
            'required_staff_count.max' => 'Cannot require more than 50 staff members.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'recurring_pattern.in' => 'Recurring pattern must be daily, weekly, or monthly.',
            'recurring_end_date.after' => 'Recurring end date must be after shift date.',
            'recurring_end_date.required_if' => 'Recurring end date is required for recurring shifts.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_recurring' => $this->boolean('is_recurring'),
            'break_duration_minutes' => $this->break_duration_minutes ?? 0,
        ]);
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add tenant_id and created_by
        $validated['tenant_id'] = Auth::user()->tenant_id;
        $validated['created_by'] = Auth::id();
        
        return $validated;
    }
}
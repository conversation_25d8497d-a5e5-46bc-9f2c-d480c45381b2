<?php

namespace Modules\Orders\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Orders\Services\OrderService;

class OrderServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the OrderService
        $this->app->singleton(\Modules\Orders\Services\OrderService::class, function ($app) {
            return new \Modules\Orders\Services\OrderService(
                $app->make(\App\Services\InventoryDeductionService::class),
                $app->make(\Modules\Kitchen\Services\KOTService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'orders');
        
        // Load migrations if needed
        // $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        
        // Publish config if needed
        // $this->publishes([
        //     __DIR__ . '/../config/orders.php' => config_path('orders.php'),
        // ], 'orders-config');
    }

    /**
     * Load module routes
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            Route::group([
                'middleware' => 'api',
                'namespace' => 'Modules\\Orders\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/api.php';
            });
        }

        // Load Web routes
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            Route::group([
                'middleware' => 'web',
                'namespace' => 'Modules\\Orders\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/web.php';
            });
        }
    }
}
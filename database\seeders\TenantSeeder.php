<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tenant;

class TenantSeeder extends Seeder
{
    public function run()
    {
        if (class_exists(\Database\Factories\TenantFactory::class)) {
            Tenant::factory()->count(5)->create();
        } else {
            Tenant::create([
                'name' => 'Default Tenant',
                'code' => 'DEFAULT_TENANT',
                'business_type' => 'Restaurant',
                'country_id' => null,
                'primary_contact_name' => '<PERSON>',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '************',
                'business_address' => '123 Main St',
                'timezone' => 'UTC',
                'currency_code' => 'USD',
                'language_code' => 'en',
                'status' => 'active',
            ]);
        }
    }
}
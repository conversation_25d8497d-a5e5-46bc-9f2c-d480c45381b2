@extends('layouts.app')

@section('title', 'Tenant Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">{{ $tenant->name }} Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Tenant</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Tenant Info Card -->
    <div class="row">
        <div class="col-xl-4 col-lg-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-soft-primary text-primary rounded">
                                    <i class="mdi mdi-store font-size-18"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">{{ $tenant->name }}</h5>
                            <p class="text-muted mb-0">Code: {{ $tenant->code }}</p>
                            <p class="text-muted mb-0">Status: 
                                <span class="badge badge-soft-{{ $tenant->is_active ? 'success' : 'danger' }}">
                                    {{ $tenant->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Info Card -->
        <div class="col-xl-4 col-lg-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-soft-info text-info rounded">
                                    <i class="mdi mdi-credit-card font-size-18"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            @if($subscription)
                                <h5 class="mb-1">{{ $subscription->plan->name }}</h5>
                                <p class="text-muted mb-0">{{ ucfirst($subscription->billing_cycle) }}</p>
                                <p class="text-muted mb-0">Status: 
                                    <span class="badge badge-soft-{{ $subscription->status === 'active' ? 'success' : 'warning' }}">
                                        {{ ucfirst($subscription->status) }}
                                    </span>
                                </p>
                            @else
                                <h5 class="mb-1">No Subscription</h5>
                                <p class="text-muted mb-0">Please subscribe to a plan</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branches Count Card -->
        <div class="col-xl-4 col-lg-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-soft-warning text-warning rounded">
                                    <i class="mdi mdi-map-marker-multiple font-size-18"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1">{{ $statistics['branches_count'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Total Branches</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Statistics -->
    @if($subscription)
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Usage Statistics</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="mb-0">{{ $usage['branches'] ?? 0 }} / {{ $subscription->plan->max_branches ?? '∞' }}</h5>
                                <p class="text-muted mb-0">Branches</p>
                                @if($subscription->plan->max_branches)
                                    <div class="progress mt-2" style="height: 6px;">
                                        <div class="progress-bar" style="width: {{ min(100, (($usage['branches'] ?? 0) / $subscription->plan->max_branches) * 100) }}%"></div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="mb-0">{{ $usage['users'] ?? 0 }} / {{ $subscription->plan->max_users ?? '∞' }}</h5>
                                <p class="text-muted mb-0">Users</p>
                                @if($subscription->plan->max_users)
                                    <div class="progress mt-2" style="height: 6px;">
                                        <div class="progress-bar" style="width: {{ min(100, (($usage['users'] ?? 0) / $subscription->plan->max_users) * 100) }}%"></div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="mb-0">{{ $usage['menu_items'] ?? 0 }} / {{ $subscription->plan->max_menu_items ?? '∞' }}</h5>
                                <p class="text-muted mb-0">Menu Items</p>
                                @if($subscription->plan->max_menu_items)
                                    <div class="progress mt-2" style="height: 6px;">
                                        <div class="progress-bar" style="width: {{ min(100, (($usage['menu_items'] ?? 0) / $subscription->plan->max_menu_items) * 100) }}%"></div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h5 class="mb-0">{{ $usage['orders'] ?? 0 }} / {{ $subscription->plan->max_orders_per_month ?? '∞' }}</h5>
                                <p class="text-muted mb-0">Orders (This Month)</p>
                                @if($subscription->plan->max_orders_per_month)
                                    <div class="progress mt-2" style="height: 6px;">
                                        <div class="progress-bar" style="width: {{ min(100, (($usage['orders'] ?? 0) / $subscription->plan->max_orders_per_month) * 100) }}%"></div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Quick Actions</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('tenant.subscription') }}" class="btn btn-outline-primary w-100">
                                <i class="mdi mdi-credit-card me-2"></i>Manage Subscription
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('tenant.usage') }}" class="btn btn-outline-info w-100">
                                <i class="mdi mdi-chart-line me-2"></i>View Usage
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('tenant.billing') }}" class="btn btn-outline-warning w-100">
                                <i class="mdi mdi-receipt me-2"></i>Billing History
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('tenant.edit', $tenant) }}" class="btn btn-outline-secondary w-100">
                                <i class="mdi mdi-pencil me-2"></i>Edit Tenant
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Recent Activity</h4>
                </div>
                <div class="card-body">
                    @if(isset($recentActivity) && count($recentActivity) > 0)
                        <div class="timeline">
                            @foreach($recentActivity as $activity)
                                <div class="timeline-item">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">{{ $activity['title'] }}</h6>
                                        <p class="timeline-text">{{ $activity['description'] }}</p>
                                        <small class="text-muted">{{ $activity['created_at']->diffForHumans() }}</small>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="mdi mdi-information-outline font-size-48 text-muted"></i>
                            <h5 class="mt-3">No Recent Activity</h5>
                            <p class="text-muted">Activity will appear here as you use the system.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #007bff;
}

.timeline-marker::before {
    content: '';
    position: absolute;
    left: 4px;
    top: 15px;
    width: 2px;
    height: 30px;
    background-color: #dee2e6;
}

.timeline-item:last-child .timeline-marker::before {
    display: none;
}
</style>
@endpush
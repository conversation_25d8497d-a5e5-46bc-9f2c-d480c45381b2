<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;

class OrderSeeder extends Seeder
{
    public function run()
    {
        $branch = \App\Models\Branch::first();

        if (!$branch) {
            $this->command->info('No branch found. Skipping OrderSeeder.');
            return;
        }

        Order::create([
            'branch_id' => $branch->id,
            'order_number' => 'ORD20240001',
            'status' => 'pending',
            'total_amount' => 100.00,
            // Add other required fields here
        ]);
    }
}
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuCategory;

class MenuCategorySeeder extends Seeder
{
    public function run()
    {
        if (class_exists(\Database\Factories\MenuCategoryFactory::class)) {
            MenuCategory::factory()->count(10)->create();
        } else {
            $menu = \App\Models\Menu::first();
            $branch = \App\Models\Branch::first();

            if (!$menu || !$branch) {
                $this->command->info('No menu or branch found. Please seed menus and branches first.');
                return;
            }

            MenuCategory::create([
                'menu_id' => $menu->id,
                'name' => 'Starters',
                'code' => 'STARTERS',
                'description' => 'Appetizers and light bites',
                'is_active' => true,
                'sort_order' => 1,

                // Add other required fields here
            ]);
        }
    }
}
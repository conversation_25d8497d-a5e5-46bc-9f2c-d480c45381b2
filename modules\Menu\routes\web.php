<?php

use Illuminate\Support\Facades\Route;
use Modules\Menu\Http\Controllers\Web\MenuWebController;
use Modules\Menu\Http\Controllers\Web\CategoryWebController;
use Modules\Menu\Http\Controllers\Web\MenuItemWebController;
use Modules\Menu\Http\Controllers\Web\AddonWebController;
use Modules\Menu\Http\Controllers\Web\VariantWebController;
use Modules\Menu\Http\Middleware\MenuAccessMiddleware;

/*
|--------------------------------------------------------------------------
| Web Routes for Menu Module
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Menu module.
|
*/

Route::prefix('menu')
    ->middleware(['web', 'auth', MenuAccessMiddleware::class])
    ->name('menu.web.')
    ->group(function () {
        
        // ==================== MENU ROUTES ====================
        Route::resource('menus', MenuWebController::class);
        
        // ==================== CATEGORY ROUTES ====================
        Route::resource('categories', CategoryWebController::class);
        
        // ==================== MENU ITEM ROUTES ====================
        Route::resource('menu-items', MenuItemWebController::class);
        Route::post('menu-items/{id}/toggle-availability', [MenuItemWebController::class, 'toggleAvailability'])
            ->name('menu-items.toggle-availability');
        Route::get('menu-items/{id}/availability', [MenuItemWebController::class, 'availability'])
            ->name('menu-items.availability');
        Route::post('menu-items/set-availability', [MenuItemWebController::class, 'setAvailability'])
            ->name('menu-items.set-availability');
        
        // ==================== ADDON ROUTES ====================
        Route::prefix('menu-items/{menuItemId}/addons')->name('addons.')->group(function () {
            Route::get('/', [AddonWebController::class, 'index'])->name('index');
            Route::get('/create', [AddonWebController::class, 'create'])->name('create');
            Route::post('/', [AddonWebController::class, 'store'])->name('store');
            Route::get('/{addonId}', [AddonWebController::class, 'show'])->name('show');
            Route::get('/{addonId}/edit', [AddonWebController::class, 'edit'])->name('edit');
            Route::put('/{addonId}', [AddonWebController::class, 'update'])->name('update');
            Route::delete('/{addonId}', [AddonWebController::class, 'destroy'])->name('destroy');
        });
        
        // ==================== VARIANT ROUTES ====================
        Route::prefix('menu-items/{menuItemId}/variants')->name('variants.')->group(function () {
            Route::get('/', [VariantWebController::class, 'index'])->name('index');
            Route::get('/create', [VariantWebController::class, 'create'])->name('create');
            Route::post('/', [VariantWebController::class, 'store'])->name('store');
            Route::get('/{variantId}', [VariantWebController::class, 'show'])->name('show');
            Route::get('/{variantId}/edit', [VariantWebController::class, 'edit'])->name('edit');
            Route::put('/{variantId}', [VariantWebController::class, 'update'])->name('update');
            Route::delete('/{variantId}', [VariantWebController::class, 'destroy'])->name('destroy');
        });
        
    });

// Dashboard route for menu overview
Route::get('/dashboard/menu', function () {
    return redirect()->route('menu.web.menus.index');
})->middleware(['web', 'auth'])->name('dashboard.menu');
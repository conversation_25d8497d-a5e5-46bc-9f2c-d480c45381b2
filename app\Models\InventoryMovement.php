<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryMovement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_inventory_id',
        'type',
        'quantity',
        'unit_cost',
        'total_cost',
        'reference_type',
        'reference_id',
        'reference_number',
        'reason',
        'notes',
        'user_id',
        'performed_at',
        'batch_number',
        'expiry_date',
        'supplier_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'quantity' => 'decimal:6',
            'unit_cost' => 'decimal:4',
            'total_cost' => 'decimal:2',
            'performed_at' => 'datetime',
            'expiry_date' => 'date',
        ];
    }

    // Relationships
    public function branchInventory()
    {
        return $this->belongsTo(BranchInventory::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    public function reference()
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeInbound($query)
    {
        return $query->whereIn('type', ['add', 'initial_stock', 'return', 'transfer_in']);
    }

    public function scopeOutbound($query)
    {
        return $query->whereIn('type', ['subtract', 'waste', 'transfer_out', 'consumption']);
    }
}
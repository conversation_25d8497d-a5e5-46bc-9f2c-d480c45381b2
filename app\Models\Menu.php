<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Menu extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'code',
        'description',
        'menu_type',
        'start_time',
        'end_time',
        'available_days',
        'is_active',
        'is_default',
        'sort_order',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'available_days' => 'array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function categories()
    {
        return $this->hasMany(MenuCategory::class);
    }

    public function menuItems()
    {
        return $this->hasMany(MenuItem::class);
    }

    /**
     * Get active categories for this menu
     */
    public function activeCategories()
    {
        return $this->categories()->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get active menu items for this menu
     */
    public function activeMenuItems()
    {
        return $this->menuItems()->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Check if menu is available at given time
     */
    public function isAvailableAt($time = null, $dayOfWeek = null)
    {
        if (!$this->is_active) {
            return false;
        }

        $currentTime = $time ?? now()->format('H:i');
        $currentDay = $dayOfWeek ?? now()->dayOfWeek;

        // Check time availability
        if ($this->start_time && $this->end_time) {
            if ($currentTime < $this->start_time || $currentTime > $this->end_time) {
                return false;
            }
        }

        // Check day availability
        if ($this->available_days && !in_array($currentDay, $this->available_days)) {
            return false;
        }

        return true;
    }

    /**
     * Scope to get menus for a specific branch
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to get active menus
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get default menu for branch
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }
}
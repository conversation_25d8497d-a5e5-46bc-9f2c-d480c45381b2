# Restaurant POS Dashboard

A responsive, modular dashboard layout built with Laravel Blade, supporting multiple modules and optimized for both web and mobile devices.

## Features

### 🎨 **Modern UI/UX**
- Clean, professional design with Tailwind CSS
- Responsive layout that works on all devices
- Dark/Light mode toggle with system preference detection
- Smooth animations and transitions
- Accessibility-compliant (ARIA attributes, semantic HTML)

### 🌍 **Multi-language Support**
- Full RTL/LTR support for Arabic and English
- Dynamic direction switching based on locale
- Comprehensive translation system using Laravel Localization
- Language switcher with flag icons

### 📊 **Dashboard Components**
- **Statistics Cards**: Display key metrics with trend indicators
- **Interactive Charts**: Sales trends and category breakdowns using Chart.js
- **Data Tables**: Sortable, paginated tables for orders and data
- **Real-time Notifications**: Dropdown with mark-as-read functionality
- **Quick Actions**: Fast access to common tasks

### 🧩 **Modular Architecture**
- Reusable Blade components for consistent UI
- Sidebar navigation with multi-level menu support
- Collapsible sidebar for better screen utilization
- Component-based design for easy maintenance

### ⚡ **Performance Optimized**
- Lazy loading for charts and heavy content
- Minimized DOM operations
- Efficient JavaScript with vanilla JS (no Alpine.js)
- CSS custom properties for theme switching
- Print-friendly styles

## File Structure

```
resources/
├── views/
│   ├── layouts/
│   │   └── dashboard.blade.php          # Master dashboard layout
│   ├── components/
│   │   ├── sidebar.blade.php            # Navigation sidebar
│   │   ├── navbar.blade.php             # Top navigation bar
│   │   ├── card.blade.php               # Statistics card component
│   │   ├── button.blade.php             # Reusable button component
│   │   ├── input.blade.php              # Form input component
│   │   ├── alert.blade.php              # Alert/notification component
│   │   └── dropdown.blade.php           # Dropdown menu component
│   └── dashboard.blade.php              # Main dashboard view
├── css/
│   └── app.css                          # Enhanced styles with RTL support
└── js/
    └── dashboard.js                     # Dashboard functionality

app/Http/Controllers/
└── DashboardController.php              # Dashboard logic and API endpoints

lang/
├── en/
│   └── messages.php                     # English translations
└── ar/
    └── messages.php                     # Arabic translations

routes/
└── web.php                              # Dashboard routes
```

## Components Overview

### 1. Master Layout (`layouts/dashboard.blade.php`)
- HTML5 semantic structure
- Dynamic RTL/LTR direction
- Meta tags for SEO and mobile optimization
- Font Awesome and Chart.js integration
- Vite asset compilation
- Theme management system

### 2. Sidebar Component (`components/sidebar.blade.php`)
- Collapsible navigation
- Multi-level menu structure
- Module-based organization
- Mobile-responsive hamburger menu
- Active state management

### 3. Navbar Component (`components/navbar.blade.php`)
- Breadcrumb navigation
- Global search functionality
- Theme toggle button
- Language switcher
- Notifications dropdown
- User profile menu

### 4. Reusable Components
- **Card**: Statistics display with icons and trends
- **Button**: Multiple variants and states
- **Input**: Form inputs with validation states
- **Alert**: Success/error/warning messages
- **Dropdown**: Customizable dropdown menus

## Setup Instructions

### 1. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 2. Environment Configuration
Ensure your `.env` file has the correct locale settings:
```env
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
```

### 3. Build Assets
```bash
# Development
npm run dev

# Production
npm run build

# Watch for changes
npm run dev -- --watch
```

### 4. Database Setup
```bash
# Run migrations
php artisan migrate

# Seed sample data (optional)
php artisan db:seed
```

### 5. Start Development Server
```bash
php artisan serve
```

## Usage

### Accessing the Dashboard
Navigate to `/dashboard` after authentication. The dashboard will automatically detect your browser's language preference and display the appropriate locale.

### Language Switching
Use the language switcher in the top navbar to switch between English and Arabic. The entire interface will update, including:
- Text direction (LTR/RTL)
- Menu layouts
- Chart labels
- Date/time formats

### Theme Management
Click the theme toggle button to switch between light and dark modes. Your preference is saved locally and synced with the server.

### Mobile Usage
The dashboard is fully responsive:
- Sidebar collapses to hamburger menu on mobile
- Touch-friendly interface elements
- Optimized chart displays
- Swipe gestures for navigation

## Customization

### Adding New Menu Items
Edit `resources/views/components/sidebar.blade.php` and add your menu items:

```blade
<li class="nav-item">
    <a href="{{ route('your.route') }}" class="nav-link">
        <i class="fas fa-your-icon"></i>
        <span>{{ __('messages.your_menu_item') }}</span>
    </a>
</li>
```

### Creating New Components
Follow the existing component structure:

```blade
@props([
    'param1' => 'default_value',
    'param2' => null
])

<div {{ $attributes->merge(['class' => 'your-component-class']) }}>
    <!-- Component content -->
    {{ $slot }}
</div>
```

### Adding Translations
Add new translation keys to both language files:

```php
// lang/en/messages.php
'your_key' => 'English Text',

// lang/ar/messages.php
'your_key' => 'النص العربي',
```

### Styling Customization
Modify `resources/css/app.css` for custom styles. The CSS includes:
- CSS custom properties for theming
- RTL-specific styles
- Component-specific classes
- Responsive breakpoints
- Print styles

## API Endpoints

The dashboard includes several AJAX endpoints:

- `POST /toggle-theme` - Save theme preference
- `POST /notifications/mark-read` - Mark notification as read
- `GET /dashboard/stats` - Get dashboard statistics
- `GET /dashboard/chart-data` - Get chart data

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Considerations

- Charts are initialized only when visible
- Images use lazy loading
- CSS and JS are minified in production
- Database queries are optimized
- Caching is implemented for static data

## Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Reduced motion preferences

## Troubleshooting

### Common Issues

1. **Charts not displaying**: Ensure Chart.js is loaded and canvas elements exist
2. **RTL layout issues**: Check CSS custom properties and direction attributes
3. **Translation missing**: Verify translation keys exist in both language files
4. **Theme not persisting**: Check localStorage and server-side session storage

### Debug Mode
Enable debug mode in your `.env` file:
```env
APP_DEBUG=true
```

## Contributing

When adding new features:
1. Follow the existing component structure
2. Add translations for both languages
3. Ensure mobile responsiveness
4. Test with both themes
5. Validate accessibility compliance

## License

This dashboard is part of the Restaurant POS system and follows the same licensing terms.
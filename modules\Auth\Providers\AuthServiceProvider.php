<?php

namespace Modules\Auth\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Auth\Services\AuthService;
class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(AuthService::class, function ($app) {
            return new AuthService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // No specific middleware alias needed for Sanctum here, as it uses 'auth:api' guard directly.
        
        // Load routes with proper middleware groups
        Route::group([
            'middleware' => ['api'],
            'prefix' => 'api',
            'namespace' => 'Modules\\Auth\\Http\\Controllers',
        ], function () {
            $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        });
        
        Route::group([
            'middleware' => ['web'],
            'namespace' => 'Modules\\Auth\\Http\\Controllers',
        ], function () {
            $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        });
    }
}

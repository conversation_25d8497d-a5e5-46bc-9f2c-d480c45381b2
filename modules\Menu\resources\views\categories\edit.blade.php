@extends('menu::layouts.app')

@section('title', 'Edit Category')

@section('header-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('menu.web.categories.show', $category->id) }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Category
        </a>
        <a href="{{ route('menu.web.categories.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-list me-1"></i>
            All Categories
        </a>
    </div>
@endsection

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Edit Category</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('menu.web.categories.update', $category->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- Category Name -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $category->name) }}" required 
                                   placeholder="e.g., Appetizers, Main Course">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Menu Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="menu_id" class="form-label">Menu <span class="text-danger">*</span></label>
                            <select class="form-select @error('menu_id') is-invalid @enderror" 
                                    id="menu_id" name="menu_id" required>
                                <option value="">Select Menu</option>
                                @if(isset($menus))
                                    @foreach($menus as $menu)
                                        <option value="{{ $menu->id }}" 
                                                {{ old('menu_id', $category->menu_id) == $menu->id ? 'selected' : '' }}>
                                            {{ $menu->name }} ({{ ucfirst(str_replace('_', ' ', $menu->menu_type)) }})
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            @error('menu_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" 
                                  placeholder="Optional description for this category">{{ old('description', $category->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <!-- Sort Order -->
                        <div class="col-md-6 mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', $category->sort_order) }}" 
                                   min="0" placeholder="0">
                            <small class="text-muted">Lower numbers appear first</small>
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Status -->
                        <div class="col-md-6 mb-3 d-flex align-items-center">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" 
                                       id="is_active" name="is_active" value="1"
                                       {{ old('is_active', $category->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    <strong>Active Category</strong>
                                </label>
                            </div>
                            <div class="ms-3">
                                <small class="text-muted">Inactive categories won't be visible to customers</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Category Image (Optional) -->
                    <div class="mb-3">
                        <label for="image_url" class="form-label">Category Image URL</label>
                        <input type="url" class="form-control @error('image_url') is-invalid @enderror" 
                               id="image_url" name="image_url" value="{{ old('image_url', $category->image_url) }}" 
                               placeholder="https://example.com/image.jpg">
                        <small class="text-muted">Optional: Add an image URL to represent this category</small>
                        @error('image_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Current Image Preview -->
                    @if($category->image_url)
                        <div class="mb-3">
                            <label class="form-label">Current Image</label>
                            <div class="border rounded p-2">
                                <img src="{{ $category->image_url }}" alt="{{ $category->name }}" 
                                     class="img-thumbnail" style="max-height: 100px; object-fit: cover;">
                                <div class="mt-2">
                                    <small class="text-muted">{{ $category->image_url }}</small>
                                </div>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Preview Section -->
                    <div class="mb-4">
                        <h6>Preview</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;" id="category-icon">
                                            @if($category->image_url)
                                                <img src="{{ $category->image_url }}" alt="Category" 
                                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;">
                                            @else
                                                <i class="bi bi-grid-3x3-gap text-white"></i>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col">
                                        <h6 class="mb-1" id="preview-name">{{ $category->name }}</h6>
                                        <p class="mb-0 text-muted small" id="preview-description">
                                            {{ $category->description ?: 'Category description will appear here' }}
                                        </p>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge {{ $category->is_active ? 'bg-success' : 'bg-secondary' }}" id="preview-status">
                                            {{ $category->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Menu Items Warning -->
                    @if($category->menuItems && $category->menuItems->count() > 0)
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong> This category has {{ $category->menuItems->count() }} menu item(s). 
                            Changing the menu will move all items to the new menu.
                        </div>
                    @endif
                    
                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('menu.web.categories.show', $category->id) }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-1"></i>
                            Cancel
                        </a>
                        <div>
                            <button type="submit" name="action" value="save" class="btn btn-primary me-2">
                                <i class="bi bi-check-circle me-1"></i>
                                Update Category
                            </button>
                            <button type="submit" name="action" value="save_and_add_item" class="btn btn-success">
                                <i class="bi bi-plus-circle me-1"></i>
                                Update & Add Item
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Danger Zone -->
        <div class="card mt-4 border-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    Danger Zone
                </h6>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h6>Delete Category</h6>
                        <p class="mb-0 text-muted">
                            Permanently delete this category and all its menu items. This action cannot be undone.
                        </p>
                    </div>
                    <div class="col-auto">
                        <button type="button" class="btn btn-outline-danger" onclick="deleteCategory()">
                            <i class="bi bi-trash me-1"></i>
                            Delete Category
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Warning!</strong> This will permanently delete the category "{{ $category->name }}" and all its associated menu items.
                </div>
                <p>Are you sure you want to delete this category? This action cannot be undone.</p>
                @if($category->menuItems && $category->menuItems->count() > 0)
                    <div class="alert alert-warning">
                        <strong>{{ $category->menuItems->count() }} menu item(s)</strong> will also be deleted.
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ route('menu.web.categories.destroy', $category->id) }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>
                        Delete Category
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Real-time preview updates
document.getElementById('name').addEventListener('input', function() {
    const previewName = document.getElementById('preview-name');
    previewName.textContent = this.value || 'Category Name';
});

document.getElementById('description').addEventListener('input', function() {
    const previewDescription = document.getElementById('preview-description');
    previewDescription.textContent = this.value || 'Category description will appear here';
});

document.getElementById('is_active').addEventListener('change', function() {
    const previewStatus = document.getElementById('preview-status');
    if (this.checked) {
        previewStatus.textContent = 'Active';
        previewStatus.className = 'badge bg-success';
    } else {
        previewStatus.textContent = 'Inactive';
        previewStatus.className = 'badge bg-secondary';
    }
});

// Image preview
document.getElementById('image_url').addEventListener('input', function() {
    const categoryIcon = document.getElementById('category-icon');
    if (this.value) {
        categoryIcon.innerHTML = `<img src="${this.value}" alt="Category" style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;">`;
    } else {
        categoryIcon.innerHTML = '<i class="bi bi-grid-3x3-gap text-white"></i>';
    }
});

// Delete category function
function deleteCategory() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteCategoryModal'));
    deleteModal.show();
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const menuId = document.getElementById('menu_id').value;
    
    if (!name) {
        e.preventDefault();
        alert('Please enter a category name');
        document.getElementById('name').focus();
        return;
    }
    
    if (!menuId) {
        e.preventDefault();
        alert('Please select a menu');
        document.getElementById('menu_id').focus();
        return;
    }
});

// Warn about menu change if category has items
document.getElementById('menu_id').addEventListener('change', function() {
    const originalMenuId = '{{ $category->menu_id }}';
    const itemCount = {{ $category->menuItems ? $category->menuItems->count() : 0 }};
    
    if (this.value !== originalMenuId && itemCount > 0) {
        if (!confirm(`This category has ${itemCount} menu item(s). Changing the menu will move all items to the new menu. Continue?`)) {
            this.value = originalMenuId;
        }
    }
});
</script>
@endpush
<?php

namespace Modules\Menu\Helpers;

class MenuHelper
{
    /**
     * Calculate menu item profit margin
     */
    public static function calculateProfitMargin(float $price, float $costPrice): float
    {
        if ($costPrice <= 0) {
            return 0;
        }
        
        return round((($price - $costPrice) / $price) * 100, 2);
    }

    /**
     * Format price for display
     */
    public static function formatPrice(float $price, string $currency = '$'): string
    {
        return $currency . number_format($price, 2);
    }

    /**
     * Get dietary information options
     */
    public static function getDietaryOptions(): array
    {
        return [
            'vegetarian' => 'Vegetarian',
            'vegan' => 'Vegan',
            'gluten_free' => 'Gluten Free',
            'dairy_free' => 'Dairy Free',
            'nut_free' => 'Nut Free',
            'halal' => 'Halal',
            'kosher' => 'Kosher',
            'keto' => 'Keto Friendly',
            'low_carb' => 'Low Carb',
            'high_protein' => 'High Protein',
        ];
    }

    /**
     * Get common allergens
     */
    public static function getAllergens(): array
    {
        return [
            'nuts' => 'Nuts',
            'dairy' => 'Dairy',
            'eggs' => 'Eggs',
            'soy' => 'Soy',
            'wheat' => 'Wheat/Gluten',
            'fish' => 'Fish',
            'shellfish' => 'Shellfish',
            'sesame' => 'Sesame',
        ];
    }

    /**
     * Generate menu item SKU
     */
    public static function generateSKU(string $categoryCode, string $itemName): string
    {
        $nameCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $itemName), 0, 4));
        $timestamp = substr(time(), -4);
        
        return $categoryCode . '-' . $nameCode . '-' . $timestamp;
    }

    /**
     * Calculate preparation time category
     */
    public static function getPreparationTimeCategory(int $minutes): string
    {
        if ($minutes <= 5) {
            return 'Quick';
        } elseif ($minutes <= 15) {
            return 'Standard';
        } elseif ($minutes <= 30) {
            return 'Extended';
        } else {
            return 'Long';
        }
    }

    /**
     * Get calorie category
     */
    public static function getCalorieCategory(?int $calories): string
    {
        if (!$calories) {
            return 'Unknown';
        }
        
        if ($calories < 200) {
            return 'Light';
        } elseif ($calories < 400) {
            return 'Moderate';
        } elseif ($calories < 600) {
            return 'Substantial';
        } else {
            return 'Heavy';
        }
    }

    /**
     * Validate menu item data
     */
    public static function validateMenuItemData(array $data): array
    {
        $errors = [];
        
        if (empty($data['name'])) {
            $errors[] = 'Menu item name is required';
        }
        
        if (!isset($data['price']) || $data['price'] <= 0) {
            $errors[] = 'Valid price is required';
        }
        
        if (!isset($data['category_id'])) {
            $errors[] = 'Category is required';
        }
        
        return $errors;
    }

    /**
     * Get menu item status badge class
     */
    public static function getStatusBadgeClass(bool $isAvailable): string
    {
        return $isAvailable ? 'badge-success' : 'badge-danger';
    }

    /**
     * Get menu item status text
     */
    public static function getStatusText(bool $isAvailable): string
    {
        return $isAvailable ? 'Available' : 'Unavailable';
    }

    /**
     * Calculate total price with modifiers
     */
    public static function calculateTotalPrice(float $basePrice, array $modifiers = []): float
    {
        $total = $basePrice;
        
        foreach ($modifiers as $modifier) {
            $total += $modifier['price'] ?? 0;
        }
        
        return round($total, 2);
    }

    /**
     * Format dietary info for display
     */
    public static function formatDietaryInfo(?string $dietaryInfo): array
    {
        if (!$dietaryInfo) {
            return [];
        }
        
        $info = json_decode($dietaryInfo, true);
        if (!is_array($info)) {
            return [];
        }
        
        $options = self::getDietaryOptions();
        $formatted = [];
        
        foreach ($info as $key) {
            if (isset($options[$key])) {
                $formatted[] = $options[$key];
            }
        }
        
        return $formatted;
    }

    /**
     * Format allergens for display
     */
    public static function formatAllergens(?string $allergens): array
    {
        if (!$allergens) {
            return [];
        }
        
        $allergenList = json_decode($allergens, true);
        if (!is_array($allergenList)) {
            return [];
        }
        
        $options = self::getAllergens();
        $formatted = [];
        
        foreach ($allergenList as $key) {
            if (isset($options[$key])) {
                $formatted[] = $options[$key];
            }
        }
        
        return $formatted;
    }
}
<?php

namespace Modules\Menu\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\MenuService;
use Modules\Menu\Http\Requests\StoreMenuRequest;
use Modules\Menu\Http\Requests\UpdateMenuRequest;
use Modules\Menu\Http\Resources\MenuResource;

class MenuController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Get all menus
     */
    public function getMenus(Request $request): JsonResponse
    {
        $menus = $this->menuService->getAllMenus($request->all());
        return response()->json(MenuResource::collection($menus));
    }

    /**
     * Store a newly created menu
     */
    public function storeMenu(StoreMenuRequest $request): JsonResponse
    {
        $menu = $this->menuService->createMenu($request->validated());
        return response()->json(new MenuResource($menu), 201);
    }

    /**
     * Display the specified menu
     */
    public function showMenu(string $id): JsonResponse
    {
        $menu = $this->menuService->getMenuById($id);
        return response()->json(new MenuResource($menu));
    }

    /**
     * Update the specified menu
     */
    public function updateMenu(UpdateMenuRequest $request, string $id): JsonResponse
    {
        $menu = $this->menuService->updateMenu($id, $request->validated());
        return response()->json(new MenuResource($menu));
    }

    /**
     * Remove the specified menu
     */
    public function destroyMenu(string $id): JsonResponse
    {
        try {
            $this->menuService->deleteMenu($id);
            return response()->json(['message' => 'Menu deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Get menus for a specific branch
     */
    public function getMenusForBranch(string $branchId): JsonResponse
    {
        $menus = $this->menuService->getMenusForBranch($branchId);
        return response()->json(MenuResource::collection($menus));
    }
}
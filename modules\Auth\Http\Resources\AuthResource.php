<?php

namespace Modules\Auth\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuthResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'access_token' => $this->resource['access_token'] ?? null,
            'token_type' => 'Bearer',
            'expires_at' => $this->resource['expires_at'] ?? null,
            'user' => new UserResource($this->resource['user']),
            'abilities' => $this->resource['abilities'] ?? [],
        ];
    }
}
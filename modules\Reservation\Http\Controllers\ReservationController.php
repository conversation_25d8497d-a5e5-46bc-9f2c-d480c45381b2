<?php

namespace Modules\Reservation\Http\Controllers;

use App\Models\Reservation;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Carbon;
use Modules\Reservation\Http\Requests\StoreReservationRequest;
use Modules\Reservation\Http\Requests\UpdateReservationRequest;
use Modules\Reservation\Http\Requests\CheckAvailabilityRequest;
use Modules\Reservation\Services\ReservationService;

class ReservationController extends Controller
{
    protected $reservationService;

    public function __construct(ReservationService $reservationService)
    {
        $this->reservationService = $reservationService;
    }

    /**
     * Display a listing of reservations
     */
    public function index(Request $request)
    {
        $query = Reservation::with(['branch', 'table', 'area', 'reservationStatus', 'customer']);

        // Filter by branch if provided
        if ($request->has('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by date range
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('reservation_datetime', [
                Carbon::parse($request->start_date),
                Carbon::parse($request->end_date)
            ]);
        }

        // Filter by status
        if ($request->has('status_id')) {
            $query->where('reservation_status_id', $request->status_id);
        }

        // Filter by today's reservations
        if ($request->has('today') && $request->today) {
            $query->whereDate('reservation_datetime', Carbon::today());
        }

        $reservations = $query->orderBy('reservation_datetime')->paginate($request->get('per_page', 15));
        
        return response()->json($reservations);
    }

    /**
     * Store a newly created reservation
     */
    public function store(StoreReservationRequest $request)
    {
        try {
            $reservation = $this->reservationService->createReservation($request->validated());
            return response()->json($reservation->load(['branch', 'table', 'area', 'reservationStatus']), 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create reservation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified reservation
     */
    public function show(Reservation $reservation)
    {
        return response()->json($reservation->load(['branch', 'table', 'area', 'reservationStatus', 'customer']));
    }

    /**
     * Update the specified reservation
     */
    public function update(UpdateReservationRequest $request, Reservation $reservation)
    {
        try {
            $updatedReservation = $this->reservationService->updateReservation($reservation, $request->validated());
            return response()->json($updatedReservation->load(['branch', 'table', 'area', 'reservationStatus']));
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update reservation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified reservation
     */
    public function destroy(Reservation $reservation)
    {
        try {
            $reservation->delete();
            return response()->json(null, 204);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete reservation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check table availability
     */
    public function checkAvailability(CheckAvailabilityRequest $request)
    {
        try {
            $datetime = Carbon::parse($request->reservation_datetime);
            $availableTables = $this->reservationService->checkTableAvailability(
                $request->branch_id,
                $datetime,
                $request->duration_minutes ?? 120,
                $request->party_size
            );

            return response()->json([
                'available_tables' => $availableTables,
                'total_available' => $availableTables->count(),
                'requested_datetime' => $datetime->format('Y-m-d H:i:s'),
                'duration_minutes' => $request->duration_minutes ?? 120
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to check availability',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel a reservation
     */
    public function cancel(Request $request, Reservation $reservation)
    {
        try {
            $reason = $request->input('reason');
            $cancelledReservation = $this->reservationService->cancelReservation($reservation, $reason);
            
            return response()->json([
                'message' => 'Reservation cancelled successfully',
                'reservation' => $cancelledReservation->load(['branch', 'table', 'area', 'reservationStatus'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to cancel reservation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark reservation as seated
     */
    public function seat(Request $request, Reservation $reservation)
    {
        try {
            $tableId = $request->input('table_id');
            $seatedReservation = $this->reservationService->seatReservation($reservation, $tableId);
            
            return response()->json([
                'message' => 'Reservation marked as seated',
                'reservation' => $seatedReservation->load(['branch', 'table', 'area', 'reservationStatus'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to seat reservation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete a reservation
     */
    public function complete(Reservation $reservation)
    {
        try {
            $completedReservation = $this->reservationService->completeReservation($reservation);
            
            return response()->json([
                'message' => 'Reservation completed successfully',
                'reservation' => $completedReservation->load(['branch', 'table', 'area', 'reservationStatus'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to complete reservation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get today's reservations for a branch
     */
    public function todayReservations(Request $request)
    {
        try {
            $branchId = $request->input('branch_id');
            if (!$branchId) {
                return response()->json(['message' => 'Branch ID is required'], 400);
            }

            $reservations = $this->reservationService->getTodayReservations($branchId);
            
            return response()->json([
                'date' => Carbon::today()->format('Y-m-d'),
                'total_reservations' => $reservations->count(),
                'reservations' => $reservations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get today\'s reservations',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}